{"_from": "clone@^1.0.2", "_id": "clone@1.0.4", "_inBundle": false, "_integrity": "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==", "_location": "/defaults/clone", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "clone@^1.0.2", "name": "clone", "escapedName": "clone", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/defaults"], "_resolved": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "_shasum": "da309cc263df15994c688ca902179ca3c7cd7c7e", "_spec": "clone@^1.0.2", "_where": "C:\\code\\t\\t101\\front\\node_modules\\defaults", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}], "dependencies": {}, "deprecated": false, "description": "deep cloning of objects and arrays", "devDependencies": {"nodeunit": "~0.9.0"}, "engines": {"node": ">=0.8"}, "homepage": "https://github.com/pvorb/node-clone#readme", "license": "MIT", "main": "clone.js", "name": "clone", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "scripts": {"test": "nodeunit test.js"}, "tags": ["clone", "object", "array", "function", "date"], "version": "1.0.4"}