{"_from": "dns-equal@^1.0.0", "_id": "dns-equal@1.0.0", "_inBundle": false, "_integrity": "sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg==", "_location": "/dns-equal", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dns-equal@^1.0.0", "name": "dns-equal", "escapedName": "dns-equal", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/bonjour"], "_resolved": "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz", "_shasum": "b39e7f1da6eb0a75ba9c17324b34753c47e0654d", "_spec": "dns-equal@^1.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\bonjour", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son"}, "bugs": {"url": "https://github.com/watson/dns-equal/issues"}, "bundleDependencies": false, "coordinates": [56.010004025953165, 11.961870541375674], "dependencies": {}, "deprecated": false, "description": "Compare DNS record strings for equality", "devDependencies": {"standard": "^5.4.1"}, "homepage": "https://github.com/watson/dns-equal#readme", "keywords": ["dns", "compare", "comparing", "equal", "equality", "match", "downcase", "lowercase", "case-insensitive"], "license": "MIT", "main": "index.js", "name": "dns-equal", "repository": {"type": "git", "url": "git+https://github.com/watson/dns-equal.git"}, "scripts": {"test": "standard && node test.js"}, "version": "1.0.0"}