{"_from": "define-data-property@^1.1.4", "_id": "define-data-property@1.1.4", "_inBundle": false, "_integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==", "_location": "/define-data-property", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "define-data-property@^1.1.4", "name": "define-data-property", "escapedName": "define-data-property", "rawSpec": "^1.1.4", "saveSpec": null, "fetchSpec": "^1.1.4"}, "_requiredBy": ["/define-properties", "/set-function-length", "/set-function-name"], "_resolved": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "_shasum": "894dc141bb7d3060ae4366f6a0107e68fbe48c5e", "_spec": "define-data-property@^1.1.4", "_where": "C:\\code\\t\\t101\\front\\node_modules\\set-function-length", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/define-data-property/issues"}, "bundleDependencies": false, "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "deprecated": false, "description": "Define a data property on an object. Will fall back to assignment in an engine without descriptors.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/call-bind": "^1.0.5", "@types/define-properties": "^1.1.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/get-intrinsic": "^1.2.2", "@types/gopd": "^1.0.3", "@types/has-property-descriptors": "^1.0.3", "@types/object-inspect": "^1.8.4", "@types/object.getownpropertydescriptors": "^2.1.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "hasown": "^2.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "object.getownpropertydescriptors": "^2.1.7", "reflect.ownkeys": "^1.1.4", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/define-data-property#readme", "keywords": ["define", "data", "property", "object", "accessor", "javascript", "ecmascript", "enumerable", "configurable", "writable"], "license": "MIT", "main": "index.js", "name": "define-data-property", "publishConfig": {"ignore": [".github/workflows", "types/reflect.ownkeys"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/define-data-property.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "tsc": "tsc -p .", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "types": "./index.d.ts", "version": "1.1.4"}