{"_from": "entities@^2.0.0", "_id": "entities@2.2.0", "_inBundle": false, "_integrity": "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==", "_location": "/htmlparser2/entities", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "entities@^2.0.0", "name": "entities", "escapedName": "entities", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/htmlparser2", "/htmlparser2/dom-serializer"], "_resolved": "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz", "_shasum": "098dc90ebb83d8dffa089d55256b351d34c4da55", "_spec": "entities@^2.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\htmlparser2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/entities/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Encode & decode XML and HTML entities with ease", "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.11.8", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "coveralls": "*", "eslint": "^7.11.0", "eslint-config-prettier": "^7.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^26.5.3", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "directories": {"lib": "lib/"}, "files": ["lib/**/*"], "funding": "https://github.com/fb55/entities?sponsor=1", "homepage": "https://github.com/fb55/entities#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "keywords": ["entity", "decoding", "encoding", "html", "xml", "html entities"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "name": "entities", "prettier": {"tabWidth": 4, "proseWrap": "always"}, "repository": {"type": "git", "url": "git://github.com/fb55/entities.git"}, "scripts": {"build": "tsc && cp -r src/maps lib", "coverage": "cat coverage/lcov.info | coveralls", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint .", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier '**/*.{ts,md,json,yml}'", "test": "jest --coverage && npm run lint"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "2.2.0"}