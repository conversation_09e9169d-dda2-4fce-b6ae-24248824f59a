{"_from": "css-tree@1.0.0-alpha.29", "_id": "css-tree@1.0.0-alpha.29", "_inBundle": false, "_integrity": "sha512-sRNb1XydwkW9IOci6iB2xmy8IGCj6r/fr+JWitvJ2JxQRPzN3T4AGGVWCMlVmVwM1gtgALJRmGIlWv5ppnGGkg==", "_location": "/csso/css-tree", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "css-tree@1.0.0-alpha.29", "name": "css-tree", "escapedName": "css-tree", "rawSpec": "1.0.0-alpha.29", "saveSpec": null, "fetchSpec": "1.0.0-alpha.29"}, "_requiredBy": ["/csso"], "_resolved": "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.29.tgz", "_shasum": "3fa9d4ef3142cbd1c301e7664c1f352bd82f5a39", "_spec": "css-tree@1.0.0-alpha.29", "_where": "C:\\code\\t\\t101\\front\\node_modules\\csso", "author": {"name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com", "url": "https://github.com/lahmatiy"}, "browser": {"./data": "./dist/default-syntax.json"}, "bugs": {"url": "https://github.com/csstree/csstree/issues"}, "bundleDependencies": false, "dependencies": {"mdn-data": "~1.1.0", "source-map": "^0.5.3"}, "deprecated": false, "description": "CSSTree is a tool set to work with CSS, including fast detailed parser (string->AST), walker (AST traversal), generator (AST->string) and lexer (validation and matching) based on knowledge of spec and browser implementations", "devDependencies": {"browserify": "^13.0.0", "coveralls": "^2.11.6", "eslint": "^2.13.1", "istanbul": "^0.4.2", "jscs": "~3.0.7", "json-to-ast": "~2.0.1", "mocha": "^3.0.2", "uglify-js": "^2.6.1"}, "engines": {"node": ">=0.10.0"}, "eslintConfig": {"env": {"node": true, "mocha": true, "es6": true}, "rules": {"no-duplicate-case": 2, "no-undef": 2, "no-unused-vars": [2, {"vars": "all", "args": "after-used"}]}}, "files": ["bin", "data", "dist/csstree.js", "dist/default-syntax.json", "docs", "lib", "CHANGELOG.md", "LICENSE", "README.md"], "homepage": "https://github.com/csstree/csstree", "keywords": ["css", "ast", "tokenizer", "parser", "walker", "lexer", "generator", "utils", "syntax", "validation"], "license": "MIT", "main": "./lib/index", "maintainers": [{"name": "<PERSON>", "email": "rd<PERSON><PERSON>@gmail.com"}], "name": "css-tree", "repository": {"type": "git", "url": "git+https://github.com/csstree/csstree.git"}, "scripts": {"build": "npm run gen:syntax && browserify --standalone csstree lib/index.js | uglifyjs --compress --mangle -o dist/csstree.js", "coverage": "istanbul cover _mocha -- -R min", "coveralls": "istanbul cover _mocha --report lcovonly -- -R min && cat ./coverage/lcov.info | coveralls", "gen:syntax": "node scripts/gen-syntax-data", "hydrogen": "node --trace-hydrogen --trace-phase=Z --trace-deopt --code-comments --hydrogen-track-positions --redirect-code-traces --redirect-code-traces-to=code.asm --trace_hydrogen_file=code.cfg --print-opt-code bin/parse --stat -o /dev/null", "lint": "jscs data lib scripts test && eslint data lib scripts test && node scripts/review-syntax-patch --lint && node scripts/update-docs --lint", "lint-and-test": "npm run lint && npm test", "prepublish": "npm run build", "review:syntax-patch": "node scripts/review-syntax-patch", "test": "mocha --reporter progress", "travis": "npm run lint-and-test && npm run coveralls", "update:docs": "node scripts/update-docs"}, "version": "1.0.0-alpha.29"}