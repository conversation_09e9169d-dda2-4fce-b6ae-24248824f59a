{"remainingRequest": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\code\\t\\157\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\code\\t\\157\\front\\src\\components\\common\\BreadCrumbs.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\code\\t\\157\\front\\src\\components\\common\\BreadCrumbs.vue", "mtime": 1645616830448}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["pathToRegexp", "generateTitle", "data", "levelList", "watch", "$route", "getBreadcrumb", "created", "breadcrumbStyleChange", "methods", "route", "matched", "filter", "item", "meta", "first", "path", "concat", "isDashboard", "name", "trim", "toLocaleLowerCase", "pathCompile", "params", "to<PERSON><PERSON>", "compile", "handleLink", "redirect", "$router", "push", "val", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "innerText", "style", "color", "str", "headHeight", "parseInt", "marginTop"], "sources": ["src/components/common/BreadCrumbs.vue"], "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\" style=\"height:50px;backgroundColor:rgba(255, 255, 255, 1);borderRadius:4px;padding:0px 20px 0px 20px;boxShadow:0px 0px 0px #f903d4;borderWidth:0px;borderStyle:dotted solid double dashed;borderColor:#ff0000;\">\r\n    <transition-group name=\"breadcrumb\" class=\"box\" :style=\"1==1?'justifyContent:flex-start;':1==2?'justifyContent:center;':'justifyContent:flex-end;'\">\r\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.name }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.name }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { generateTitle } from '@/utils/i18n'\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n    this.breadcrumbStyleChange()\r\n  },\r\n  methods: {\r\n    generateTitle,\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let route = this.$route\r\n      let matched = route.matched.filter(item => item.meta)\r\n      const first = matched[0]\r\n      matched = [{ path: '/index' }].concat(matched)\r\n\r\n      this.levelList = matched.filter(item => item.meta)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim().toLocaleLowerCase() === 'Index'.toLocaleLowerCase()\r\n    },\r\n    pathCompile(path) {\r\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n      const { params } = this.$route\r\n      var toPath = pathToRegexp.compile(path)\r\n      return toPath(params)\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    },\r\n    breadcrumbStyleChange(val) {\r\n      this.$nextTick(()=>{\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__separator').forEach(el=>{\n          el.innerText = \"/\"\n          el.style.color = \"rgba(255, 69, 0, 1)\"\n        })\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner a').forEach(el=>{\n          el.style.color = \"#303133\"\n        })\n        document.querySelectorAll('.app-breadcrumb .el-breadcrumb__inner .no-redirect').forEach(el=>{\n          el.style.color = \"rgba(25, 169, 123, 1)\"\n        })\n\n        let str = \"vertical\"\n        if(\"vertical\" === str) {\n          let headHeight = \"70px\"\n          headHeight = parseInt(headHeight) + 10 + 'px'\n          document.querySelectorAll('.app-breadcrumb').forEach(el=>{\n            el.style.marginTop = headHeight\n          })\n        }\n\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb {\r\n  display: block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n\r\n  .box {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 100%;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n  }\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAYA,OAAAA,YAAA;AACA,SAAAC,aAAA;AACA;EACAC,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,OAAA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAD,aAAA;IACA,KAAAE,qBAAA;EACA;EACAC,OAAA;IACAR,aAAA;IACAK,cAAA;MACA;MACA,IAAAI,KAAA,QAAAL,MAAA;MACA,IAAAM,OAAA,GAAAD,KAAA,CAAAC,OAAA,CAAAC,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,IAAA;MACA,MAAAC,KAAA,GAAAJ,OAAA;MACAA,OAAA;QAAAK,IAAA;MAAA,GAAAC,MAAA,CAAAN,OAAA;MAEA,KAAAR,SAAA,GAAAQ,OAAA,CAAAC,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,IAAA;IACA;IACAI,YAAAR,KAAA;MACA,MAAAS,IAAA,GAAAT,KAAA,IAAAA,KAAA,CAAAS,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,iBAAA,eAAAA,iBAAA;IACA;IACAC,YAAAN,IAAA;MACA;MACA;QAAAO;MAAA,SAAAlB,MAAA;MACA,IAAAmB,MAAA,GAAAxB,YAAA,CAAAyB,OAAA,CAAAT,IAAA;MACA,OAAAQ,MAAA,CAAAD,MAAA;IACA;IACAG,WAAAb,IAAA;MACA;QAAAc,QAAA;QAAAX;MAAA,IAAAH,IAAA;MACA,IAAAc,QAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,QAAA;QACA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAb,IAAA;IACA;IACAR,sBAAAsB,GAAA;MACA,KAAAC,SAAA;QACAC,QAAA,CAAAC,gBAAA,8CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,SAAA;UACAD,EAAA,CAAAE,KAAA,CAAAC,KAAA;QACA;QACAN,QAAA,CAAAC,gBAAA,4CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAE,KAAA,CAAAC,KAAA;QACA;QACAN,QAAA,CAAAC,gBAAA,uDAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAE,KAAA,CAAAC,KAAA;QACA;QAEA,IAAAC,GAAA;QACA,mBAAAA,GAAA;UACA,IAAAC,UAAA;UACAA,UAAA,GAAAC,QAAA,CAAAD,UAAA;UACAR,QAAA,CAAAC,gBAAA,oBAAAC,OAAA,CAAAC,EAAA;YACAA,EAAA,CAAAE,KAAA,CAAAK,SAAA,GAAAF,UAAA;UACA;QACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}