<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ChongwulingyangDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.chongwulingyang_name as chongwulingyangName
        ,a.chongwu_types as chongwuTypes
        ,a.chongwulingyang_photo as chongwulingyangPhoto
        ,a.jieshu_types as jieshuTypes
        ,a.chongwulingyang_content as chongwulingyangContent
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.ChongwulingyangView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段

        FROM chongwulingyang  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.chongwulingyangName != '' and params.chongwulingyangName != null and params.chongwulingyangName != 'null' ">
                and a.chongwulingyang_name like CONCAT('%',#{params.chongwulingyangName},'%')
            </if>
            <if test="params.chongwuTypes != null and params.chongwuTypes != ''">
                and a.chongwu_types = #{params.chongwuTypes}
            </if>
            <if test="params.jieshuTypes != null and params.jieshuTypes != ''">
                and a.jieshu_types = #{params.jieshuTypes}
            </if>
            <if test=" params.chongwulingyangContent != '' and params.chongwulingyangContent != null and params.chongwulingyangContent != 'null' ">
                and a.chongwulingyang_content like CONCAT('%',#{params.chongwulingyangContent},'%')
            </if>

        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>