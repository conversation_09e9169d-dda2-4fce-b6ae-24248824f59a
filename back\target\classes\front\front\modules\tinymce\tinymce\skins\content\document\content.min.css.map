{"version": 3, "sources": ["content/document/content.css"], "names": [], "mappings": ";;;;;;AAMA,cACE,KACE,WAAY,SAGhB,KACE,YAAa,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,MAAM,CAAE,MAAM,CAAE,SAAS,CAAE,WAAW,CAAE,gBAAgB,CAAE,WAEhI,cACE,KACE,iBAAkB,KAClB,WAAY,EAAE,EAAE,IAAI,gBACpB,WAAY,WACZ,OAAQ,KAAK,KAAK,EAClB,UAAW,MACX,WAAY,mBACZ,QAAS,KAAK,KAAK,KAAK,MAG5B,MACE,gBAAiB,SAGnB,SADA,SAEE,OAAQ,IAAI,MAAM,KAClB,QAAS,MAEX,kBACE,MAAO,KACP,WAAY,OACZ,WAAY,OAEd,GACE,aAAc,KACd,aAAc,MACd,aAAc,IAAI,EAAE,EAAE,EAExB,4CACE,YAAa,IAAI,MAAM,KACvB,YAAa,OACb,aAAc,KAEhB,sCACE,aAAc,IAAI,MAAM,KACxB,aAAc,OACd,cAAe", "file": "content.min.css", "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n */\n@media screen {\n  html {\n    background: #f4f4f4;\n  }\n}\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n}\n@media screen {\n  body {\n    background-color: #fff;\n    box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);\n    box-sizing: border-box;\n    margin: 1rem auto 0;\n    max-width: 820px;\n    min-height: calc(100vh - 1rem);\n    padding: 4rem 6rem 6rem 6rem;\n  }\n}\ntable {\n  border-collapse: collapse;\n}\ntable th,\ntable td {\n  border: 1px solid #ccc;\n  padding: 0.4rem;\n}\nfigure figcaption {\n  color: #999;\n  margin-top: 0.25rem;\n  text-align: center;\n}\nhr {\n  border-color: #ccc;\n  border-style: solid;\n  border-width: 1px 0 0 0;\n}\n.mce-content-body:not([dir=rtl]) blockquote {\n  border-left: 2px solid #ccc;\n  margin-left: 1.5rem;\n  padding-left: 1rem;\n}\n.mce-content-body[dir=rtl] blockquote {\n  border-right: 2px solid #ccc;\n  margin-right: 1.5rem;\n  padding-right: 1rem;\n}\n"]}