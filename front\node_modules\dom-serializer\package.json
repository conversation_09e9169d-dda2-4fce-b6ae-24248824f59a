{"_from": "dom-serializer@0", "_id": "dom-serializer@0.2.2", "_inBundle": false, "_integrity": "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==", "_location": "/dom-serializer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dom-serializer@0", "name": "dom-serializer", "escapedName": "dom-serializer", "rawSpec": "0", "saveSpec": null, "fetchSpec": "0"}, "_requiredBy": ["/domutils"], "_resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz", "_shasum": "1afb81f533717175d478655debc5e332d9f9bb51", "_spec": "dom-serializer@0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\domutils", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "bundleDependencies": false, "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "deprecated": false, "description": "render dom nodes to string", "devDependencies": {"cheerio": "^1.0.0-rc.2", "expect.js": "~0.3.1", "htmlparser2": "^3.10.0", "lodash": "^4.17.11", "mocha": "^6.2.0", "xyz": "^3.0.0"}, "files": ["index.js", "index.d.ts", "foreignNames.json"], "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "keywords": ["html", "xml", "render"], "license": "MIT", "main": "./index.js", "name": "dom-serializer", "prettier": {"singleQuote": true}, "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "scripts": {"test": "mocha test.js"}, "version": "0.2.2"}