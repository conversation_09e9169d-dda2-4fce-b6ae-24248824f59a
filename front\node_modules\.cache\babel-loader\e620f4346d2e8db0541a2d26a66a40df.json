{"remainingRequest": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\code\\t\\157\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\code\\t\\157\\front\\src\\views\\modules\\dictionary\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\code\\t\\157\\front\\src\\views\\modules\\dictionary\\list.vue", "mtime": 1730041301059}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["AddOrUpdate", "styleJs", "data", "searchForm", "key", "sessionTable", "role", "userId", "form", "id", "dicCode", "dicName", "codeIndex", "indexName", "superId", "<PERSON><PERSON><PERSON>", "createTime", "dataList", "pageIndex", "pageSize", "totalPage", "dataListLoading", "dataListSelections", "showFlag", "sfshVisiable", "shForm", "chartVisiable", "echartsDate", "Date", "addOrUpdateFlag", "contents", "layouts", "json_fields", "created", "listStyle", "init", "getDataList", "contentStyleChange", "mounted", "$storage", "get", "filters", "htmlfilter", "val", "replace", "components", "computed", "methods", "chartDialog", "_this", "params", "dateFormat", "riqi", "getFullYear", "thisTable", "tableName", "sumColum", "date", "$nextTick", "statistic", "$echarts", "document", "getElementById", "$http", "url", "method", "then", "code", "yAxisName", "xAxisName", "series", "yAxis", "for<PERSON>ach", "item", "index", "tempMap", "name", "legend", "type", "push", "option", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "toolbox", "feature", "magicType", "show", "saveAsImage", "xAxis", "axisLabel", "formatter", "setOption", "window", "onresize", "resize", "$message", "message", "duration", "onClose", "search", "contentSearchStyleChange", "contentBtnAdAllStyleChange", "contentSearchBtnStyleChange", "contentTableBtnStyleChange", "contentPageStyleChange", "querySelectorAll", "el", "textAlign", "inputFontPosition", "style", "height", "inputHeight", "lineHeight", "inputFontColor", "fontSize", "inputFontSize", "borderWidth", "inputBorderWidth", "borderStyle", "inputBorderStyle", "borderColor", "inputBorderColor", "borderRadius", "inputBorderRadius", "backgroundColor", "inputBgColor", "inputTitle", "inputTitleColor", "inputTitleSize", "setTimeout", "inputIconColor", "searchBtnHeight", "searchBtnFontColor", "searchBtnFontSize", "searchBtnBorderWidth", "searchBtnBorderStyle", "searchBtnBorderColor", "searchBtnBorderRadius", "searchBtnBgColor", "btnAdAllHeight", "btnAdAllAddFontColor", "btnAdAllFontSize", "btnAdAllBorderWidth", "btnAdAllBorderStyle", "btnAdAllBorderColor", "btnAdAllBorderRadius", "btnAdAllAddBgColor", "btnAdAllDelFontColor", "btnAdAllDelBgColor", "btnAdAllWarnFontColor", "btnAdAllWarnBgColor", "rowStyle", "row", "rowIndex", "tableStripe", "tableStripeFontColor", "cellStyle", "tableStripeBgColor", "headerRowStyle", "tableHeaderFontColor", "headerCellStyle", "tableHeaderBgColor", "arr", "pageTotal", "pageSizes", "pagePrevNext", "pagePager", "pageJumper", "join", "pageEachNum", "page", "limit", "sort", "undefined", "list", "total", "sizeChangeHandle", "currentChangeHandle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrUpdateHandler", "crossAddOrUpdateFlag", "$refs", "addOrUpdate", "download", "file", "open", "delete<PERSON><PERSON><PERSON>", "ids", "Number", "map", "$confirm", "confirmButtonText", "cancelButtonText", "error", "msg", "dictionaryUploadSuccess", "dictionaryUploadError"], "sources": ["src/views/modules/dictionary/list.vue"], "sourcesContent": ["<template>\r\n    <div class=\"main-content\">\r\n\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                         \r\n                     <el-form-item :label=\"contents.inputTitle == 1 ? '编码名字' : ''\">\r\n                         <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.indexName\" placeholder=\"编码名字\" clearable></el-input>\r\n                     </el-form-item>\r\n                        \r\n\r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('dictionary','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('dictionary','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/wurenchangku/upload/dictionaryMuBan.xls\"\r\n                        >批量导入字典表数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('dictionary','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"wurenchangku/file/upload\"\r\n                                :on-success=\"dictionaryUploadSuccess\"\r\n                                :on-error=\"dictionaryUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('dictionary','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入字典表数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('dictionary','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"dictionary.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('dictionary','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"dicCode\"\r\n                                   header-align=\"center\"\r\n                                   label=\"字段\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.dicCode}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"dicName\"\r\n                                   header-align=\"center\"\r\n                                   label=\"字段名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.dicName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"codeIndex\"\r\n                                      header-align=\"center\"\r\n                                      label=\"编码\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.codeIndex}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"indexName\"\r\n                                   header-align=\"center\"\r\n                                   label=\"编码名字\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.indexName}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                   prop=\"beizhu\"\r\n                                   header-align=\"center\"\r\n                                   label=\"备注\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.beizhu}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('dictionary','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('dictionary','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('dictionary','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n            userId:\"\",//当前登录人的id\r\n    //级联表下拉框搜索条件\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                dicCode : null,\r\n                dicName : null,\r\n                codeIndex : null,\r\n                indexName : null,\r\n                superId : null,\r\n                beizhu : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                //本表字段\r\n                     '字段': \"dicCode\",\r\n                     '字段名': \"dicName\",\r\n                     '编码': \"codeIndex\",\r\n                     '编码名字': \"indexName\",\r\n                     '备注': \"beizhu\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n            this.userId = this.$storage.get(\"userId\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\", //%Y-%m\r\n                    riqi :_this.echartsDate.getFullYear(),\r\n                    thisTable : {//当前表\r\n                        tableName :\"dictionary\",//当前表表名,\r\n                        sumColum : 'dictionary_number', //求和字段\r\n                        date : 'insert_time',//分组日期字段\r\n                        // string : 'dictionary_name',//分组字符串字段\r\n                        // types : 'dictionary_types',//分组下拉框字段\r\n                    },\r\n                    // joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yonghu\",//级联表表名\r\n                    //     // date : 'insert_time',//分组日期字段\r\n                    //     string : 'yonghu_name',//分组字符串字段\r\n                    //     // types : 'yonghu_types',//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let yAxisName = \"数值\";//根据查询数据具体改(单列要改,多列不改)\r\n                            let xAxisName = \"月份\";\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                // tempMap.name= [\"数值\"];//根据查询数据具体改(单列要改,多列不改)\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: xAxisName,\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: yAxisName,//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value}' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option,true);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                ////饼状图\r\n                //_this.chartVisiable = true;\r\n                // this.$nextTick(()=>{\r\n                //     var statistic = this.$echarts.init(document.getElementById(\"statistic\"),'macarons');\r\n                //     let params = {\r\n                //         tableName: \"dictionary\",\r\n                //         groupColumn: \"dictionary_types\",\r\n                //     }\r\n                //     this.$http({\r\n                //         url: \"newSelectGroupCount\",\r\n                //         method: \"get\",\r\n                //         params: params\r\n                //     }).then(({data}) => {\r\n                //         if (data && data.code === 0) {\r\n                //             let res = data.data;\r\n                //             let xAxis = [];\r\n                //             let yAxis = [];\r\n                //             let pArray = []\r\n                //             for(let i=0;i<res.length;i++){\r\n                //                 xAxis.push(res[i].name);\r\n                //                 yAxis.push(res[i].value);\r\n                //                 pArray.push({\r\n                //                     value: res[i].value,\r\n                //                     name: res[i].name\r\n                //                 })\r\n                //                 var option = {};\r\n                //                 option = {\r\n                //                     title: {\r\n                //                         text: '保险合同类型统计',\r\n                //                         left: 'center'\r\n                //                     },\r\n                //                     tooltip: {\r\n                //                         trigger: 'item',\r\n                //                         formatter: '{b} : {c} ({d}%)'\r\n                //                     },\r\n                //                     series: [\r\n                //                         {\r\n                //                             type: 'pie',\r\n                //                             radius: '55%',\r\n                //                             center: ['50%', '60%'],\r\n                //                             data: pArray,\r\n                //                             emphasis: {\r\n                //                                 itemStyle: {\r\n                //                                     shadowBlur: 10,\r\n                //                                     shadowOffsetX: 0,\r\n                //                                     shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                //                                 }\r\n                //                             }\r\n                //                         }\r\n                //                     ]\r\n                //                 };\r\n                //                 statistic.setOption(option);\r\n                //                 window.onresize = function() {\r\n                //                     statistic.resize();\r\n                //                 };\r\n                //             }\r\n                //         }\r\n                //     });\r\n                // })\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                }\r\n\r\n                                         \r\n                if (this.searchForm.indexName!= '' && this.searchForm.indexName!= undefined) {\r\n                    params['indexName'] = '%' + this.searchForm.indexName + '%'\r\n                }\r\n                        \r\n                params['dictionaryDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"dictionary/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"dictionary/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            dictionaryUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"dictionary/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入字典表数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            dictionaryUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & ::v-deep el-pagination__sizes{\r\n      & ::v-deep el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n  \r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  } \r\n\r\n  .tables {\r\n\t& ::v-deep .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: rgba(88, 84, 84, 1);\r\n\t\tfont-size: 10px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 20px;\r\n\t\tbackground-color: rgba(153, 204, 51, 1);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: rgba(91, 87, 87, 1);\r\n\t\tfont-size: 10px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 20px;\r\n\t\tbackground-color: rgba(255, 255, 102, 1);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: rgba(255, 255, 255, 1);\r\n\t\tfont-size: 10px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 20px;\r\n\t\tbackground-color: rgba(51, 102, 0, 1);\r\n\t}\r\n\r\n    & ::v-deep .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"], "mappings": "AA2LA,OAAAA,WAAA;AACA,OAAAC,OAAA;AACA;EACAC,KAAA;IACA;MACAC,UAAA;QACAC,GAAA;MACA;MACAC,YAAA;MAAA;MACAC,IAAA;MAAA;MACAC,MAAA;MAAA;MACA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,OAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,YAAA;MACAC,MAAA;MACAC,aAAA;MACAC,WAAA,MAAAC,IAAA;MAAA;MACAC,eAAA;MACAC,QAAA;MACAC,OAAA;MAEA;MACAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAH,QAAA,GAAA7B,OAAA,CAAAiC,SAAA;IACA,KAAAC,IAAA;IACA,KAAAC,WAAA;IACA,KAAAC,kBAAA;EACA;EACAC,QAAA;IACA;IACA,KAAAjC,YAAA,QAAAkC,QAAA,CAAAC,GAAA;IACA,KAAAlC,IAAA,QAAAiC,QAAA,CAAAC,GAAA;IACA,KAAAjC,MAAA,QAAAgC,QAAA,CAAAC,GAAA;EAEA;EACAC,OAAA;IACAC,UAAA,WAAAA,CAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,OAAA,aAAAA,OAAA;IACA;EACA;EACAC,UAAA;IACA7C;EACA;EACA8C,QAAA,GACA;EACAC,OAAA;IACAC,YAAA;MACA,IAAAC,KAAA;MACA,IAAAC,MAAA;QACAC,UAAA;QAAA;QACAC,IAAA,EAAAH,KAAA,CAAAtB,WAAA,CAAA0B,WAAA;QACAC,SAAA;UAAA;UACAC,SAAA;UAAA;UACAC,QAAA;UAAA;UACAC,IAAA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAR,KAAA,CAAAvB,aAAA;MACAuB,KAAA,CAAAS,SAAA;QACA,IAAAC,SAAA,QAAAC,QAAA,CAAAzB,IAAA,CAAA0B,QAAA,CAAAC,cAAA;QACA,KAAAC,KAAA;UACAC,GAAA;UACAC,MAAA;UACAf,MAAA,EAAAA;QACA,GAAAgB,IAAA;UAAAhE;QAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;YAEA;YACA;YACA,IAAAC,SAAA;YACA,IAAAC,SAAA;YACA,IAAAC,MAAA;YACApE,IAAA,CAAAA,IAAA,CAAAqE,KAAA,CAAAC,OAAA,WAAAC,IAAA,EAAAC,KAAA;cACA,IAAAC,OAAA;cACA;cACAA,OAAA,CAAAC,IAAA,GAAA1E,IAAA,CAAAA,IAAA,CAAA2E,MAAA,CAAAH,KAAA;cACAC,OAAA,CAAAG,IAAA;cACAH,OAAA,CAAAzE,IAAA,GAAAuE,IAAA;cACAH,MAAA,CAAAS,IAAA,CAAAJ,OAAA;YAEA;YAEA,IAAAK,MAAA;cACAC,OAAA;gBACAC,OAAA;gBACAC,WAAA;kBACAL,IAAA;kBACAM,UAAA;oBACAC,KAAA;kBACA;gBACA;cACA;cACAC,OAAA;gBACAC,OAAA;kBACA;kBACAC,SAAA;oBAAAC,IAAA;oBAAAX,IAAA;kBAAA;kBAAA;kBACA;kBACAY,WAAA;oBAAAD,IAAA;kBAAA;gBACA;cACA;cACAZ,MAAA;gBACA3E,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAA2E,MAAA;cACA;cACAc,KAAA,GACA;gBACAb,IAAA;gBACAF,IAAA,EAAAP,SAAA;gBACAnE,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAAyF,KAAA;gBACAR,WAAA;kBACAL,IAAA;gBACA;cACA,EACA;cACAP,KAAA,GACA;gBACAO,IAAA;gBAAA;gBACAF,IAAA,EAAAR,SAAA;gBAAA;gBACAwB,SAAA;kBACAC,SAAA;gBACA;cACA,EACA;cACAvB,MAAA,EAAAA,MAAA;YACA;YACA;YACAX,SAAA,CAAAmC,SAAA,CAAAd,MAAA;YACA;YACAe,MAAA,CAAAC,QAAA;cACArC,SAAA,CAAAsC,MAAA;YACA;YACA;UACA;YACA,KAAAC,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,EAAAA,CAAA;gBACA,KAAAC,MAAA;cACA;YACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAjE,mBAAA;MACA,KAAAkE,wBAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,sBAAA;IACA;IACAJ,yBAAA;MACA,KAAA7C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,wCAAApC,OAAA,CAAAqC,EAAA;UACA,IAAAC,SAAA;UACA,SAAAhF,QAAA,CAAAiF,iBAAA,OACAD,SAAA;UACA,SAAAhF,QAAA,CAAAiF,iBAAA,OAAAD,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAF,SAAA,GAAAA,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAnF,QAAA,CAAAoF,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAArF,QAAA,CAAAoF,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAvD,QAAA,CAAAsF,cAAA;UACAP,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAvF,QAAA,CAAAwF,aAAA;UACAT,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAAzF,QAAA,CAAA0F,gBAAA;UACAX,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA3F,QAAA,CAAA4F,gBAAA;UACAb,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA7F,QAAA,CAAA8F,gBAAA;UACAf,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAA/F,QAAA,CAAAgG,iBAAA;UACAjB,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAjG,QAAA,CAAAkG,YAAA;QACA;QACA,SAAAlG,QAAA,CAAAmG,UAAA;UACApE,QAAA,CAAA+C,gBAAA,4CAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAvD,QAAA,CAAAoG,eAAA;YACArB,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAvF,QAAA,CAAAqG,cAAA;YACAtB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAArF,QAAA,CAAAoF,WAAA;UACA;QACA;QACAkB,UAAA;UACAvE,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAvD,QAAA,CAAAuG,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAArF,QAAA,CAAAoF,WAAA;UACA;UACArD,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAvD,QAAA,CAAAuG,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAArF,QAAA,CAAAoF,WAAA;UACA;UACArD,QAAA,CAAA+C,gBAAA,uCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAArF,QAAA,CAAAoF,WAAA;UACA;QACA;MACA;IACA;IACA;IACAT,4BAAA;MACA,KAAA/C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,2CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAnF,QAAA,CAAAwG,eAAA;UACAzB,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAvD,QAAA,CAAAyG,kBAAA;UACA1B,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAvF,QAAA,CAAA0G,iBAAA;UACA3B,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAAzF,QAAA,CAAA2G,oBAAA;UACA5B,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA3F,QAAA,CAAA4G,oBAAA;UACA7B,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA7F,QAAA,CAAA6G,oBAAA;UACA9B,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAA/F,QAAA,CAAA8G,qBAAA;UACA/B,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAjG,QAAA,CAAA+G,gBAAA;QACA;MACA;IACA;IACA;IACArC,2BAAA;MACA,KAAA9C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,0CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAnF,QAAA,CAAAgH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAvD,QAAA,CAAAiH,oBAAA;UACAlC,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAvF,QAAA,CAAAkH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAAzF,QAAA,CAAAmH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA3F,QAAA,CAAAoH,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA7F,QAAA,CAAAqH,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAA/F,QAAA,CAAAsH,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAjG,QAAA,CAAAuH,kBAAA;QACA;QACAxF,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAnF,QAAA,CAAAgH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAvD,QAAA,CAAAwH,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAvF,QAAA,CAAAkH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAAzF,QAAA,CAAAmH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA3F,QAAA,CAAAoH,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA7F,QAAA,CAAAqH,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAA/F,QAAA,CAAAsH,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAjG,QAAA,CAAAyH,kBAAA;QACA;QACA1F,QAAA,CAAA+C,gBAAA,0CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAnF,QAAA,CAAAgH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAvD,QAAA,CAAA0H,qBAAA;UACA3C,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAvF,QAAA,CAAAkH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAAzF,QAAA,CAAAmH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA3F,QAAA,CAAAoH,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA7F,QAAA,CAAAqH,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAA/F,QAAA,CAAAsH,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAjG,QAAA,CAAA2H,mBAAA;QACA;MACA;IACA;IACA;IACAC,SAAA;MAAAC,GAAA;MAAAC;IAAA;MACA,IAAAA,QAAA;QACA,SAAA9H,QAAA,CAAA+H,WAAA;UACA;YAAAxE,KAAA,OAAAvD,QAAA,CAAAgI;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,UAAA;MAAAJ,GAAA;MAAAC;IAAA;MACA,IAAAA,QAAA;QACA,SAAA9H,QAAA,CAAA+H,WAAA;UACA;YAAA9B,eAAA,OAAAjG,QAAA,CAAAkI;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,eAAA;MAAAN,GAAA;MAAAC;IAAA;MACA;QAAAvE,KAAA,OAAAvD,QAAA,CAAAoI;MAAA;IACA;IACAC,gBAAA;MAAAR,GAAA;MAAAC;IAAA;MACA;QAAA7B,eAAA,OAAAjG,QAAA,CAAAsI;MAAA;IACA;IACA;IACA1D,2BAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;IAAA,CACA;IACA;IACAC,uBAAA;MACA,IAAA0D,GAAA;MACA,SAAAvI,QAAA,CAAAwI,SAAA,EAAAD,GAAA,CAAAtF,IAAA;MACA,SAAAjD,QAAA,CAAAyI,SAAA,EAAAF,GAAA,CAAAtF,IAAA;MACA,SAAAjD,QAAA,CAAA0I,YAAA;QACAH,GAAA,CAAAtF,IAAA;QACA,SAAAjD,QAAA,CAAA2I,SAAA,EAAAJ,GAAA,CAAAtF,IAAA;QACAsF,GAAA,CAAAtF,IAAA;MACA;MACA,SAAAjD,QAAA,CAAA4I,UAAA,EAAAL,GAAA,CAAAtF,IAAA;MACA,KAAAhD,OAAA,GAAAsI,GAAA,CAAAM,IAAA;MACA,KAAA7I,QAAA,CAAA8I,WAAA;IACA;IAEAzI,KAAA,GACA;IACAmE,OAAA;MACA,KAAApF,SAAA;MACA,KAAAkB,WAAA;IACA;IACA;IACAA,YAAA;MACA,KAAAf,eAAA;MACA,IAAA6B,MAAA;QACA2H,IAAA,OAAA3J,SAAA;QACA4J,KAAA,OAAA3J,QAAA;QACA4J,IAAA;MACA;MAGA,SAAA5K,UAAA,CAAAU,SAAA,eAAAV,UAAA,CAAAU,SAAA,IAAAmK,SAAA;QACA9H,MAAA,2BAAA/C,UAAA,CAAAU,SAAA;MACA;MAEAqC,MAAA;;MAGA,KAAAa,KAAA;QACAC,GAAA;QACAC,MAAA;QACAf,MAAA,EAAAA;MACA,GAAAgB,IAAA;QAAAhE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACA,KAAAlD,QAAA,GAAAf,IAAA,CAAAA,IAAA,CAAA+K,IAAA;UACA,KAAA7J,SAAA,GAAAlB,IAAA,CAAAA,IAAA,CAAAgL,KAAA;QACA;UACA,KAAAjK,QAAA;UACA,KAAAG,SAAA;QACA;QACA,KAAAC,eAAA;MACA;;MAEA;MACA;IACA;IACA;IACA8J,iBAAAxI,GAAA;MACA,KAAAxB,QAAA,GAAAwB,GAAA;MACA,KAAAzB,SAAA;MACA,KAAAkB,WAAA;IACA;IACA;IACAgJ,oBAAAzI,GAAA;MACA,KAAAzB,SAAA,GAAAyB,GAAA;MACA,KAAAP,WAAA;IACA;IACA;IACAiJ,uBAAA1I,GAAA;MACA,KAAArB,kBAAA,GAAAqB,GAAA;IACA;IACA;IACA2I,mBAAA7K,EAAA,EAAAqE,IAAA;MACA,KAAAvD,QAAA;MACA,KAAAM,eAAA;MACA,KAAA0J,oBAAA;MACA,IAAAzG,IAAA;QACAA,IAAA;MACA;MACA,KAAApB,SAAA;QACA,KAAA8H,KAAA,CAAAC,WAAA,CAAAtJ,IAAA,CAAA1B,EAAA,EAAAqE,IAAA;MACA;IACA;IACA;IACA4G,SAAAC,IAAA;MACA5F,MAAA,CAAA6F,IAAA;IACA;IACA;IACAC,cAAApL,EAAA;MACA,IAAAqL,GAAA,GAAArL,EAAA,IAAAsL,MAAA,CAAAtL,EAAA,UAAAa,kBAAA,CAAA0K,GAAA,CAAAvH,IAAA;QACA,OAAAsH,MAAA,CAAAtH,IAAA,CAAAhE,EAAA;MACA;MAEA,KAAAwL,QAAA,SAAAxL,EAAA;QACAyL,iBAAA;QACAC,gBAAA;QACArH,IAAA;MACA,GAAAZ,IAAA;QACA,KAAAH,KAAA;UACAC,GAAA;UACAC,MAAA;UACA/D,IAAA,EAAA4L;QACA,GAAA5H,IAAA;UAAAhE;QAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;YACA,KAAA+B,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,EAAAA,CAAA;gBACA,KAAAC,MAAA;cACA;YACA;UACA;YACA,KAAAJ,QAAA,CAAAkG,KAAA,CAAAlM,IAAA,CAAAmM,GAAA;UACA;QACA;MACA;IACA;IACA;IACAC,wBAAApM,IAAA;MACA,IAAA+C,KAAA;MACAA,KAAA,CAAAc,KAAA;QACAC,GAAA,uCAAA9D,IAAA,CAAAyL,IAAA;QACA1H,MAAA;MACA,GAAAC,IAAA;QAAAhE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACAlB,KAAA,CAAAiD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,EAAAA,CAAA;cACApD,KAAA,CAAAqD,MAAA;YACA;UACA;QACA;UACArD,KAAA,CAAAiD,QAAA,CAAAkG,KAAA,CAAAlM,IAAA,CAAAmM,GAAA;QACA;MACA;IAEA;IACA;IACAE,sBAAArM,IAAA;MACA,KAAAgG,QAAA,CAAAkG,KAAA;IACA;EACA;AACA", "ignoreList": []}]}