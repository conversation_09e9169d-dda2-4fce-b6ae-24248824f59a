{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\xuangmu\\yuanma\\code1\\front\\src\\components\\index\\IndexMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\components\\index\\IndexMain.vue", "mtime": 1649064850700}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IG1lbnUgZnJvbSAiQC91dGlscy9tZW51IjsKZXhwb3J0IGRlZmF1bHQgewogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBtZW51TGlzdDogW10sCiAgICAgIHJvbGU6ICIiLAogICAgICBjdXJyZW50SW5kZXg6IC0yLAogICAgICBpdGVtTWVudTogW10sCiAgICAgIHRpdGxlOiAnJwogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICBsZXQgbWVudXMgPSBtZW51Lmxpc3QoKTsKICAgIHRoaXMubWVudUxpc3QgPSBtZW51czsKICAgIHRoaXMucm9sZSA9IHRoaXMuJHN0b3JhZ2UuZ2V0KCJyb2xlIik7CiAgfSwKICBtZXRob2RzOiB7CiAgICBtZW51SGFuZGxlcihtZW51KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBuYW1lOiBtZW51LnRhYmxlTmFtZQogICAgICB9KTsKICAgICAgdGhpcy50aXRsZSA9IG1lbnUubWVudTsKICAgIH0sCiAgICB0aXRsZUNoYW5nZShpbmRleCwgbWVudXMpIHsKICAgICAgdGhpcy5jdXJyZW50SW5kZXggPSBpbmRleDsKICAgICAgdGhpcy5pdGVtTWVudSA9IG1lbnVzOwogICAgICBjb25zb2xlLmxvZyhtZW51cyk7CiAgICB9LAogICAgaG9tZUNoYW5nZShpbmRleCkgewogICAgICB0aGlzLml0ZW1NZW51ID0gW107CiAgICAgIHRoaXMudGl0bGUgPSAiIjsKICAgICAgdGhpcy5jdXJyZW50SW5kZXggPSBpbmRleDsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgIG5hbWU6ICdob21lJwogICAgICB9KTsKICAgIH0sCiAgICBjZW50ZXJDaGFuZ2UoaW5kZXgpIHsKICAgICAgdGhpcy5pdGVtTWVudSA9IFt7CiAgICAgICAgImJ1dHRvbnMiOiBbIuaWsOWiniIsICLmn6XnnIsiLCAi5L+u5pS5IiwgIuWIoOmZpCJdLAogICAgICAgICJtZW51IjogIuS/ruaUueWvhueggSIsCiAgICAgICAgInRhYmxlTmFtZSI6ICJ1cGRhdGVQYXNzd29yZCIKICAgICAgfSwgewogICAgICAgICJidXR0b25zIjogWyLmlrDlop4iLCAi5p+l55yLIiwgIuS/ruaUuSIsICLliKDpmaQiXSwKICAgICAgICAibWVudSI6ICLkuKrkurrkv6Hmga8iLAogICAgICAgICJ0YWJsZU5hbWUiOiAiY2VudGVyIgogICAgICB9XTsKICAgICAgdGhpcy50aXRsZSA9ICIiOwogICAgICB0aGlzLmN1cnJlbnRJbmRleCA9IGluZGV4OwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgbmFtZTogJ2hvbWUnCiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["menu", "data", "menuList", "role", "currentIndex", "itemMenu", "title", "mounted", "menus", "list", "$storage", "get", "methods", "menu<PERSON><PERSON><PERSON>", "$router", "push", "name", "tableName", "titleChange", "index", "console", "log", "homeChange", "centerChange"], "sources": ["src/components/index/IndexMain.vue"], "sourcesContent": ["<template>\n\t<el-main>\n\t\t<bread-crumbs :title=\"title\" class=\"bread-crumbs\"></bread-crumbs>\n\t\t<router-view class=\"router-view\"></router-view>\n\t</el-main>\n</template>\n<script>\n\timport menu from \"@/utils/menu\";\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmenuList: [],\n\t\t\t\trole: \"\",\n\t\t\t\tcurrentIndex: -2,\n\t\t\t\titemMenu: [],\n\t\t\t\ttitle: ''\n\t\t\t};\n\t\t},\n\t\tmounted() {\n\t\t\tlet menus = menu.list();\n\t\t\tthis.menuList = menus;\n\t\t\tthis.role = this.$storage.get(\"role\");\n\t\t},\n\t\tmethods: {\n\t\t\tmenuHandler(menu) {\n\t\t\t\tthis.$router.push({\n\t\t\t\t\tname: menu.tableName\n\t\t\t\t});\n\t\t\t\tthis.title = menu.menu;\n\t\t\t},\n\t\t\ttitleChange(index, menus) {\n\t\t\t\tthis.currentIndex = index\n\t\t\t\tthis.itemMenu = menus;\n\t\t\t\tconsole.log(menus);\n\t\t\t},\n\t\t\thomeChange(index) {\n\t\t\t\tthis.itemMenu = [];\n\t\t\t\tthis.title = \"\"\n\t\t\t\tthis.currentIndex = index\n\t\t\t\tthis.$router.push({\n\t\t\t\t\tname: 'home'\n\t\t\t\t});\n\t\t\t},\n\t\t\tcenterChange(index) {\n\t\t\t\tthis.itemMenu = [{\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\n\t\t\t\t\t\"menu\": \"修改密码\",\n\t\t\t\t\t\"tableName\": \"updatePassword\"\n\t\t\t\t}, {\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\n\t\t\t\t\t\"menu\": \"个人信息\",\n\t\t\t\t\t\"tableName\": \"center\"\n\t\t\t\t}];\n\t\t\t\tthis.title = \"\"\n\t\t\t\tthis.currentIndex = index\n\t\t\t\tthis.$router.push({\n\t\t\t\t\tname: 'home'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n</script>\n<style lang=\"scss\" scoped>\n\ta {\n\t\ttext-decoration: none;\n\t\tcolor: #555;\n\t}\n\n\ta:hover {\n\t\tbackground: #00c292;\n\t}\n\n\t.nav-list {\n\t\twidth: 100%;\n\t\tmargin: 0 auto;\n\t\ttext-align: left;\n\t\tmargin-top: 20px;\n\n\t\t.nav-title {\n\t\t\tdisplay: inline-block;\n\t\t\tfont-size: 15px;\n\t\t\tcolor: #333;\n\t\t\tpadding: 15px 25px;\n\t\t\tborder: none;\n\t\t}\n\n\t\t.nav-title.active {\n\t\t\tcolor: #555;\n\t\t\tcursor: default;\n\t\t\tbackground-color: #fff;\n\t\t}\n\t}\n\n\t.nav-item {\n\t\tmargin-top: 20px;\n\t\tbackground: #FFFFFF;\n\t\tpadding: 15px 0;\n\n\t\t.menu {\n\t\t\tpadding: 15px 25px;\n\t\t}\n\t}\n\n\t.el-main {\n\t\tbackground-color: #F6F8FA;\n\t\tpadding: 0 24px;\n\t\t// padding-top: 60px;\n\t}\n\n\t.router-view {\n\t\tpadding: 10px;\n\t\tmargin-top: 10px;\n\t\tbackground: #FFFFFF;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.bread-crumbs {\n\t\twidth: 100%;\n\t\t// border-bottom: 1px solid #e9eef3;\n\t\t// border-top: 1px solid #e9eef3;\n\t\tmargin-top: 10px;\n\t\tbox-sizing: border-box;\n\t}\n</style>\n"], "mappings": "AAOA,OAAAA,IAAA;AACA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,YAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACA,IAAAC,KAAA,GAAAR,IAAA,CAAAS,IAAA;IACA,KAAAP,QAAA,GAAAM,KAAA;IACA,KAAAL,IAAA,QAAAO,QAAA,CAAAC,GAAA;EACA;EACAC,OAAA;IACAC,YAAAb,IAAA;MACA,KAAAc,OAAA,CAAAC,IAAA;QACAC,IAAA,EAAAhB,IAAA,CAAAiB;MACA;MACA,KAAAX,KAAA,GAAAN,IAAA,CAAAA,IAAA;IACA;IACAkB,YAAAC,KAAA,EAAAX,KAAA;MACA,KAAAJ,YAAA,GAAAe,KAAA;MACA,KAAAd,QAAA,GAAAG,KAAA;MACAY,OAAA,CAAAC,GAAA,CAAAb,KAAA;IACA;IACAc,WAAAH,KAAA;MACA,KAAAd,QAAA;MACA,KAAAC,KAAA;MACA,KAAAF,YAAA,GAAAe,KAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACAO,aAAAJ,KAAA;MACA,KAAAd,QAAA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;MACA,KAAAC,KAAA;MACA,KAAAF,YAAA,GAAAe,KAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}