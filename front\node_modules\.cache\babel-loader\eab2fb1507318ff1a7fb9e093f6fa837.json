{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\xuangmu\\yuanma\\code1\\front\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\views\\login.vue", "mtime": 1752474902675}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["menu", "data", "rulesForm", "username", "password", "role", "code", "menus", "tableName", "codes", "num", "color", "rotate", "size", "mounted", "list", "created", "setInputColor", "getRandCode", "methods", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "style", "backgroundColor", "height", "lineHeight", "borderRadius", "setTimeout", "register", "$storage", "set", "$router", "push", "path", "login", "i", "$message", "error", "toLowerCase", "length", "<PERSON><PERSON><PERSON>", "$http", "url", "method", "then", "token", "userId", "replace", "msg", "len", "randomString", "chars", "colors", "sizes", "output", "key", "Math", "floor", "random", "j", "plus"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"container loginIn\" :style=\"{ backgroundImage: 'url(' + require('@/assets/img/bg.jpg') + ')' }\">\r\n\r\n            <div :class=\"2 == 1 ? 'left' : 2 == 2 ? 'left center' : 'left right'\" style=\"backgroundColor: rgba(225, 225, 225, 1)\">\r\n                <el-form class=\"login-form\" label-position=\"left\" :label-width=\"3 == 3 ? '56px' : '0px'\">\r\n                    <div class=\"title-container\"><h3 class=\"title\" style=\"color: rgba(255, 69, 0, 1)\">流浪动物管理系统</h3></div>\r\n                    <el-form-item :label=\"3 == 3 ? '用户名' : ''\" :class=\"'style'+3\">\r\n                        <span v-if=\"3 != 3\" class=\"svg-container\" style=\"color:rgba(255, 69, 0, 1);line-height:50px\"><svg-icon icon-class=\"user\" /></span>\r\n                        <el-input placeholder=\"请输入用户名\" name=\"username\" type=\"text\" v-model=\"rulesForm.username\" />\r\n                    </el-form-item>\r\n                    <el-form-item :label=\"3 == 3 ? '密码' : ''\" :class=\"'style'+3\">\r\n                        <span v-if=\"3 != 3\" class=\"svg-container\" style=\"color:rgba(255, 69, 0, 1);line-height:50px\"><svg-icon icon-class=\"password\" /></span>\r\n                        <el-input placeholder=\"请输入密码\" name=\"password\" type=\"password\" v-model=\"rulesForm.password\" />\r\n                    </el-form-item>\r\n                    <el-form-item v-if=\"0 == '1'\" class=\"code\" :label=\"3 == 3 ? '验证码' : ''\" :class=\"'style'+3\">\r\n                        <span v-if=\"3 != 3\" class=\"svg-container\" style=\"color:rgba(255, 69, 0, 1);line-height:50px\"><svg-icon icon-class=\"code\" /></span>\r\n                        <el-input placeholder=\"请输入验证码\" name=\"code\" type=\"text\" v-model=\"rulesForm.code\" />\r\n                        <div class=\"getCodeBt\" @click=\"getRandCode(4)\" style=\"height:50px;line-height:50px\">\r\n                            <span v-for=\"(item, index) in codes\" :key=\"index\" :style=\"{color:item.color,transform:item.rotate,fontSize:item.size}\">{{ item.num }}</span>\r\n                        </div>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"角色\" prop=\"loginInRole\" class=\"role\">\r\n                        <el-radio\r\n                                v-for=\"item in menus\"\r\n                                v-if=\"item.hasBackLogin=='是'\"\r\n                                v-bind:key=\"item.roleName\"\r\n                                v-model=\"rulesForm.role\"\r\n                                :label=\"item.roleName\"\r\n                        >{{item.roleName}}</el-radio>\r\n                    </el-form-item>\r\n                    <el-button type=\"primary\" @click=\"login()\" class=\"loginInBt\" style=\"padding:0;font-size:16px;border-radius:15px;height:44px;line-height:44px;width:100%;backgroundColor:rgba(255, 69, 0, 1); borderColor:rgba(255, 69, 0, 1); color:rgba(255, 255, 255, 1)\">{{'2' == '1' ? '登录' : 'login'}}</el-button>\r\n                    <el-form-item class=\"setting\">\r\n\t\t\t\t<div style=\"color:rgba(25, 169, 123, 1)\" class=\"register\" @click=\"register('yonghu')\">用户注册</div>\r\n\t\t\t\t<div style=\"color:rgba(25, 169, 123, 1)\" class=\"register\" @click=\"register('ziyuanzhe')\">自愿者注册</div>\r\n                    </el-form-item>\r\n                </el-form>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import menu from \"@/utils/menu\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                rulesForm: {\r\n                    username: \"\",\r\n                    password: \"\",\r\n                    role: \"\",\r\n                    code: '',\r\n                },\r\n                menus: [],\r\n                tableName: \"\",\r\n                codes: [{\r\n                    num: 1,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 2,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 3,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 4,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                }],\r\n            };\r\n        },\r\n        mounted() {\r\n            let menus = menu.list();\r\n            this.menus = menus;\r\n        },\r\n        created() {\r\n            this.setInputColor()\r\n            this.getRandCode()\r\n        },\r\n        methods: {\r\n            setInputColor(){\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.loginIn .el-input__inner').forEach(el=>{\r\n                        el.style.backgroundColor = \"rgba(194, 189, 189, 0.39)\"\r\n                        el.style.color = \"rgba(51, 51, 51, 1)\"\r\n                        el.style.height = \"50px\"\r\n                        el.style.lineHeight = \"50px\"\r\n                        el.style.borderRadius = \"15px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .style3 .el-form-item__label').forEach(el=>{\r\n                        el.style.height = \"50px\"\r\n                        el.style.lineHeight = \"50px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .el-form-item__label').forEach(el=>{\r\n                        el.style.color = \"rgba(255, 69, 0, 1)\"\r\n                    })\r\n                    setTimeout(()=>{\r\n                        document.querySelectorAll('.loginIn .role .el-radio__label').forEach(el=>{\r\n                            el.style.color = \"rgba(255, 69, 0, 1)\"\r\n                        })\r\n                    },350)\r\n                })\r\n\r\n            },\r\n            register(tableName){\r\n                this.$storage.set(\"loginTable\", tableName);\r\n                this.$router.push({path:'/register'})\r\n            },\r\n            // 登陆\r\n            login() {\r\n                let code = ''\r\n                for(let i in this.codes) {\r\n                    code += this.codes[i].num\r\n                }\r\n                if ('0' == '1' && !this.rulesForm.code) {\r\n                    this.$message.error(\"请输入验证码\");\r\n                    return;\r\n                }\r\n                if ('0' == '1' && this.rulesForm.code.toLowerCase() != code.toLowerCase()) {\r\n                    this.$message.error(\"验证码输入有误\");\r\n                    this.getRandCode()\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.username) {\r\n                    this.$message.error(\"请输入用户名\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.password) {\r\n                    this.$message.error(\"请输入密码\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.role) {\r\n                    this.$message.error(\"请选择角色\");\r\n                    return;\r\n                }\r\n                let menus = this.menus;\r\n                for (let i = 0; i < menus.length; i++) {\r\n                    if (menus[i].roleName == this.rulesForm.role) {\r\n                        this.tableName = menus[i].tableName;\r\n                    }\r\n                }\r\n                this.$http({\r\n                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n                    method: \"post\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        // 多角色存储\r\n                        this.$storage.set(`Token_${this.rulesForm.role}`, data.token);\r\n                        this.$storage.set(`userId_${this.rulesForm.role}`, data.userId);\r\n                        this.$storage.set(`role_${this.rulesForm.role}`, this.rulesForm.role);\r\n                        this.$storage.set(`sessionTable_${this.rulesForm.role}`, this.tableName);\r\n                        // 当前激活身份\r\n                        this.$storage.set(\"Token\", data.token);\r\n                        this.$storage.set(\"userId\", data.userId);\r\n                        this.$storage.set(\"role\", this.rulesForm.role);\r\n                        this.$storage.set(\"sessionTable\", this.tableName);\r\n                        this.$storage.set(\"adminName\", this.rulesForm.username);\r\n                        this.$router.replace({ path: \"/index/\" });\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            getRandCode(len = 4){\r\n                this.randomString(len)\r\n            },\r\n            randomString(len = 4) {\r\n                let chars = [\r\n                    \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\", \"k\",\r\n                    \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\", \"u\", \"v\",\r\n                    \"w\", \"x\", \"y\", \"z\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\",\r\n                    \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\",\r\n                    \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"0\", \"1\", \"2\",\r\n                    \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"\r\n                ]\r\n                let colors = [\"0\", \"1\", \"2\",\"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"a\", \"b\", \"c\", \"d\", \"e\", \"f\"]\r\n                let sizes = ['14', '15', '16', '17', '18']\r\n\r\n                let output = [];\r\n                for (let i = 0; i < len; i++) {\r\n                    // 随机验证码\r\n                    let key = Math.floor(Math.random()*chars.length)\r\n                    this.codes[i].num = chars[key]\r\n                    // 随机验证码颜色\r\n                    let code = '#'\r\n                    for (let j = 0; j < 6; j++) {\r\n                        let key = Math.floor(Math.random()*colors.length)\r\n                        code += colors[key]\r\n                    }\r\n                    this.codes[i].color = code\r\n                    // 随机验证码方向\r\n                    let rotate = Math.floor(Math.random()*60)\r\n                    let plus = Math.floor(Math.random()*2)\r\n                    if(plus == 1) rotate = '-'+rotate\r\n                    this.codes[i].rotate = 'rotate('+rotate+'deg)'\r\n                    // 随机验证码字体大小\r\n                    let size = Math.floor(Math.random()*sizes.length)\r\n                    this.codes[i].size = sizes[size]+'px'\r\n                }\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .loginIn {\r\n        min-height: 100vh;\r\n        position: relative;\r\n        background-repeat: no-repeat;\r\n        background-position: center center;\r\n        background-size: cover;\r\n\r\n    .left {\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        width: 360px;\r\n        height: 100%;\r\n\r\n    .login-form {\r\n        background-color: transparent;\r\n        width: 100%;\r\n        right: inherit;\r\n        padding: 0 12px;\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        justify-content: center;\r\n        flex-direction: column;\r\n    }\r\n\r\n    .title-container {\r\n        text-align: center;\r\n        font-size: 24px;\r\n\r\n    .title {\r\n        margin: 20px 0;\r\n    }\r\n    }\r\n\r\n    .el-form-item {\r\n        position: relative;\r\n\r\n    .svg-container {\r\n        padding: 6px 5px 6px 15px;\r\n        color: #889aa4;\r\n        vertical-align: middle;\r\n        display: inline-block;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        z-index: 1;\r\n        padding: 0;\r\n        line-height: 40px;\r\n        width: 30px;\r\n        text-align: center;\r\n    }\r\n\r\n    .el-input {\r\n        display: inline-block;\r\n        height: 40px;\r\n        width: 100%;\r\n\r\n    & ::v-deep input {\r\n          background: transparent;\r\n          border: 0px;\r\n          -webkit-appearance: none;\r\n          padding: 0 15px 0 30px;\r\n          color: #fff;\r\n          height: 40px;\r\n      }\r\n    }\r\n\r\n    }\r\n\r\n\r\n    }\r\n\r\n    .center {\r\n        position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        width: 360px;\r\n        transform: translate3d(-50%,-50%,0);\r\n        height: 446px;\r\n        border-radius: 8px;\r\n    }\r\n\r\n    .right {\r\n        position: absolute;\r\n        left: inherit;\r\n        right: 0;\r\n        top: 0;\r\n        width: 360px;\r\n        height: 100%;\r\n    }\r\n\r\n    .code {\r\n    .el-form-item__content {\r\n        position: relative;\r\n\r\n    .getCodeBt {\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n        line-height: 40px;\r\n        width: 100px;\r\n        background-color: rgba(51,51,51,0.4);\r\n        color: #fff;\r\n        text-align: center;\r\n        border-radius: 0 4px 4px 0;\r\n        height: 40px;\r\n        overflow: hidden;\r\n\r\n    span {\r\n        padding: 0 5px;\r\n        display: inline-block;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n    }\r\n    }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 130px 0 30px;\r\n      }\r\n    }\r\n    }\r\n    }\r\n\r\n    .setting {\r\n    & ::v-deep .el-form-item__content {\r\n          padding: 0 15px;\r\n          box-sizing: border-box;\r\n          line-height: 32px;\r\n          height: 32px;\r\n          font-size: 14px;\r\n          color: #999;\r\n          margin: 0 !important;\r\n\r\n    .register {\r\n        float: left;\r\n        width: 50%;\r\n    }\r\n\r\n    .reset {\r\n        float: right;\r\n        width: 50%;\r\n        text-align: right;\r\n    }\r\n    }\r\n    }\r\n\r\n    .style2 {\r\n        padding-left: 30px;\r\n\r\n    .svg-container {\r\n        left: -30px !important;\r\n    }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 15px !important;\r\n      }\r\n    }\r\n    }\r\n\r\n    .code.style2, .code.style3 {\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 115px 0 15px;\r\n      }\r\n    }\r\n    }\r\n\r\n    .style3 {\r\n    & ::v-deep .el-form-item__label {\r\n          padding-right: 6px;\r\n      }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 15px !important;\r\n      }\r\n    }\r\n    }\r\n\r\n    .role {\r\n    & ::v-deep .el-form-item__label {\r\n          width: 56px !important;\r\n      }\r\n\r\n    & ::v-deep .el-radio {\r\n          margin-right: 12px;\r\n      }\r\n    }\r\n\r\n    }\r\n</style>\r\n"], "mappings": "AA2CA,OAAAA,IAAA;AACA;EACAC,KAAA;IACA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACAC,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,IAAAP,KAAA,GAAAP,IAAA,CAAAe,IAAA;IACA,KAAAR,KAAA,GAAAA,KAAA;EACA;EACAS,QAAA;IACA,KAAAC,aAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAF,cAAA;MACA,KAAAG,SAAA;QACAC,QAAA,CAAAC,gBAAA,8BAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAC,eAAA;UACAF,EAAA,CAAAC,KAAA,CAAAd,KAAA;UACAa,EAAA,CAAAC,KAAA,CAAAE,MAAA;UACAH,EAAA,CAAAC,KAAA,CAAAG,UAAA;UACAJ,EAAA,CAAAC,KAAA,CAAAI,YAAA;QACA;QACAR,QAAA,CAAAC,gBAAA,0CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAE,MAAA;UACAH,EAAA,CAAAC,KAAA,CAAAG,UAAA;QACA;QACAP,QAAA,CAAAC,gBAAA,kCAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAd,KAAA;QACA;QACAmB,UAAA;UACAT,QAAA,CAAAC,gBAAA,oCAAAC,OAAA,CAAAC,EAAA;YACAA,EAAA,CAAAC,KAAA,CAAAd,KAAA;UACA;QACA;MACA;IAEA;IACAoB,SAAAvB,SAAA;MACA,KAAAwB,QAAA,CAAAC,GAAA,eAAAzB,SAAA;MACA,KAAA0B,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAC,MAAA;MACA,IAAA/B,IAAA;MACA,SAAAgC,CAAA,SAAA7B,KAAA;QACAH,IAAA,SAAAG,KAAA,CAAA6B,CAAA,EAAA5B,GAAA;MACA;MACA,wBAAAR,SAAA,CAAAI,IAAA;QACA,KAAAiC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,uBAAAtC,SAAA,CAAAI,IAAA,CAAAmC,WAAA,MAAAnC,IAAA,CAAAmC,WAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA,KAAAtB,WAAA;QACA;MACA;MACA,UAAAhB,SAAA,CAAAC,QAAA;QACA,KAAAoC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAtC,SAAA,CAAAE,QAAA;QACA,KAAAmC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAtC,SAAA,CAAAG,IAAA;QACA,KAAAkC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,IAAAjC,KAAA,QAAAA,KAAA;MACA,SAAA+B,CAAA,MAAAA,CAAA,GAAA/B,KAAA,CAAAmC,MAAA,EAAAJ,CAAA;QACA,IAAA/B,KAAA,CAAA+B,CAAA,EAAAK,QAAA,SAAAzC,SAAA,CAAAG,IAAA;UACA,KAAAG,SAAA,GAAAD,KAAA,CAAA+B,CAAA,EAAA9B,SAAA;QACA;MACA;MACA,KAAAoC,KAAA;QACAC,GAAA,UAAArC,SAAA,wBAAAN,SAAA,CAAAC,QAAA,kBAAAD,SAAA,CAAAE,QAAA;QACA0C,MAAA;MACA,GAAAC,IAAA;QAAA9C;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAK,IAAA;UACA;UACA,KAAA0B,QAAA,CAAAC,GAAA,eAAA/B,SAAA,CAAAG,IAAA,IAAAJ,IAAA,CAAA+C,KAAA;UACA,KAAAhB,QAAA,CAAAC,GAAA,gBAAA/B,SAAA,CAAAG,IAAA,IAAAJ,IAAA,CAAAgD,MAAA;UACA,KAAAjB,QAAA,CAAAC,GAAA,cAAA/B,SAAA,CAAAG,IAAA,SAAAH,SAAA,CAAAG,IAAA;UACA,KAAA2B,QAAA,CAAAC,GAAA,sBAAA/B,SAAA,CAAAG,IAAA,SAAAG,SAAA;UACA;UACA,KAAAwB,QAAA,CAAAC,GAAA,UAAAhC,IAAA,CAAA+C,KAAA;UACA,KAAAhB,QAAA,CAAAC,GAAA,WAAAhC,IAAA,CAAAgD,MAAA;UACA,KAAAjB,QAAA,CAAAC,GAAA,cAAA/B,SAAA,CAAAG,IAAA;UACA,KAAA2B,QAAA,CAAAC,GAAA,sBAAAzB,SAAA;UACA,KAAAwB,QAAA,CAAAC,GAAA,mBAAA/B,SAAA,CAAAC,QAAA;UACA,KAAA+B,OAAA,CAAAgB,OAAA;YAAAd,IAAA;UAAA;QACA;UACA,KAAAG,QAAA,CAAAC,KAAA,CAAAvC,IAAA,CAAAkD,GAAA;QACA;MACA;IACA;IACAjC,YAAAkC,GAAA;MACA,KAAAC,YAAA,CAAAD,GAAA;IACA;IACAC,aAAAD,GAAA;MACA,IAAAE,KAAA,IACA,uDACA,uDACA,uDACA,uDACA,uDACA,kCACA;MACA,IAAAC,MAAA;MACA,IAAAC,KAAA;MAEA,IAAAC,MAAA;MACA,SAAAnB,CAAA,MAAAA,CAAA,GAAAc,GAAA,EAAAd,CAAA;QACA;QACA,IAAAoB,GAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAP,KAAA,CAAAZ,MAAA;QACA,KAAAjC,KAAA,CAAA6B,CAAA,EAAA5B,GAAA,GAAA4C,KAAA,CAAAI,GAAA;QACA;QACA,IAAApD,IAAA;QACA,SAAAwD,CAAA,MAAAA,CAAA,MAAAA,CAAA;UACA,IAAAJ,GAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAN,MAAA,CAAAb,MAAA;UACApC,IAAA,IAAAiD,MAAA,CAAAG,GAAA;QACA;QACA,KAAAjD,KAAA,CAAA6B,CAAA,EAAA3B,KAAA,GAAAL,IAAA;QACA;QACA,IAAAM,MAAA,GAAA+C,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA,IAAAE,IAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA,IAAAE,IAAA,OAAAnD,MAAA,SAAAA,MAAA;QACA,KAAAH,KAAA,CAAA6B,CAAA,EAAA1B,MAAA,eAAAA,MAAA;QACA;QACA,IAAAC,IAAA,GAAA8C,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAL,KAAA,CAAAd,MAAA;QACA,KAAAjC,KAAA,CAAA6B,CAAA,EAAAzB,IAAA,GAAA2C,KAAA,CAAA3C,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}