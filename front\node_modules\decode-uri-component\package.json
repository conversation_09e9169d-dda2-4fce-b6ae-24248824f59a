{"_from": "decode-uri-component@^0.2.0", "_id": "decode-uri-component@0.2.2", "_inBundle": false, "_integrity": "sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==", "_location": "/decode-uri-component", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "decode-uri-component@^0.2.0", "name": "decode-uri-component", "escapedName": "decode-uri-component", "rawSpec": "^0.2.0", "saveSpec": null, "fetchSpec": "^0.2.0"}, "_requiredBy": ["/source-map-resolve"], "_resolved": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "_shasum": "e69dbe25d37941171dd540e024c444cd5188e1e9", "_spec": "decode-uri-component@^0.2.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\source-map-resolve", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/SamVerschueren"}, "bugs": {"url": "https://github.com/SamVerschueren/decode-uri-component/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A better decodeURIComponent", "devDependencies": {"ava": "^0.17.0", "coveralls": "^2.13.1", "nyc": "^10.3.2", "xo": "^0.16.0"}, "engines": {"node": ">=0.10"}, "files": ["index.js"], "homepage": "https://github.com/SamVerschueren/decode-uri-component#readme", "keywords": ["decode", "uri", "component", "decodeuricomponent", "components", "decoder", "url"], "license": "MIT", "name": "decode-uri-component", "repository": {"type": "git", "url": "git+https://github.com/SamVerschueren/decode-uri-component.git"}, "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "test": "xo && nyc ava"}, "version": "0.2.2"}