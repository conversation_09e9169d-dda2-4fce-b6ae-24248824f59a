{"_from": "de-indent@^1.0.2", "_id": "de-indent@1.0.2", "_inBundle": false, "_integrity": "sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==", "_location": "/de-indent", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "de-indent@^1.0.2", "name": "de-indent", "escapedName": "de-indent", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/vue-template-compiler"], "_resolved": "https://registry.npmjs.org/de-indent/-/de-indent-1.0.2.tgz", "_shasum": "b2038e846dc33baa5796128d0804b455b8c1e21d", "_spec": "de-indent@^1.0.2", "_where": "C:\\code\\t\\t101\\front\\node_modules\\vue-template-compiler", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/yyx990803/de-indent/issues"}, "bundleDependencies": false, "deprecated": false, "description": "remove extra indent from a block of code", "devDependencies": {"mocha": "^2.3.4"}, "homepage": "https://github.com/yyx990803/de-indent#readme", "keywords": ["deindent"], "license": "MIT", "main": "index.js", "name": "de-indent", "repository": {"type": "git", "url": "git+https://github.com/yyx990803/de-indent.git"}, "scripts": {"test": "mocha"}, "version": "1.0.2"}