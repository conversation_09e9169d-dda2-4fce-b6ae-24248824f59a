<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ForumDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.forum_name as forumName
        ,a.yonghu_id as yonghuId
        ,a.ziyuanzhe_id as ziyuanzheId
        ,a.users_id as usersId
        ,a.forum_content as forumContent
        ,a.super_ids as superIds
        ,a.forum_state_types as forumStateTypes
        ,a.insert_time as insertTime
        ,a.update_time as updateTime
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.ForumView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段
        ,yonghu.yonghu_name as yonghu<PERSON><PERSON>
        ,yonghu.yonghu_photo as yonghu<PERSON>hoto
        ,yonghu.yonghu_phone as yonghu<PERSON><PERSON>
        ,yonghu.yonghu_email as yonghuEmail
        ,yonghu.yonghu_delete as yonghuDelete
        ,ziyuanzhe.ziyuanzhe_name as ziyuanzheName
        ,ziyuanzhe.ziyuanzhe_photo as ziyuanzhePhoto
        ,ziyuanzhe.ziyuanzhe_phone as ziyuanzhePhone
        ,ziyuanzhe.ziyuanzhe_email as ziyuanzheEmail
        ,ziyuanzhe.ziyuanzhe_delete as ziyuanzheDelete
        ,users.username as uusername
        ,users.password as upassword
        ,users.role as urole
        ,users.addtime as uaddtime

        FROM forum  a
        left JOIN yonghu yonghu ON a.yonghu_id = yonghu.id
        left JOIN ziyuanzhe ziyuanzhe ON a.ziyuanzhe_id = ziyuanzhe.id
        left JOIN users users ON a.users_id = users.id

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.forumName != '' and params.forumName != null and params.forumName != 'null' ">
                and a.forum_name like CONCAT('%',#{params.forumName},'%')
            </if>
            <if test="params.yonghuId != null and params.yonghuId != ''">
                and (
                    a.yonghu_id = #{params.yonghuId}
                )
            </if>
            <if test="params.ziyuanzheId != null and params.ziyuanzheId != ''">
                and (
                    a.ziyuanzhe_id = #{params.ziyuanzheId}
                )
            </if>
            <if test="params.usersId != null and params.usersId != ''">
                and (
                    a.users_id = #{params.usersId}
                )
            </if>
            <if test=" params.forumContent != '' and params.forumContent != null and params.forumContent != 'null' ">
                and a.forum_content like CONCAT('%',#{params.forumContent},'%')
            </if>
            <if test="params.superIdsStart != null and params.superIdsStart != ''">
                <![CDATA[  and a.super_ids >= #{params.superIdsStart}   ]]>
            </if>
            <if test="params.superIdsEnd != null and params.superIdsEnd != ''">
                <![CDATA[  and a.super_ids <= #{params.superIdsEnd}   ]]>
            </if>
             <if test="params.superIds != null and params.superIds != ''">
                and a.super_ids = #{params.superIds}
             </if>
            <if test="params.forumStateTypes != null and params.forumStateTypes != ''">
                and a.forum_state_types = #{params.forumStateTypes}
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test=" params.updateTimeStart != '' and params.updateTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.update_time) >= UNIX_TIMESTAMP(#{params.updateTimeStart}) ]]>
            </if>
            <if test=" params.updateTimeEnd != '' and params.updateTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.update_time) <= UNIX_TIMESTAMP(#{params.updateTimeEnd}) ]]>
            </if>

                <!-- 判断用户的id不为空 -->
            <if test=" params.yonghuIdNotNull != '' and params.yonghuIdNotNull != null and params.yonghuIdNotNull != 'null' ">
                and a.yonghu_id IS NOT NULL
            </if>
            <if test=" params.yonghuName != '' and params.yonghuName != null and params.yonghuName != 'null' ">
                and yonghu.yonghu_name like CONCAT('%',#{params.yonghuName},'%')
            </if>
            <if test=" params.yonghuPhone != '' and params.yonghuPhone != null and params.yonghuPhone != 'null' ">
                and yonghu.yonghu_phone like CONCAT('%',#{params.yonghuPhone},'%')
            </if>
            <if test=" params.yonghuEmail != '' and params.yonghuEmail != null and params.yonghuEmail != 'null' ">
                and yonghu.yonghu_email like CONCAT('%',#{params.yonghuEmail},'%')
            </if>
            <if test="params.yonghuDeleteStart != null  and params.yonghuDeleteStart != '' ">
                <![CDATA[  and yonghu.yonghu_delete >= #{params.yonghuDeleteStart}   ]]>
            </if>
            <if test="params.yonghuDeleteEnd != null  and params.yonghuDeleteEnd != '' ">
                <![CDATA[  and yonghu.yonghu_delete <= #{params.yonghuDeleteEnd}   ]]>
            </if>
            <if test="params.yonghuDelete != null  and params.yonghuDelete != '' ">
                and yonghu.yonghu_delete = #{params.yonghuDelete}
            </if>
                <!-- 判断自愿者的id不为空 -->
            <if test=" params.ziyuanzheIdNotNull != '' and params.ziyuanzheIdNotNull != null and params.ziyuanzheIdNotNull != 'null' ">
                and a.ziyuanzhe_id IS NOT NULL
            </if>
            <if test=" params.ziyuanzheName != '' and params.ziyuanzheName != null and params.ziyuanzheName != 'null' ">
                and ziyuanzhe.ziyuanzhe_name like CONCAT('%',#{params.ziyuanzheName},'%')
            </if>
            <if test=" params.ziyuanzhePhone != '' and params.ziyuanzhePhone != null and params.ziyuanzhePhone != 'null' ">
                and ziyuanzhe.ziyuanzhe_phone like CONCAT('%',#{params.ziyuanzhePhone},'%')
            </if>
            <if test=" params.ziyuanzheEmail != '' and params.ziyuanzheEmail != null and params.ziyuanzheEmail != 'null' ">
                and ziyuanzhe.ziyuanzhe_email like CONCAT('%',#{params.ziyuanzheEmail},'%')
            </if>
            <if test="params.ziyuanzheDeleteStart != null  and params.ziyuanzheDeleteStart != '' ">
                <![CDATA[  and ziyuanzhe.ziyuanzhe_delete >= #{params.ziyuanzheDeleteStart}   ]]>
            </if>
            <if test="params.ziyuanzheDeleteEnd != null  and params.ziyuanzheDeleteEnd != '' ">
                <![CDATA[  and ziyuanzhe.ziyuanzhe_delete <= #{params.ziyuanzheDeleteEnd}   ]]>
            </if>
            <if test="params.ziyuanzheDelete != null  and params.ziyuanzheDelete != '' ">
                and ziyuanzhe.ziyuanzhe_delete = #{params.ziyuanzheDelete}
            </if>
                <!-- 判断用户表的id不为空 -->
            <if test=" params.usersIdNotNull != '' and params.usersIdNotNull != null and params.usersIdNotNull != 'null' ">
                and a.users_id IS NOT NULL
            </if>
            <if test=" params.username != '' and params.username != null and params.username != 'null' ">
                and users.username like CONCAT('%',#{params.username},'%')
            </if>
            <if test=" params.password != '' and params.password != null and params.password != 'null' ">
                and users.password like CONCAT('%',#{params.password},'%')
            </if>
            <if test=" params.role != '' and params.role != null and params.role != 'null' ">
                and users.role like CONCAT('%',#{params.role},'%')
            </if>
            <if test=" params.addtimeStart != '' and params.addtimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(users.addtime) >= UNIX_TIMESTAMP(#{params.addtimeStart}) ]]>
            </if>
            <if test=" params.addtimeEnd != '' and params.addtimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(users.addtime) <= UNIX_TIMESTAMP(#{params.addtimeEnd}) ]]>
            </if>
        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>