# Supported syntaxes

> ⚠️ This is a basic implementation of support reference. Further improvements are pending. See comments on each section for details.

> ⚠️ This page is auto-generated. Please, don't update any content between special comments.

<!-- MarkdownTOC -->

- [Atrules](#atrules)
- [Functional pseudos](#functional-pseudos)
- [Properties](#properties)

<!-- /MarkdownTOC -->

## Atrules

At-rules with a custom parsing rules. In fact, any at-rule is supported but prelude and block are parsing with default rules. Validation support isn't supported for at-rules at the moment.

<!-- gen:atrule -->

- `@font-face`
- `@import`
- `@media`
- `@page`
- `@supports`

<!-- /gen:atrule -->

## Functional pseudos

Functional pseudos with a custom parsing rule. Non-listed functional pseudos don't parse a content inside parentheses and represent it as a `Raw` node if any. There is no difference between pseudo class and pseudo element on parsing, like no validation for functional pseudos is supported at the moment.

<!-- gen:pseudo -->

- `:dir()`
- `:has()`
- `:lang()`
- `:matches()`
- `:not()`
- `:nth-child()`
- `:nth-last-child()`
- `:nth-last-of-type()`
- `:nth-of-type()`
- `:slotted()`

<!-- /gen:pseudo -->

## Properties

Support for a property means CSSTree has a grammar for such property, so its value can be checked (validated) for complience to spec or browser inplementations (for non-standart properties). The validation doesn't perform on parsing stage and should be done as a separate step using Lexer API. In other words any property value can be parsed with default parsing rules, but validation is possible for listed properties only.

<!-- gen:properties -->

- `-moz-appearance`
- `-moz-background-clip`
- `-moz-binding`
- `-moz-border-bottom-colors`
- `-moz-border-left-colors`
- `-moz-border-radius-bottomleft`
- `-moz-border-radius-bottomright`
- `-moz-border-radius-topleft`
- `-moz-border-radius-topright`
- `-moz-border-right-colors`
- `-moz-border-top-colors`
- `-moz-context-properties`
- `-moz-float-edge`
- `-moz-force-broken-image-icon`
- `-moz-image-region`
- `-moz-orient`
- `-moz-osx-font-smoothing`
- `-moz-outline-radius`
- `-moz-outline-radius-bottomleft`
- `-moz-outline-radius-bottomright`
- `-moz-outline-radius-topleft`
- `-moz-outline-radius-topright`
- `-moz-stack-sizing`
- `-moz-text-blink`
- `-moz-user-focus`
- `-moz-user-input`
- `-moz-user-modify`
- `-moz-user-select`
- `-moz-window-dragging`
- `-moz-window-shadow`
- `-ms-accelerator`
- `-ms-block-progression`
- `-ms-content-zoom-chaining`
- `-ms-content-zoom-limit`
- `-ms-content-zoom-limit-max`
- `-ms-content-zoom-limit-min`
- `-ms-content-zoom-snap`
- `-ms-content-zoom-snap-points`
- `-ms-content-zoom-snap-type`
- `-ms-content-zooming`
- `-ms-filter`
- `-ms-flex-align`
- `-ms-flex-item-align`
- `-ms-flex-line-pack`
- `-ms-flex-negative`
- `-ms-flex-order`
- `-ms-flex-pack`
- `-ms-flex-positive`
- `-ms-flex-preferred-size`
- `-ms-flow-from`
- `-ms-flow-into`
- `-ms-grid-column-align`
- `-ms-grid-row-align`
- `-ms-high-contrast-adjust`
- `-ms-hyphenate-limit-chars`
- `-ms-hyphenate-limit-lines`
- `-ms-hyphenate-limit-zone`
- `-ms-ime-align`
- `-ms-interpolation-mode`
- `-ms-overflow-style`
- `-ms-scroll-chaining`
- `-ms-scroll-limit`
- `-ms-scroll-limit-x-max`
- `-ms-scroll-limit-x-min`
- `-ms-scroll-limit-y-max`
- `-ms-scroll-limit-y-min`
- `-ms-scroll-rails`
- `-ms-scroll-snap-points-x`
- `-ms-scroll-snap-points-y`
- `-ms-scroll-snap-type`
- `-ms-scroll-snap-x`
- `-ms-scroll-snap-y`
- `-ms-scroll-translation`
- `-ms-scrollbar-3dlight-color`
- `-ms-scrollbar-arrow-color`
- `-ms-scrollbar-base-color`
- `-ms-scrollbar-darkshadow-color`
- `-ms-scrollbar-face-color`
- `-ms-scrollbar-highlight-color`
- `-ms-scrollbar-shadow-color`
- `-ms-scrollbar-track-color`
- `-ms-text-autospace`
- `-ms-touch-select`
- `-ms-user-select`
- `-ms-wrap-flow`
- `-ms-wrap-margin`
- `-ms-wrap-through`
- `-webkit-appearance`
- `-webkit-background-clip`
- `-webkit-border-before`
- `-webkit-border-before-color`
- `-webkit-border-before-style`
- `-webkit-border-before-width`
- `-webkit-box-reflect`
- `-webkit-column-break-after`
- `-webkit-column-break-before`
- `-webkit-column-break-inside`
- `-webkit-font-smoothing`
- `-webkit-line-clamp`
- `-webkit-mask`
- `-webkit-mask-attachment`
- `-webkit-mask-box-image`
- `-webkit-mask-clip`
- `-webkit-mask-composite`
- `-webkit-mask-image`
- `-webkit-mask-origin`
- `-webkit-mask-position`
- `-webkit-mask-position-x`
- `-webkit-mask-position-y`
- `-webkit-mask-repeat`
- `-webkit-mask-repeat-x`
- `-webkit-mask-repeat-y`
- `-webkit-mask-size`
- `-webkit-overflow-scrolling`
- `-webkit-print-color-adjust`
- `-webkit-tap-highlight-color`
- `-webkit-text-fill-color`
- `-webkit-text-security`
- `-webkit-text-stroke`
- `-webkit-text-stroke-color`
- `-webkit-text-stroke-width`
- `-webkit-touch-callout`
- `-webkit-user-drag`
- `-webkit-user-modify`
- `-webkit-user-select`
- `align-content`
- `align-items`
- `align-self`
- `alignment-baseline`
- `all`
- `animation`
- `animation-delay`
- `animation-direction`
- `animation-duration`
- `animation-fill-mode`
- `animation-iteration-count`
- `animation-name`
- `animation-play-state`
- `animation-timing-function`
- `appearance`
- `azimuth`
- `backdrop-filter`
- `backface-visibility`
- `background`
- `background-attachment`
- `background-blend-mode`
- `background-clip`
- `background-color`
- `background-image`
- `background-origin`
- `background-position`
- `background-position-x`
- `background-position-y`
- `background-repeat`
- `background-size`
- `baseline-shift`
- `behavior`
- `block-overflow`
- `block-size`
- `border`
- `border-block-end`
- `border-block-end-color`
- `border-block-end-style`
- `border-block-end-width`
- `border-block-start`
- `border-block-start-color`
- `border-block-start-style`
- `border-block-start-width`
- `border-bottom`
- `border-bottom-color`
- `border-bottom-left-radius`
- `border-bottom-right-radius`
- `border-bottom-style`
- `border-bottom-width`
- `border-collapse`
- `border-color`
- `border-image`
- `border-image-outset`
- `border-image-repeat`
- `border-image-slice`
- `border-image-source`
- `border-image-width`
- `border-inline-end`
- `border-inline-end-color`
- `border-inline-end-style`
- `border-inline-end-width`
- `border-inline-start`
- `border-inline-start-color`
- `border-inline-start-style`
- `border-inline-start-width`
- `border-left`
- `border-left-color`
- `border-left-style`
- `border-left-width`
- `border-radius`
- `border-right`
- `border-right-color`
- `border-right-style`
- `border-right-width`
- `border-spacing`
- `border-style`
- `border-top`
- `border-top-color`
- `border-top-left-radius`
- `border-top-right-radius`
- `border-top-style`
- `border-top-width`
- `border-width`
- `bottom`
- `box-align`
- `box-decoration-break`
- `box-direction`
- `box-flex`
- `box-flex-group`
- `box-lines`
- `box-ordinal-group`
- `box-orient`
- `box-pack`
- `box-shadow`
- `box-sizing`
- `break-after`
- `break-before`
- `break-inside`
- `caption-side`
- `caret-color`
- `clear`
- `clip`
- `clip-path`
- `clip-rule`
- `color`
- `color-adjust`
- `column-count`
- `column-fill`
- `column-gap`
- `column-rule`
- `column-rule-color`
- `column-rule-style`
- `column-rule-width`
- `column-span`
- `column-width`
- `columns`
- `contain`
- `content`
- `counter-increment`
- `counter-reset`
- `cue`
- `cue-after`
- `cue-before`
- `cursor`
- `direction`
- `display`
- `dominant-baseline`
- `empty-cells`
- `fill`
- `fill-opacity`
- `fill-rule`
- `filter`
- `flex`
- `flex-basis`
- `flex-direction`
- `flex-flow`
- `flex-grow`
- `flex-shrink`
- `flex-wrap`
- `float`
- `font`
- `font-family`
- `font-feature-settings`
- `font-kerning`
- `font-language-override`
- `font-optical-sizing`
- `font-size`
- `font-size-adjust`
- `font-stretch`
- `font-style`
- `font-synthesis`
- `font-variant`
- `font-variant-alternates`
- `font-variant-caps`
- `font-variant-east-asian`
- `font-variant-ligatures`
- `font-variant-numeric`
- `font-variant-position`
- `font-variation-settings`
- `font-weight`
- `gap`
- `glyph-orientation-horizontal`
- `glyph-orientation-vertical`
- `grid`
- `grid-area`
- `grid-auto-columns`
- `grid-auto-flow`
- `grid-auto-rows`
- `grid-column`
- `grid-column-end`
- `grid-column-gap`
- `grid-column-start`
- `grid-gap`
- `grid-row`
- `grid-row-end`
- `grid-row-gap`
- `grid-row-start`
- `grid-template`
- `grid-template-areas`
- `grid-template-columns`
- `grid-template-rows`
- `hanging-punctuation`
- `height`
- `hyphens`
- `image-orientation`
- `image-rendering`
- `image-resolution`
- `ime-mode`
- `initial-letter`
- `initial-letter-align`
- `inline-size`
- `isolation`
- `justify-content`
- `justify-items`
- `justify-self`
- `kerning`
- `left`
- `letter-spacing`
- `line-break`
- `line-clamp`
- `line-height`
- `line-height-step`
- `list-style`
- `list-style-image`
- `list-style-position`
- `list-style-type`
- `margin`
- `margin-block-end`
- `margin-block-start`
- `margin-bottom`
- `margin-inline-end`
- `margin-inline-start`
- `margin-left`
- `margin-right`
- `margin-top`
- `marker`
- `marker-end`
- `marker-mid`
- `marker-start`
- `mask`
- `mask-border`
- `mask-border-mode`
- `mask-border-outset`
- `mask-border-repeat`
- `mask-border-slice`
- `mask-border-source`
- `mask-border-width`
- `mask-clip`
- `mask-composite`
- `mask-image`
- `mask-mode`
- `mask-origin`
- `mask-position`
- `mask-repeat`
- `mask-size`
- `mask-type`
- `max-block-size`
- `max-height`
- `max-inline-size`
- `max-lines`
- `max-width`
- `min-block-size`
- `min-height`
- `min-inline-size`
- `min-width`
- `mix-blend-mode`
- `object-fit`
- `object-position`
- `offset`
- `offset-anchor`
- `offset-block-end`
- `offset-block-start`
- `offset-distance`
- `offset-inline-end`
- `offset-inline-start`
- `offset-path`
- `offset-position`
- `offset-rotate`
- `opacity`
- `order`
- `orphans`
- `outline`
- `outline-color`
- `outline-offset`
- `outline-style`
- `outline-width`
- `overflow`
- `overflow-anchor`
- `overflow-block`
- `overflow-clip-box`
- `overflow-inline`
- `overflow-wrap`
- `overflow-x`
- `overflow-y`
- `overscroll-behavior`
- `overscroll-behavior-x`
- `overscroll-behavior-y`
- `padding`
- `padding-block-end`
- `padding-block-start`
- `padding-bottom`
- `padding-inline-end`
- `padding-inline-start`
- `padding-left`
- `padding-right`
- `padding-top`
- `page-break-after`
- `page-break-before`
- `page-break-inside`
- `paint-order`
- `pause`
- `pause-after`
- `pause-before`
- `perspective`
- `perspective-origin`
- `place-content`
- `pointer-events`
- `position`
- `quotes`
- `resize`
- `rest`
- `rest-after`
- `rest-before`
- `right`
- `rotate`
- `row-gap`
- `ruby-align`
- `ruby-merge`
- `ruby-position`
- `scale`
- `scroll-behavior`
- `scroll-snap-coordinate`
- `scroll-snap-destination`
- `scroll-snap-points-x`
- `scroll-snap-points-y`
- `scroll-snap-type`
- `scroll-snap-type-x`
- `scroll-snap-type-y`
- `shape-image-threshold`
- `shape-margin`
- `shape-outside`
- `shape-rendering`
- `speak`
- `speak-as`
- `src`
- `stroke`
- `stroke-dasharray`
- `stroke-dashoffset`
- `stroke-linecap`
- `stroke-linejoin`
- `stroke-miterlimit`
- `stroke-opacity`
- `stroke-width`
- `tab-size`
- `table-layout`
- `text-align`
- `text-align-last`
- `text-anchor`
- `text-combine-upright`
- `text-decoration`
- `text-decoration-color`
- `text-decoration-line`
- `text-decoration-skip`
- `text-decoration-skip-ink`
- `text-decoration-style`
- `text-emphasis`
- `text-emphasis-color`
- `text-emphasis-position`
- `text-emphasis-style`
- `text-indent`
- `text-justify`
- `text-orientation`
- `text-overflow`
- `text-rendering`
- `text-shadow`
- `text-size-adjust`
- `text-transform`
- `text-underline-position`
- `top`
- `touch-action`
- `transform`
- `transform-box`
- `transform-origin`
- `transform-style`
- `transition`
- `transition-delay`
- `transition-duration`
- `transition-property`
- `transition-timing-function`
- `translate`
- `unicode-bidi`
- `unicode-range`
- `user-select`
- `vertical-align`
- `visibility`
- `voice-balance`
- `voice-duration`
- `voice-family`
- `voice-pitch`
- `voice-range`
- `voice-rate`
- `voice-stress`
- `voice-volume`
- `white-space`
- `widows`
- `width`
- `will-change`
- `word-break`
- `word-spacing`
- `word-wrap`
- `writing-mode`
- `z-index`
- `zoom`

<!-- /gen:properties -->
