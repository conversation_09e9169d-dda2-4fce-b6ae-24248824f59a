{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\xuangmu\\yuanma\\code1\\front\\src\\views\\modules\\dictionarySex\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\views\\modules\\dictionarySex\\add-or-update.vue", "mtime": 1751514458861}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["styleJs", "isNumber", "isIntNumer", "isEmail", "isPhone", "isMobile", "isURL", "checkIdCard", "data", "self", "addEditForm", "id", "type", "ro", "codeIndex", "indexName", "superId", "<PERSON><PERSON><PERSON>", "ruleForm", "rules", "props", "computed", "created", "addStyle", "addEditStyleChange", "addEditUploadStyleChange", "methods", "init", "info", "$http", "url", "method", "then", "code", "maxCodeIndex", "$message", "error", "msg", "reg", "RegExp", "onSubmit", "$refs", "validate", "valid", "message", "duration", "onClose", "parent", "showFlag", "addOrUpdateFlag", "dictionaryCrossAddOrUpdateFlag", "search", "contentStyleChange", "back", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "style", "height", "inputHeight", "color", "inputFontColor", "fontSize", "inputFontSize", "borderWidth", "inputBorderWidth", "borderStyle", "inputBorderStyle", "borderColor", "inputBorderColor", "borderRadius", "inputBorderRadius", "backgroundColor", "inputBgColor", "lineHeight", "inputLableColor", "inputLableFontSize", "selectHeight", "selectFontColor", "selectFontSize", "selectBorderWidth", "selectBorderStyle", "selectBorderColor", "selectBorderRadius", "selectBgColor", "selectLableColor", "selectLableFontSize", "selectIconFontColor", "selectIconFontSize", "dateHeight", "dateFontColor", "dateFontSize", "dateBorder<PERSON>idth", "dateBorderStyle", "dateBorderColor", "dateBorderRadius", "dateBgColor", "dateLableColor", "dateLableFontSize", "dateIconFontColor", "dateIconFontSize", "iconLineHeight", "parseInt", "uploadHeight", "uploadBorderWidth", "width", "uploadBorderStyle", "uploadBorderColor", "uploadBorderRadius", "uploadBgColor", "uploadLableColor", "uploadLableFontSize", "uploadIconFontColor", "uploadIconFontSize", "display", "textareaHeight", "textareaFontColor", "textareaFontSize", "textareaBorderWidth", "textareaBorderStyle", "textareaBorderColor", "textareaBorderRadius", "textareaBgColor", "textareaLableColor", "textareaLableFontSize", "btnSaveWidth", "btnSaveHeight", "btnSaveFontColor", "btnSaveFontSize", "btnSaveBorderWidth", "btnSaveBorderStyle", "btnSaveBorderColor", "btnSaveBorderRadius", "btnSaveBgColor", "btnCancelWidth", "btnCancelHeight", "btnCancelFontColor", "btnCancelFontSize", "btnCancelBorderWidth", "btnCancelBorderStyle", "btnCancelBorderColor", "btnCancelBorderRadius", "btnCancelBgColor"], "sources": ["src/views/modules/dictionarySex/add-or-update.vue"], "sourcesContent": ["<template>\r\n    <div class=\"addEdit-block\">\r\n        <el-form\r\n                class=\"detail-form-content\"\r\n                ref=\"ruleForm\"\r\n                :model=\"ruleForm\"\r\n                :rules=\"rules\"\r\n                label-width=\"80px\"\r\n                :style=\"{backgroundColor:addEditForm.addEditBoxColor}\"\r\n        >\r\n            <el-row>\r\n                <el-col :span=\"12\">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"性别类型编码\" prop=\"codeIndex\">\r\n                        <el-input v-model=\"ruleForm.codeIndex\"\r\n                                  placeholder=\"性别类型编码\" clearable  :readonly=\"ro.codeIndex\"></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"性别类型编码\" prop=\"codeIndex\">\r\n                            <el-input v-model=\"ruleForm.codeIndex\"\r\n                                      placeholder=\"性别类型编码\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"性别类型\" prop=\"indexName\">\r\n                        <el-input v-model=\"ruleForm.indexName\"\r\n                                  placeholder=\"性别类型\" clearable  :readonly=\"ro.indexName\"></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"性别类型\" prop=\"indexName\">\r\n                            <el-input v-model=\"ruleForm.indexName\"\r\n                                      placeholder=\"性别类型\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>\r\n                <!--<el-col :span=\"12\">\r\n                    <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"备注\" prop=\"beizhu\">\r\n                        <el-input v-model=\"ruleForm.beizhu\"\r\n                                  placeholder=\"备注\" clearable  :readonly=\"ro.beizhu\"></el-input>\r\n                    </el-form-item>\r\n                    <div v-else>\r\n                        <el-form-item class=\"input\" label=\"备注\" prop=\"beizhu\">\r\n                            <el-input v-model=\"ruleForm.beizhu\"\r\n                                      placeholder=\"备注\" readonly></el-input>\r\n                        </el-form-item>\r\n                    </div>\r\n                </el-col>-->\r\n            </el-row>\r\n            <el-form-item class=\"btn\">\r\n                <el-button v-if=\"type!='info'\" type=\"primary\" class=\"btn-success\" @click=\"onSubmit\">提交</el-button>\r\n                <el-button v-if=\"type!='info'\" class=\"btn-close\" @click=\"back()\">取消</el-button>\r\n                <el-button v-if=\"type=='info'\" class=\"btn-close\" @click=\"back()\">返回</el-button>\r\n            </el-form-item>\r\n        </el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import styleJs from \"../../../utils/style.js\";\r\n    // 数字，邮件，手机，url，身份证校验\r\n    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\n    export default {\r\n        data() {\r\n            let self = this\r\n            return {\r\n                addEditForm:null,\r\n                id: '',\r\n                type: '',\r\n                ro:{\r\n                    codeIndex : true,\r\n                    indexName : false,\r\n                    superId : false,\r\n                    beizhu : false,\r\n                },\r\n                ruleForm: {\r\n                    codeIndex: '',\r\n                    indexName: '',\r\n                    superId : '',\r\n                    beizhu : '',\r\n                },\r\n                rules: {\r\n                    /*beizhu: [\r\n                        { required: true, message: '备注不能为空', trigger: 'blur' },\r\n                        {  pattern: /^[1-9]\\d*$/,\r\n                            message: '备注只能为正整数',\r\n                            trigger: 'blur'\r\n                        }\r\n                    ],*/\r\n                }\r\n            };\r\n        },\r\n        props: [\"parent\"],\r\n        computed: {\r\n        },\r\n        created() {\r\n            this.addEditForm = styleJs.addStyle();\r\n            this.addEditStyleChange()\r\n            this.addEditUploadStyleChange()\r\n        },\r\n        methods: {\r\n            // 初始化\r\n            init(id,type) {\r\n                if (id) {\r\n                    this.id = id;\r\n                    this.type = type;\r\n                }\r\n                if(this.type=='info'||this.type=='else'){\r\n                    this.info(id);\r\n                }else{\r\n                    //查询最大值 start\r\n                    this.$http({\r\n                        url: `dictionary/maxCodeIndex`,\r\n                        method: \"post\",\r\n                        data: {\"dicCode\":\"sex_types\"}\r\n                    }).then(({ data }) => {\r\n                        if (data && data.code === 0) {\r\n                            this.ruleForm.codeIndex = data.maxCodeIndex;\r\n                        } else {\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                    //查询最大值 end\r\n                }\r\n            },\r\n            // 多级联动参数\r\n            info(id) {\r\n                this.$http({\r\n                    url: `dictionary/info/${id}`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                    this.ruleForm = data.data;\r\n                    //解决前台上传图片后台不显示的问题\r\n                    let reg=new RegExp('../../../upload','g')//g代表全部\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n            },\r\n            // 提交\r\n            onSubmit() {\r\n                if((!this.ruleForm.indexName)){\r\n                    this.$message.error('性别类型不能为空');\r\n                    return\r\n                }\r\n                this.$refs[\"ruleForm\"].validate(valid => {\r\n                    if (valid) {\r\n                        let ruleForm = this.ruleForm;\r\n                        ruleForm[\"dicCode\"]=\"sex_types\";\r\n                        ruleForm[\"dicName\"]=\"性别类型\";\r\n                        this.$http({\r\n                            url: `dictionary/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n                            method: \"post\",\r\n                            data: ruleForm\r\n                        }).then(({ data }) => {\r\n                            if (data && data.code === 0) {\r\n                                this.$message({\r\n                                    message: \"操作成功\",\r\n                                    type: \"success\",\r\n                                    duration: 1500,\r\n                                    onClose: () => {\r\n                                        this.parent.showFlag = true;\r\n                                        this.parent.addOrUpdateFlag = false;\r\n                                        this.parent.dictionaryCrossAddOrUpdateFlag = false;\r\n                                        this.parent.search();\r\n                                        this.parent.contentStyleChange();\r\n\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                this.$message.error(data.msg);\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            // 返回\r\n            back() {\r\n                this.parent.showFlag = true;\r\n                this.parent.addOrUpdateFlag = false;\r\n                this.parent.dictionaryCrossAddOrUpdateFlag = false;\r\n                this.parent.contentStyleChange();\r\n            },\r\n            addEditStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    // input\r\n                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n                    el.style.height = this.addEditForm.inputHeight\r\n                el.style.color = this.addEditForm.inputFontColor\r\n                el.style.fontSize = this.addEditForm.inputFontSize\r\n                el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n                el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n                el.style.borderColor = this.addEditForm.inputBorderColor\r\n                el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n                el.style.backgroundColor = this.addEditForm.inputBgColor\r\n            })\r\n                document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n                    el.style.lineHeight = this.addEditForm.inputHeight\r\n                el.style.color = this.addEditForm.inputLableColor\r\n                el.style.fontSize = this.addEditForm.inputLableFontSize\r\n            })\r\n                // select\r\n                document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n                    el.style.height = this.addEditForm.selectHeight\r\n                el.style.color = this.addEditForm.selectFontColor\r\n                el.style.fontSize = this.addEditForm.selectFontSize\r\n                el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n                el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n                el.style.borderColor = this.addEditForm.selectBorderColor\r\n                el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n                el.style.backgroundColor = this.addEditForm.selectBgColor\r\n            })\r\n                document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n                    el.style.lineHeight = this.addEditForm.selectHeight\r\n                el.style.color = this.addEditForm.selectLableColor\r\n                el.style.fontSize = this.addEditForm.selectLableFontSize\r\n            })\r\n                document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n                    el.style.color = this.addEditForm.selectIconFontColor\r\n                el.style.fontSize = this.addEditForm.selectIconFontSize\r\n            })\r\n                // date\r\n                document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n                    el.style.height = this.addEditForm.dateHeight\r\n                el.style.color = this.addEditForm.dateFontColor\r\n                el.style.fontSize = this.addEditForm.dateFontSize\r\n                el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n                el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n                el.style.borderColor = this.addEditForm.dateBorderColor\r\n                el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n                el.style.backgroundColor = this.addEditForm.dateBgColor\r\n            })\r\n                document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n                    el.style.lineHeight = this.addEditForm.dateHeight\r\n                el.style.color = this.addEditForm.dateLableColor\r\n                el.style.fontSize = this.addEditForm.dateLableFontSize\r\n            })\r\n                document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n                    el.style.color = this.addEditForm.dateIconFontColor\r\n                el.style.fontSize = this.addEditForm.dateIconFontSize\r\n                el.style.lineHeight = this.addEditForm.dateHeight\r\n            })\r\n                // upload\r\n                let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n                document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n                    el.style.width = this.addEditForm.uploadHeight\r\n                el.style.height = this.addEditForm.uploadHeight\r\n                el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n            })\r\n                document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n                    el.style.lineHeight = this.addEditForm.uploadHeight\r\n                el.style.color = this.addEditForm.uploadLableColor\r\n                el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n            })\r\n                document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n                    el.style.color = this.addEditForm.uploadIconFontColor\r\n                el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n                el.style.lineHeight = iconLineHeight\r\n                el.style.display = 'block'\r\n            })\r\n                // 多文本输入框\r\n                document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n                    el.style.height = this.addEditForm.textareaHeight\r\n                el.style.color = this.addEditForm.textareaFontColor\r\n                el.style.fontSize = this.addEditForm.textareaFontSize\r\n                el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n                el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n                el.style.borderColor = this.addEditForm.textareaBorderColor\r\n                el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n                el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n            })\r\n                document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n                    // el.style.lineHeight = this.addEditForm.textareaHeight\r\n                    el.style.color = this.addEditForm.textareaLableColor\r\n                el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n            })\r\n                // 保存\r\n                document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n                    el.style.width = this.addEditForm.btnSaveWidth\r\n                el.style.height = this.addEditForm.btnSaveHeight\r\n                el.style.color = this.addEditForm.btnSaveFontColor\r\n                el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n                el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n                el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n                el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n                el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n                el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n            })\r\n                // 返回\r\n                document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n                    el.style.width = this.addEditForm.btnCancelWidth\r\n                el.style.height = this.addEditForm.btnCancelHeight\r\n                el.style.color = this.addEditForm.btnCancelFontColor\r\n                el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n                el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n                el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n                el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n                el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n                el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n            })\r\n            })\r\n            },\r\n            addEditUploadStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n                    el.style.width = this.addEditForm.uploadHeight\r\n                el.style.height = this.addEditForm.uploadHeight\r\n                el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n            })\r\n            })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\">\r\n.editor{\r\n  height: 500px;\r\n\r\n  & ::v-deep .ql-container {\r\n\t  height: 310px;\r\n  }\r\n}\r\n.amap-wrapper {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n.search-box {\r\n  position: absolute;\r\n}\r\n.addEdit-block {\r\n\tmargin: -10px;\r\n}\r\n.detail-form-content {\r\n\tpadding: 12px;\r\n}\r\n.btn .el-button {\r\n  padding: 0;\r\n}</style>\r\n"], "mappings": "AA2DA,OAAAA,OAAA;AACA;AACA,SAAAC,QAAA,EAAAC,UAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,KAAA,EAAAC,WAAA;AACA;EACAC,KAAA;IACA,IAAAC,IAAA;IACA;MACAC,WAAA;MACAC,EAAA;MACAC,IAAA;MACAC,EAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,QAAA;QACAJ,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAE,KAAA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;MANA;IAQA;EACA;EACAC,KAAA;EACAC,QAAA,GACA;EACAC,QAAA;IACA,KAAAZ,WAAA,GAAAV,OAAA,CAAAuB,QAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,wBAAA;EACA;EACAC,OAAA;IACA;IACAC,KAAAhB,EAAA,EAAAC,IAAA;MACA,IAAAD,EAAA;QACA,KAAAA,EAAA,GAAAA,EAAA;QACA,KAAAC,IAAA,GAAAA,IAAA;MACA;MACA,SAAAA,IAAA,mBAAAA,IAAA;QACA,KAAAgB,IAAA,CAAAjB,EAAA;MACA;QACA;QACA,KAAAkB,KAAA;UACAC,GAAA;UACAC,MAAA;UACAvB,IAAA;YAAA;UAAA;QACA,GAAAwB,IAAA;UAAAxB;QAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAyB,IAAA;YACA,KAAAf,QAAA,CAAAJ,SAAA,GAAAN,IAAA,CAAA0B,YAAA;UACA;YACA,KAAAC,QAAA,CAAAC,KAAA,CAAA5B,IAAA,CAAA6B,GAAA;UACA;QACA;QACA;MACA;IACA;IACA;IACAT,KAAAjB,EAAA;MACA,KAAAkB,KAAA;QACAC,GAAA,qBAAAnB,EAAA;QACAoB,MAAA;MACA,GAAAC,IAAA;QAAAxB;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAyB,IAAA;UACA,KAAAf,QAAA,GAAAV,IAAA,CAAAA,IAAA;UACA;UACA,IAAA8B,GAAA,OAAAC,MAAA;QACA;UACA,KAAAJ,QAAA,CAAAC,KAAA,CAAA5B,IAAA,CAAA6B,GAAA;QACA;MACA;IACA;IACA;IACAG,SAAA;MACA,UAAAtB,QAAA,CAAAH,SAAA;QACA,KAAAoB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAK,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAzB,QAAA,QAAAA,QAAA;UACAA,QAAA;UACAA,QAAA;UACA,KAAAW,KAAA;YACAC,GAAA,sBAAAZ,QAAA,CAAAP,EAAA;YACAoB,MAAA;YACAvB,IAAA,EAAAU;UACA,GAAAc,IAAA;YAAAxB;UAAA;YACA,IAAAA,IAAA,IAAAA,IAAA,CAAAyB,IAAA;cACA,KAAAE,QAAA;gBACAS,OAAA;gBACAhC,IAAA;gBACAiC,QAAA;gBACAC,OAAA,EAAAA,CAAA;kBACA,KAAAC,MAAA,CAAAC,QAAA;kBACA,KAAAD,MAAA,CAAAE,eAAA;kBACA,KAAAF,MAAA,CAAAG,8BAAA;kBACA,KAAAH,MAAA,CAAAI,MAAA;kBACA,KAAAJ,MAAA,CAAAK,kBAAA;gBAEA;cACA;YACA;cACA,KAAAjB,QAAA,CAAAC,KAAA,CAAA5B,IAAA,CAAA6B,GAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAgB,KAAA;MACA,KAAAN,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAE,eAAA;MACA,KAAAF,MAAA,CAAAG,8BAAA;MACA,KAAAH,MAAA,CAAAK,kBAAA;IACA;IACA5B,mBAAA;MACA,KAAA8B,SAAA;QACA;QACAC,QAAA,CAAAC,gBAAA,2CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAC,MAAA,QAAAlD,WAAA,CAAAmD,WAAA;UACAH,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAqD,cAAA;UACAL,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAuD,aAAA;UACAP,EAAA,CAAAC,KAAA,CAAAO,WAAA,QAAAxD,WAAA,CAAAyD,gBAAA;UACAT,EAAA,CAAAC,KAAA,CAAAS,WAAA,QAAA1D,WAAA,CAAA2D,gBAAA;UACAX,EAAA,CAAAC,KAAA,CAAAW,WAAA,QAAA5D,WAAA,CAAA6D,gBAAA;UACAb,EAAA,CAAAC,KAAA,CAAAa,YAAA,QAAA9D,WAAA,CAAA+D,iBAAA;UACAf,EAAA,CAAAC,KAAA,CAAAe,eAAA,QAAAhE,WAAA,CAAAiE,YAAA;QACA;QACApB,QAAA,CAAAC,gBAAA,+CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAiB,UAAA,QAAAlE,WAAA,CAAAmD,WAAA;UACAH,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAmE,eAAA;UACAnB,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAoE,kBAAA;QACA;QACA;QACAvB,QAAA,CAAAC,gBAAA,4CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAC,MAAA,QAAAlD,WAAA,CAAAqE,YAAA;UACArB,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAsE,eAAA;UACAtB,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAuE,cAAA;UACAvB,EAAA,CAAAC,KAAA,CAAAO,WAAA,QAAAxD,WAAA,CAAAwE,iBAAA;UACAxB,EAAA,CAAAC,KAAA,CAAAS,WAAA,QAAA1D,WAAA,CAAAyE,iBAAA;UACAzB,EAAA,CAAAC,KAAA,CAAAW,WAAA,QAAA5D,WAAA,CAAA0E,iBAAA;UACA1B,EAAA,CAAAC,KAAA,CAAAa,YAAA,QAAA9D,WAAA,CAAA2E,kBAAA;UACA3B,EAAA,CAAAC,KAAA,CAAAe,eAAA,QAAAhE,WAAA,CAAA4E,aAAA;QACA;QACA/B,QAAA,CAAAC,gBAAA,gDAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAiB,UAAA,QAAAlE,WAAA,CAAAqE,YAAA;UACArB,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAA6E,gBAAA;UACA7B,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAA8E,mBAAA;QACA;QACAjC,QAAA,CAAAC,gBAAA,6CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAA+E,mBAAA;UACA/B,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAgF,kBAAA;QACA;QACA;QACAnC,QAAA,CAAAC,gBAAA,0CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAC,MAAA,QAAAlD,WAAA,CAAAiF,UAAA;UACAjC,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAkF,aAAA;UACAlC,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAmF,YAAA;UACAnC,EAAA,CAAAC,KAAA,CAAAO,WAAA,QAAAxD,WAAA,CAAAoF,eAAA;UACApC,EAAA,CAAAC,KAAA,CAAAS,WAAA,QAAA1D,WAAA,CAAAqF,eAAA;UACArC,EAAA,CAAAC,KAAA,CAAAW,WAAA,QAAA5D,WAAA,CAAAsF,eAAA;UACAtC,EAAA,CAAAC,KAAA,CAAAa,YAAA,QAAA9D,WAAA,CAAAuF,gBAAA;UACAvC,EAAA,CAAAC,KAAA,CAAAe,eAAA,QAAAhE,WAAA,CAAAwF,WAAA;QACA;QACA3C,QAAA,CAAAC,gBAAA,8CAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAiB,UAAA,QAAAlE,WAAA,CAAAiF,UAAA;UACAjC,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAyF,cAAA;UACAzC,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAA0F,iBAAA;QACA;QACA7C,QAAA,CAAAC,gBAAA,yCAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAA2F,iBAAA;UACA3C,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAA4F,gBAAA;UACA5C,EAAA,CAAAC,KAAA,CAAAiB,UAAA,QAAAlE,WAAA,CAAAiF,UAAA;QACA;QACA;QACA,IAAAY,cAAA,GAAAC,QAAA,MAAA9F,WAAA,CAAA+F,YAAA,IAAAD,QAAA,MAAA9F,WAAA,CAAAgG,iBAAA;QACAnD,QAAA,CAAAC,gBAAA,oDAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAgD,KAAA,QAAAjG,WAAA,CAAA+F,YAAA;UACA/C,EAAA,CAAAC,KAAA,CAAAC,MAAA,QAAAlD,WAAA,CAAA+F,YAAA;UACA/C,EAAA,CAAAC,KAAA,CAAAO,WAAA,QAAAxD,WAAA,CAAAgG,iBAAA;UACAhD,EAAA,CAAAC,KAAA,CAAAS,WAAA,QAAA1D,WAAA,CAAAkG,iBAAA;UACAlD,EAAA,CAAAC,KAAA,CAAAW,WAAA,QAAA5D,WAAA,CAAAmG,iBAAA;UACAnD,EAAA,CAAAC,KAAA,CAAAa,YAAA,QAAA9D,WAAA,CAAAoG,kBAAA;UACApD,EAAA,CAAAC,KAAA,CAAAe,eAAA,QAAAhE,WAAA,CAAAqG,aAAA;QACA;QACAxD,QAAA,CAAAC,gBAAA,gDAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAiB,UAAA,QAAAlE,WAAA,CAAA+F,YAAA;UACA/C,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAsG,gBAAA;UACAtD,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAuG,mBAAA;QACA;QACA1D,QAAA,CAAAC,gBAAA,yCAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAwG,mBAAA;UACAxD,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAyG,kBAAA;UACAzD,EAAA,CAAAC,KAAA,CAAAiB,UAAA,GAAA2B,cAAA;UACA7C,EAAA,CAAAC,KAAA,CAAAyD,OAAA;QACA;QACA;QACA7D,QAAA,CAAAC,gBAAA,iDAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAC,MAAA,QAAAlD,WAAA,CAAA2G,cAAA;UACA3D,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAA4G,iBAAA;UACA5D,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAA6G,gBAAA;UACA7D,EAAA,CAAAC,KAAA,CAAAO,WAAA,QAAAxD,WAAA,CAAA8G,mBAAA;UACA9D,EAAA,CAAAC,KAAA,CAAAS,WAAA,QAAA1D,WAAA,CAAA+G,mBAAA;UACA/D,EAAA,CAAAC,KAAA,CAAAW,WAAA,QAAA5D,WAAA,CAAAgH,mBAAA;UACAhE,EAAA,CAAAC,KAAA,CAAAa,YAAA,QAAA9D,WAAA,CAAAiH,oBAAA;UACAjE,EAAA,CAAAC,KAAA,CAAAe,eAAA,QAAAhE,WAAA,CAAAkH,eAAA;QACA;QACArE,QAAA,CAAAC,gBAAA,kDAAAC,OAAA,CAAAC,EAAA;UACA;UACAA,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAmH,kBAAA;UACAnE,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAoH,qBAAA;QACA;QACA;QACAvE,QAAA,CAAAC,gBAAA,qCAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAgD,KAAA,QAAAjG,WAAA,CAAAqH,YAAA;UACArE,EAAA,CAAAC,KAAA,CAAAC,MAAA,QAAAlD,WAAA,CAAAsH,aAAA;UACAtE,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAuH,gBAAA;UACAvE,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAwH,eAAA;UACAxE,EAAA,CAAAC,KAAA,CAAAO,WAAA,QAAAxD,WAAA,CAAAyH,kBAAA;UACAzE,EAAA,CAAAC,KAAA,CAAAS,WAAA,QAAA1D,WAAA,CAAA0H,kBAAA;UACA1E,EAAA,CAAAC,KAAA,CAAAW,WAAA,QAAA5D,WAAA,CAAA2H,kBAAA;UACA3E,EAAA,CAAAC,KAAA,CAAAa,YAAA,QAAA9D,WAAA,CAAA4H,mBAAA;UACA5E,EAAA,CAAAC,KAAA,CAAAe,eAAA,QAAAhE,WAAA,CAAA6H,cAAA;QACA;QACA;QACAhF,QAAA,CAAAC,gBAAA,mCAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAgD,KAAA,QAAAjG,WAAA,CAAA8H,cAAA;UACA9E,EAAA,CAAAC,KAAA,CAAAC,MAAA,QAAAlD,WAAA,CAAA+H,eAAA;UACA/E,EAAA,CAAAC,KAAA,CAAAG,KAAA,QAAApD,WAAA,CAAAgI,kBAAA;UACAhF,EAAA,CAAAC,KAAA,CAAAK,QAAA,QAAAtD,WAAA,CAAAiI,iBAAA;UACAjF,EAAA,CAAAC,KAAA,CAAAO,WAAA,QAAAxD,WAAA,CAAAkI,oBAAA;UACAlF,EAAA,CAAAC,KAAA,CAAAS,WAAA,QAAA1D,WAAA,CAAAmI,oBAAA;UACAnF,EAAA,CAAAC,KAAA,CAAAW,WAAA,QAAA5D,WAAA,CAAAoI,oBAAA;UACApF,EAAA,CAAAC,KAAA,CAAAa,YAAA,QAAA9D,WAAA,CAAAqI,qBAAA;UACArF,EAAA,CAAAC,KAAA,CAAAe,eAAA,QAAAhE,WAAA,CAAAsI,gBAAA;QACA;MACA;IACA;IACAvH,yBAAA;MACA,KAAA6B,SAAA;QACAC,QAAA,CAAAC,gBAAA,+EAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAgD,KAAA,QAAAjG,WAAA,CAAA+F,YAAA;UACA/C,EAAA,CAAAC,KAAA,CAAAC,MAAA,QAAAlD,WAAA,CAAA+F,YAAA;UACA/C,EAAA,CAAAC,KAAA,CAAAO,WAAA,QAAAxD,WAAA,CAAAgG,iBAAA;UACAhD,EAAA,CAAAC,KAAA,CAAAS,WAAA,QAAA1D,WAAA,CAAAkG,iBAAA;UACAlD,EAAA,CAAAC,KAAA,CAAAW,WAAA,QAAA5D,WAAA,CAAAmG,iBAAA;UACAnD,EAAA,CAAAC,KAAA,CAAAa,YAAA,QAAA9D,WAAA,CAAAoG,kBAAA;UACApD,EAAA,CAAAC,KAAA,CAAAe,eAAA,QAAAhE,WAAA,CAAAqG,aAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}