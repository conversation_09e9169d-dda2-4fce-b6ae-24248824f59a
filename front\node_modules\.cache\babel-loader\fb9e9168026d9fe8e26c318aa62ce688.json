{"remainingRequest": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\code\\t\\157\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\code\\t\\157\\front\\src\\views\\modules\\buhuoshneqing\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\code\\t\\157\\front\\src\\views\\modules\\buhuoshneqing\\list.vue", "mtime": 1730041301063}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["AddOrUpdate", "styleJs", "data", "searchForm", "key", "sessionTable", "role", "userId", "buhuotixingTypesSelectSearch", "form", "id", "buhuotixingId", "yonghuId", "buhuoshneqingYesnoTypes", "buhuoshneqingYesnoText", "createTime", "dataList", "pageIndex", "pageSize", "totalPage", "dataListLoading", "dataListSelections", "showFlag", "sfshVisiable", "shForm", "chartVisiable", "echartsDate", "Date", "addOrUpdateFlag", "contents", "layouts", "buhuoshneqingYesnoTypesVisible", "json_fields", "created", "listStyle", "init", "getDataList", "contentStyleChange", "mounted", "$storage", "get", "filters", "htmlfilter", "val", "replace", "components", "computed", "methods", "chartDialog", "_this", "params", "dateFormat", "riqi", "getFullYear", "thisTable", "tableName", "sumColum", "date", "$nextTick", "statistic", "$echarts", "document", "getElementById", "$http", "url", "method", "then", "code", "yAxisName", "xAxisName", "series", "yAxis", "for<PERSON>ach", "item", "index", "tempMap", "name", "legend", "type", "push", "option", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "toolbox", "feature", "magicType", "show", "saveAsImage", "xAxis", "axisLabel", "formatter", "setOption", "window", "onresize", "resize", "$message", "message", "duration", "onClose", "search", "contentSearchStyleChange", "contentBtnAdAllStyleChange", "contentSearchBtnStyleChange", "contentTableBtnStyleChange", "contentPageStyleChange", "querySelectorAll", "el", "textAlign", "inputFontPosition", "style", "height", "inputHeight", "lineHeight", "inputFontColor", "fontSize", "inputFontSize", "borderWidth", "inputBorderWidth", "borderStyle", "inputBorderStyle", "borderColor", "inputBorderColor", "borderRadius", "inputBorderRadius", "backgroundColor", "inputBgColor", "inputTitle", "inputTitleColor", "inputTitleSize", "setTimeout", "inputIconColor", "searchBtnHeight", "searchBtnFontColor", "searchBtnFontSize", "searchBtnBorderWidth", "searchBtnBorderStyle", "searchBtnBorderColor", "searchBtnBorderRadius", "searchBtnBgColor", "btnAdAllHeight", "btnAdAllAddFontColor", "btnAdAllFontSize", "btnAdAllBorderWidth", "btnAdAllBorderStyle", "btnAdAllBorderColor", "btnAdAllBorderRadius", "btnAdAllAddBgColor", "btnAdAllDelFontColor", "btnAdAllDelBgColor", "btnAdAllWarnFontColor", "btnAdAllWarnBgColor", "rowStyle", "row", "rowIndex", "tableStripe", "tableStripeFontColor", "cellStyle", "tableStripeBgColor", "headerRowStyle", "tableHeaderFontColor", "headerCellStyle", "tableHeaderBgColor", "arr", "pageTotal", "pageSizes", "pagePrevNext", "pagePager", "pageJumper", "join", "pageEachNum", "page", "limit", "sort", "buhuotixingName", "undefined", "buhuotixingTypes", "yo<PERSON><PERSON><PERSON><PERSON>", "list", "total", "sizeChangeHandle", "currentChangeHandle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrUpdateHandler", "crossAddOrUpdateFlag", "$refs", "addOrUpdate", "download", "file", "open", "delete<PERSON><PERSON><PERSON>", "ids", "Number", "map", "$confirm", "confirmButtonText", "cancelButtonText", "error", "msg", "buhuoshneqingUploadSuccess", "buhuoshneqingUploadError", "openYesnoTypes", "buhuoshneqingYesnoTypesShenhe", "alert", "buhuotixingStautsTypes"], "sources": ["src/views/modules/buhuoshneqing/list.vue"], "sourcesContent": ["<template>\r\n    <div class=\"main-content\">\r\n        <el-dialog title=\"审核\" :visible.sync=\"buhuoshneqingYesnoTypesVisible\">\r\n            <el-form :model=\"form\">\r\n                <input type=\"hidden\" v-model=\"form.id\">\r\n                <el-form-item label=\"审核\" >\r\n                    <el-select v-model=\"form.buhuoshneqingYesnoTypes\" placeholder=\"请选择审核类型\">\r\n                        <el-option label=\"通过\" value=\"2\"></el-option>\r\n                        <el-option label=\"拒绝\" value=\"3\"></el-option>\r\n                    </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"审核意见\">\r\n                    <el-input type=\"textarea\" v-model=\"form.buhuoshneqingYesnoText\" placeholder=\"审核意见\"></el-input>\r\n                </el-form-item>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"buhuoshneqingYesnoTypesVisible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"buhuoshneqingYesnoTypesShenhe\">提 交</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                                                \r\n                                         \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '物品名称' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.buhuotixingName\" placeholder=\"物品名称\" clearable></el-input>\r\n                    </el-form-item>\r\n                     \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '物品类型' : ''\">\r\n                        <el-select v-model=\"searchForm.buhuotixingTypes\" placeholder=\"请选择物品类型\">\r\n                            <el-option label=\"=-请选择-=\" value=\"\"></el-option>\r\n                            <el-option\r\n                                    v-for=\"(item,index) in buhuotixingTypesSelectSearch\"\r\n                                    v-bind:key=\"index\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                <!--lable是要展示的名称-->\r\n                                <!--value是值-->\r\n                            </el-option>\r\n                        </el-select>\r\n                    </el-form-item>\r\n                                                                                                                         \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '员工姓名' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuName\" placeholder=\"员工姓名\" clearable></el-input>\r\n                    </el-form-item>\r\n                                                                                                                        \r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('buhuoshneqing','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('buhuoshneqing','删除')\"\r\n                                :disabled=\"dataListSelections.length <= 0\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                @click=\"deleteHandler()\"\r\n                        >删除</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('buhuoshneqing','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('buhuoshneqing','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/wurenchangku/upload/buhuoshneqingMuBan.xls\"\r\n                        >批量导入补货申请数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('buhuoshneqing','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"wurenchangku/file/upload\"\r\n                                :on-success=\"buhuoshneqingUploadSuccess\"\r\n                                :on-error=\"buhuoshneqingUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('buhuoshneqing','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入补货申请数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('buhuoshneqing','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"buhuoshneqing.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('buhuoshneqing','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column  v-if=\"contents.tableSelection\"\r\n                                      type=\"selection\"\r\n                                      header-align=\"center\"\r\n                                      align=\"center\"\r\n                                      width=\"50\">\r\n                    </el-table-column>\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"buhuotixingName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"物品名称\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.buhuotixingName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"buhuotixingTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"物品类型\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.buhuotixingValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"buhuotixingNumber\"\r\n                                      header-align=\"center\"\r\n                                      label=\"补货数量\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.buhuotixingNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"buhuotixingStautsTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"补货状态\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.buhuotixingStautsValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"员工姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\" prop=\"yonghuPhoto\"\r\n                               header-align=\"center\"\r\n                               width=\"200\"\r\n                               label=\"头像\">\r\n                        <template slot-scope=\"scope\">\r\n                            <div v-if=\"scope.row.yonghuPhoto\">\r\n                                <img :src=\"scope.row.yonghuPhoto\" width=\"100\" height=\"100\">\r\n                            </div>\r\n                            <div v-else>无图片</div>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuPhone\"\r\n                                      header-align=\"center\"\r\n                                      label=\"员工手机号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuPhone}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuIdNumber\"\r\n                                      header-align=\"center\"\r\n                                      label=\"员工身份证号\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuIdNumber}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"yonghuEmail\"\r\n                                      header-align=\"center\"\r\n                                      label=\"邮箱\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.yonghuEmail}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"buhuoshneqingYesnoTypes\"\r\n                                      header-align=\"center\"\r\n                                      label=\"审核状态\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.buhuoshneqingYesnoValue}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"buhuoshneqingYesnoText\"\r\n                                      header-align=\"center\"\r\n                                      label=\"审核意见\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.buhuoshneqingYesnoText != null &&scope.row.buhuoshneqingYesnoText.length>10\">\r\n                                {{scope.row.buhuoshneqingYesnoText.slice(0,10)}}...\r\n                            </span>\r\n                            <span v-else>\r\n                                {{scope.row.buhuoshneqingYesnoText}}\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('buhuoshneqing','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('buhuoshneqing','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n                            <el-button v-if=\"isAuth('buhuoshneqing','删除')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除</el-button>\r\n                            <el-button v-if=\"isAuth('buhuoshneqing','审核') && scope.row.buhuoshneqingYesnoTypes == 1 \" type=\"primary\" icon=\"el-icon-thumb\" size=\"mini\" @click=\"openYesnoTypes(scope.row.id,scope.row.buhuotixingId)\">审核</el-button>\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">返回</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n            userId:\"\",//当前登录人的id\r\n    //级联表下拉框搜索条件\r\n              buhuotixingTypesSelectSearch : [],\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                buhuotixingId : null,\r\n                yonghuId : null,\r\n                buhuoshneqingYesnoTypes : null,\r\n                buhuoshneqingYesnoText : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            buhuoshneqingYesnoTypesVisible : false,\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                     '物品名称': 'buhuotixingName',\r\n                     '物品类型': 'buhuotixingTypes',\r\n                     '补货数量': 'buhuotixingNumber',\r\n                     '补货状态': 'buhuotixingStautsTypes',\r\n                     '员工姓名': 'yonghuName',\r\n                     '头像': 'yonghuPhoto',\r\n                     '员工手机号': 'yonghuPhone',\r\n                     '员工身份证号': 'yonghuIdNumber',\r\n                     '邮箱': 'yonghuEmail',\r\n                //本表字段\r\n                     '审核状态': \"buhuoshneqingYesnoTypes\",\r\n                     '审核意见': \"buhuoshneqingYesnoText\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n            this.userId = this.$storage.get(\"userId\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\", //%Y-%m\r\n                    riqi :_this.echartsDate.getFullYear(),\r\n                    thisTable : {//当前表\r\n                        tableName :\"buhuoshneqing\",//当前表表名,\r\n                        sumColum : 'buhuoshneqing_number', //求和字段\r\n                        date : 'insert_time',//分组日期字段\r\n                        // string : 'buhuoshneqing_name',//分组字符串字段\r\n                        // types : 'buhuoshneqing_types',//分组下拉框字段\r\n                    },\r\n                    // joinTable : {//级联表（可以不存在）\r\n                    //     tableName :\"yonghu\",//级联表表名\r\n                    //     // date : 'insert_time',//分组日期字段\r\n                    //     string : 'yonghu_name',//分组字符串字段\r\n                    //     // types : 'yonghu_types',//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let yAxisName = \"数值\";//根据查询数据具体改(单列要改,多列不改)\r\n                            let xAxisName = \"月份\";\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                // tempMap.name= [\"数值\"];//根据查询数据具体改(单列要改,多列不改)\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: xAxisName,\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: yAxisName,//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value}' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option,true);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                ////饼状图\r\n                //_this.chartVisiable = true;\r\n                // this.$nextTick(()=>{\r\n                //     var statistic = this.$echarts.init(document.getElementById(\"statistic\"),'macarons');\r\n                //     let params = {\r\n                //         tableName: \"buhuoshneqing\",\r\n                //         groupColumn: \"buhuoshneqing_types\",\r\n                //     }\r\n                //     this.$http({\r\n                //         url: \"newSelectGroupCount\",\r\n                //         method: \"get\",\r\n                //         params: params\r\n                //     }).then(({data}) => {\r\n                //         if (data && data.code === 0) {\r\n                //             let res = data.data;\r\n                //             let xAxis = [];\r\n                //             let yAxis = [];\r\n                //             let pArray = []\r\n                //             for(let i=0;i<res.length;i++){\r\n                //                 xAxis.push(res[i].name);\r\n                //                 yAxis.push(res[i].value);\r\n                //                 pArray.push({\r\n                //                     value: res[i].value,\r\n                //                     name: res[i].name\r\n                //                 })\r\n                //                 var option = {};\r\n                //                 option = {\r\n                //                     title: {\r\n                //                         text: '保险合同类型统计',\r\n                //                         left: 'center'\r\n                //                     },\r\n                //                     tooltip: {\r\n                //                         trigger: 'item',\r\n                //                         formatter: '{b} : {c} ({d}%)'\r\n                //                     },\r\n                //                     series: [\r\n                //                         {\r\n                //                             type: 'pie',\r\n                //                             radius: '55%',\r\n                //                             center: ['50%', '60%'],\r\n                //                             data: pArray,\r\n                //                             emphasis: {\r\n                //                                 itemStyle: {\r\n                //                                     shadowBlur: 10,\r\n                //                                     shadowOffsetX: 0,\r\n                //                                     shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                //                                 }\r\n                //                             }\r\n                //                         }\r\n                //                     ]\r\n                //                 };\r\n                //                 statistic.setOption(option);\r\n                //                 window.onresize = function() {\r\n                //                     statistic.resize();\r\n                //                 };\r\n                //             }\r\n                //         }\r\n                //     });\r\n                // })\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                }\r\n\r\n                                         \r\n                if (this.searchForm.buhuotixingName!= '' && this.searchForm.buhuotixingName!= undefined) {\r\n                    params['buhuotixingName'] = '%' + this.searchForm.buhuotixingName + '%'\r\n                }\r\n                     \r\n                if (this.searchForm.buhuotixingTypes!= '' && this.searchForm.buhuotixingTypes!= undefined) {\r\n                    params['buhuotixingTypes'] = this.searchForm.buhuotixingTypes\r\n                }\r\n                                                                                                                         \r\n                if (this.searchForm.yonghuName!= '' && this.searchForm.yonghuName!= undefined) {\r\n                    params['yonghuName'] = '%' + this.searchForm.yonghuName + '%'\r\n                }\r\n                                                                                                                                                                        \r\n                params['buhuoshneqingDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"buhuoshneqing/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                this.$http({\r\n                    url: \"dictionary/page?dicCode=buhuotixing_types&page=1&limit=100\",\r\n                    method: \"get\",\r\n                    page: 1,\r\n                    limit: 100,\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.buhuotixingTypesSelectSearch = data.data.list;\r\n                    }\r\n                });\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"buhuoshneqing/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            buhuoshneqingUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"buhuoshneqing/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入补货申请数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            buhuoshneqingUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n            openYesnoTypes(id,buhuotixingId) {\r\n                let _this = this;\r\n                _this.form.id = null;\r\n                _this.form.buhuotixingId = null;\r\n                _this.form.id = id;\r\n                _this.form.buhuotixingId = buhuotixingId;\r\n                _this.form.buhuoshneqingYesnoTypes = \"请选择审核结果\";\r\n                _this.buhuoshneqingYesnoTypesVisible = true;\r\n            },\r\n\r\n            buhuoshneqingYesnoTypesShenhe() {\r\n                let _this = this;\r\n                if(_this.form.buhuoshneqingYesnoTypes == \"请选择审核结果\"){\r\n                    alert(\"请选择审核结果\");\r\n                    return false;\r\n                }\r\n                this.$http({\r\n                    url:`buhuoshneqing/update`,\r\n                    method: \"post\",\r\n                    data: _this.form\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.$message({\r\n                            message: \"审核成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.buhuoshneqingYesnoTypesVisible = false;\r\n                                _this.search();\r\n\t\t\t\t\t\t\t\tif(_this.form.buhuoshneqingYesnoTypes == 2){\r\n\t\t\t\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\t\t\t\tid:_this.form.buhuotixingId,\r\n\t\t\t\t\t\t\t\t\t\tbuhuotixingStautsTypes:2\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\t\t    url:`buhuotixing/update`,\r\n\t\t\t\t\t\t\t\t\t    method: \"post\",\r\n\t\t\t\t\t\t\t\t\t    data: params\r\n\t\t\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\t\t    if (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\t    } else {\r\n\t\t\t\t\t\t\t\t\t        this.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t\t    }\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\r\n                            }\r\n                        });\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                        _this.buhuoshneqingYesnoTypesVisible = false;\r\n                    }\r\n                });\r\n            },        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & ::v-deep el-pagination__sizes{\r\n      & ::v-deep el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n  \r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  } \r\n\r\n  .tables {\r\n\t& ::v-deep .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: rgba(88, 84, 84, 1);\r\n\t\tfont-size: 10px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 20px;\r\n\t\tbackground-color: rgba(153, 204, 51, 1);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: rgba(91, 87, 87, 1);\r\n\t\tfont-size: 10px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 20px;\r\n\t\tbackground-color: rgba(255, 255, 102, 1);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: rgba(255, 255, 255, 1);\r\n\t\tfont-size: 10px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 20px;\r\n\t\tbackground-color: rgba(51, 102, 0, 1);\r\n\t}\r\n\r\n    & ::v-deep .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"], "mappings": "AAmRA,OAAAA,WAAA;AACA,OAAAC,OAAA;AACA;EACAC,KAAA;IACA;MACAC,UAAA;QACAC,GAAA;MACA;MACAC,YAAA;MAAA;MACAC,IAAA;MAAA;MACAC,MAAA;MAAA;MACA;MACAC,4BAAA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,aAAA;QACAC,QAAA;QACAC,uBAAA;QACAC,sBAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,YAAA;MACAC,MAAA;MACAC,aAAA;MACAC,WAAA,MAAAC,IAAA;MAAA;MACAC,eAAA;MACAC,QAAA;MACAC,OAAA;MAEAC,8BAAA;MAEA;MACAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAJ,QAAA,GAAA5B,OAAA,CAAAiC,SAAA;IACA,KAAAC,IAAA;IACA,KAAAC,WAAA;IACA,KAAAC,kBAAA;EACA;EACAC,QAAA;IACA;IACA,KAAAjC,YAAA,QAAAkC,QAAA,CAAAC,GAAA;IACA,KAAAlC,IAAA,QAAAiC,QAAA,CAAAC,GAAA;IACA,KAAAjC,MAAA,QAAAgC,QAAA,CAAAC,GAAA;EAEA;EACAC,OAAA;IACAC,UAAA,WAAAA,CAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,OAAA,aAAAA,OAAA;IACA;EACA;EACAC,UAAA;IACA7C;EACA;EACA8C,QAAA,GACA;EACAC,OAAA;IACAC,YAAA;MACA,IAAAC,KAAA;MACA,IAAAC,MAAA;QACAC,UAAA;QAAA;QACAC,IAAA,EAAAH,KAAA,CAAAvB,WAAA,CAAA2B,WAAA;QACAC,SAAA;UAAA;UACAC,SAAA;UAAA;UACAC,QAAA;UAAA;UACAC,IAAA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAR,KAAA,CAAAxB,aAAA;MACAwB,KAAA,CAAAS,SAAA;QACA,IAAAC,SAAA,QAAAC,QAAA,CAAAzB,IAAA,CAAA0B,QAAA,CAAAC,cAAA;QACA,KAAAC,KAAA;UACAC,GAAA;UACAC,MAAA;UACAf,MAAA,EAAAA;QACA,GAAAgB,IAAA;UAAAhE;QAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;YAEA;YACA;YACA,IAAAC,SAAA;YACA,IAAAC,SAAA;YACA,IAAAC,MAAA;YACApE,IAAA,CAAAA,IAAA,CAAAqE,KAAA,CAAAC,OAAA,WAAAC,IAAA,EAAAC,KAAA;cACA,IAAAC,OAAA;cACA;cACAA,OAAA,CAAAC,IAAA,GAAA1E,IAAA,CAAAA,IAAA,CAAA2E,MAAA,CAAAH,KAAA;cACAC,OAAA,CAAAG,IAAA;cACAH,OAAA,CAAAzE,IAAA,GAAAuE,IAAA;cACAH,MAAA,CAAAS,IAAA,CAAAJ,OAAA;YAEA;YAEA,IAAAK,MAAA;cACAC,OAAA;gBACAC,OAAA;gBACAC,WAAA;kBACAL,IAAA;kBACAM,UAAA;oBACAC,KAAA;kBACA;gBACA;cACA;cACAC,OAAA;gBACAC,OAAA;kBACA;kBACAC,SAAA;oBAAAC,IAAA;oBAAAX,IAAA;kBAAA;kBAAA;kBACA;kBACAY,WAAA;oBAAAD,IAAA;kBAAA;gBACA;cACA;cACAZ,MAAA;gBACA3E,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAA2E,MAAA;cACA;cACAc,KAAA,GACA;gBACAb,IAAA;gBACAF,IAAA,EAAAP,SAAA;gBACAnE,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAAyF,KAAA;gBACAR,WAAA;kBACAL,IAAA;gBACA;cACA,EACA;cACAP,KAAA,GACA;gBACAO,IAAA;gBAAA;gBACAF,IAAA,EAAAR,SAAA;gBAAA;gBACAwB,SAAA;kBACAC,SAAA;gBACA;cACA,EACA;cACAvB,MAAA,EAAAA,MAAA;YACA;YACA;YACAX,SAAA,CAAAmC,SAAA,CAAAd,MAAA;YACA;YACAe,MAAA,CAAAC,QAAA;cACArC,SAAA,CAAAsC,MAAA;YACA;YACA;UACA;YACA,KAAAC,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,EAAAA,CAAA;gBACA,KAAAC,MAAA;cACA;YACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAjE,mBAAA;MACA,KAAAkE,wBAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,sBAAA;IACA;IACAJ,yBAAA;MACA,KAAA7C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,wCAAApC,OAAA,CAAAqC,EAAA;UACA,IAAAC,SAAA;UACA,SAAAjF,QAAA,CAAAkF,iBAAA,OACAD,SAAA;UACA,SAAAjF,QAAA,CAAAkF,iBAAA,OAAAD,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAF,SAAA,GAAAA,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAApF,QAAA,CAAAqF,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAAtF,QAAA,CAAAqF,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAxD,QAAA,CAAAuF,cAAA;UACAP,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAxF,QAAA,CAAAyF,aAAA;UACAT,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA1F,QAAA,CAAA2F,gBAAA;UACAX,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA5F,QAAA,CAAA6F,gBAAA;UACAb,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA9F,QAAA,CAAA+F,gBAAA;UACAf,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAAhG,QAAA,CAAAiG,iBAAA;UACAjB,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAlG,QAAA,CAAAmG,YAAA;QACA;QACA,SAAAnG,QAAA,CAAAoG,UAAA;UACApE,QAAA,CAAA+C,gBAAA,4CAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAxD,QAAA,CAAAqG,eAAA;YACArB,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAxF,QAAA,CAAAsG,cAAA;YACAtB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAAtF,QAAA,CAAAqF,WAAA;UACA;QACA;QACAkB,UAAA;UACAvE,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAxD,QAAA,CAAAwG,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAAtF,QAAA,CAAAqF,WAAA;UACA;UACArD,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAxD,QAAA,CAAAwG,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAAtF,QAAA,CAAAqF,WAAA;UACA;UACArD,QAAA,CAAA+C,gBAAA,uCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAAtF,QAAA,CAAAqF,WAAA;UACA;QACA;MACA;IACA;IACA;IACAT,4BAAA;MACA,KAAA/C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,2CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAApF,QAAA,CAAAyG,eAAA;UACAzB,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAxD,QAAA,CAAA0G,kBAAA;UACA1B,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAxF,QAAA,CAAA2G,iBAAA;UACA3B,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA1F,QAAA,CAAA4G,oBAAA;UACA5B,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA5F,QAAA,CAAA6G,oBAAA;UACA7B,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA9F,QAAA,CAAA8G,oBAAA;UACA9B,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAAhG,QAAA,CAAA+G,qBAAA;UACA/B,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAlG,QAAA,CAAAgH,gBAAA;QACA;MACA;IACA;IACA;IACArC,2BAAA;MACA,KAAA9C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,0CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAApF,QAAA,CAAAiH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAxD,QAAA,CAAAkH,oBAAA;UACAlC,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAxF,QAAA,CAAAmH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA1F,QAAA,CAAAoH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA5F,QAAA,CAAAqH,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA9F,QAAA,CAAAsH,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAAhG,QAAA,CAAAuH,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAlG,QAAA,CAAAwH,kBAAA;QACA;QACAxF,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAApF,QAAA,CAAAiH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAxD,QAAA,CAAAyH,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAxF,QAAA,CAAAmH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA1F,QAAA,CAAAoH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA5F,QAAA,CAAAqH,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA9F,QAAA,CAAAsH,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAAhG,QAAA,CAAAuH,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAlG,QAAA,CAAA0H,kBAAA;QACA;QACA1F,QAAA,CAAA+C,gBAAA,0CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAApF,QAAA,CAAAiH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAAxD,QAAA,CAAA2H,qBAAA;UACA3C,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAAxF,QAAA,CAAAmH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA1F,QAAA,CAAAoH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAA5F,QAAA,CAAAqH,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAA9F,QAAA,CAAAsH,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAAhG,QAAA,CAAAuH,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAlG,QAAA,CAAA4H,mBAAA;QACA;MACA;IACA;IACA;IACAC,SAAA;MAAAC,GAAA;MAAAC;IAAA;MACA,IAAAA,QAAA;QACA,SAAA/H,QAAA,CAAAgI,WAAA;UACA;YAAAxE,KAAA,OAAAxD,QAAA,CAAAiI;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,UAAA;MAAAJ,GAAA;MAAAC;IAAA;MACA,IAAAA,QAAA;QACA,SAAA/H,QAAA,CAAAgI,WAAA;UACA;YAAA9B,eAAA,OAAAlG,QAAA,CAAAmI;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,eAAA;MAAAN,GAAA;MAAAC;IAAA;MACA;QAAAvE,KAAA,OAAAxD,QAAA,CAAAqI;MAAA;IACA;IACAC,gBAAA;MAAAR,GAAA;MAAAC;IAAA;MACA;QAAA7B,eAAA,OAAAlG,QAAA,CAAAuI;MAAA;IACA;IACA;IACA1D,2BAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;IAAA,CACA;IACA;IACAC,uBAAA;MACA,IAAA0D,GAAA;MACA,SAAAxI,QAAA,CAAAyI,SAAA,EAAAD,GAAA,CAAAtF,IAAA;MACA,SAAAlD,QAAA,CAAA0I,SAAA,EAAAF,GAAA,CAAAtF,IAAA;MACA,SAAAlD,QAAA,CAAA2I,YAAA;QACAH,GAAA,CAAAtF,IAAA;QACA,SAAAlD,QAAA,CAAA4I,SAAA,EAAAJ,GAAA,CAAAtF,IAAA;QACAsF,GAAA,CAAAtF,IAAA;MACA;MACA,SAAAlD,QAAA,CAAA6I,UAAA,EAAAL,GAAA,CAAAtF,IAAA;MACA,KAAAjD,OAAA,GAAAuI,GAAA,CAAAM,IAAA;MACA,KAAA9I,QAAA,CAAA+I,WAAA;IACA;IAEAzI,KAAA,GACA;IACAmE,OAAA;MACA,KAAArF,SAAA;MACA,KAAAmB,WAAA;IACA;IACA;IACAA,YAAA;MACA,KAAAhB,eAAA;MACA,IAAA8B,MAAA;QACA2H,IAAA,OAAA5J,SAAA;QACA6J,KAAA,OAAA5J,QAAA;QACA6J,IAAA;MACA;MAGA,SAAA5K,UAAA,CAAA6K,eAAA,eAAA7K,UAAA,CAAA6K,eAAA,IAAAC,SAAA;QACA/H,MAAA,iCAAA/C,UAAA,CAAA6K,eAAA;MACA;MAEA,SAAA7K,UAAA,CAAA+K,gBAAA,eAAA/K,UAAA,CAAA+K,gBAAA,IAAAD,SAAA;QACA/H,MAAA,4BAAA/C,UAAA,CAAA+K,gBAAA;MACA;MAEA,SAAA/K,UAAA,CAAAgL,UAAA,eAAAhL,UAAA,CAAAgL,UAAA,IAAAF,SAAA;QACA/H,MAAA,4BAAA/C,UAAA,CAAAgL,UAAA;MACA;MAEAjI,MAAA;;MAGA,KAAAa,KAAA;QACAC,GAAA;QACAC,MAAA;QACAf,MAAA,EAAAA;MACA,GAAAgB,IAAA;QAAAhE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACA,KAAAnD,QAAA,GAAAd,IAAA,CAAAA,IAAA,CAAAkL,IAAA;UACA,KAAAjK,SAAA,GAAAjB,IAAA,CAAAA,IAAA,CAAAmL,KAAA;QACA;UACA,KAAArK,QAAA;UACA,KAAAG,SAAA;QACA;QACA,KAAAC,eAAA;MACA;;MAEA;MACA,KAAA2C,KAAA;QACAC,GAAA;QACAC,MAAA;QACA4G,IAAA;QACAC,KAAA;MACA,GAAA5G,IAAA;QAAAhE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACA,KAAA3D,4BAAA,GAAAN,IAAA,CAAAA,IAAA,CAAAkL,IAAA;QACA;MACA;MACA;IACA;IACA;IACAE,iBAAA3I,GAAA;MACA,KAAAzB,QAAA,GAAAyB,GAAA;MACA,KAAA1B,SAAA;MACA,KAAAmB,WAAA;IACA;IACA;IACAmJ,oBAAA5I,GAAA;MACA,KAAA1B,SAAA,GAAA0B,GAAA;MACA,KAAAP,WAAA;IACA;IACA;IACAoJ,uBAAA7I,GAAA;MACA,KAAAtB,kBAAA,GAAAsB,GAAA;IACA;IACA;IACA8I,mBAAA/K,EAAA,EAAAoE,IAAA;MACA,KAAAxD,QAAA;MACA,KAAAM,eAAA;MACA,KAAA8J,oBAAA;MACA,IAAA5G,IAAA;QACAA,IAAA;MACA;MACA,KAAApB,SAAA;QACA,KAAAiI,KAAA,CAAAC,WAAA,CAAAzJ,IAAA,CAAAzB,EAAA,EAAAoE,IAAA;MACA;IACA;IACA;IACA+G,SAAAC,IAAA;MACA/F,MAAA,CAAAgG,IAAA;IACA;IACA;IACAC,cAAAtL,EAAA;MACA,IAAAuL,GAAA,GAAAvL,EAAA,IAAAwL,MAAA,CAAAxL,EAAA,UAAAW,kBAAA,CAAA8K,GAAA,CAAA1H,IAAA;QACA,OAAAyH,MAAA,CAAAzH,IAAA,CAAA/D,EAAA;MACA;MAEA,KAAA0L,QAAA,SAAA1L,EAAA;QACA2L,iBAAA;QACAC,gBAAA;QACAxH,IAAA;MACA,GAAAZ,IAAA;QACA,KAAAH,KAAA;UACAC,GAAA;UACAC,MAAA;UACA/D,IAAA,EAAA+L;QACA,GAAA/H,IAAA;UAAAhE;QAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;YACA,KAAA+B,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,EAAAA,CAAA;gBACA,KAAAC,MAAA;cACA;YACA;UACA;YACA,KAAAJ,QAAA,CAAAqG,KAAA,CAAArM,IAAA,CAAAsM,GAAA;UACA;QACA;MACA;IACA;IACA;IACAC,2BAAAvM,IAAA;MACA,IAAA+C,KAAA;MACAA,KAAA,CAAAc,KAAA;QACAC,GAAA,0CAAA9D,IAAA,CAAA4L,IAAA;QACA7H,MAAA;MACA,GAAAC,IAAA;QAAAhE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACAlB,KAAA,CAAAiD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,EAAAA,CAAA;cACApD,KAAA,CAAAqD,MAAA;YACA;UACA;QACA;UACArD,KAAA,CAAAiD,QAAA,CAAAqG,KAAA,CAAArM,IAAA,CAAAsM,GAAA;QACA;MACA;IAEA;IACA;IACAE,yBAAAxM,IAAA;MACA,KAAAgG,QAAA,CAAAqG,KAAA;IACA;IACAI,eAAAjM,EAAA,EAAAC,aAAA;MACA,IAAAsC,KAAA;MACAA,KAAA,CAAAxC,IAAA,CAAAC,EAAA;MACAuC,KAAA,CAAAxC,IAAA,CAAAE,aAAA;MACAsC,KAAA,CAAAxC,IAAA,CAAAC,EAAA,GAAAA,EAAA;MACAuC,KAAA,CAAAxC,IAAA,CAAAE,aAAA,GAAAA,aAAA;MACAsC,KAAA,CAAAxC,IAAA,CAAAI,uBAAA;MACAoC,KAAA,CAAAlB,8BAAA;IACA;IAEA6K,8BAAA;MACA,IAAA3J,KAAA;MACA,IAAAA,KAAA,CAAAxC,IAAA,CAAAI,uBAAA;QACAgM,KAAA;QACA;MACA;MACA,KAAA9I,KAAA;QACAC,GAAA;QACAC,MAAA;QACA/D,IAAA,EAAA+C,KAAA,CAAAxC;MACA,GAAAyD,IAAA;QAAAhE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA;UACA,KAAA+B,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,EAAAA,CAAA;cACApD,KAAA,CAAAlB,8BAAA;cACAkB,KAAA,CAAAqD,MAAA;cACA,IAAArD,KAAA,CAAAxC,IAAA,CAAAI,uBAAA;gBACA,IAAAqC,MAAA;kBACAxC,EAAA,EAAAuC,KAAA,CAAAxC,IAAA,CAAAE,aAAA;kBACAmM,sBAAA;gBACA;gBACA,KAAA/I,KAAA;kBACAC,GAAA;kBACAC,MAAA;kBACA/D,IAAA,EAAAgD;gBACA,GAAAgB,IAAA;kBAAAhE;gBAAA;kBACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiE,IAAA,SACA;oBACA,KAAA+B,QAAA,CAAAqG,KAAA,CAAArM,IAAA,CAAAsM,GAAA;kBACA;gBACA;cACA;YAEA;UACA;QACA;UACA,KAAAtG,QAAA,CAAAqG,KAAA,CAAArM,IAAA,CAAAsM,GAAA;UACAvJ,KAAA,CAAAlB,8BAAA;QACA;MACA;IACA;EAAA;AACA", "ignoreList": []}]}