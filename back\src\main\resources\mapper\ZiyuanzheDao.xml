<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ZiyuanzheDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.username as username
        ,a.password as password
        ,a.ziyuanzhe_name as ziyuanzheName
        ,a.ziyuanzhe_photo as ziyuanzhePhoto
        ,a.ziyuanzhe_phone as ziyuanzhePhone
        ,a.ziyuanzhe_email as ziyuanzheEmail
        ,a.sex_types as sexTypes
        ,a.ziyuanzhe_delete as ziyuanzheDelete
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.ZiyuanzheView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段

        FROM ziyuanzhe  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.username != '' and params.username != null and params.username != 'null' ">
                and a.username like CONCAT('%',#{params.username},'%')
            </if>
            <if test=" params.password != '' and params.password != null and params.password != 'null' ">
                and a.password like CONCAT('%',#{params.password},'%')
            </if>
            <if test=" params.ziyuanzheName != '' and params.ziyuanzheName != null and params.ziyuanzheName != 'null' ">
                and a.ziyuanzhe_name like CONCAT('%',#{params.ziyuanzheName},'%')
            </if>
            <if test=" params.ziyuanzhePhone != '' and params.ziyuanzhePhone != null and params.ziyuanzhePhone != 'null' ">
                and a.ziyuanzhe_phone like CONCAT('%',#{params.ziyuanzhePhone},'%')
            </if>
            <if test=" params.ziyuanzheEmail != '' and params.ziyuanzheEmail != null and params.ziyuanzheEmail != 'null' ">
                and a.ziyuanzhe_email like CONCAT('%',#{params.ziyuanzheEmail},'%')
            </if>
            <if test="params.sexTypes != null and params.sexTypes != ''">
                and a.sex_types = #{params.sexTypes}
            </if>
            <if test="params.ziyuanzheDeleteStart != null and params.ziyuanzheDeleteStart != ''">
                <![CDATA[  and a.ziyuanzhe_delete >= #{params.ziyuanzheDeleteStart}   ]]>
            </if>
            <if test="params.ziyuanzheDeleteEnd != null and params.ziyuanzheDeleteEnd != ''">
                <![CDATA[  and a.ziyuanzhe_delete <= #{params.ziyuanzheDeleteEnd}   ]]>
            </if>
             <if test="params.ziyuanzheDelete != null and params.ziyuanzheDelete != ''">
                and a.ziyuanzhe_delete = #{params.ziyuanzheDelete}
             </if>

        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>