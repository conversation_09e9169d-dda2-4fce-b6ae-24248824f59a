{"_from": "cross-spawn@^7.0.0", "_id": "cross-spawn@7.0.3", "_inBundle": false, "_integrity": "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==", "_location": "/default-gateway/cross-spawn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cross-spawn@^7.0.0", "name": "cross-spawn", "escapedName": "cross-spawn", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/default-gateway/execa"], "_resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz", "_shasum": "f73a85b9d5d41d045551c177e2882d4ac85728a6", "_spec": "cross-spawn@^7.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\default-gateway\\node_modules\\execa", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/moxystudio/node-cross-spawn/issues"}, "bundleDependencies": false, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "deprecated": false, "description": "Cross platform child_process#spawn and child_process#spawnSync", "devDependencies": {"@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "babel-core": "^6.26.3", "babel-jest": "^24.9.0", "babel-preset-moxy": "^3.1.0", "eslint": "^5.16.0", "eslint-config-moxy": "^7.1.0", "husky": "^3.0.5", "jest": "^24.9.0", "lint-staged": "^9.2.5", "mkdirp": "^0.5.1", "rimraf": "^3.0.0", "standard-version": "^7.0.0"}, "engines": {"node": ">= 8"}, "files": ["lib"], "homepage": "https://github.com/moxystudio/node-cross-spawn", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "keywords": ["spawn", "spawnSync", "windows", "cross-platform", "path-ext", "shebang", "cmd", "execute"], "license": "MIT", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "main": "index.js", "name": "cross-spawn", "repository": {"type": "git", "url": "git+ssh://**************/moxystudio/node-cross-spawn.git"}, "scripts": {"lint": "eslint .", "postrelease": "git push --follow-tags origin HEAD && npm publish", "prerelease": "npm t && npm run lint", "release": "standard-version", "test": "jest --env node --coverage"}, "version": "7.0.3"}