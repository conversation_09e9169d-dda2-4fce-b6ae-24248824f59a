{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\src\\utils\\i18n.js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\utils\\i18n.js", "mtime": 1649064848737}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gdHJhbnNsYXRlIHJvdXRlci5tZXRhLnRpdGxlLCBiZSB1c2VkIGluIGJyZWFkY3J1bWIgc2lkZWJhciB0YWdzdmlldwpleHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVUaXRsZSh0aXRsZSkgewogIGNvbnN0IGhhc0tleSA9IHRoaXMuJHRlKCdyb3V0ZS4nICsgdGl0bGUpOwogIGlmIChoYXNLZXkpIHsKICAgIC8vICR0IDp0aGlzIG1ldGhvZCBmcm9tIHZ1ZS1pMThuLCBpbmplY3QgaW4gQC9sYW5nL2luZGV4LmpzCiAgICBjb25zdCB0cmFuc2xhdGVkVGl0bGUgPSB0aGlzLiR0KCdyb3V0ZS4nICsgdGl0bGUpOwogICAgcmV0dXJuIHRyYW5zbGF0ZWRUaXRsZTsKICB9CiAgcmV0dXJuIHRpdGxlOwp9"}, {"version": 3, "names": ["generateTitle", "title", "<PERSON><PERSON><PERSON>", "$te", "translatedTitle", "$t"], "sources": ["D:/xuangmu/yuanma/code1/front/src/utils/i18n.js"], "sourcesContent": ["// translate router.meta.title, be used in breadcrumb sidebar tagsview\r\nexport function generateTitle(title) {\r\n  const hasKey = this.$te('route.' + title)\r\n\r\n  if (hasKey) {\r\n    // $t :this method from vue-i18n, inject in @/lang/index.js\r\n    const translatedTitle = this.$t('route.' + title)\r\n\r\n    return translatedTitle\r\n  }\r\n  return title\r\n}\r\n"], "mappings": "AAAA;AACA,OAAO,SAASA,aAAaA,CAACC,KAAK,EAAE;EACnC,MAAMC,MAAM,GAAG,IAAI,CAACC,GAAG,CAAC,QAAQ,GAAGF,KAAK,CAAC;EAEzC,IAAIC,MAAM,EAAE;IACV;IACA,MAAME,eAAe,GAAG,IAAI,CAACC,EAAE,CAAC,QAAQ,GAAGJ,KAAK,CAAC;IAEjD,OAAOG,eAAe;EACxB;EACA,OAAOH,KAAK;AACd", "ignoreList": []}]}