
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/


!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";var e=2311,n=function(){return e++},v="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:function(t){var e={},i=t.match(/Firefox\/([\d.]+)/),n=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),r=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);i&&(e.firefox=!0,e.version=i[1]);n&&(e.ie=!0,e.version=n[1]);r&&(e.edge=!0,e.version=r[1]);o&&(e.weChat=!0);return{browser:e,os:{},node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!e.ie&&!e.edge,pointerEventsSupported:"onpointerdown"in window&&(e.edge||e.ie&&11<=e.version),domSupported:"undefined"!=typeof document}}(navigator.userAgent);var s={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},l={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},u=Object.prototype.toString,i=Array.prototype,a=i.forEach,h=i.filter,r=i.slice,c=i.map,d=i.reduce,o={};function f(t,e){"createCanvas"===t&&(g=null),o[t]=e}function b(t){if(null==t||"object"!=typeof t)return t;var e=t,i=u.call(t);if("[object Array]"===i){if(!$(t)){e=[];for(var n=0,r=t.length;n<r;n++)e[n]=b(t[n])}}else if(l[i]){if(!$(t)){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(n=0,r=t.length;n<r;n++)e[n]=b(t[n])}}}else if(!s[i]&&!$(t)&&!V(t))for(var a in e={},t)t.hasOwnProperty(a)&&(e[a]=b(t[a]));return e}function m(t,e,i){if(!N(e)||!N(t))return i?b(e):t;for(var n in e)if(e.hasOwnProperty(n)){var r=t[n],o=e[n];!N(o)||!N(r)||O(o)||O(r)||V(o)||V(r)||R(o)||R(r)||$(o)||$(r)?!i&&n in t||(t[n]=b(e[n])):m(r,o,i)}return t}function p(t,e){for(var i=t[0],n=1,r=t.length;n<r;n++)i=m(i,t[n],e);return i}function k(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function A(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function y(){return o.createCanvas()}var g;function _(){return g=g||y().getContext("2d")}function x(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i}return-1}function w(t,e){var i=t.prototype;function n(){}for(var r in n.prototype=e.prototype,t.prototype=new n,i)i.hasOwnProperty(r)&&(t.prototype[r]=i[r]);(t.prototype.constructor=t).superClass=e}function S(t,e,i){A(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,i)}function L(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function D(t,e,i){if(t&&e)if(t.forEach&&t.forEach===a)t.forEach(e,i);else if(t.length===+t.length)for(var n=0,r=t.length;n<r;n++)e.call(i,t[n],n,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(i,t[o],o,t)}function P(t,e,i){if(t&&e){if(t.map&&t.map===c)return t.map(e,i);for(var n=[],r=0,o=t.length;r<o;r++)n.push(e.call(i,t[r],r,t));return n}}function M(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===d)return t.reduce(e,i,n);for(var r=0,o=t.length;r<o;r++)i=e.call(n,i,t[r],r,t);return i}}function I(t,e,i){if(t&&e){if(t.filter&&t.filter===h)return t.filter(e,i);for(var n=[],r=0,o=t.length;r<o;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function C(t,e){var i=r.call(arguments,2);return function(){return t.apply(e,i.concat(r.call(arguments)))}}function T(t){var e=r.call(arguments,1);return function(){return t.apply(this,e.concat(r.call(arguments)))}}function O(t){return"[object Array]"===u.call(t)}function z(t){return"function"==typeof t}function E(t){return"[object String]"===u.call(t)}function N(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function R(t){return!!s[u.call(t)]}function B(t){return!!l[u.call(t)]}function V(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function F(t){return t!=t}function H(t){for(var e=0,i=arguments.length;e<i;e++)if(null!=arguments[e])return arguments[e]}function W(t,e){return null!=t?t:e}function G(t,e,i){return null!=t?t:null!=e?e:i}function Z(){return Function.call.apply(r,arguments)}function U(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function X(t,e){if(!t)throw new Error(e)}function Y(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}o.createCanvas=function(){return document.createElement("canvas")};var j="__ec_primitive__";function q(t){t[j]=!0}function $(t){return t[j]}function K(t){var i=O(t);this.data={};var n=this;function e(t,e){i?n.set(t,e):n.set(e,t)}t instanceof K?t.each(e):t&&D(t,e)}function Q(t){return new K(t)}function J(){}K.prototype={constructor:K,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){for(var i in void 0!==e&&(t=C(t,e)),this.data)this.data.hasOwnProperty(i)&&t(this.data[i],i)},removeKey:function(t){delete this.data[t]}};var tt=(Object.freeze||Object)({$override:f,clone:b,merge:m,mergeAll:p,extend:k,defaults:A,createCanvas:y,getContext:_,indexOf:x,inherits:w,mixin:S,isArrayLike:L,each:D,map:P,reduce:M,filter:I,find:function(t,e,i){if(t&&e)for(var n=0,r=t.length;n<r;n++)if(e.call(i,t[n],n,t))return t[n]},bind:C,curry:T,isArray:O,isFunction:z,isString:E,isObject:N,isBuiltInObject:R,isTypedArray:B,isDom:V,eqNaN:F,retrieve:H,retrieve2:W,retrieve3:G,slice:Z,normalizeCssArray:U,assert:X,trim:Y,setAsPrimitive:q,isPrimitive:$,createHashMap:Q,concatArray:function(t,e){for(var i=new t.constructor(t.length+e.length),n=0;n<t.length;n++)i[n]=t[n];var r=t.length;for(n=0;n<e.length;n++)i[n+r]=e[n];return i},noop:J}),et="undefined"==typeof Float32Array?Array:Float32Array;function it(t,e){var i=new et(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function nt(t,e){return t[0]=e[0],t[1]=e[1],t}function rt(t){var e=new et(2);return e[0]=t[0],e[1]=t[1],e}function ot(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t}function at(t,e,i,n){return t[0]=e[0]+i[0]*n,t[1]=e[1]+i[1]*n,t}function st(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function lt(t){return Math.sqrt(ht(t))}var ut=lt;function ht(t){return t[0]*t[0]+t[1]*t[1]}var ct=ht;function dt(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t}function ft(t,e){var i=lt(e);return 0===i?(t[0]=0,t[1]=0):(t[0]=e[0]/i,t[1]=e[1]/i),t}function pt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var gt=pt;function mt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var vt=mt;function yt(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t}function _t(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t}function xt(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}var wt=(Object.freeze||Object)({create:it,copy:nt,clone:rt,set:function(t,e,i){return t[0]=e,t[1]=i,t},add:ot,scaleAndAdd:at,sub:st,len:lt,length:ut,lenSquare:ht,lengthSquare:ct,mul:function(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t},div:function(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:dt,normalize:ft,distance:pt,dist:gt,distanceSquare:mt,distSquare:vt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,i,n){return t[0]=e[0]+n*(i[0]-e[0]),t[1]=e[1]+n*(i[1]-e[1]),t},applyTransform:yt,min:_t,max:xt});function bt(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function St(t,e){return{target:t,topTarget:e&&e.topTarget}}bt.prototype={constructor:bt,_dragStart:function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(St(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(r,o,t),this.dispatchToElement(St(e,t),"drag",t.event);var a=this.findHover(i,n,e).target,s=this._dropTarget;e!==(this._dropTarget=a)&&(s&&a!==s&&this.dispatchToElement(St(s,t),"dragleave",t.event),a&&a!==s&&this.dispatchToElement(St(a,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(St(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(St(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var Mt=Array.prototype.slice,It=function(t){this._$handlers={},this._$eventProcessor=t};function Ct(t,e,i,n,r,o){var a=t._$handlers;if("function"==typeof i&&(r=n,n=i,i=null),!n||!e)return t;i=function(t,e){var i=t._$eventProcessor;return null!=e&&i&&i.normalizeQuery&&(e=i.normalizeQuery(e)),e}(t,i),a[e]||(a[e]=[]);for(var s=0;s<a[e].length;s++)if(a[e][s].h===n)return t;var l={h:n,one:o,query:i,ctx:r||t,callAtLast:n.zrEventfulCallAtLast},u=a[e].length-1,h=a[e][u];return h&&h.callAtLast?a[e].splice(u,0,l):a[e].push(l),t}It.prototype={constructor:It,one:function(t,e,i,n){return Ct(this,t,e,i,n,!0)},on:function(t,e,i,n){return Ct(this,t,e,i,n,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,o=i[t].length;r<o;r++)i[t][r].h!==e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t].length&&delete i[t]}else delete i[t];return this},trigger:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;3<r&&(n=Mt.call(n,1));for(var o=e.length,a=0;a<o;){var s=e[a];if(i&&i.filter&&null!=s.query&&!i.filter(t,s.query))a++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,n[1]);break;case 3:s.h.call(s.ctx,n[1],n[2]);break;default:s.h.apply(s.ctx,n)}s.one?(e.splice(a,1),o--):a++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;4<r&&(n=Mt.call(n,1,n.length-1));for(var o=n[n.length-1],a=e.length,s=0;s<a;){var l=e[s];if(i&&i.filter&&null!=l.query&&!i.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(o);break;case 2:l.h.call(o,n[1]);break;case 3:l.h.call(o,n[1],n[2]);break;default:l.h.apply(o,n)}l.one?(e.splice(s,1),a--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var Tt=Math.log(2);function At(t,e,i,n,r,o){var a=n+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Tt);return t[i][l]}for(var u=n|1<<i,h=i+1;n&1<<h;)h++;for(var c=0,d=0,f=0;d<s;d++){var p=1<<d;p&r||(c+=(f%2?-1:1)*t[i][d]*At(t,e-1,h,u,r|p,o),f++)}return o[a]=c}function Dt(t,e){var i=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],n={},r=At(i,8,0,0,0,n);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*At(i,7,0===a?1:0,1<<a,1<<s,n)/r*e[a];return function(t,e,i){var n=e*o[6]+i*o[7]+1;t[0]=(e*o[0]+i*o[1]+o[2])/n,t[1]=(e*o[3]+i*o[4]+o[5])/n}}}var kt="___zrEVENTSAVED",Pt=[];function Lt(t,e,i,n,r){if(e.getBoundingClientRect&&v.domSupported&&!Ot(e)){var o=e[kt]||(e[kt]={}),a=function(t,e,i){for(var n=i?"invTrans":"trans",r=e[n],o=e.srcCoords,a=!0,s=[],l=[],u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,d=h.left,f=h.top;s.push(d,f),a=a&&o&&d===o[c]&&f===o[1+c],l.push(t[u].offsetLeft,t[u].offsetTop)}return a&&r?r:(e.srcCoords=s,e[n]=i?Dt(l,s):Dt(s,l))}(function(t,e){var i=e.markers;if(i)return i;i=e.markers=[];for(var n=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",n[l]+":0",r[u]+":0",n[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),i.push(a)}return i}(e,o),o,r);if(a)return a(t,i,n),!0}return!1}function Ot(t){return"CANVAS"===t.nodeName.toUpperCase()}var zt="undefined"!=typeof window&&!!window.addEventListener,Et=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Nt=[];function Rt(t,e,i,n){return i=i||{},n||!v.canvasSupported?Bt(t,e,i):v.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):Bt(t,e,i),i}function Bt(t,e,i){if(v.domSupported&&t.getBoundingClientRect){var n=e.clientX,r=e.clientY;if(Ot(t)){var o=t.getBoundingClientRect();return i.zrX=n-o.left,void(i.zrY=r-o.top)}if(Lt(Nt,t,n,r))return i.zrX=Nt[0],void(i.zrY=Nt[1])}i.zrX=i.zrY=0}function Vt(t){return t||window.event}function Ft(t,e,i){if(null!=(e=Vt(e)).zrX)return e;var n=e.type;if(n&&0<=n.indexOf("touch")){var r="touchend"!==n?e.targetTouches[0]:e.changedTouches[0];r&&Rt(t,r,e,i)}else Rt(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&Et.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function Ht(t,e,i,n){zt?t.addEventListener(e,i,n):t.attachEvent("on"+e,i)}var Wt=zt?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};function Gt(t){return 2===t.which||3===t.which}function Zt(){this._track=[]}function Ut(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}Zt.prototype={constructor:Zt,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,i){var n=t.touches;if(n){for(var r={points:[],touches:[],target:e,event:t},o=0,a=n.length;o<a;o++){var s=n[o],l=Rt(i,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in Xt)if(Xt.hasOwnProperty(e)){var i=Xt[e](this._track,t);if(i)return i}}};var Xt={pinch:function(t,e){var i=t.length;if(i){var n=(t[i-1]||{}).points,r=(t[i-2]||{}).points||n;if(r&&1<r.length&&n&&1<n.length){var o=Ut(n)/Ut(r);isFinite(o)||(o=1),e.pinchScale=o;var a=function(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}(n);return e.pinchX=a[0],e.pinchY=a[1],{type:"pinch",target:t[0].target,event:e}}}}},Yt="silent";function jt(){Wt(this.event)}function qt(){}qt.prototype.dispose=function(){};function $t(t,e,i,n){It.call(this),this.storage=t,this.painter=e,this.painterRoot=n,i=i||new qt,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,bt.call(this),this.setHandlerProxy(i)}var Kt=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"];function Qt(t,e,i){if(t[t.rectHover?"rectContain":"contain"](e,i)){for(var n,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,i))return!1;r.silent&&(n=!0),r=r.parent}return!n||Yt}return!1}function Jt(t,e,i){var n=t.painter;return e<0||e>n.getWidth()||i<0||i>n.getHeight()}$t.prototype={constructor:$t,setHandlerProxy:function(e){this.proxy&&this.proxy.dispose(),e&&(D(Kt,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},mousemove:function(t){var e=t.zrX,i=t.zrY,n=Jt(this,e,i),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target);var a=this._hovered=n?{x:e,y:i}:this.findHover(e,i),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,i=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&(i||this.trigger("globalout",{type:"globalout",event:t}))},resize:function(t){this._hovered={}},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,i){var n=(t=t||{}).target;if(!n||!n.silent){for(var r="on"+e,o=function(t,e,i){return{type:t,event:i,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch,which:i.which,stop:jt}}(e,t,i);n&&(n[r]&&(o.cancelBubble=n[r].call(n,o)),n.trigger(e,o),n=n.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},findHover:function(t,e,i){for(var n=this.storage.getDisplayList(),r={x:t,y:e},o=n.length-1;0<=o;o--){var a;if(n[o]!==i&&!n[o].ignore&&(a=Qt(n[o],t,e))&&(r.topTarget||(r.topTarget=n[o]),a!==Yt)){r.target=n[o];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new Zt);var i=this._gestureMgr;"start"===e&&i.clear();var n=i.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&i.clear(),n){var r=n.type;t.gestureEvent=r,this.dispatchToElement({target:n.target},r,n.event)}}},D(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){$t.prototype[a]=function(t){var e,i,n=t.zrX,r=t.zrY,o=Jt(this,n,r);if("mouseup"===a&&o||(i=(e=this.findHover(n,r)).target),"mousedown"===a)this._downEl=i,this._downPoint=[t.zrX,t.zrY],this._upEl=i;else if("mouseup"===a)this._upEl=i;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<gt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}}),S($t,It),S($t,bt);var te="undefined"==typeof Float32Array?Array:Float32Array;function ee(){var t=new te(6);return ie(t),t}function ie(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ne(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function re(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],o=e[0]*i[2]+e[2]*i[3],a=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function oe(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t}function ae(t,e,i){var n=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],u=Math.sin(i),h=Math.cos(i);return t[0]=n*h+a*u,t[1]=-n*u+a*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*o+u*l,t[5]=h*l-u*o,t}function se(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t}function le(t,e){var i=e[0],n=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=i*a-o*n;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-a*r)*l,t[5]=(o*r-i*s)*l,t):null}var ue=(Object.freeze||Object)({create:ee,identity:ie,copy:ne,mul:re,translate:oe,rotate:ae,scale:se,invert:le,clone:function(t){var e=ee();return ne(e,t),e}}),he=ie;function ce(t){return 5e-5<t||t<-5e-5}var de=function(t){(t=t||{}).position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},fe=de.prototype;fe.transform=null,fe.needLocalTransform=function(){return ce(this.rotation)||ce(this.position[0])||ce(this.position[1])||ce(this.scale[0]-1)||ce(this.scale[1]-1)};var pe=[];fe.updateTransform=function(){var t=this.parent,e=t&&t.transform,i=this.needLocalTransform(),n=this.transform;if(i||e){n=n||ee(),i?this.getLocalTransform(n):he(n),e&&(i?re(n,t.transform,n):ne(n,t.transform)),this.transform=n;var r=this.globalScaleRatio;if(null!=r&&1!==r){this.getGlobalScale(pe);var o=pe[0]<0?-1:1,a=pe[1]<0?-1:1,s=((pe[0]-o)*r+o)/pe[0]||0,l=((pe[1]-a)*r+a)/pe[1]||0;n[0]*=s,n[1]*=s,n[2]*=l,n[3]*=l}this.invTransform=this.invTransform||ee(),le(this.invTransform,n)}else n&&he(n)},fe.getLocalTransform=function(t){return de.getLocalTransform(this,t)},fe.setTransform=function(t){var e=this.transform,i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},fe.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var ge=[],me=ee();fe.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=this.position,r=this.scale;ce(e-1)&&(e=Math.sqrt(e)),ce(i-1)&&(i=Math.sqrt(i)),t[0]<0&&(e=-e),t[3]<0&&(i=-i),n[0]=t[4],n[1]=t[5],r[0]=e,r[1]=i,this.rotation=Math.atan2(-t[1]/i,t[0]/e)}},fe.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(re(ge,t.invTransform,e),e=ge);var i=this.origin;i&&(i[0]||i[1])&&(me[4]=i[0],me[5]=i[1],re(ge,e,me),ge[4]-=i[0],ge[5]-=i[1],e=ge),this.setLocalTransform(e)}},fe.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},fe.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&yt(i,i,n),i},fe.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&yt(i,i,n),i},de.getLocalTransform=function(t,e){he(e=e||[]);var i=t.origin,n=t.scale||[1,1],r=t.rotation||0,o=t.position||[0,0];return i&&(e[4]-=i[0],e[5]-=i[1]),se(e,e,n),r&&ae(e,e,r),i&&(e[4]+=i[0],e[5]+=i[1]),e[4]+=o[0],e[5]+=o[1],e};var ve={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),-i*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?i*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:i*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-ve.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*ve.bounceIn(2*t):.5*ve.bounceOut(2*t-1)+.5}};function ye(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}ye.prototype={constructor:ye,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var i=(t-this._startTime-this._pausedTime)/this._life;if(!(i<0)){i=Math.min(i,1);var n=this.easing,r="string"==typeof n?ve[n]:n,o="function"==typeof r?r(i):i;return this.fire("frame",o),1===i?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){this[t="on"+t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};function _e(){this.head=null,this.tail=null,this._len=0}var xe=_e.prototype;xe.insert=function(t){var e=new be(t);return this.insertEntry(e),e},xe.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},xe.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},xe.len=function(){return this._len},xe.clear=function(){this.head=this.tail=null,this._len=0};function we(t){this._list=new _e,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null}var be=function(t){this.value=t,this.next,this.prev},Se=we.prototype;Se.put=function(t,e){var i=this._list,n=this._map,r=null;if(null==n[t]){var o=i.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&0<o){var s=i.head;i.remove(s),delete n[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new be(e),a.key=t,i.insertEntry(a),n[t]=a}return r},Se.get=function(t){var e=this._map[t],i=this._list;if(null!=e)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},Se.clear=function(){this._list.clear(),this._map={}};var Me={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Ie(t){return(t=Math.round(t))<0?0:255<t?255:t}function Ce(t){return t<0?0:1<t?1:t}function Te(t){return t.length&&"%"===t.charAt(t.length-1)?Ie(parseFloat(t)/100*255):Ie(parseInt(t,10))}function Ae(t){return t.length&&"%"===t.charAt(t.length-1)?Ce(parseFloat(t)/100):Ce(parseFloat(t))}function De(t,e,i){return i<0?i+=1:1<i&&(i-=1),6*i<1?t+(e-t)*i*6:2*i<1?e:3*i<2?t+(e-t)*(2/3-i)*6:t}function ke(t,e,i){return t+(e-t)*i}function Pe(t,e,i,n,r){return t[0]=e,t[1]=i,t[2]=n,t[3]=r,t}function Le(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Oe=new we(20),ze=null;function Ee(t,e){ze&&Le(ze,e),ze=Oe.put(t,ze||e.slice())}function Ne(t,e){if(t){e=e||[];var i=Oe.get(t);if(i)return Le(e,i);var n,r=(t+="").replace(/ /g,"").toLowerCase();if(r in Me)return Le(e,Me[r]),Ee(t,e),e;if("#"===r.charAt(0))return 4===r.length?0<=(n=parseInt(r.substr(1),16))&&n<=4095?(Pe(e,(3840&n)>>4|(3840&n)>>8,240&n|(240&n)>>4,15&n|(15&n)<<4,1),Ee(t,e),e):void Pe(e,0,0,0,1):7===r.length?0<=(n=parseInt(r.substr(1),16))&&n<=16777215?(Pe(e,(16711680&n)>>16,(65280&n)>>8,255&n,1),Ee(t,e),e):void Pe(e,0,0,0,1):void 0;var o=r.indexOf("("),a=r.indexOf(")");if(-1!==o&&a+1===r.length){var s=r.substr(0,o),l=r.substr(o+1,a-(o+1)).split(","),u=1;switch(s){case"rgba":if(4!==l.length)return void Pe(e,0,0,0,1);u=Ae(l.pop());case"rgb":return 3!==l.length?void Pe(e,0,0,0,1):(Pe(e,Te(l[0]),Te(l[1]),Te(l[2]),u),Ee(t,e),e);case"hsla":return 4!==l.length?void Pe(e,0,0,0,1):(l[3]=Ae(l[3]),Re(l,e),Ee(t,e),e);case"hsl":return 3!==l.length?void Pe(e,0,0,0,1):(Re(l,e),Ee(t,e),e);default:return}}Pe(e,0,0,0,1)}}function Re(t,e){var i=(parseFloat(t[0])%360+360)%360/360,n=Ae(t[1]),r=Ae(t[2]),o=r<=.5?r*(n+1):r+n-r*n,a=2*r-o;return Pe(e=e||[],Ie(255*De(a,o,i+1/3)),Ie(255*De(a,o,i)),Ie(255*De(a,o,i-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Be(t,e){var i=Ne(t);if(i){for(var n=0;n<3;n++)i[n]=e<0?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0,255<i[n]?i[n]=255:t[n]<0&&(i[n]=0);return Ue(i,4===i.length?"rgba":"rgb")}}function Ve(t){var e=Ne(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function Fe(t,e,i){if(e&&e.length&&0<=t&&t<=1){i=i||[];var n=t*(e.length-1),r=Math.floor(n),o=Math.ceil(n),a=e[r],s=e[o],l=n-r;return i[0]=Ie(ke(a[0],s[0],l)),i[1]=Ie(ke(a[1],s[1],l)),i[2]=Ie(ke(a[2],s[2],l)),i[3]=Ce(ke(a[3],s[3],l)),i}}var He=Fe;function We(t,e,i){if(e&&e.length&&0<=t&&t<=1){var n=t*(e.length-1),r=Math.floor(n),o=Math.ceil(n),a=Ne(e[r]),s=Ne(e[o]),l=n-r,u=Ue([Ie(ke(a[0],s[0],l)),Ie(ke(a[1],s[1],l)),Ie(ke(a[2],s[2],l)),Ce(ke(a[3],s[3],l))],"rgba");return i?{color:u,leftIndex:r,rightIndex:o,value:n}:u}}var Ge=We;function Ze(t,e){if((t=Ne(t))&&null!=e)return t[3]=Ce(e),Ue(t,"rgba")}function Ue(t,e){if(t&&t.length){var i=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(i+=","+t[3]),e+"("+i+")"}}var Xe=(Object.freeze||Object)({parse:Ne,lift:Be,toHex:Ve,fastLerp:Fe,fastMapToColor:He,lerp:We,mapToColor:Ge,modifyHSL:function(t,e,i,n){if(t=Ne(t))return t=function(t){if(t){var e,i,n=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(n,r,o),s=Math.max(n,r,o),l=s-a,u=(s+a)/2;if(0==l)i=e=0;else{i=u<.5?l/(s+a):l/(2-s-a);var h=((s-n)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-o)/6+l/2)/l;n===s?e=d-c:r===s?e=1/3+h-d:o===s&&(e=2/3+c-h),e<0&&(e+=1),1<e&&(e-=1)}var f=[360*e,i,u];return null!=t[3]&&f.push(t[3]),f}}(t),null!=e&&(t[0]=function(t){return(t=Math.round(t))<0?0:360<t?360:t}(e)),null!=i&&(t[1]=Ae(i)),null!=n&&(t[2]=Ae(n)),Ue(Re(t),"rgba")},modifyAlpha:Ze,stringify:Ue}),Ye=Array.prototype.slice;function je(t,e){return t[e]}function qe(t,e,i){t[e]=i}function $e(t,e,i){return(e-t)*i+t}function Ke(t,e,i){return.5<i?e:t}function Qe(t,e,i,n,r){var o=t.length;if(1===r)for(var a=0;a<o;a++)n[a]=$e(t[a],e[a],i);else{var s=o&&t[0].length;for(a=0;a<o;a++)for(var l=0;l<s;l++)n[a][l]=$e(t[a][l],e[a][l],i)}}function Je(t,e,i){var n=t.length,r=e.length;if(n!==r)if(r<n)t.length=r;else for(var o=n;o<r;o++)t.push(1===i?e[o]:Ye.call(e[o]));var a=t[0]&&t[0].length;for(o=0;o<t.length;o++)if(1===i)isNaN(t[o])&&(t[o]=e[o]);else for(var s=0;s<a;s++)isNaN(t[o][s])&&(t[o][s]=e[o][s])}function ti(t,e,i){if(t===e)return!0;var n=t.length;if(n!==e.length)return!1;if(1===i){for(var r=0;r<n;r++)if(t[r]!==e[r])return!1}else{var o=t[0].length;for(r=0;r<n;r++)for(var a=0;a<o;a++)if(t[r][a]!==e[r][a])return!1}return!0}function ei(t,e,i,n,r,o,a,s,l){var u=t.length;if(1===l)for(var h=0;h<u;h++)s[h]=ii(t[h],e[h],i[h],n[h],r,o,a);else{var c=t[0].length;for(h=0;h<u;h++)for(var d=0;d<c;d++)s[h][d]=ii(t[h][d],e[h][d],i[h][d],n[h][d],r,o,a)}}function ii(t,e,i,n,r,o,a){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*a+(-3*(e-i)-2*s-l)*o+s*r+e}function ni(t){if(L(t)){var e=t.length;if(L(t[0])){for(var i=[],n=0;n<e;n++)i.push(Ye.call(t[n]));return i}return Ye.call(t)}return t}function ri(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function oi(t,e,i,n,o,r){var a=t._getter,s=t._setter,l="spline"===e,u=n.length;if(u){var h,c=L(n[0].value),d=!1,f=!1,p=c?function(t){var e=t[t.length-1].value;return L(e&&e[0])?2:1}(n):0;n.sort(function(t,e){return t.time-e.time}),h=n[u-1].time;for(var g=[],m=[],v=n[0].value,y=!0,_=0;_<u;_++){g.push(n[_].time/h);var x=n[_].value;if(c&&ti(x,v,p)||!c&&x===v||(y=!1),"string"==typeof(v=x)){var w=Ne(x);w?(x=w,d=!0):f=!0}m.push(x)}if(r||!y){var b=m[u-1];for(_=0;_<u-1;_++)c?Je(m[_],b,p):!isNaN(m[_])||isNaN(b)||f||d||(m[_]=b);c&&Je(a(t._target,o),b,p);var S,M,I,C,T,A=0,D=0;if(d)var k=[0,0,0,0];var P=new ye({target:t._target,life:h,loop:t._loop,delay:t._delay,onframe:function(t,e){var i;if(e<0)i=0;else if(e<D){for(i=Math.min(A+1,u-1);0<=i&&!(g[i]<=e);i--);i=Math.min(i,u-2)}else{for(i=A;i<u&&!(g[i]>e);i++);i=Math.min(i-1,u-2)}D=e;var n=g[(A=i)+1]-g[i];if(0!=n)if(S=(e-g[i])/n,l)if(I=m[i],M=m[0===i?i:i-1],C=m[u-2<i?u-1:i+1],T=m[u-3<i?u-1:i+2],c)ei(M,I,C,T,S,S*S,S*S*S,a(t,o),p);else{if(d)r=ei(M,I,C,T,S,S*S,S*S*S,k,1),r=ri(k);else{if(f)return Ke(I,C,S);r=ii(M,I,C,T,S,S*S,S*S*S)}s(t,o,r)}else if(c)Qe(m[i],m[i+1],S,a(t,o),p);else{var r;if(d)Qe(m[i],m[i+1],S,k,1),r=ri(k);else{if(f)return Ke(m[i],m[i+1],S);r=$e(m[i],m[i+1],S)}s(t,o,r)}},ondestroy:i});return e&&"spline"!==e&&(P.easing=e),P}}}function ai(t,e,i,n){this._tracks={},this._target=t,this._loop=e||!1,this._getter=i||je,this._setter=n||qe,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]}ai.prototype={when:function(t,e){var i=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:ni(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,i=0;i<e;i++)t[i].call(this)},start:function(t,e){function i(){--o||r._doneCallback()}var n,r=this,o=0;for(var a in this._tracks)if(this._tracks.hasOwnProperty(a)){var s=oi(this,t,i,this._tracks[a],a,e);s&&(this._clipList.push(s),o++,this.animation&&this.animation.addClip(s),n=s)}if(n){var l=n.onframe;n.onframe=function(t,e){l(t,e);for(var i=0;i<r._onframeList.length;i++)r._onframeList[i](t,e)}}return o||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this.animation,n=0;n<e.length;n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var si=1;"undefined"!=typeof window&&(si=Math.max(window.devicePixelRatio||1,1));var li=si,ui=function(){};function hi(){this.animators=[]}var ci=ui;function di(t,e,i,n,r,o,a,s){E(n)?(o=r,r=n,n=0):z(r)?(o=r,r="linear",n=0):z(n)?(o=n,n=0):i=z(i)?(o=i,500):i||500,t.stopAnimation(),function t(e,i,n,r,o,a,s){var l={};var u=0;for(var h in r)r.hasOwnProperty(h)&&(null!=n[h]?N(r[h])&&!L(r[h])?t(e,i?i+"."+h:h,n[h],r[h],o,a,s):(s?(l[h]=n[h],fi(e,i,h,r[h])):l[h]=r[h],u++):null==r[h]||s||fi(e,i,h,r[h]));0<u&&e.animate(i,!1).when(null==o?500:o,l).delay(a||0)}(t,"",t,e,i,n,s);var l=t.animators.slice(),u=l.length;function h(){--u||o&&o()}u||o&&o();for(var c=0;c<l.length;c++)l[c].done(h).start(r,a)}function fi(t,e,i,n){if(e){var r={};r[e]={},r[e][i]=n,t.attr(r)}else t.attr(i,n)}hi.prototype={constructor:hi,animate:function(t,e){var i,n=!1,r=this,o=this.__zr;if(t){var a=t.split("."),s=r;n="shape"===a[0];for(var l=0,u=a.length;l<u;l++)s=s&&s[a[l]];s&&(i=s)}else i=r;if(i){var h=r.animators,c=new ai(i,e);return c.during(function(t){r.dirty(n)}).done(function(){h.splice(x(h,c),1)}),h.push(c),o&&o.animation.addAnimator(c),c}ci('Property "'+t+'" is not existed in element '+r.id)},stopAnimation:function(t){for(var e=this.animators,i=e.length,n=0;n<i;n++)e[n].stop(t);return e.length=0,this},animateTo:function(t,e,i,n,r,o){di(this,t,e,i,n,r,o)},animateFrom:function(t,e,i,n,r,o){di(this,t,e,i,n,r,o,!0)}};var pi=function(t){de.call(this,t),It.call(this,t),hi.call(this,t),this.id=t.id||n()};pi.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var i=this[t];(i=i||(this[t]=[]))[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(N(t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),(this.clipPath=t).__zr=e,(t.__clipTarget=this).dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},S(pi,hi),S(pi,de),S(pi,It);var gi,mi,vi,yi,_i=yt,xi=Math.min,wi=Math.max;function bi(t,e,i,n){i<0&&(t+=i,i=-i),n<0&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}bi.prototype={constructor:bi,union:function(t){var e=xi(t.x,this.x),i=xi(t.y,this.y);this.width=wi(t.x+t.width,this.x+this.width)-e,this.height=wi(t.y+t.height,this.y+this.height)-i,this.x=e,this.y=i},applyTransform:(gi=[],mi=[],vi=[],yi=[],function(t){if(t){gi[0]=vi[0]=this.x,gi[1]=yi[1]=this.y,mi[0]=yi[0]=this.x+this.width,mi[1]=vi[1]=this.y+this.height,_i(gi,gi,t),_i(mi,mi,t),_i(vi,vi,t),_i(yi,yi,t),this.x=xi(gi[0],mi[0],vi[0],yi[0]),this.y=xi(gi[1],mi[1],vi[1],yi[1]);var e=wi(gi[0],mi[0],vi[0],yi[0]),i=wi(gi[1],mi[1],vi[1],yi[1]);this.width=e-this.x,this.height=i-this.y}}),calculateTransform:function(t){var e=t.width/this.width,i=t.height/this.height,n=ee();return oe(n,n,[-this.x,-this.y]),se(n,n,[e,i]),oe(n,n,[t.x,t.y]),n},intersect:function(t){if(!t)return!1;t instanceof bi||(t=bi.create(t));var e=this,i=e.x,n=e.x+e.width,r=e.y,o=e.y+e.height,a=t.x,s=t.x+t.width,l=t.y,u=t.y+t.height;return!(n<a||s<i||o<l||u<r)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},clone:function(){return new bi(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},bi.create=function(t){return new bi(t.x,t.y,t.width,t.height)};var Si=function(t){for(var e in t=t||{},pi.call(this,t),t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};Si.prototype={constructor:Si,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e.length;i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var i=this._children,n=i.indexOf(e);0<=n&&(i.splice(n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof Si&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var e=this.__zr,i=this.__storage,n=this._children,r=x(n,t);return r<0||(n.splice(r,1),t.parent=null,i&&(i.delFromStorage(t),t instanceof Si&&t.delChildrenFromStorage(i)),e&&e.refresh()),this},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i.length;e++)t=i[e],n&&(n.delFromStorage(t),t instanceof Si&&t.delChildrenFromStorage(n)),t.parent=null;return i.length=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i.length;n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children.length;i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.addToStorage(i),i instanceof Si&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.delFromStorage(i),i instanceof Si&&i.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new bi(0,0,0,0),n=t||this._children,r=[],o=0;o<n.length;o++){var a=n[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(r);l?(i.copy(s),i.applyTransform(l),(e=e||i.clone()).union(i)):(e=e||s.clone()).union(s)}}return e||i}},w(Si,pi);var Mi=32,Ii=7;function Ci(t,e,i,n){var r=e+1;if(r===i)return 1;if(n(t[r++],t[e])<0){for(;r<i&&n(t[r],t[r-1])<0;)r++;!function(t,e,i){i--;for(;e<i;){var n=t[e];t[e++]=t[i],t[i--]=n}}(t,e,r)}else for(;r<i&&0<=n(t[r],t[r-1]);)r++;return r-e}function Ti(t,e,i,n,r){for(n===e&&n++;n<i;n++){for(var o,a=t[n],s=e,l=n;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=n-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function Ai(t,e,i,n,r,o){var a=0,s=0,l=1;if(0<o(t,e[i+r])){for(s=n-r;l<s&&0<o(t,e[i+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[i+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s);var u=a;a=r-l,l=r-u}for(a++;a<l;){var h=a+(l-a>>>1);0<o(t,e[i+h])?a=h+1:l=h}return l}function Di(t,e,i,n,r,o){var a=0,s=0,l=1;if(o(t,e[i+r])<0){for(s=r+1;l<s&&o(t,e[i+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=n-r;l<s&&0<=o(t,e[i+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[i+h])<0?l=h:a=h+1}return l}function ki(p,g){var a,s,m=Ii,l=0,v=[];function e(t){var e=a[t],i=s[t],n=a[t+1],r=s[t+1];s[t]=i+r,t===l-3&&(a[t+1]=a[t+2],s[t+1]=s[t+2]),l--;var o=Di(p[n],p,e,i,0,g);e+=o,0!==(i-=o)&&0!==(r=Ai(p[e+i-1],p,n,r,r-1,g))&&(i<=r?function(t,e,i,n){var r=0;for(r=0;r<e;r++)v[r]=p[t+r];var o=0,a=i,s=t;if(p[s++]=p[a++],0==--n){for(r=0;r<e;r++)p[s+r]=v[o+r];return}if(1===e){for(r=0;r<n;r++)p[s+r]=p[a+r];return p[s+n]=v[o]}var l,u,h,c=m;for(;;){u=l=0,h=!1;do{if(g(p[a],v[o])<0){if(p[s++]=p[a++],u++,(l=0)==--n){h=!0;break}}else if(p[s++]=v[o++],l++,u=0,1==--e){h=!0;break}}while((l|u)<c);if(h)break;do{if(0!==(l=Di(p[a],v,o,e,0,g))){for(r=0;r<l;r++)p[s+r]=v[o+r];if(s+=l,o+=l,(e-=l)<=1){h=!0;break}}if(p[s++]=p[a++],0==--n){h=!0;break}if(0!==(u=Ai(v[o],p,a,n,0,g))){for(r=0;r<u;r++)p[s+r]=p[a+r];if(s+=u,a+=u,0===(n-=u)){h=!0;break}}if(p[s++]=v[o++],1==--e){h=!0;break}c--}while(Ii<=l||Ii<=u);if(h)break;c<0&&(c=0),c+=2}if((m=c)<1&&(m=1),1===e){for(r=0;r<n;r++)p[s+r]=p[a+r];p[s+n]=v[o]}else{if(0===e)throw new Error;for(r=0;r<e;r++)p[s+r]=v[o+r]}}(e,i,n,r):function(t,e,i,n){var r=0;for(r=0;r<n;r++)v[r]=p[i+r];var o=t+e-1,a=n-1,s=i+n-1,l=0,u=0;if(p[s--]=p[o--],0==--e){for(l=s-(n-1),r=0;r<n;r++)p[l+r]=v[r];return}if(1===n){for(u=(s-=e)+1,l=(o-=e)+1,r=e-1;0<=r;r--)p[u+r]=p[l+r];return p[s]=v[a]}var h=m;for(;;){var c=0,d=0,f=!1;do{if(g(v[a],p[o])<0){if(p[s--]=p[o--],c++,(d=0)==--e){f=!0;break}}else if(p[s--]=v[a--],d++,c=0,1==--n){f=!0;break}}while((c|d)<h);if(f)break;do{if(0!==(c=e-Di(v[a],p,t,e,e-1,g))){for(e-=c,u=(s-=c)+1,l=(o-=c)+1,r=c-1;0<=r;r--)p[u+r]=p[l+r];if(0===e){f=!0;break}}if(p[s--]=v[a--],1==--n){f=!0;break}if(0!==(d=n-Ai(p[o],v,0,n,n-1,g))){for(n-=d,u=(s-=d)+1,l=(a-=d)+1,r=0;r<d;r++)p[u+r]=v[l+r];if(n<=1){f=!0;break}}if(p[s--]=p[o--],0==--e){f=!0;break}h--}while(Ii<=c||Ii<=d);if(f)break;h<0&&(h=0),h+=2}(m=h)<1&&(m=1);if(1===n){for(u=(s-=e)+1,l=(o-=e)+1,r=e-1;0<=r;r--)p[u+r]=p[l+r];p[s]=v[a]}else{if(0===n)throw new Error;for(l=s-(n-1),r=0;r<n;r++)p[l+r]=v[r]}}(e,i,n,r))}a=[],s=[],this.mergeRuns=function(){for(;1<l;){var t=l-2;if(1<=t&&s[t-1]<=s[t]+s[t+1]||2<=t&&s[t-2]<=s[t]+s[t-1])s[t-1]<s[t+1]&&t--;else if(s[t]>s[t+1])break;e(t)}},this.forceMergeRuns=function(){for(;1<l;){var t=l-2;0<t&&s[t-1]<s[t+1]&&t--,e(t)}},this.pushRun=function(t,e){a[l]=t,s[l]=e,l+=1}}function Pi(t,e,i,n){i=i||0;var r=(n=n||t.length)-i;if(!(r<2)){var o=0;if(r<Mi)Ti(t,i,n,i+(o=Ci(t,i,n,e)),e);else{var a=new ki(t,e),s=function(t){for(var e=0;Mi<=t;)e|=1&t,t>>=1;return t+e}(r);do{if((o=Ci(t,i,n,e))<s){var l=r;s<l&&(l=s),Ti(t,i,i+l,i+o,e),o=l}a.pushRun(i,o),a.mergeRuns(),r-=o,i+=o}while(0!==r);a.forceMergeRuns()}}}function Li(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function Oi(){this._roots=[],this._displayList=[],this._displayListLen=0}Oi.prototype={constructor:Oi,traverse:function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,r=e.length;n<r;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,v.canvasSupported&&Pi(i,Li)},_updateAndAddDisplayable:function(t,e,i){if(!t.ignore||i){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var n=t.clipPath;if(n){e=e?e.slice():[];for(var r=n,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).clipPath}if(t.isGroup){for(var a=t._children,s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,i)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof Si&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var i=this._roots[e];i instanceof Si&&i.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array){e=0;for(var n=t.length;e<n;e++)this.delRoot(t[e])}else{var r=x(this._roots,t);0<=r&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof Si&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:Li};var zi={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},Ei=function(t,e,i){return zi.hasOwnProperty(e)?i*t.dpr:i},Ni={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},Ri=9,Bi=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],Vi=function(t){this.extendFrom(t,!1)};function Fi(t,e,i){var n=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(n=n*i.width+i.x,r=r*i.width+i.x,o=o*i.height+i.y,a=a*i.height+i.y),n=isNaN(n)?0:n,r=isNaN(r)?1:r,o=isNaN(o)?0:o,a=isNaN(a)?0:a,t.createLinearGradient(n,o,r,a)}function Hi(t,e,i){var n=i.width,r=i.height,o=Math.min(n,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*n+i.x,s=s*r+i.y,l*=o),t.createRadialGradient(a,s,0,a,s,l)}Vi.prototype={constructor:Vi,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,i){var n=this,r=i&&i.style,o=!r||t.__attrCachedBy!==Ni.STYLE_BIND;t.__attrCachedBy=Ni.STYLE_BIND;for(var a=0;a<Bi.length;a++){var s=Bi[a],l=s[0];!o&&n[l]===r[l]||(t[l]=Ei(t,l,n[l]||s[1]))}if(!o&&n.fill===r.fill||(t.fillStyle=n.fill),!o&&n.stroke===r.stroke||(t.strokeStyle=n.stroke),!o&&n.opacity===r.opacity||(t.globalAlpha=null==n.opacity?1:n.opacity),!o&&n.blend===r.blend||(t.globalCompositeOperation=n.blend||"source-over"),this.hasStroke()){var u=n.lineWidth;t.lineWidth=u/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&0<this.lineWidth},extendFrom:function(t,e){if(t)for(var i in t)!t.hasOwnProperty(i)||!0!==e&&(!1===e?this.hasOwnProperty(i):null==t[i])||(this[i]=t[i])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,i){for(var n=("radial"===e.type?Hi:Fi)(t,e,i),r=e.colorStops,o=0;o<r.length;o++)n.addColorStop(r[o].offset,r[o].color);return n}};for(var Wi=Vi.prototype,Gi=0;Gi<Bi.length;Gi++){var Zi=Bi[Gi];Zi[0]in Wi||(Wi[Zi[0]]=Zi[1])}Vi.getGradient=Wi.getGradient;function Ui(t,e){this.image=t,this.repeat=e,this.type="pattern"}function Xi(){return!1}function Yi(t,e,i){var n=y(),r=e.getWidth(),o=e.getHeight(),a=n.style;return a&&(a.position="absolute",a.left=0,a.top=0,a.width=r+"px",a.height=o+"px",n.setAttribute("data-zr-dom-id",t)),n.width=r*i,n.height=o*i,n}function ji(t,e,i){var n;i=i||li,"string"==typeof t?n=Yi(t,e,i):N(t)&&(t=(n=t).id),this.id=t;var r=(this.dom=n).style;r&&(n.onselectstart=Xi,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=i}ji.prototype={constructor:ji,__dirty:!0,__used:!(Ui.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")}),__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=Yi("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),n.width=t*i,n.height=e*i,o&&(o.width=t*i,o.height=e*i,1!==i&&this.ctxBack.scale(i,i))},clear:function(t,e){var i,n=this.dom,r=this.ctx,o=n.width,a=n.height,s=(e=e||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,o/u,a/u)),r.clearRect(0,0,o,a),e&&"transparent"!==e&&(e.colorStops?(i=e.__canvasGradient||Vi.getGradient(r,e,{x:0,y:0,width:o,height:a}),e.__canvasGradient=i):e.image&&(i=Ui.prototype.getCanvasPattern.call(e,r)),r.save(),r.fillStyle=i||e,r.fillRect(0,0,o,a),r.restore());if(s){var h=this.domBack;r.save(),r.globalAlpha=l,r.drawImage(h,0,0,o,a),r.restore()}}};var qi="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},$i=new we(50);function Ki(t){if("string"!=typeof t)return t;var e=$i.get(t);return e&&e.image}function Qi(t,e,i,n,r){if(t){if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!i)return e;var o=$i.get(t),a={hostEl:i,cb:n,cbPayload:r};return o?tn(e=o.image)||o.pending.push(a):((e=new Image).onload=e.onerror=Ji,$i.put(t,e.__cachedImgObj={image:e,pending:[a]}),e.src=e.__zrImageSrc=t),e}return e}function Ji(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var i=t.pending[e],n=i.cb;n&&n(this,i.cbPayload),i.hostEl.dirty()}t.pending.length=0}function tn(t){return t&&t.width&&t.height}var en={},nn=0,rn=5e3,on=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,an="12px sans-serif",sn={};function ln(t,e){var i=t+":"+(e=e||an);if(en[i])return en[i];for(var n,r,o=(t+"").split("\n"),a=0,s=0,l=o.length;s<l;s++)a=Math.max((n=o[s],r=e,sn.measureText(n,r)).width,a);return rn<nn&&(nn=0,en={}),nn++,en[i]=a}function un(t,e,i,n,r,o,a,s){return a?function(t,e,i,n,r,o,a,s){var l=_n(t,{rich:a,truncate:s,font:e,textAlign:i,textPadding:r,textLineHeight:o}),u=l.outerWidth,h=l.outerHeight,c=hn(0,u,i),d=cn(0,h,n);return new bi(c,d,u,h)}(t,e,i,n,r,o,a,s):function(t,e,i,n,r,o,a){var s=yn(t,e,r,o,a),l=ln(t,e);r&&(l+=r[1]+r[3]);var u=s.outerHeight,h=hn(0,l,i),c=cn(0,u,n),d=new bi(h,c,l,u);return d.lineHeight=s.lineHeight,d}(t,e,i,n,r,o,s)}function hn(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function cn(t,e,i){return"middle"===i?t-=e/2:"bottom"===i&&(t-=e),t}function dn(t,e,i){var n=e.textPosition,r=e.textDistance,o=i.x,a=i.y;r=r||0;var s=i.height,l=i.width,u=s/2,h="left",c="top";switch(n){case"left":o-=r,a+=u,h="right",c="middle";break;case"right":o+=r+l,a+=u,c="middle";break;case"top":o+=l/2,a-=r,h="center",c="bottom";break;case"bottom":o+=l/2,a+=s+r,h="center";break;case"inside":o+=l/2,a+=u,h="center",c="middle";break;case"insideLeft":o+=r,a+=u,c="middle";break;case"insideRight":o+=l-r,a+=u,h="right",c="middle";break;case"insideTop":o+=l/2,a+=r,h="center";break;case"insideBottom":o+=l/2,a+=s-r,h="center",c="bottom";break;case"insideTopLeft":o+=r,a+=r;break;case"insideTopRight":o+=l-r,a+=r,h="right";break;case"insideBottomLeft":o+=r,a+=s-r,c="bottom";break;case"insideBottomRight":o+=l-r,a+=s-r,h="right",c="bottom"}return(t=t||{}).x=o,t.y=a,t.textAlign=h,t.textVerticalAlign=c,t}function fn(t,e,i,n,r){if(!e)return"";var o=(t+"").split("\n");r=pn(e,i,n,r);for(var a=0,s=o.length;a<s;a++)o[a]=gn(o[a],r);return o.join("\n")}function pn(t,e,i,n){(n=k({},n)).font=e;i=W(i,"...");n.maxIterations=W(n.maxIterations,2);var r=n.minChar=W(n.minChar,0);n.cnCharWidth=ln("国",e);var o=n.ascCharWidth=ln("a",e);n.placeholder=W(n.placeholder,"");for(var a=t=Math.max(0,t-1),s=0;s<r&&o<=a;s++)a-=o;var l=ln(i,e);return a<l&&(i="",l=0),a=t-l,n.ellipsis=i,n.ellipsisWidth=l,n.contentWidth=a,n.containerWidth=t,n}function gn(t,e){var i=e.containerWidth,n=e.font,r=e.contentWidth;if(!i)return"";var o=ln(t,n);if(o<=i)return t;for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?mn(t,r,e.ascCharWidth,e.cnCharWidth):0<o?Math.floor(t.length*r/o):0;o=ln(t=t.substr(0,s),n)}return""===t&&(t=e.placeholder),t}function mn(t,e,i,n){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?i:n}return o}function vn(t){return ln("国",t)}function yn(t,e,i,n,r){null!=t&&(t+="");var o=W(n,vn(e)),a=t?t.split("\n"):[],s=a.length*o,l=s,u=!0;if(i&&(l+=i[0]+i[2]),t&&r){u=!1;var h=r.outerHeight,c=r.outerWidth;if(null!=h&&h<l)t="",a=[];else if(null!=c)for(var d=pn(c-(i?i[1]+i[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),f=0,p=a.length;f<p;f++)a[f]=gn(a[f],d)}return{lines:a,height:s,outerHeight:l,lineHeight:o,canCacheByTextString:u}}function _n(t,e){var i={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return i;for(var n,r=on.lastIndex=0;null!=(n=on.exec(t));){var o=n.index;r<o&&xn(i,t.substring(r,o)),xn(i,n[2],n[1]),r=on.lastIndex}r<t.length&&xn(i,t.substring(r,t.length));var a=i.lines,s=0,l=0,u=[],h=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;h&&(null!=d&&(d-=h[1]+h[3]),null!=f&&(f-=h[0]+h[2]));for(var p=0;p<a.length;p++){for(var g=a[p],m=0,v=0,y=0;y<g.tokens.length;y++){var _=(D=g.tokens[y]).styleName&&e.rich[D.styleName]||{},x=D.textPadding=_.textPadding,w=D.font=_.font||e.font,b=D.textHeight=W(_.textHeight,vn(w));if(x&&(b+=x[0]+x[2]),D.height=b,D.lineHeight=G(_.textLineHeight,e.textLineHeight,b),D.textAlign=_&&_.textAlign||e.textAlign,D.textVerticalAlign=_&&_.textVerticalAlign||"middle",null!=f&&s+D.lineHeight>f)return{lines:[],width:0,height:0};D.textWidth=ln(D.text,w);var S=_.textWidth,M=null==S||"auto"===S;if("string"==typeof S&&"%"===S.charAt(S.length-1))D.percentWidth=S,u.push(D),S=0;else{if(M){S=D.textWidth;var I=_.textBackgroundColor,C=I&&I.image;C&&tn(C=Ki(C))&&(S=Math.max(S,C.width*b/C.height))}var T=x?x[1]+x[3]:0;S+=T;var A=null!=d?d-v:null;null!=A&&A<S&&(!M||A<T?(D.text="",D.textWidth=S=0):(D.text=fn(D.text,A-T,w,c.ellipsis,{minChar:c.minChar}),D.textWidth=ln(D.text,w),S=D.textWidth+T))}v+=D.width=S,_&&(m=Math.max(m,D.lineHeight))}g.width=v,s+=g.lineHeight=m,l=Math.max(l,v)}i.outerWidth=i.width=W(e.textWidth,l),i.outerHeight=i.height=W(e.textHeight,s),h&&(i.outerWidth+=h[1]+h[3],i.outerHeight+=h[0]+h[2]);for(p=0;p<u.length;p++){var D,k=(D=u[p]).percentWidth;D.width=parseInt(k,10)/100*l}return i}function xn(t,e,i){for(var n=""===e,r=e.split("\n"),o=t.lines,a=0;a<r.length;a++){var s=r[a],l={styleName:i,text:s,isLineHolder:!s&&!n};if(a)o.push({tokens:[l]});else{var u=(o[o.length-1]||(o[0]={tokens:[]})).tokens,h=u.length;1===h&&u[0].isLineHolder?u[0]=l:!s&&h&&!n||u.push(l)}}}function wn(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&Y(e)||t.textFont||t.font}function bn(t,e){var i,n,r,o,a,s=e.x,l=e.y,u=e.width,h=e.height,c=e.r;u<0&&(s+=u,u=-u),h<0&&(l+=h,h=-h),"number"==typeof c?i=n=r=o=c:c instanceof Array?1===c.length?i=n=r=o=c[0]:2===c.length?(i=r=c[0],n=o=c[1]):3===c.length?(i=c[0],n=o=c[1],r=c[2]):(i=c[0],n=c[1],r=c[2],o=c[3]):i=n=r=o=0,u<i+n&&(i*=u/(a=i+n),n*=u/a),u<r+o&&(r*=u/(a=r+o),o*=u/a),h<n+r&&(n*=h/(a=n+r),r*=h/a),h<i+o&&(i*=h/(a=i+o),o*=h/a),t.moveTo(s+i,l),t.lineTo(s+u-n,l),0!==n&&t.arc(s+u-n,l+n,n,-Math.PI/2,0),t.lineTo(s+u,l+h-r),0!==r&&t.arc(s+u-r,l+h-r,r,0,Math.PI/2),t.lineTo(s+o,l+h),0!==o&&t.arc(s+o,l+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,l+i),0!==i&&t.arc(s+i,l+i,i,Math.PI,1.5*Math.PI)}sn.measureText=function(t,e){var i=_();return i.font=e||an,i.measureText(t)};var Sn=an,Mn={left:1,right:1,center:1},In={top:1,bottom:1,middle:1},Cn=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],Tn={},An={};function Dn(t){return kn(t),D(t.rich,kn),t}function kn(t){if(t){t.font=wn(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||Mn[e]?e:"left";var i=t.textVerticalAlign||t.textBaseline;"center"===i&&(i="middle"),t.textVerticalAlign=null==i||In[i]?i:"top",t.textPadding&&(t.textPadding=U(t.textPadding))}}function Pn(t,e,i,n,r,o){n.rich?function(t,e,i,n,r,o){o!==Ri&&(e.__attrCachedBy=Ni.NONE);var a=t.__textCotentBlock;a&&!t.__dirtyText||(a=t.__textCotentBlock=_n(i,n));!function(t,e,i,n,r){var o=i.width,a=i.outerWidth,s=i.outerHeight,l=n.textPadding,u=Rn(An,t,n,r),h=u.baseX,c=u.baseY,d=u.textAlign,f=u.textVerticalAlign;Ln(e,n,r,h,c);var p=hn(h,a,d),g=cn(c,s,f),m=p,v=g;l&&(m+=l[3],v+=l[0]);var y=m+o;zn(n)&&En(t,e,n,p,g,a,s);for(var _=0;_<i.lines.length;_++){for(var x,w=i.lines[_],b=w.tokens,S=b.length,M=w.lineHeight,I=w.width,C=0,T=m,A=y,D=S-1;C<S&&(!(x=b[C]).textAlign||"left"===x.textAlign);)On(t,e,x,n,M,v,T,"left"),I-=x.width,T+=x.width,C++;for(;0<=D&&"right"===(x=b[D]).textAlign;)On(t,e,x,n,M,v,A,"right"),I-=x.width,A-=x.width,D--;for(T+=(o-(T-m)-(y-A)-I)/2;C<=D;)x=b[C],On(t,e,x,n,M,v,T+x.width/2,"center"),T+=x.width,C++;v+=M}}(t,e,a,n,r)}(t,e,i,n,r,o):function(t,e,i,n,r,o){var a,s=zn(n),l=!1,u=e.__attrCachedBy===Ni.PLAIN_TEXT;o!==Ri?(o&&(a=o.style,l=!s&&u&&a),e.__attrCachedBy=s?Ni.NONE:Ni.PLAIN_TEXT):u&&(e.__attrCachedBy=Ni.NONE);var h=n.font||Sn;l&&h===(a.font||Sn)||(e.font=h);var c=t.__computedFont;t.__styleFont!==h&&(t.__styleFont=h,c=t.__computedFont=e.font);var d=n.textPadding,f=n.textLineHeight,p=t.__textCotentBlock;p&&!t.__dirtyText||(p=t.__textCotentBlock=yn(i,c,d,f,n.truncate));var g=p.outerHeight,m=p.lines,v=p.lineHeight,y=Rn(An,t,n,r),_=y.baseX,x=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;Ln(e,n,r,_,x);var S=cn(x,g,b),M=_,I=S;if(s||d){var C=ln(i,c);d&&(C+=d[1]+d[3]);var T=hn(_,C,w);s&&En(t,e,n,T,S,C,g),d&&(M=Wn(_,w,d),I+=d[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=n.opacity||1;for(var A=0;A<Cn.length;A++){var D=Cn[A],k=D[0],P=D[1],L=n[k];l&&L===a[k]||(e[P]=Ei(e,P,L||D[2]))}I+=v/2;var O=n.textStrokeWidth,z=l?a.textStrokeWidth:null,E=!l||O!==z,N=!l||E||n.textStroke!==a.textStroke,R=Vn(n.textStroke,O),B=Fn(n.textFill);R&&(E&&(e.lineWidth=O),N&&(e.strokeStyle=R));B&&(l&&n.textFill===a.textFill||(e.fillStyle=B));if(1===m.length)R&&e.strokeText(m[0],M,I),B&&e.fillText(m[0],M,I);else for(A=0;A<m.length;A++)R&&e.strokeText(m[A],M,I),B&&e.fillText(m[A],M,I),I+=v}(t,e,i,n,r,o)}function Ln(t,e,i,n,r){if(i&&e.textRotation){var o=e.textOrigin;"center"===o?(n=i.width/2+i.x,r=i.height/2+i.y):o&&(n=o[0]+i.x,r=o[1]+i.y),t.translate(n,r),t.rotate(-e.textRotation),t.translate(-n,-r)}}function On(t,e,i,n,r,o,a,s){var l=n.rich[i.styleName]||{};l.text=i.text;var u=i.textVerticalAlign,h=o+r/2;"top"===u?h=o+i.height/2:"bottom"===u&&(h=o+r-i.height/2),!i.isLineHolder&&zn(l)&&En(t,e,l,"right"===s?a-i.width:"center"===s?a-i.width/2:a,h-i.height/2,i.width,i.height);var c=i.textPadding;c&&(a=Wn(a,s,c),h-=i.height/2-c[2]-i.textHeight/2),Bn(e,"shadowBlur",G(l.textShadowBlur,n.textShadowBlur,0)),Bn(e,"shadowColor",l.textShadowColor||n.textShadowColor||"transparent"),Bn(e,"shadowOffsetX",G(l.textShadowOffsetX,n.textShadowOffsetX,0)),Bn(e,"shadowOffsetY",G(l.textShadowOffsetY,n.textShadowOffsetY,0)),Bn(e,"textAlign",s),Bn(e,"textBaseline","middle"),Bn(e,"font",i.font||Sn);var d=Vn(l.textStroke||n.textStroke,p),f=Fn(l.textFill||n.textFill),p=W(l.textStrokeWidth,n.textStrokeWidth);d&&(Bn(e,"lineWidth",p),Bn(e,"strokeStyle",d),e.strokeText(i.text,a,h)),f&&(Bn(e,"fillStyle",f),e.fillText(i.text,a,h))}function zn(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function En(t,e,i,n,r,o,a){var s=i.textBackgroundColor,l=i.textBorderWidth,u=i.textBorderColor,h=E(s);if(Bn(e,"shadowBlur",i.textBoxShadowBlur||0),Bn(e,"shadowColor",i.textBoxShadowColor||"transparent"),Bn(e,"shadowOffsetX",i.textBoxShadowOffsetX||0),Bn(e,"shadowOffsetY",i.textBoxShadowOffsetY||0),h||l&&u){e.beginPath();var c=i.textBorderRadius;c?bn(e,{x:n,y:r,width:o,height:a,r:c}):e.rect(n,r,o,a),e.closePath()}if(h)if(Bn(e,"fillStyle",s),null!=i.fillOpacity){var d=e.globalAlpha;e.globalAlpha=i.fillOpacity*i.opacity,e.fill(),e.globalAlpha=d}else e.fill();else if(N(s)){var f=s.image;(f=Qi(f,null,t,Nn,s))&&tn(f)&&e.drawImage(f,n,r,o,a)}if(l&&u)if(Bn(e,"lineWidth",l),Bn(e,"strokeStyle",u),null!=i.strokeOpacity){d=e.globalAlpha;e.globalAlpha=i.strokeOpacity*i.opacity,e.stroke(),e.globalAlpha=d}else e.stroke()}function Nn(t,e){e.image=t}function Rn(t,e,i,n){var r=i.x||0,o=i.y||0,a=i.textAlign,s=i.textVerticalAlign;if(n){var l=i.textPosition;if(l instanceof Array)r=n.x+Hn(l[0],n.width),o=n.y+Hn(l[1],n.height);else{var u=e&&e.calculateTextPosition?e.calculateTextPosition(Tn,i,n):dn(Tn,i,n);r=u.x,o=u.y,a=a||u.textAlign,s=s||u.textVerticalAlign}var h=i.textOffset;h&&(r+=h[0],o+=h[1])}return(t=t||{}).baseX=r,t.baseY=o,t.textAlign=a,t.textVerticalAlign=s,t}function Bn(t,e,i){return t[e]=Ei(t,e,i),t[e]}function Vn(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Fn(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Hn(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function Wn(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}function Gn(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Zn(){}var Un=new bi;function Xn(t){for(var e in t=t||{},pi.call(this,t),t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new Vi(t.style,this),this._rect=null,this.__clipPaths=null}function Yn(t){Xn.call(this,t)}Xn.prototype={constructor:Xn,type:"displayable",__dirty:!0,invisible:!(Zn.prototype={constructor:Zn,drawRectText:function(t,e){var i=this.style;e=i.textRect||e,this.__dirty&&Dn(i);var n=i.text;if(null!=n&&(n+=""),Gn(n,i)){t.save();var r=this.transform;i.transformText?this.setTransform(t):r&&(Un.copy(e),Un.applyTransform(r),e=Un),Pn(this,t,n,i,e,Ri),t.restore()}}}),z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(i[0],i[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?pi.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new Vi(t,this),this.dirty(!1),this},calculateTextPosition:null},w(Xn,pi),S(Xn,Zn),Yn.prototype={constructor:Yn,type:"image",brush:function(t,e){var i=this.style,n=i.image;i.bind(t,this,e);var r=this._image=Qi(n,this._image,this,this.onload);if(r&&tn(r)){var o=i.x||0,a=i.y||0,s=i.width,l=i.height,u=r.width/r.height;if(null==s&&null!=l?s=l*u:null==l&&null!=s?l=s/u:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),i.sWidth&&i.sHeight){var h=i.sx||0,c=i.sy||0;t.drawImage(r,h,c,i.sWidth,i.sHeight,o,a,s,l)}else if(i.sx&&i.sy){var d=s-(h=i.sx),f=l-(c=i.sy);t.drawImage(r,h,c,d,f,o,a,s,l)}else t.drawImage(r,o,a,s,l);null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new bi(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},w(Yn,Xn);var jn=314159;function qn(t){return parseInt(t,10)}var $n=new bi(0,0,0,0),Kn=new bi(0,0,0,0);function Qn(t,e,i){this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=k({},i||{}),this.dpr=i.devicePixelRatio||li,this._singleCanvas=n;var r=(this.root=t).style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var o=this._zlevelList=[],a=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,n){var s=t.width,l=t.height;null!=i.width&&(s=i.width),null!=i.height&&(l=i.height),this.dpr=i.devicePixelRatio||1,t.width=s*this.dpr,t.height=l*this.dpr,this._width=s,this._height=l;var u=new ji(t,this,this.dpr);u.__builtin__=!0,u.initContext(),(a[jn]=u).zlevel=jn,o.push(jn),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var h=this._domRoot=function(t,e){var i=document.createElement("div");return i.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}(this._width,this._height);t.appendChild(h)}this._hoverlayer=null,this._hoverElements=[]}Qn.prototype={constructor:Qn,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var n=0;n<i.length;n++){var r=i[n],o=this._layers[r];if(!o.__builtin__&&o.refresh){var a=0===n?this._backgroundColor:null;o.refresh(a)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return(i.__from=t).__hoverMir=i,e&&i.setStyle(e),this._hoverElements.push(i),i}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=x(i,e);0<=n&&i.splice(n,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,i=0;i<e.length;i++){var n=e[i].__from;n&&(n.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,i=this._hoverlayer;if(i&&i.clear(),e){Pi(t,this.storage.displayableSortFunc);var n={};(i=i||(this._hoverlayer=this.getLayer(1e5))).ctx.save();for(var r=0;r<e;){var o=t[r],a=o.__from;a&&a.__zr?(r++,a.invisible||(o.transform=a.transform,o.invTransform=a.invTransform,o.__clipPaths=a.__clipPaths,this._doPaintEl(o,i,!0,n))):(t.splice(r,1),a.__hoverMir=null,e--)}i.ctx.restore()}},getHoverLayer:function(){return this.getLayer(1e5)},_paintList:function(t,e,i){if(this._redrawId===i){e=e||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!n){var r=this;qi(function(){r._paintList(t,e,i)})}}},_compositeManually:function(){var e=this.getLayer(jn).ctx,i=this._domRoot.width,n=this._domRoot.height;e.clearRect(0,0,i,n),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,i,n)})},_doPaintList:function(t,e){for(var i=[],n=0;n<this._zlevelList.length;n++){var r=this._zlevelList[n];(s=this._layers[r]).__builtin__&&s!==this._hoverlayer&&(s.__dirty||e)&&i.push(s)}for(var o=!0,a=0;a<i.length;a++){var s,l=(s=i[a]).ctx,u={};l.save();var h=e?s.__startIndex:s.__drawIndex,c=!e&&s.incremental&&Date.now,d=c&&Date.now(),f=s.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(s.__startIndex===s.__endIndex)s.clear(!1,f);else if(h===s.__startIndex){var p=t[h];p.incremental&&p.notClear&&!e||s.clear(!1,f)}-1===h&&(console.error("For some unknown reason. drawIndex is -1"),h=s.__startIndex);for(var g=h;g<s.__endIndex;g++){var m=t[g];if(this._doPaintEl(m,s,e,u),m.__dirty=m.__dirtyText=!1,c)if(15<Date.now()-d)break}s.__drawIndex=g,s.__drawIndex<s.__endIndex&&(o=!1),u.prevElClipPaths&&l.restore(),l.restore()}return v.wxa&&D(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,i,n){var r=e.ctx,o=t.transform;if((e.__dirty||i)&&!t.invisible&&0!==t.style.opacity&&(!o||o[0]||o[3])&&(!t.culling||!function(t,e,i){return $n.copy(t.getBoundingRect()),t.transform&&$n.applyTransform(t.transform),Kn.width=e,Kn.height=i,!$n.intersect(Kn)}(t,this._width,this._height))){var a=t.__clipPaths,s=n.prevElClipPaths;s&&!function(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var i=0;i<t.length;i++)if(t[i]!==e[i])return!0;return!1}(a,s)||(s&&(r.restore(),n.prevElClipPaths=null,n.prevEl=null),a&&(r.save(),function(t,e){for(var i=0;i<t.length;i++){var n=t[i];n.setTransform(e),e.beginPath(),n.buildPath(e,n.shape),e.clip(),n.restoreTransform(e)}}(a,r),n.prevElClipPaths=a)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,n.prevEl||null),(n.prevEl=t).afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=jn);var i=this._layers[t];return i||((i=new ji("zr_"+t,this,this.dpr)).zlevel=t,i.__builtin__=!0,this._layerConfig[t]?m(i,this._layerConfig[t],!0):this._layerConfig[t-.01]&&m(i,this._layerConfig[t-.01],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},insertLayer:function(t,e){var i=this._layers,n=this._zlevelList,r=n.length,o=null,a=-1,s=this._domRoot;if(i[t])ci("ZLevel "+t+" has been used already");else if(function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(0<r&&t>n[0]){for(a=0;a<r-1&&!(n[a]<t&&n[a+1]>t);a++);o=i[n[a]]}if(n.splice(a+1,0,t),!(i[t]=e).virtual)if(o){var l=o.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)}else ci("Layer of zlevel "+t+" is not valid")},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r.length;n++)i=r[n],t.call(e,this._layers[i],i)},eachBuiltinLayer:function(t,e){var i,n,r,o=this._zlevelList;for(r=0;r<o.length;r++)n=o[r],(i=this._layers[n]).__builtin__&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,o=this._zlevelList;for(r=0;r<o.length;r++)n=o[r],(i=this._layers[n]).__builtin__||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var i=1;i<t.length;i++){if((a=t[i]).zlevel!==t[i-1].zlevel||a.incremental){this._needsManuallyCompositing=!0;break}}var n,r=null,o=0;for(i=0;i<t.length;i++){var a,s,l=(a=t[i]).zlevel;n!==l&&(n=l,o=0),a.incremental?((s=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):s=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),s.__builtin__||ci("ZLevel "+l+" has been used by unkown layer "+s.id),s!==r&&(s.__used=!0,s.__startIndex!==i&&(s.__dirty=!0),s.__startIndex=i,s.incremental?s.__drawIndex=-1:s.__drawIndex=i,e(i),r=s),a.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=i))}e(i),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?m(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var r=this._zlevelList[n];if(r===t||r===t+.01)m(this._layers[r],i[t],!0)}}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(x(i,t),1))},resize:function(e,i){if(this._domRoot.style){var t=this._domRoot;t.style.display="none";var n=this._opts;if(null!=e&&(n.width=e),null!=i&&(n.height=i),e=this._getSize(0),i=this._getSize(1),t.style.display="",this._width!==e||i!==this._height){for(var r in t.style.width=e+"px",t.style.height=i+"px",this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(e,i);D(this._progressiveLayers,function(t){t.resize(e,i)}),this.refresh(!0)}this._width=e,this._height=i}else{if(null==e||null==i)return;this._width=e,this._height=i,this.getLayer(jn).resize(e,i)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[jn].dom;var e=new ji("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var o={},a=this.storage.getDisplayList(!0),s=0;s<a.length;s++){var l=a[s];this._doPaintEl(l,e,!0,o)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],n=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[n]||qn(s[i])||qn(a.style[i]))-(qn(s[r])||0)-(qn(s[o])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),n=i.getContext("2d"),r=t.getBoundingRect(),o=t.style,a=o.shadowBlur*e,s=o.shadowOffsetX*e,l=o.shadowOffsetY*e,u=o.hasStroke()?o.lineWidth:0,h=Math.max(u/2,a-s),c=Math.max(u/2,s+a),d=Math.max(u/2,a-l),f=Math.max(u/2,l+a),p=r.width+h+c,g=r.height+d+f;i.width=p*e,i.height=g*e,n.scale(e,e),n.clearRect(0,0,p,g),n.dpr=e;var m={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[h-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(n);var v=new Yn({style:{x:0,y:0,image:i}});return null!=m.position&&(v.position=t.position=m.position),null!=m.rotation&&(v.rotation=t.rotation=m.rotation),null!=m.scale&&(v.scale=t.scale=m.scale),v}};function Jn(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,It.call(this)}Jn.prototype={constructor:Jn,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),i=0;i<e.length;i++)this.addClip(e[i])},removeClip:function(t){var e=x(this._clips,t);0<=e&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e.length;i++)this.removeClip(e[i]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i.length,r=[],o=[],a=0;a<n;a++){var s=i[a],l=s.step(t,e);l&&(r.push(l),o.push(s))}for(a=0;a<n;)i[a]._needsRemove?(i[a]=i[n-1],i.pop(),n--):a++;n=r.length;for(a=0;a<n;a++)o[a].fire(r[a]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){var e=this;this._running=!0,qi(function t(){e._running&&(qi(t),e._paused||e._update())})},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){var i=new ai(t,(e=e||{}).loop,e.getter,e.setter);return this.addAnimator(i),i}},S(Jn,It);var tr,er,ir=v.domSupported,nr=(er={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:tr=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:P(tr,function(t){var e=t.replace("mouse","pointer");return er.hasOwnProperty(e)?e:t})}),rr={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]};function or(t){return"mousewheel"===t&&v.browser.firefox?"DOMMouseScroll":t}function ar(t){var e=t.pointerType;return"pen"===e||"touch"===e}function sr(t){t&&(t.zrByTouch=!0)}function lr(t,e){for(var i=e,n=!1;i&&9!==i.nodeType&&!(n=i.domBelongToZr||i!==e&&i===t.painterRoot);)i=i.parentNode;return n}function ur(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}var hr=ur.prototype;hr.stopPropagation=hr.stopImmediatePropagation=hr.preventDefault=J;var cr={mousedown:function(t){t=Ft(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Ft(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||vr(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=Ft(this.dom,t),vr(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=Ft(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=lr(this,e),this.trigger("mouseout",t)},touchstart:function(t){sr(t=Ft(this.dom,t)),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),cr.mousemove.call(this,t),cr.mousedown.call(this,t)},touchmove:function(t){sr(t=Ft(this.dom,t)),this.handler.processGesture(t,"change"),cr.mousemove.call(this,t)},touchend:function(t){sr(t=Ft(this.dom,t)),this.handler.processGesture(t,"end"),cr.mouseup.call(this,t),+new Date-this._lastTouchMoment<300&&cr.click.call(this,t)},pointerdown:function(t){cr.mousedown.call(this,t)},pointermove:function(t){ar(t)||cr.mousemove.call(this,t)},pointerup:function(t){cr.mouseup.call(this,t)},pointerout:function(t){ar(t)||cr.mouseout.call(this,t)}};D(["click","mousewheel","dblclick","contextmenu"],function(e){cr[e]=function(t){t=Ft(this.dom,t),this.trigger(e,t)}});var dr={pointermove:function(t){ar(t)||dr.mousemove.call(this,t)},pointerup:function(t){dr.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;vr(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function fr(i,n){var r=n.domHandlers;v.pointerEventsSupported?D(nr.pointer,function(e){gr(n,e,function(t){r[e].call(i,t)})}):(v.touchEventsSupported&&D(nr.touch,function(e){gr(n,e,function(t){r[e].call(i,t),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout(function(){t.touching=!1,t.touchTimer=null},700)}(n)})}),D(nr.mouse,function(e){gr(n,e,function(t){t=Vt(t),n.touching||r[e].call(i,t)})}))}function pr(i,n){function t(e){gr(n,e,function(t){t=Vt(t),lr(i,t.target)||(t=function(t,e){return Ft(t.dom,new ur(t,e),!0)}(i,t),n.domHandlers[e].call(i,t))},{capture:!0})}v.pointerEventsSupported?D(rr.pointer,t):v.touchEventsSupported||D(rr.mouse,t)}function gr(t,e,i,n){t.mounted[e]=i,t.listenerOpts[e]=n,Ht(t.domTarget,or(e),i,n)}function mr(t){var e,i,n,r,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,i=or(a),n=o[a],r=t.listenerOpts[a],zt?e.removeEventListener(i,n,r):e.detachEvent("on"+i,n));t.mounted={}}function vr(t,e){if(t._mayPointerCapture=null,ir&&t._pointerCapturing^e){t._pointerCapturing=e;var i=t._globalHandlerScope;e?pr(t,i):mr(i)}}function yr(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function _r(t,e){It.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new yr(t,cr),ir&&(this._globalHandlerScope=new yr(document,dr)),this._pointerCapturing=!1,this._mayPointerCapture=null,fr(this,this._localHandlerScope)}var xr=_r.prototype;xr.dispose=function(){mr(this._localHandlerScope),ir&&mr(this._globalHandlerScope)},xr.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},S(_r,It);var wr=!v.canvasSupported,br={canvas:Qn},Sr={};function Mr(t,e){var i=new Cr(n(),t,e);return Sr[i.id]=i}function Ir(t,e){br[t]=e}var Cr=function(t,e,i){i=i||{},this.dom=e,this.id=t;var n=this,r=new Oi,o=i.renderer;if(wr){if(!br.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");o="vml"}else o&&br[o]||(o="canvas");var a=new br[o](e,r,i,t);this.storage=r,this.painter=a;var s=v.node||v.worker?null:new _r(a.getViewportRoot(),a.root);this.handler=new $t(r,a,s,a.root),this.animation=new Jn({stage:{update:C(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,u=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(n)},r.addToStorage=function(t){u.call(r,t),t.addSelfToZr(n)}};Cr.prototype={constructor:Cr,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var i=this.painter.addHover(t,e);return this.refreshHover(),i}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,function(t){delete Sr[t]}(this.id)}};var Tr=(Object.freeze||Object)({version:"4.3.2",init:Mr,dispose:function(t){if(t)t.dispose();else{for(var e in Sr)Sr.hasOwnProperty(e)&&Sr[e].dispose();Sr={}}return this},getInstance:function(t){return Sr[t]},registerPainter:Ir}),Ar=D,Dr=N,kr=O,Pr="series\0";function Lr(t){return t instanceof Array?t:null==t?[]:[t]}function Or(t,e,i){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var n=0,r=i.length;n<r;n++){var o=i[n];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var zr=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function Er(t){return!Dr(t)||kr(t)||t instanceof Date?t:t.value}function Nr(t,r){r=(r||[]).slice();var o=P(t||[],function(t,e){return{exist:t}});return Ar(r,function(t,e){if(Dr(t)){for(var i=0;i<o.length;i++)if(!o[i].option&&null!=t.id&&o[i].exist.id===t.id+"")return o[i].option=t,void(r[e]=null);for(i=0;i<o.length;i++){var n=o[i].exist;if(!(o[i].option||null!=n.id&&null!=t.id||null==t.name||Vr(t)||Vr(n)||n.name!==t.name+""))return o[i].option=t,void(r[e]=null)}}}),Ar(r,function(t,e){if(Dr(t)){for(var i=0;i<o.length;i++){var n=o[i].exist;if(!o[i].option&&!Vr(n)&&null==t.id){o[i].option=t;break}}i>=o.length&&o.push({option:t})}}),o}function Rr(t){var a=Q();Ar(t,function(t,e){var i=t.exist;i&&a.set(i.id,t)}),Ar(t,function(t,e){var i=t.option;X(!i||null==i.id||!a.get(i.id)||a.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&a.set(i.id,t),t.keyInfo||(t.keyInfo={})}),Ar(t,function(t,e){var i=t.exist,n=t.option,r=t.keyInfo;if(Dr(n)){if(r.name=null!=n.name?n.name+"":i?i.name:Pr+e,i)r.id=i.id;else if(null!=n.id)r.id=n.id+"";else for(var o=0;r.id="\0"+r.name+"\0"+o++,a.get(r.id););a.set(r.id,t)}})}function Br(t){var e=t.name;return!(!e||!e.indexOf(Pr))}function Vr(t){return Dr(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")}function Fr(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?O(t.dataIndex)?P(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?O(t.name)?P(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Hr(){var e="__\0ec_inner_"+Wr+++"_"+Math.random().toFixed(5);return function(t){return t[e]||(t[e]={})}}var Wr=0;function Gr(s,l,u){if(E(l)){var t={};t[l+"Index"]=0,l=t}var e=u&&u.defaultMainType;!e||Zr(l,e+"Index")||Zr(l,e+"Id")||Zr(l,e+"Name")||(l[e+"Index"]=0);var h={};return Ar(l,function(t,e){t=l[e];if("dataIndex"!==e&&"dataIndexInside"!==e){var i=e.match(/^(\w+)(Index|Id|Name)$/)||[],n=i[1],r=(i[2]||"").toLowerCase();if(!(!n||!r||null==t||"index"===r&&"none"===t||u&&u.includeMainTypes&&x(u.includeMainTypes,n)<0)){var o={mainType:n};"index"===r&&"all"===t||(o[r]=t);var a=s.queryComponents(o);h[n+"Models"]=a,h[n+"Model"]=a[0]}}else h[e]=t}),h}function Zr(t,e){return t&&t.hasOwnProperty(e)}function Ur(t,e,i){t.setAttribute?t.setAttribute(e,i):t[e]=i}function Xr(t){return"auto"===t?v.domSupported?"html":"richText":t||"html"}var Yr=".",jr="___EC__COMPONENT__CONTAINER___";function qr(t){var e={main:"",sub:""};return t&&(t=t.split(Yr),e.main=t[0]||"",e.sub=t[1]||""),e}function $r(t){(t.$constructor=t).extend=function(t){function e(){t.$constructor?t.$constructor.apply(this,arguments):i.apply(this,arguments)}var i=this;return k(e.prototype,t),e.extend=this.extend,e.superCall=Jr,e.superApply=to,w(e,this),e.superClass=i,e}}var Kr=0;function Qr(t){var e=["__\0is_clz",Kr++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function Jr(t,e){var i=Z(arguments,2);return this.superClass.prototype[e].apply(t,i)}function to(t,e,i){return this.superClass.prototype[e].apply(t,i)}function eo(i,t){t=t||{};var r={};if(i.registerClass=function(t,e){if(e)if(function(t){X(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}(e),(e=qr(e)).sub){if(e.sub!==jr){(function(t){var e=r[t.main];e&&e[jr]||((e=r[t.main]={})[jr]=!0);return e})(e)[e.sub]=t}}else r[e.main]=t;return t},i.getClass=function(t,e,i){var n=r[t];if(n&&n[jr]&&(n=e?n[e]:null),i&&!n)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return n},i.getClassesByMainType=function(t){t=qr(t);var i=[],e=r[t.main];return e&&e[jr]?D(e,function(t,e){e!==jr&&i.push(t)}):i.push(e),i},i.hasClass=function(t){return t=qr(t),!!r[t.main]},i.getAllClassMainTypes=function(){var i=[];return D(r,function(t,e){i.push(e)}),i},i.hasSubTypes=function(t){t=qr(t);var e=r[t.main];return e&&e[jr]},i.parseClassType=qr,t.registerWhenExtend){var n=i.extend;n&&(i.extend=function(t){var e=n.call(this,t);return i.registerClass(e,t.type)})}return i}function io(s){for(var t=0;t<s.length;t++)s[t][1]||(s[t][1]=s[t][0]);return function(t,e,i){for(var n={},r=0;r<s.length;r++){var o=s[r][1];if(!(e&&0<=x(e,o)||i&&x(i,o)<0)){var a=t.getShallow(o);null!=a&&(n[s[r][0]]=a)}}return n}}var no=io([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),ro={getLineStyle:function(t){var e=no(this,t);return e.lineDash=this.getLineDash(e.lineWidth),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),i=Math.max(t,2),n=4*t;return"solid"!==e&&null!=e&&("dashed"===e?[n,n]:[i,i])}},oo=io([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),ao={getAreaStyle:function(t,e){return oo(this,t,e)}},so=Math.pow,lo=Math.sqrt,uo=1e-8,ho=1e-4,co=lo(3),fo=1/3,po=it(),go=it(),mo=it();function vo(t){return-uo<t&&t<uo}function yo(t){return uo<t||t<-uo}function _o(t,e,i,n,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*n+3*o*i)}function xo(t,e,i,n,r){var o=1-r;return 3*(((e-t)*o+2*(i-e)*r)*o+(n-i)*r*r)}function wo(t,e,i,n,r){var o=6*i-12*e+6*t,a=9*e+3*n-3*t-9*i,s=3*e-3*t,l=0;if(vo(a)){if(yo(o))0<=(h=-s/o)&&h<=1&&(r[l++]=h)}else{var u=o*o-4*a*s;if(vo(u))r[0]=-o/(2*a);else if(0<u){var h,c=lo(u),d=(-o-c)/(2*a);0<=(h=(-o+c)/(2*a))&&h<=1&&(r[l++]=h),0<=d&&d<=1&&(r[l++]=d)}}return l}function bo(t,e,i,n,r,o){var a=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=n}function So(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function Mo(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function Io(t,e,i){var n=t+i-2*e;return 0==n?.5:(t-e)/n}function Co(t,e,i,n,r){var o=(e-t)*n+t,a=(i-e)*n+e,s=(a-o)*n+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=i}var To=Math.min,Ao=Math.max,Do=Math.sin,ko=Math.cos,Po=2*Math.PI,Lo=it(),Oo=it(),zo=it();function Eo(t,e,i){if(0!==t.length){var n,r=t[0],o=r[0],a=r[0],s=r[1],l=r[1];for(n=1;n<t.length;n++)r=t[n],o=To(o,r[0]),a=Ao(a,r[0]),s=To(s,r[1]),l=Ao(l,r[1]);e[0]=o,e[1]=s,i[0]=a,i[1]=l}}function No(t,e,i,n,r,o){r[0]=To(t,i),r[1]=To(e,n),o[0]=Ao(t,i),o[1]=Ao(e,n)}var Ro=[],Bo=[];function Vo(t,e,i,n,r,o,a,s,l,u){var h,c=wo,d=_o,f=c(t,i,r,a,Ro);for(l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0,h=0;h<f;h++){var p=d(t,i,r,a,Ro[h]);l[0]=To(p,l[0]),u[0]=Ao(p,u[0])}for(f=c(e,n,o,s,Bo),h=0;h<f;h++){var g=d(e,n,o,s,Bo[h]);l[1]=To(g,l[1]),u[1]=Ao(g,u[1])}l[0]=To(t,l[0]),u[0]=Ao(t,u[0]),l[0]=To(a,l[0]),u[0]=Ao(a,u[0]),l[1]=To(e,l[1]),u[1]=Ao(e,u[1]),l[1]=To(s,l[1]),u[1]=Ao(s,u[1])}function Fo(t,e,i,n,r,o,a,s,l){var u=_t,h=xt,c=Math.abs(r-o);if(c%Po<1e-4&&1e-4<c)return s[0]=t-i,s[1]=e-n,l[0]=t+i,void(l[1]=e+n);if(Lo[0]=ko(r)*i+t,Lo[1]=Do(r)*n+e,Oo[0]=ko(o)*i+t,Oo[1]=Do(o)*n+e,u(s,Lo,Oo),h(l,Lo,Oo),(r%=Po)<0&&(r+=Po),(o%=Po)<0&&(o+=Po),o<r&&!a?o+=Po:r<o&&a&&(r+=Po),a){var d=o;o=r,r=d}for(var f=0;f<o;f+=Math.PI/2)r<f&&(zo[0]=ko(f)*i+t,zo[1]=Do(f)*n+e,u(s,zo,s),h(l,zo,l))}var Ho={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Wo=[],Go=[],Zo=[],Uo=[],Xo=Math.min,Yo=Math.max,jo=Math.cos,qo=Math.sin,$o=Math.sqrt,Ko=Math.abs,Qo="undefined"!=typeof Float32Array,Jo=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};function ta(t,e,i,n,r,o,a){if(0===r)return!1;var s=r,l=0;if(e+s<a&&n+s<a||a<e-s&&a<n-s||t+s<o&&i+s<o||o<t-s&&o<i-s)return!1;if(t===i)return Math.abs(o-t)<=s/2;var u=(l=(e-n)/(t-i))*o-a+(t*n-i*e)/(t-i);return u*u/(l*l+1)<=s/2*s/2}function ea(t,e,i,n,r,o,a,s,l,u,h){if(0===l)return!1;var c=l;return!(e+c<h&&n+c<h&&o+c<h&&s+c<h||h<e-c&&h<n-c&&h<o-c&&h<s-c||t+c<u&&i+c<u&&r+c<u&&a+c<u||u<t-c&&u<i-c&&u<r-c&&u<a-c)&&function(t,e,i,n,r,o,a,s,l,u,h){var c,d,f,p,g,m=.005,v=1/0;po[0]=l,po[1]=u;for(var y=0;y<1;y+=.05)go[0]=_o(t,i,r,a,y),go[1]=_o(e,n,o,s,y),(p=vt(po,go))<v&&(c=y,v=p);v=1/0;for(var _=0;_<32&&!(m<ho);_++)d=c-m,f=c+m,go[0]=_o(t,i,r,a,d),go[1]=_o(e,n,o,s,d),p=vt(go,po),0<=d&&p<v?(c=d,v=p):(mo[0]=_o(t,i,r,a,f),mo[1]=_o(e,n,o,s,f),g=vt(mo,po),f<=1&&g<v?(c=f,v=g):m*=.5);return h&&(h[0]=_o(t,i,r,a,c),h[1]=_o(e,n,o,s,c)),lo(v)}(t,e,i,n,r,o,a,s,u,h,null)<=c/2}function ia(t,e,i,n,r,o,a,s,l){if(0===a)return!1;var u=a;return!(e+u<l&&n+u<l&&o+u<l||l<e-u&&l<n-u&&l<o-u||t+u<s&&i+u<s&&r+u<s||s<t-u&&s<i-u&&s<r-u)&&function(t,e,i,n,r,o,a,s,l){var u,h=.005,c=1/0;po[0]=a,po[1]=s;for(var d=0;d<1;d+=.05){go[0]=So(t,i,r,d),go[1]=So(e,n,o,d),(m=vt(po,go))<c&&(u=d,c=m)}c=1/0;for(var f=0;f<32&&!(h<ho);f++){var p=u-h,g=u+h;go[0]=So(t,i,r,p),go[1]=So(e,n,o,p);var m=vt(go,po);if(0<=p&&m<c)u=p,c=m;else{mo[0]=So(t,i,r,g),mo[1]=So(e,n,o,g);var v=vt(mo,po);g<=1&&v<c?(u=g,c=v):h*=.5}}return l&&(l[0]=So(t,i,r,u),l[1]=So(e,n,o,u)),lo(c)}(t,e,i,n,r,o,s,l,null)<=u/2}Jo.prototype={constructor:Jo,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,i){i=i||0,this._ux=Ko(i/li/t)||0,this._uy=Ko(i/li/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return(this._ctx=t)&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(Ho.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=Ko(t-this._xi)>this._ux||Ko(e-this._yi)>this._uy||this._len<5;return this.addData(Ho.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,o){return this.addData(Ho.C,t,e,i,n,r,o),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,o):this._ctx.bezierCurveTo(t,e,i,n,r,o)),this._xi=r,this._yi=o,this},quadraticCurveTo:function(t,e,i,n){return this.addData(Ho.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,o){return this.addData(Ho.A,t,e,i,i,n,r-n,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,o),this._xi=jo(r)*i+t,this._yi=qo(r)*i+e,this},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(Ho.R,t,e,i,n),this},closePath:function(){this.addData(Ho.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t;for(var e=this._dashIdx=0,i=0;i<t.length;i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Qo||(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,r=0;r<e;r++)i+=t[r].len();Qo&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[n++]=o[a];this._len=n},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var i=0;i<arguments.length;i++)e[this._len++]=arguments[i];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,n,r=this._dashSum,o=this._dashOffset,a=this._lineDash,s=this._ctx,l=this._xi,u=this._yi,h=t-l,c=e-u,d=$o(h*h+c*c),f=l,p=u,g=a.length;for(o<0&&(o=r+o),f-=(o%=r)*(h/=d),p-=o*(c/=d);0<h&&f<=t||h<0&&t<=f||0===h&&(0<c&&p<=e||c<0&&e<=p);)f+=h*(i=a[n=this._dashIdx]),p+=c*i,this._dashIdx=(n+1)%g,0<h&&f<l||h<0&&l<f||0<c&&p<u||c<0&&u<p||s[n%2?"moveTo":"lineTo"](0<=h?Xo(f,t):Yo(f,t),0<=c?Xo(p,e):Yo(p,e));h=f-t,c=p-e,this._dashOffset=-$o(h*h+c*c)},_dashedBezierTo:function(t,e,i,n,r,o){var a,s,l,u,h,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,m=this._yi,v=_o,y=0,_=this._dashIdx,x=f.length,w=0;for(d<0&&(d=c+d),d%=c,a=0;a<1;a+=.1)s=v(g,t,i,r,a+.1)-v(g,t,i,r,a),l=v(m,e,n,o,a+.1)-v(m,e,n,o,a),y+=$o(s*s+l*l);for(;_<x&&!(d<(w+=f[_]));_++);for(a=(w-d)/y;a<=1;)u=v(g,t,i,r,a),h=v(m,e,n,o,a),_%2?p.moveTo(u,h):p.lineTo(u,h),a+=f[_]/y,_=(_+1)%x;_%2!=0&&p.lineTo(r,o),s=r-u,l=o-h,this._dashOffset=-$o(s*s+l*l)},_dashedQuadraticTo:function(t,e,i,n){var r=i,o=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,o)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Qo&&(this.data=new Float32Array(t)))},getBoundingRect:function(){Wo[0]=Wo[1]=Zo[0]=Zo[1]=Number.MAX_VALUE,Go[0]=Go[1]=Uo[0]=Uo[1]=-Number.MAX_VALUE;for(var t,e,i,n,r,o,a,s,l,u,h,c,d,f,p=this.data,g=0,m=0,v=0,y=0,_=0;_<p.length;){var x=p[_++];switch(1===_&&(v=g=p[_],y=m=p[_+1]),x){case Ho.M:g=v=p[_++],m=y=p[_++],Zo[0]=v,Zo[1]=y,Uo[0]=v,Uo[1]=y;break;case Ho.L:No(g,m,p[_],p[_+1],Zo,Uo),g=p[_++],m=p[_++];break;case Ho.C:Vo(g,m,p[_++],p[_++],p[_++],p[_++],p[_],p[_+1],Zo,Uo),g=p[_++],m=p[_++];break;case Ho.Q:t=g,e=m,i=p[_++],n=p[_++],r=p[_],o=p[_+1],a=Zo,s=Uo,u=l=void 0,u=So,h=Ao(To((l=Io)(t,i,r),1),0),c=Ao(To(l(e,n,o),1),0),d=u(t,i,r,h),f=u(e,n,o,c),a[0]=To(t,r,d),a[1]=To(e,o,f),s[0]=Ao(t,r,d),s[1]=Ao(e,o,f),g=p[_++],m=p[_++];break;case Ho.A:var w=p[_++],b=p[_++],S=p[_++],M=p[_++],I=p[_++],C=p[_++]+I;_+=1;var T=1-p[_++];1===_&&(v=jo(I)*S+w,y=qo(I)*M+b),Fo(w,b,S,M,I,C,T,Zo,Uo),g=jo(C)*S+w,m=qo(C)*M+b;break;case Ho.R:No(v=g=p[_++],y=m=p[_++],v+p[_++],y+p[_++],Zo,Uo);break;case Ho.Z:g=v,m=y}_t(Wo,Wo,Zo),xt(Go,Go,Uo)}return 0===_&&(Wo[0]=Wo[1]=Go[0]=Go[1]=0),new bi(Wo[0],Wo[1],Go[0]-Wo[0],Go[1]-Wo[1])},rebuildPath:function(t){for(var e,i,n,r,o,a,s=this.data,l=this._ux,u=this._uy,h=this._len,c=0;c<h;){var d=s[c++];switch(1===c&&(e=n=s[c],i=r=s[c+1]),d){case Ho.M:e=n=s[c++],i=r=s[c++],t.moveTo(n,r);break;case Ho.L:o=s[c++],a=s[c++],(Ko(o-n)>l||Ko(a-r)>u||c===h-1)&&(t.lineTo(o,a),n=o,r=a);break;case Ho.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case Ho.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case Ho.A:var f=s[c++],p=s[c++],g=s[c++],m=s[c++],v=s[c++],y=s[c++],_=s[c++],x=s[c++],w=m<g?g:m,b=m<g?1:g/m,S=m<g?m/g:1,M=v+y;.001<Math.abs(g-m)?(t.translate(f,p),t.rotate(_),t.scale(b,S),t.arc(0,0,w,v,M,1-x),t.scale(1/b,1/S),t.rotate(-_),t.translate(-f,-p)):t.arc(f,p,w,v,M,1-x),1===c&&(e=jo(v)*g+f,i=qo(v)*m+p),n=jo(M)*g+f,r=qo(M)*m+p;break;case Ho.R:e=n=s[c],i=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case Ho.Z:t.closePath(),n=e,r=i}}}},Jo.CMD=Ho;var na=2*Math.PI;function ra(t){return(t%=na)<0&&(t+=na),t}var oa=2*Math.PI;function aa(t,e,i,n,r,o,a,s,l){if(0===a)return!1;var u=a;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(i<h-u||h+u<i)return!1;if(Math.abs(n-r)%oa<1e-4)return!0;if(o){var c=n;n=ra(r),r=ra(c)}else n=ra(n),r=ra(r);r<n&&(r+=oa);var d=Math.atan2(l,s);return d<0&&(d+=oa),n<=d&&d<=r||n<=d+oa&&d+oa<=r}function sa(t,e,i,n,r,o){if(e<o&&n<o||o<e&&o<n)return 0;if(n===e)return 0;var a=n<e?1:-1,s=(o-e)/(n-e);1!=s&&0!=s||(a=n<e?.5:-.5);var l=s*(i-t)+t;return l===r?1/0:r<l?a:0}var la=Jo.CMD,ua=2*Math.PI,ha=1e-4;var ca=[-1,-1,-1],da=[-1,-1];function fa(t,e,i,n,r,o,a,s,l,u){if(e<u&&n<u&&o<u&&s<u||u<e&&u<n&&u<o&&u<s)return 0;var h,c=function(t,e,i,n,r,o){var a=n+3*(e-i)-t,s=3*(i-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*a*l,c=s*l-9*a*u,d=l*l-3*s*u,f=0;if(vo(h)&&vo(c)){if(vo(s))o[0]=0;else 0<=(M=-l/s)&&M<=1&&(o[f++]=M)}else{var p=c*c-4*h*d;if(vo(p)){var g=c/h,m=-g/2;0<=(M=-s/a+g)&&M<=1&&(o[f++]=M),0<=m&&m<=1&&(o[f++]=m)}else if(0<p){var v=lo(p),y=h*s+1.5*a*(-c+v),_=h*s+1.5*a*(-c-v);0<=(M=(-s-((y=y<0?-so(-y,fo):so(y,fo))+(_=_<0?-so(-_,fo):so(_,fo))))/(3*a))&&M<=1&&(o[f++]=M)}else{var x=(2*h*s-3*a*c)/(2*lo(h*h*h)),w=Math.acos(x)/3,b=lo(h),S=Math.cos(w),M=(-s-2*b*S)/(3*a),I=(m=(-s+b*(S+co*Math.sin(w)))/(3*a),(-s+b*(S-co*Math.sin(w)))/(3*a));0<=M&&M<=1&&(o[f++]=M),0<=m&&m<=1&&(o[f++]=m),0<=I&&I<=1&&(o[f++]=I)}}return f}(e,n,o,s,u,ca);if(0===c)return 0;for(var d,f,p=0,g=-1,m=0;m<c;m++){var v=ca[m],y=0===v||1===v?.5:1;_o(t,i,r,a,v)<l||(g<0&&(g=wo(e,n,o,s,da),da[1]<da[0]&&1<g&&(void 0,h=da[0],da[0]=da[1],da[1]=h),d=_o(e,n,o,s,da[0]),1<g&&(f=_o(e,n,o,s,da[1]))),2===g?v<da[0]?p+=d<e?y:-y:v<da[1]?p+=f<d?y:-y:p+=s<f?y:-y:v<da[0]?p+=d<e?y:-y:p+=s<d?y:-y)}return p}function pa(t,e,i,n,r,o,a,s){if(e<s&&n<s&&o<s||s<e&&s<n&&s<o)return 0;var l=function(t,e,i,n,r){var o=t-2*e+i,a=2*(e-t),s=t-n,l=0;if(vo(o)){if(yo(a))0<=(h=-s/a)&&h<=1&&(r[l++]=h)}else{var u=a*a-4*o*s;if(vo(u))0<=(h=-a/(2*o))&&h<=1&&(r[l++]=h);else if(0<u){var h,c=lo(u),d=(-a-c)/(2*o);0<=(h=(-a+c)/(2*o))&&h<=1&&(r[l++]=h),0<=d&&d<=1&&(r[l++]=d)}}return l}(e,n,o,s,ca);if(0===l)return 0;var u=Io(e,n,o);if(0<=u&&u<=1){for(var h=0,c=So(e,n,o,u),d=0;d<l;d++){var f=0===ca[d]||1===ca[d]?.5:1;So(t,i,r,ca[d])<a||(ca[d]<u?h+=c<e?f:-f:h+=o<c?f:-f)}return h}f=0===ca[0]||1===ca[0]?.5:1;return So(t,i,r,ca[0])<a?0:o<e?f:-f}function ga(t,e,i,n,r,o,a,s){if(i<(s-=e)||s<-i)return 0;var l=Math.sqrt(i*i-s*s);ca[0]=-l,ca[1]=l;var u=Math.abs(n-r);if(u<1e-4)return 0;if(u%ua<1e-4){r=ua;var h=o?1:-1;return a>=ca[n=0]+t&&a<=ca[1]+t?h:0}if(o){l=n;n=ra(r),r=ra(l)}else n=ra(n),r=ra(r);r<n&&(r+=ua);for(var c=0,d=0;d<2;d++){var f=ca[d];if(a<f+t){var p=Math.atan2(s,f);h=o?1:-1;p<0&&(p=ua+p),(n<=p&&p<=r||n<=p+ua&&p+ua<=r)&&(p>Math.PI/2&&p<1.5*Math.PI&&(h=-h),c+=h)}}return c}function ma(t,e,i,n,r){for(var o=0,a=0,s=0,l=0,u=0,h=0;h<t.length;){var c=t[h++];switch(c===la.M&&1<h&&(i||(o+=sa(a,s,l,u,n,r))),1===h&&(l=a=t[h],u=s=t[h+1]),c){case la.M:a=l=t[h++],s=u=t[h++];break;case la.L:if(i){if(ta(a,s,t[h],t[h+1],e,n,r))return!0}else o+=sa(a,s,t[h],t[h+1],n,r)||0;a=t[h++],s=t[h++];break;case la.C:if(i){if(ea(a,s,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],e,n,r))return!0}else o+=fa(a,s,t[h++],t[h++],t[h++],t[h++],t[h],t[h+1],n,r)||0;a=t[h++],s=t[h++];break;case la.Q:if(i){if(ia(a,s,t[h++],t[h++],t[h],t[h+1],e,n,r))return!0}else o+=pa(a,s,t[h++],t[h++],t[h],t[h+1],n,r)||0;a=t[h++],s=t[h++];break;case la.A:var d=t[h++],f=t[h++],p=t[h++],g=t[h++],m=t[h++],v=t[h++];h+=1;var y=1-t[h++],_=Math.cos(m)*p+d,x=Math.sin(m)*g+f;1<h?o+=sa(a,s,_,x,n,r):(l=_,u=x);var w=(n-d)*g/p+d;if(i){if(aa(d,f,g,m,m+v,y,e,w,r))return!0}else o+=ga(d,f,g,m,m+v,y,w,r);a=Math.cos(m+v)*p+d,s=Math.sin(m+v)*g+f;break;case la.R:l=a=t[h++],u=s=t[h++];_=l+t[h++],x=u+t[h++];if(i){if(ta(l,u,_,u,e,n,r)||ta(_,u,_,x,e,n,r)||ta(_,x,l,x,e,n,r)||ta(l,x,l,u,e,n,r))return!0}else o+=sa(_,u,_,x,n,r),o+=sa(l,x,l,u,n,r);break;case la.Z:if(i){if(ta(a,s,l,u,e,n,r))return!0}else o+=sa(a,s,l,u,n,r);a=l,s=u}}return i||function(t,e){return Math.abs(t-e)<ha}(s,u)||(o+=sa(a,s,l,u,n,r)||0),0!==o}var va=Ui.prototype.getCanvasPattern,ya=Math.abs,_a=new Jo(!0);function xa(t){Xn.call(this,t),this.path=null}xa.prototype={constructor:xa,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var i,n=this.style,r=this.path||_a,o=n.hasStroke(),a=n.hasFill(),s=n.fill,l=n.stroke,u=a&&!!s.colorStops,h=o&&!!l.colorStops,c=a&&!!s.image,d=o&&!!l.image;n.bind(t,this,e),this.setTransform(t),this.__dirty&&(u&&(i=i||this.getBoundingRect(),this._fillGradient=n.getGradient(t,s,i)),h&&(i=i||this.getBoundingRect(),this._strokeGradient=n.getGradient(t,l,i)));u?t.fillStyle=this._fillGradient:c&&(t.fillStyle=va.call(s,t)),h?t.strokeStyle=this._strokeGradient:d&&(t.strokeStyle=va.call(l,t));var f=n.lineDash,p=n.lineDashOffset,g=!!t.setLineDash,m=this.getGlobalScale();if(r.setScale(m[0],m[1],this.segmentIgnoreThreshold),this.__dirtyPath||f&&!g&&o?(r.beginPath(t),f&&!g&&(r.setLineDash(f),r.setLineDashOffset(p)),this.buildPath(r,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a)if(null!=n.fillOpacity){var v=t.globalAlpha;t.globalAlpha=n.fillOpacity*n.opacity,r.fill(t),t.globalAlpha=v}else r.fill(t);if(f&&g&&(t.setLineDash(f),t.lineDashOffset=p),o)if(null!=n.strokeOpacity){v=t.globalAlpha;t.globalAlpha=n.strokeOpacity*n.opacity,r.stroke(t),t.globalAlpha=v}else r.stroke(t);f&&g&&t.setLineDash([]),null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(t,e,i){},createPathProxy:function(){this.path=new Jo},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var n=this.path;n=n||(this.path=new Jo),this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||i){r.copy(t);var o=e.lineWidth,a=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),1e-10<a&&(r.width+=o/a,r.height+=o/a,r.x-=o/a/2,r.y-=o/a/2)}return r}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect(),r=this.style;if(t=i[0],e=i[1],n.contain(t,e)){var o=this.path.data;if(r.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(1e-10<s&&(r.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,i,n){return ma(t,e,!0,i,n)}(o,a/s,t,e)))return!0}if(r.hasFill())return function(t,e,i){return ma(t,0,!1,e,i)}(o,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Xn.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(N(t))for(var n in t)t.hasOwnProperty(n)&&(i[n]=t[n]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&1e-10<ya(t[0]-1)&&1e-10<ya(t[3]-1)?Math.sqrt(ya(t[0]*t[3]-t[2]*t[1])):1}},xa.extend=function(r){function t(t){xa.call(this,t),r.style&&this.style.extendFrom(r.style,!1);var e=r.shape;if(e){this.shape=this.shape||{};var i=this.shape;for(var n in e)!i.hasOwnProperty(n)&&e.hasOwnProperty(n)&&(i[n]=e[n])}r.init&&r.init.call(this,t)}for(var e in w(t,xa),r)"style"!==e&&"shape"!==e&&(t.prototype[e]=r[e]);return t},w(xa,Xn);function wa(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}var ba=Jo.CMD,Sa=[[],[],[]],Ma=Math.sqrt,Ia=Math.atan2,Ca=function(t,e){var i,n,r,o,a,s=t.data,l=ba.M,u=ba.C,h=ba.L,c=ba.R,d=ba.A,f=ba.Q;for(o=r=0;r<s.length;){switch(i=s[r++],o=r,n=0,i){case l:case h:n=1;break;case u:n=3;break;case f:n=2;break;case d:var p=e[4],g=e[5],m=Ma(e[0]*e[0]+e[1]*e[1]),v=Ma(e[2]*e[2]+e[3]*e[3]),y=Ia(-e[1]/v,e[0]/m);s[r]*=m,s[r++]+=p,s[r]*=v,s[r++]+=g,s[r++]*=m,s[r++]*=v,s[r++]+=y,s[r++]+=y,o=r+=2;break;case c:_[0]=s[r++],_[1]=s[r++],yt(_,_,e),s[o++]=_[0],s[o++]=_[1],_[0]+=s[r++],_[1]+=s[r++],yt(_,_,e),s[o++]=_[0],s[o++]=_[1]}for(a=0;a<n;a++){var _;(_=Sa[a])[0]=s[r++],_[1]=s[r++],yt(_,_,e),s[o++]=_[0],s[o++]=_[1]}}},Ta=Math.sqrt,Aa=Math.sin,Da=Math.cos,ka=Math.PI,Pa=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(wa(t)*wa(e))},La=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Pa(t,e))};function Oa(t,e,i,n,r,o,a,s,l,u,h){var c=l*(ka/180),d=Da(c)*(t-i)/2+Aa(c)*(e-n)/2,f=-1*Aa(c)*(t-i)/2+Da(c)*(e-n)/2,p=d*d/(a*a)+f*f/(s*s);1<p&&(a*=Ta(p),s*=Ta(p));var g=(r===o?-1:1)*Ta((a*a*(s*s)-a*a*(f*f)-s*s*(d*d))/(a*a*(f*f)+s*s*(d*d)))||0,m=g*a*f/s,v=g*-s*d/a,y=(t+i)/2+Da(c)*m-Aa(c)*v,_=(e+n)/2+Aa(c)*m+Da(c)*v,x=La([1,0],[(d-m)/a,(f-v)/s]),w=[(d-m)/a,(f-v)/s],b=[(-1*d-m)/a,(-1*f-v)/s],S=La(w,b);Pa(w,b)<=-1&&(S=ka),1<=Pa(w,b)&&(S=0),0===o&&0<S&&(S-=2*ka),1===o&&S<0&&(S+=2*ka),h.addData(u,y,_,a,s,x,S,c,o)}var za=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Ea=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Na(t,e){var i=function(t){if(!t)return new Jo;for(var e,i=0,n=0,r=i,o=n,a=new Jo,s=Jo.CMD,l=t.match(za),u=0;u<l.length;u++){for(var h,c=l[u],d=c.charAt(0),f=c.match(Ea)||[],p=f.length,g=0;g<p;g++)f[g]=parseFloat(f[g]);for(var m=0;m<p;){var v,y,_,x,w,b,S,M=i,I=n;switch(d){case"l":i+=f[m++],n+=f[m++],h=s.L,a.addData(h,i,n);break;case"L":i=f[m++],n=f[m++],h=s.L,a.addData(h,i,n);break;case"m":i+=f[m++],n+=f[m++],h=s.M,a.addData(h,i,n),r=i,o=n,d="l";break;case"M":i=f[m++],n=f[m++],h=s.M,a.addData(h,i,n),r=i,o=n,d="L";break;case"h":i+=f[m++],h=s.L,a.addData(h,i,n);break;case"H":i=f[m++],h=s.L,a.addData(h,i,n);break;case"v":n+=f[m++],h=s.L,a.addData(h,i,n);break;case"V":n=f[m++],h=s.L,a.addData(h,i,n);break;case"C":h=s.C,a.addData(h,f[m++],f[m++],f[m++],f[m++],f[m++],f[m++]),i=f[m-2],n=f[m-1];break;case"c":h=s.C,a.addData(h,f[m++]+i,f[m++]+n,f[m++]+i,f[m++]+n,f[m++]+i,f[m++]+n),i+=f[m-2],n+=f[m-1];break;case"S":v=i,y=n;var C=a.len(),T=a.data;e===s.C&&(v+=i-T[C-4],y+=n-T[C-3]),h=s.C,M=f[m++],I=f[m++],i=f[m++],n=f[m++],a.addData(h,v,y,M,I,i,n);break;case"s":v=i,y=n;C=a.len(),T=a.data;e===s.C&&(v+=i-T[C-4],y+=n-T[C-3]),h=s.C,M=i+f[m++],I=n+f[m++],i+=f[m++],n+=f[m++],a.addData(h,v,y,M,I,i,n);break;case"Q":M=f[m++],I=f[m++],i=f[m++],n=f[m++],h=s.Q,a.addData(h,M,I,i,n);break;case"q":M=f[m++]+i,I=f[m++]+n,i+=f[m++],n+=f[m++],h=s.Q,a.addData(h,M,I,i,n);break;case"T":v=i,y=n;C=a.len(),T=a.data;e===s.Q&&(v+=i-T[C-4],y+=n-T[C-3]),i=f[m++],n=f[m++],h=s.Q,a.addData(h,v,y,i,n);break;case"t":v=i,y=n;C=a.len(),T=a.data;e===s.Q&&(v+=i-T[C-4],y+=n-T[C-3]),i+=f[m++],n+=f[m++],h=s.Q,a.addData(h,v,y,i,n);break;case"A":_=f[m++],x=f[m++],w=f[m++],b=f[m++],S=f[m++],Oa(M=i,I=n,i=f[m++],n=f[m++],b,S,_,x,w,h=s.A,a);break;case"a":_=f[m++],x=f[m++],w=f[m++],b=f[m++],S=f[m++],Oa(M=i,I=n,i+=f[m++],n+=f[m++],b,S,_,x,w,h=s.A,a)}}"z"!==d&&"Z"!==d||(h=s.Z,a.addData(h),i=r,n=o),e=h}return a.toStatic(),a}(t);return(e=e||{}).buildPath=function(t){if(t.setData){t.setData(i.data),(e=t.getContext())&&t.rebuildPath(e)}else{var e=t;i.rebuildPath(e)}},e.applyTransform=function(t){Ca(i,t),this.dirty(!0)},e}function Ra(t,e){return new xa(Na(t,e))}var Ba=function(t){Xn.call(this,t)};Ba.prototype={constructor:Ba,type:"text",brush:function(t,e){var i=this.style;this.__dirty&&Dn(i),i.fill=i.stroke=i.shadowBlur=i.shadowColor=i.shadowOffsetX=i.shadowOffsetY=null;var n=i.text;null!=n&&(n+=""),Gn(n,i)?(this.setTransform(t),Pn(this,t,n,i,null,e),this.restoreTransform(t)):t.__attrCachedBy=Ni.NONE},getBoundingRect:function(){var t=this.style;if(this.__dirty&&Dn(t),!this._rect){var e=t.text;null!=e?e+="":e="";var i=un(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(i.x+=t.x||0,i.y+=t.y||0,Vn(t.textStroke,t.textStrokeWidth)){var n=t.textStrokeWidth;i.x-=n/2,i.y-=n/2,i.width+=n,i.height+=n}this._rect=i}return this._rect}},w(Ba,Xn);function Va(l){return v.browser.ie&&11<=v.browser.version?function(){var t,e=this.__clipPaths,i=this.style;if(e)for(var n=0;n<e.length;n++){var r=e[n],o=r&&r.shape,a=r&&r.type;if(o&&("sector"===a&&o.startAngle===o.endAngle||"rect"===a&&(!o.width||!o.height))){for(var s=0;s<Ha.length;s++)Ha[s][2]=i[Ha[s][0]],i[Ha[s][0]]=Ha[s][1];t=!0;break}}if(l.apply(this,arguments),t)for(s=0;s<Ha.length;s++)i[Ha[s][0]]=Ha[s][2]}:l}var Fa=xa.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),Ha=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],Wa=xa.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:Va(xa.prototype.brush),buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(a),h=Math.sin(a);t.moveTo(u*r+i,h*r+n),t.lineTo(u*o+i,h*o+n),t.arc(i,n,o,a,s,!l),t.lineTo(Math.cos(s)*r+i,Math.sin(s)*r+n),0!==r&&t.arc(i,n,r,s,a,l),t.closePath()}}),Ga=xa.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=2*Math.PI;t.moveTo(i+e.r,n),t.arc(i,n,e.r,0,r,!1),t.moveTo(i+e.r0,n),t.arc(i,n,e.r0,0,r,!0)}});function Za(t,e,i,n,r,o,a){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*a+(-3*(e-i)-2*s-l)*o+s*r+e}function Ua(t,e,i){var n=e.points,r=e.smooth;if(n&&2<=n.length){if(r&&"spline"!==r){var o=function(t,e,i,n){var r,o,a,s,l=[],u=[],h=[],c=[];if(n){a=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;d<f;d++)_t(a,a,t[d]),xt(s,s,t[d]);_t(a,a,n[0]),xt(s,s,n[1])}for(d=0,f=t.length;d<f;d++){var p=t[d];if(i)r=t[d?d-1:f-1],o=t[(d+1)%f];else{if(0===d||d===f-1){l.push(rt(t[d]));continue}r=t[d-1],o=t[d+1]}st(u,o,r),dt(u,u,e);var g=pt(p,r),m=pt(p,o),v=g+m;0!==v&&(g/=v,m/=v),dt(h,u,-g),dt(c,u,m);var y=ot([],p,h),_=ot([],p,c);n&&(xt(y,y,a),_t(y,y,s),xt(_,_,a),_t(_,_,s)),l.push(y),l.push(_)}return i&&l.push(l.shift()),l}(n,r,i,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var a=n.length,s=0;s<(i?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=n[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{"spline"===r&&(n=function(t,e){for(var i=t.length,n=[],r=0,o=1;o<i;o++)r+=pt(t[o-1],t[o]);var a=r/2;a=a<i?i:a;for(o=0;o<a;o++){var s,l,u,h=o/(a-1)*(e?i:i-1),c=Math.floor(h),d=h-c,f=t[c%i];u=e?(s=t[(c-1+i)%i],l=t[(c+1)%i],t[(c+2)%i]):(s=t[0===c?c:c-1],l=t[i-2<c?i-1:c+1],t[i-3<c?i-1:c+2]);var p=d*d,g=d*p;n.push([Za(s[0],f[0],l[0],u[0],d,p,g),Za(s[1],f[1],l[1],u[1],d,p,g)])}return n}(n,i)),t.moveTo(n[0][0],n[0][1]);s=1;for(var c=n.length;s<c;s++)t.lineTo(n[s][0],n[s][1])}i&&t.closePath()}}var Xa=xa.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){Ua(t,e,!0)}}),Ya=xa.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){Ua(t,e,!1)}}),ja=Math.round;function qa(t,e,i){if(e){var n=e.x1,r=e.x2,o=e.y1,a=e.y2;t.x1=n,t.x2=r,t.y1=o,t.y2=a;var s=i&&i.lineWidth;s&&(ja(2*n)===ja(2*r)&&(t.x1=t.x2=Ka(n,s,!0)),ja(2*o)===ja(2*a)&&(t.y1=t.y2=Ka(o,s,!0)))}}function $a(t,e,i){if(e){var n=e.x,r=e.y,o=e.width,a=e.height;t.x=n,t.y=r,t.width=o,t.height=a;var s=i&&i.lineWidth;s&&(t.x=Ka(n,s,!0),t.y=Ka(r,s,!0),t.width=Math.max(Ka(n+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Ka(r+a,s,!1)-t.y,0===a?0:1))}}function Ka(t,e,i){if(!e)return t;var n=ja(2*t);return(n+ja(e))%2==0?n/2:(n+(i?1:-1))/2}var Qa={},Ja=xa.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var i,n,r,o;this.subPixelOptimize?($a(Qa,e,this.style),i=Qa.x,n=Qa.y,r=Qa.width,o=Qa.height,Qa.r=e.r,e=Qa):(i=e.x,n=e.y,r=e.width,o=e.height),e.r?bn(t,e):t.rect(i,n,r,o),t.closePath()}}),ts={},es=xa.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i,n,r,o;o=this.subPixelOptimize?(qa(ts,e,this.style),i=ts.x1,n=ts.y1,r=ts.x2,ts.y2):(i=e.x1,n=e.y1,r=e.x2,e.y2);var a=e.percent;0!==a&&(t.moveTo(i,n),a<1&&(r=i*(1-a)+r*a,o=n*(1-a)+o*a),t.lineTo(r,o))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),is=[];function ns(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?xo:_o)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?xo:_o)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?Mo:So)(t.x1,t.cpx1,t.x2,e),(i?Mo:So)(t.y1,t.cpy1,t.y2,e)]}function rs(t){this.colorStops=t||[]}var os=xa.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(i,n),null==l||null==u?(h<1&&(Co(i,a,r,h,is),a=is[1],r=is[2],Co(n,s,o,h,is),s=is[1],o=is[2]),t.quadraticCurveTo(a,s,r,o)):(h<1&&(bo(i,a,l,r,h,is),a=is[1],l=is[2],r=is[3],bo(n,s,u,o,h,is),s=is[1],u=is[2],o=is[3]),t.bezierCurveTo(a,s,l,u,r,o)))},pointAt:function(t){return ns(this.shape,t,!1)},tangentAt:function(t){var e=ns(this.shape,t,!0);return ft(e,e)}}),as=xa.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+i,u*r+n),t.arc(i,n,r,o,a,!s)}}),ss=xa.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e.length;i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t.length;i++)t[i].path||t[i].createPathProxy(),t[i].path.setScale(e[0],e[1],t[i].segmentIgnoreThreshold)},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i.length;n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),xa.prototype.getBoundingRect.call(this)}});rs.prototype={constructor:rs,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};function ls(t,e,i,n,r,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==i?1:i,this.y2=null==n?0:n,this.type="linear",this.global=o||!1,rs.call(this,r)}ls.prototype={constructor:ls},w(ls,rs);function us(t,e,i,n,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==i?.5:i,this.type="radial",this.global=r||!1,rs.call(this,n)}function hs(t){Xn.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}us.prototype={constructor:us},w(us,rs),hs.prototype.incremental=!0,hs.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},hs.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},hs.prototype.addDisplayables=function(t,e){e=e||!1;for(var i=0;i<t.length;i++)this.addDisplayable(t[i],e)},hs.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},hs.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},hs.prototype.brush=function(t,e){for(var i=this._cursor;i<this._displayables.length;i++){(n=this._displayables[i]).beforeBrush&&n.beforeBrush(t),n.brush(t,i===this._cursor?null:this._displayables[i-1]),n.afterBrush&&n.afterBrush(t)}this._cursor=i;for(i=0;i<this._temporaryDisplayables.length;i++){var n;(n=this._temporaryDisplayables[i]).beforeBrush&&n.beforeBrush(t),n.brush(t,0===i?null:this._temporaryDisplayables[i-1]),n.afterBrush&&n.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var cs=[];hs.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new bi(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var i=this._displayables[e],n=i.getBoundingRect().clone();i.needLocalTransform()&&n.applyTransform(i.getLocalTransform(cs)),t.union(n)}this._rect=t}return this._rect},hs.prototype.contain=function(t,e){var i=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(i[0],i[1]))for(var n=0;n<this._displayables.length;n++){if(this._displayables[n].contain(t,e))return!0}return!1},w(hs,Xn);var ds=Math.max,fs=Math.min,ps={},gs=1,ms="emphasis",vs="normal",ys=1,_s={},xs={};function ws(t){return xa.extend(t)}function bs(t,e){xs[t]=e}function Ss(t){if(xs.hasOwnProperty(t))return xs[t]}function Ms(t,e,i,n){var r=Ra(t,e);return i&&("center"===n&&(i=Cs(i,r.getBoundingRect())),As(r,i)),r}function Is(t,i,n){var r=new Yn({style:{image:t,x:i.x,y:i.y,width:i.width,height:i.height},onload:function(t){if("center"===n){var e={width:t.width,height:t.height};r.setStyle(Cs(i,e))}}});return r}function Cs(t,e){var i,n=e.width/e.height,r=t.height*n;return i=r<=t.width?t.height:(r=t.width)/n,{x:t.x+t.width/2-r/2,y:t.y+t.height/2-i/2,width:r,height:i}}function Ts(t,e){for(var i=[],n=t.length,r=0;r<n;r++){var o=t[r];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),i.push(o.path)}var a=new xa(e);return a.createPathProxy(),a.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},a}function As(t,e){if(t.applyTransform){var i=t.getBoundingRect().calculateTransform(e);t.applyTransform(i)}}var Ds=Ka;function ks(t){return null!=t&&"none"!==t}var Ps=Q(),Ls=0;function Os(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var i=t.__zr,n=t.useHoverLayer&&i&&"canvas"===i.painter.type;if(t.__highlighted=n?"layer":"plain",!(t.isGroup||!i&&t.useHoverLayer)){var r=t,o=t.style;n&&(o=(r=i.addHover(t)).style),Js(o),n||function(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(e){var i=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var n=t.style;for(var r in e)null!=e[r]&&(i[r]=n[r]);i.fill=n.fill,i.stroke=n.stroke}else t.__cachedNormalStl=t.__cachedNormalZ2=null}}(r),o.extendFrom(e),zs(o,e,"fill"),zs(o,e,"stroke"),Qs(o),n||(t.dirty(!1),t.z2+=gs)}}}function zs(t,e,i){!ks(e[i])&&ks(t[i])&&(t[i]=function(t){if("string"!=typeof t)return t;var e=Ps.get(t);return e||(e=Be(t,-.1),Ls<1e4&&(Ps.set(t,e),Ls++)),e}(t[i]))}function Es(t){var e=t.__highlighted;if(e&&(t.__highlighted=!1,!t.isGroup))if("layer"===e)t.__zr&&t.__zr.removeHover(t);else{var i=t.style,n=t.__cachedNormalStl;n&&(Js(i),t.setStyle(n),Qs(i));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===gs&&(t.z2=r)}}function Ns(t,e,i){var n,r=vs,o=vs;t.__highlighted&&(r=ms,n=!0),e(t,i),t.__highlighted&&(o=ms,n=!0),t.isGroup&&t.traverse(function(t){t.isGroup||e(t,i)}),n&&t.__highDownOnUpdate&&t.__highDownOnUpdate(r,o)}function Rs(t,e){e=t.__hoverStl=!1!==e&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,Es(t),Os(t))}function Bs(t){Ws(this,t)||this.__highByOuter||Ns(this,Os)}function Vs(t){Ws(this,t)||this.__highByOuter||Ns(this,Es)}function Fs(t){this.__highByOuter|=1<<(t||0),Ns(this,Os)}function Hs(t){(this.__highByOuter&=~(1<<(t||0)))||Ns(this,Es)}function Ws(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Gs(t,e){Zs(t,!0),Ns(t,Rs,e)}function Zs(t,e){var i=!1===e;if(t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,!i||t.__highDownDispatcher){var n=i?"off":"on";t[n]("mouseover",Bs)[n]("mouseout",Vs),t[n]("emphasis",Fs)[n]("normal",Hs),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!i}}function Us(t){return!(!t||!t.__highDownDispatcher)}function Xs(t){var e=_s[t];return null==e&&ys<=32&&(e=_s[t]=ys++),e}function Ys(t,e,i,n,r,o,a){var s,l=(r=r||ps).labelFetcher,u=r.labelDataIndex,h=r.labelDimIndex,c=r.labelProp,d=i.getShallow("show"),f=n.getShallow("show");(d||f)&&(l&&(s=l.getFormattedLabel(u,"normal",null,h,c)),null==s&&(s=z(r.defaultText)?r.defaultText(u,r):r.defaultText));var p=d?s:null,g=f?W(l?l.getFormattedLabel(u,"emphasis",null,h,c):null,s):null;null==p&&null==g||(js(t,i,o,r),js(e,n,a,r,!0)),t.text=p,e.text=g}function js(t,e,i,n,r){return qs(t,e,n,r),i&&k(t,i),t}function qs(t,e,i,n){if((i=i||ps).isRectText){var r;i.getTextPosition?r=i.getTextPosition(e,n):"outside"===(r=e.getShallow("position")||(n?null:"inside"))&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset");var o=e.getShallow("rotate");null!=o&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=W(e.getShallow("distance"),n?null:5)}var a,s=e.ecModel,l=s&&s.option.textStyle,u=function(t){var e;for(;t&&t!==t.ecModel;){var i=(t.option||ps).rich;if(i)for(var n in e=e||{},i)i.hasOwnProperty(n)&&(e[n]=1);t=t.parentModel}return e}(e);if(u)for(var h in a={},u)if(u.hasOwnProperty(h)){var c=e.getModel(["rich",h]);$s(a[h]={},c,l,i,n)}return t.rich=a,$s(t,e,l,i,n,!0),i.forceRich&&!i.textStyle&&(i.textStyle={}),t}function $s(t,e,i,n,r,o){i=!r&&i||ps,t.textFill=Ks(e.getShallow("color"),n)||i.color,t.textStroke=Ks(e.getShallow("textBorderColor"),n)||i.textBorderColor,t.textStrokeWidth=W(e.getShallow("textBorderWidth"),i.textBorderWidth),r||(o&&(t.insideRollbackOpt=n,Qs(t)),null==t.textFill&&(t.textFill=n.autoColor)),t.fontStyle=e.getShallow("fontStyle")||i.fontStyle,t.fontWeight=e.getShallow("fontWeight")||i.fontWeight,t.fontSize=e.getShallow("fontSize")||i.fontSize,t.fontFamily=e.getShallow("fontFamily")||i.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&n.disableBox||(t.textBackgroundColor=Ks(e.getShallow("backgroundColor"),n),t.textPadding=e.getShallow("padding"),t.textBorderColor=Ks(e.getShallow("borderColor"),n),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||i.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||i.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||i.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||i.textShadowOffsetY}function Ks(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Qs(t){var e,i=t.textPosition,n=t.insideRollbackOpt;if(n&&null==t.textFill){var r=n.autoColor,o=n.isRectText,a=n.useInsideStyle,s=!1!==a&&(!0===a||o&&i&&"string"==typeof i&&0<=i.indexOf("inside")),l=!s&&null!=r;(s||l)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),s&&(t.textFill="#fff",null==t.textStroke&&(t.textStroke=r,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),l&&(t.textFill=r)}t.insideRollback=e}function Js(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function tl(t,e){var i=e&&e.getModel("textStyle");return Y([t.fontStyle||i&&i.getShallow("fontStyle")||"",t.fontWeight||i&&i.getShallow("fontWeight")||"",(t.fontSize||i&&i.getShallow("fontSize")||12)+"px",t.fontFamily||i&&i.getShallow("fontFamily")||"sans-serif"].join(" "))}function el(t,e,i,n,r,o){if("function"==typeof r&&(o=r,r=null),n&&n.isAnimationEnabled()){var a=t?"Update":"",s=n.getShallow("animationDuration"+a),l=n.getShallow("animationEasing"+a),u=n.getShallow("animationDelay"+a);"function"==typeof u&&(u=u(r,n.getAnimationDelayParams?n.getAnimationDelayParams(e,r):null)),"function"==typeof s&&(s=s(r)),0<s?e.animateTo(i,s,u||0,l,o,!!o):(e.stopAnimation(),e.attr(i),o&&o())}else e.stopAnimation(),e.attr(i),o&&o()}function il(t,e,i,n,r){el(!0,t,e,i,n,r)}function nl(t,e,i,n,r){el(!1,t,e,i,n,r)}function rl(t,e){for(var i=ie([]);t&&t!==e;)re(i,t.getLocalTransform(),i),t=t.parent;return i}function ol(t,e,i){return e&&!L(e)&&(e=de.getLocalTransform(e)),i&&(e=le([],e)),yt([],t,e)}function al(t,e,i){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-n:"right"===t?n:0,"top"===t?-r:"bottom"===t?r:0];return o=ol(o,e,i),Math.abs(o[0])>Math.abs(o[1])?0<o[0]?"right":"left":0<o[1]?"bottom":"top"}function sl(t,e,n,i){if(t&&e){var r,o=(r={},t.traverse(function(t){!t.isGroup&&t.anid&&(r[t.anid]=t)}),r);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=o[t.anid];if(e){var i=a(t);t.attr(a(e)),il(t,i,n,t.dataIndex)}}})}function a(t){var e={position:rt(t.position),rotation:t.rotation};return t.shape&&(e.shape=k({},t.shape)),e}}function ll(t,n){return P(t,function(t){var e=t[0];e=ds(e,n.x),e=fs(e,n.x+n.width);var i=t[1];return i=ds(i,n.y),[e,i=fs(i,n.y+n.height)]})}function ul(t,e,i){var n=(e=k({rectHover:!0},e)).style={strokeNoScale:!0};if(i=i||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(n.image=t.slice(8),A(n,i),new Yn(e)):Ms(t.replace("path://",""),e,i,"center")}function hl(t,e,i,n,r,o,a,s){var l=i-t,u=n-e,h=a-r,c=s-o,d=cl(h,c,l,u);if(function(t){return t<=1e-6&&-1e-6<=t}(d))return!1;var f=t-r,p=e-o,g=cl(f,p,l,u)/d;if(g<0||1<g)return!1;var m=cl(f,p,h,c)/d;return!(m<0||1<m)}function cl(t,e,i,n){return t*n-i*e}bs("circle",Fa),bs("sector",Wa),bs("ring",Ga),bs("polygon",Xa),bs("polyline",Ya),bs("rect",Ja),bs("line",es),bs("bezierCurve",os),bs("arc",as);var dl=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:gs,CACHED_LABEL_STYLE_PROPERTIES:{color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},extendShape:ws,extendPath:function(t,e){return function(t,e){return xa.extend(Na(t,e))}(t,e)},registerShape:bs,getShapeClass:Ss,makePath:Ms,makeImage:Is,mergePath:Ts,resizePath:As,subPixelOptimizeLine:function(t){return qa(t.shape,t.shape,t.style),t},subPixelOptimizeRect:function(t){return $a(t.shape,t.shape,t.style),t},subPixelOptimize:Ds,setElementHoverStyle:Rs,setHoverStyle:Gs,setAsHighDownDispatcher:Zs,isHighDownDispatcher:Us,getHighlightDigit:Xs,setLabelStyle:Ys,modifyLabelStyle:function(t,e,i){var n=t.style;e&&(Js(n),t.setStyle(e),Qs(n)),n=t.__hoverStl,i&&n&&(Js(n),k(n,i),Qs(n))},setTextStyle:js,setText:function(t,e,i){var n,r={isRectText:!0};!1===i?n=!0:r.autoColor=i,qs(t,e,r,n)},getFont:tl,updateProps:il,initProps:nl,getTransform:rl,applyTransform:ol,transformDirection:al,groupTransition:sl,clipPointsByRect:ll,clipRectByRect:function(t,e){var i=ds(t.x,e.x),n=fs(t.x+t.width,e.x+e.width),r=ds(t.y,e.y),o=fs(t.y+t.height,e.y+e.height);if(i<=n&&r<=o)return{x:i,y:r,width:n-i,height:o-r}},createIcon:ul,linePolygonIntersect:function(t,e,i,n,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(hl(t,e,i,n,s[0],s[1],a[0],a[1]))return!0;a=s}},lineLineIntersect:hl,Group:Si,Image:Yn,Text:Ba,Circle:Fa,Sector:Wa,Ring:Ga,Polygon:Xa,Polyline:Ya,Rect:Ja,Line:es,BezierCurve:os,Arc:as,IncrementalDisplayable:hs,CompoundPath:ss,LinearGradient:ls,RadialGradient:us,BoundingRect:bi}),fl=["textStyle","color"],pl={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(fl):null)},getFont:function(){return tl({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return un(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},gl=io([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),ml={getItemStyle:function(t,e){var i=gl(this,t,e),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},vl=S,yl=Hr();function _l(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}function xl(t,e,i){for(var n=0;n<e.length&&(!e[n]||null!=(t=t&&"object"==typeof t?t[e[n]]:null));n++);return null==t&&i&&(t=i.get(e)),t}function wl(t,e){var i=yl(t).getParent;return i?i.call(t,e):t.parentModel}_l.prototype={constructor:_l,init:null,mergeOption:function(t){m(this.option,t,!0)},get:function(t,e){return null==t?this.option:xl(this.option,this.parsePath(t),!e&&wl(this,t))},getShallow:function(t,e){var i=this.option,n=null==i?i:i[t],r=!e&&wl(this,t);return null==n&&r&&(n=r.getShallow(t)),n},getModel:function(t,e){var i;return new _l(null==t?this.option:xl(this.option,t=this.parsePath(t)),e=e||(i=wl(this,t))&&i.getModel(t),this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){return new this.constructor(b(this.option))},setReadOnly:function(t){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){yl(this).getParent=t},isAnimationEnabled:function(){if(!v.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},$r(_l),Qr(_l),vl(_l,ro),vl(_l,ao),vl(_l,pl),vl(_l,ml);var bl=0;function Sl(t){return[t||"",bl++,Math.random().toFixed(5)].join("_")}var Ml=1e-4;function Il(t,e,i,n){var r=e[1]-e[0],o=i[1]-i[0];if(0==r)return 0==o?i[0]:(i[0]+i[1])/2;if(n)if(0<r){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*o+i[0]}function Cl(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?function(t){return t.replace(/^\s+|\s+$/g,"")}(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function Tl(t,e,i){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),i?t:+t}function Al(t){return t.sort(function(t,e){return t-e}),t}function Dl(t){if(t=+t,isNaN(t))return 0;for(var e=1,i=0;Math.round(t*e)/e!==t;)e*=10,i++;return i}function kl(t){var e=t.toString(),i=e.indexOf("e");if(0<i){var n=+e.slice(i+1);return n<0?-n:0}var r=e.indexOf(".");return r<0?0:e.length-1-r}function Pl(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),o=Math.round(i(Math.abs(e[1]-e[0]))/n),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function Ll(t,e,i){if(!t[e])return 0;var n=M(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return 0;for(var r=Math.pow(10,i),o=P(t,function(t){return(isNaN(t)?0:t)/n*r*100}),a=100*r,s=P(o,function(t){return Math.floor(t)}),l=M(s,function(t,e){return t+e},0),u=P(o,function(t,e){return t-s[e]});l<a;){for(var h=Number.NEGATIVE_INFINITY,c=null,d=0,f=u.length;d<f;++d)u[d]>h&&(h=u[d],c=d);++s[c],u[c]=0,++l}return s[e]/r}function Ol(t){var e=2*Math.PI;return(t%e+e)%e}function zl(t){return-Ml<t&&t<Ml}var El=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Nl(t){if(t instanceof Date)return t;if("string"!=typeof t)return null==t?new Date(NaN):new Date(Math.round(t));var e=El.exec(t);if(!e)return new Date(NaN);if(e[8]){var i=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(i-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,i,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}function Rl(t){return Math.pow(10,Bl(t))}function Bl(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return 10<=t/Math.pow(10,e)&&e++,e}function Vl(t,e){var i=Bl(t),n=Math.pow(10,i),r=t/n;return t=(e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10)*n,-20<=i?+t.toFixed(i<0?-i:0):t}var Fl=(Object.freeze||Object)({linearMap:Il,parsePercent:Cl,round:Tl,asc:Al,getPrecision:Dl,getPrecisionSafe:kl,getPixelPrecision:Pl,getPercentWithPrecision:Ll,MAX_SAFE_INTEGER:9007199254740991,remRadian:Ol,isRadianAroundZero:zl,parseDate:Nl,quantity:Rl,quantityExponent:Bl,nice:Vl,quantile:function(t,e){var i=(t.length-1)*e+1,n=Math.floor(i),r=+t[n-1],o=i-n;return o?r+o*(t[n]-r):r},reformIntervals:function(t){t.sort(function(t,e){return function t(e,i,n){return e.interval[n]<i.interval[n]||e.interval[n]===i.interval[n]&&(e.close[n]-i.close[n]==(n?-1:1)||!n&&t(e,i,1))}(t,e,0)?-1:1});for(var e=-1/0,i=1,n=0;n<t.length;){for(var r=t[n].interval,o=t[n].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-i),e=r[a],i=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(n,1):n++}return t},isNumeric:function(t){return 0<=t-parseFloat(t)}});function Hl(t){return isNaN(t)?"-":(t=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<t.length?"."+t[1]:"")}function Wl(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}var Gl=U,Zl=/([&<>"'])/g,Ul={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Xl(t){return null==t?"":(t+"").replace(Zl,function(t,e){return Ul[e]})}function Yl(t,e){return"{"+t+(null==e?"":e)+"}"}var jl=["a","b","c","d","e","f","g"];function ql(t,e,i){O(e)||(e=[e]);var n=e.length;if(!n)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=jl[o];t=t.replace(Yl(a),Yl(a,0))}for(var s=0;s<n;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Yl(jl[l],s),i?Xl(u):u)}return t}function $l(t,e){var i=(t=E(t)?{color:t,extraCssText:e}:t||{}).color,n=t.type,r=(e=t.extraCssText,t.renderMode||"html"),o=t.markerId||"X";return i?"html"===r?"subItem"===n?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Xl(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+Xl(i)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+o+"|}  ",style:{color:i}}:""}function Kl(t,e){return"0000".substr(0,e-(t+="").length)+t}function Ql(t,e,i){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var n=Nl(e),r=i?"UTC":"",o=n["get"+r+"FullYear"](),a=n["get"+r+"Month"]()+1,s=n["get"+r+"Date"](),l=n["get"+r+"Hours"](),u=n["get"+r+"Minutes"](),h=n["get"+r+"Seconds"](),c=n["get"+r+"Milliseconds"]();return t=t.replace("MM",Kl(a,2)).replace("M",a).replace("yyyy",o).replace("yy",o%100).replace("dd",Kl(s,2)).replace("d",s).replace("hh",Kl(l,2)).replace("h",l).replace("mm",Kl(u,2)).replace("m",u).replace("ss",Kl(h,2)).replace("s",h).replace("SSS",Kl(c,3))}function Jl(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}var tu=fn;function eu(t,e){if("_blank"===e||"blank"===e){var i=window.open();i.opener=null,i.location=t}else window.open(t,e)}var iu=(Object.freeze||Object)({addCommas:Hl,toCamelCase:Wl,normalizeCssArray:Gl,encodeHTML:Xl,formatTpl:ql,formatTplSimple:function(i,t,n){return D(t,function(t,e){i=i.replace("{"+e+"}",n?Xl(t):t)}),i},getTooltipMarker:$l,formatTime:Ql,capitalFirst:Jl,truncateText:tu,getTextBoundingRect:function(t){return un(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)},getTextRect:function(t,e,i,n,r,o,a,s){return un(t,e,i,n,r,s,o,a)},windowOpen:eu}),nu=D,ru=["left","right","top","bottom","width","height"],ou=[["width","left","right"],["height","top","bottom"]];function au(h,c,d,f,p){var g=0,m=0;null==f&&(f=1/0),null==p&&(p=1/0);var v=0;c.eachChild(function(t,e){var i,n,r=t.position,o=t.getBoundingRect(),a=c.childAt(e+1),s=a&&a.getBoundingRect();if("horizontal"===h){var l=o.width+(s?-s.x+o.x:0);v=f<(i=g+l)||t.newline?(g=0,i=l,m+=v+d,o.height):Math.max(v,o.height)}else{var u=o.height+(s?-s.y+o.y:0);v=p<(n=m+u)||t.newline?(g+=v+d,m=0,n=u,o.width):Math.max(v,o.width)}t.newline||(r[0]=g,r[1]=m,"horizontal"===h?g=i+d:m=n+d)})}var su=au;T(au,"vertical"),T(au,"horizontal");function lu(t,e,i){i=Gl(i||0);var n=e.width,r=e.height,o=Cl(t.left,n),a=Cl(t.top,r),s=Cl(t.right,n),l=Cl(t.bottom,r),u=Cl(t.width,n),h=Cl(t.height,r),c=i[2]+i[0],d=i[1]+i[3],f=t.aspect;switch(isNaN(u)&&(u=n-s-d-o),isNaN(h)&&(h=r-l-c-a),null!=f&&(isNaN(u)&&isNaN(h)&&(n/r<f?u=.8*n:h=.8*r),isNaN(u)&&(u=f*h),isNaN(h)&&(h=u/f)),isNaN(o)&&(o=n-s-u-d),isNaN(a)&&(a=r-l-h-c),t.left||t.right){case"center":o=n/2-u/2-i[3];break;case"right":o=n-u-d}switch(t.top||t.bottom){case"middle":case"center":a=r/2-h/2-i[0];break;case"bottom":a=r-h-c}o=o||0,a=a||0,isNaN(u)&&(u=n-d-o-(s||0)),isNaN(h)&&(h=r-c-a-(l||0));var p=new bi(o+i[3],a+i[0],u,h);return p.margin=i,p}function uu(t,e,i,n,r){var o=!r||!r.hv||r.hv[0],a=!r||!r.hv||r.hv[1],s=r&&r.boundingMode||"all";if(o||a){var l;if("raw"===s)l="group"===t.type?new bi(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(l=t.getBoundingRect(),t.needLocalTransform()){var u=t.getLocalTransform();(l=l.clone()).applyTransform(u)}e=lu(A({width:l.width,height:l.height},e),i,n);var h=t.position,c=o?e.x-l.x:0,d=a?e.y-l.y:0;t.attr("position","raw"===s?[c,d]:[h[0]+c,h[1]+d])}}function hu(l,u,t){N(t)||(t={});var h=t.ignoreSize;O(h)||(h=[h,h]);var e=n(ou[0],0),i=n(ou[1],1);function n(t,e){var i={},n=0,r={},o=0;if(nu(t,function(t){r[t]=l[t]}),nu(t,function(t){c(u,t)&&(i[t]=r[t]=u[t]),d(i,t)&&n++,d(r,t)&&o++}),h[e])return d(u,t[1])?r[t[2]]=null:d(u,t[2])&&(r[t[1]]=null),r;if(2!==o&&n){if(2<=n)return i;for(var a=0;a<t.length;a++){var s=t[a];if(!c(i,s)&&c(l,s)){i[s]=l[s];break}}return i}return r}function c(t,e){return t.hasOwnProperty(e)}function d(t,e){return null!=t[e]&&"auto"!==t[e]}function r(t,e,i){nu(t,function(t){e[t]=i[t]})}r(ou[0],l,e),r(ou[1],l,i)}function cu(t){return du({},t)}function du(e,i){return i&&e&&nu(ru,function(t){i.hasOwnProperty(t)&&(e[t]=i[t])}),e}var fu,pu,gu,mu=Hr(),vu=_l.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,i,n){_l.call(this,t,e,i,n),this.uid=Sl("ec_cpt_model")},init:function(t,e,i,n){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?cu(t):{};m(t,e.getTheme().get(this.mainType)),m(t,this.getDefaultOption()),i&&hu(t,n,i)},mergeOption:function(t,e){m(this.option,t,!0);var i=this.layoutMode;i&&hu(this.option,t,i)},optionUpdated:function(t,e){},getDefaultOption:function(){var t=mu(this);if(!t.defaultOption){for(var e=[],i=this.constructor;i;){var n=i.prototype.defaultOption;n&&e.push(n),i=i.superClass}for(var r={},o=e.length-1;0<=o;o--)r=m(r,e[o],!0);t.defaultOption=r}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});function yu(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}eo(vu,{registerWhenExtend:!0}),pu={},(fu=vu).registerSubTypeDefaulter=function(t,e){t=qr(t),pu[t.main]=e},fu.determineSubType=function(t,e){var i=e.type;if(!i){var n=qr(t).main;fu.hasSubTypes(t)&&pu[n]&&(i=pu[n](e))}return i},gu=function(t){var e=[];D(vu.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=P(e,function(t){return qr(t).main}),"dataset"!==t&&x(e,"dataset")<=0&&e.unshift("dataset");return e},vu.topologicalTravel=function(t,e,i,n){if(t.length){var r=function(e){var r={},o=[];return D(e,function(i){var n=yu(r,i),t=function(t,e){var i=[];return D(t,function(t){0<=x(e,t)&&i.push(t)}),i}(n.originalDeps=gu(i),e);n.entryCount=t.length,0===n.entryCount&&o.push(i),D(t,function(t){x(n.predecessor,t)<0&&n.predecessor.push(t);var e=yu(r,t);x(e.successor,t)<0&&e.successor.push(i)})}),{graph:r,noEntryList:o}}(e),o=r.graph,a=r.noEntryList,s={};for(D(t,function(t){s[t]=!0});a.length;){var l=a.pop(),u=o[l],h=!!s[l];h&&(i.call(n,l,u.originalDeps.slice()),delete s[l]),D(u.successor,h?d:c)}D(s,function(){throw new Error("Circle dependency may exists")})}function c(t){o[t].entryCount--,0===o[t].entryCount&&a.push(t)}function d(t){s[t]=!0,c(t)}},S(vu,{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}});var _u="";"undefined"!=typeof navigator&&(_u=navigator.platform||"");var xu={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:_u.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},wu=Hr();var bu={clearColorPalette:function(){wu(this).colorIdx=0,wu(this).colorNameMap={}},getColorFromPalette:function(t,e,i){var n=wu(e=e||this),r=n.colorIdx||0,o=n.colorNameMap=n.colorNameMap||{};if(o.hasOwnProperty(t))return o[t];var a=Lr(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=i&&s?function(t,e){for(var i=t.length,n=0;n<i;n++)if(t[n].length>e)return t[n];return t[i-1]}(s,i):a;if((l=l||a)&&l.length){var u=l[r];return t&&(o[t]=u),n.colorIdx=(r+1)%l.length,u}}},Su="original",Mu="arrayRows",Iu="objectRows",Cu="keyedColumns",Tu="unknown",Au="typedArray",Du="column",ku="row";function Pu(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===Cu?{}:[]),this.sourceFormat=t.sourceFormat||Tu,this.seriesLayoutBy=t.seriesLayoutBy||Du,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&Q(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}Pu.seriesDataToSource=function(t){return new Pu({data:t,sourceFormat:B(t)?Au:Su,fromDataset:!1})},Qr(Pu);var Lu={Must:1,Might:2,Not:3},Ou=Hr();function zu(t){var e=t.option,i=e.data,n=B(i)?Au:Su,r=!1,o=e.seriesLayoutBy,a=e.sourceHeader,s=e.dimensions,l=Vu(t);if(l){var u=l.option;i=u.source,n=Ou(l).sourceFormat,r=!0,o=o||u.seriesLayoutBy,null==a&&(a=u.sourceHeader),s=s||u.dimensions}var h=function(t,e,i,n,r){if(!t)return{dimensionsDefine:Eu(r)};var o,a;if(e===Mu)"auto"===n||null==n?Nu(function(t){null!=t&&"-"!==t&&(E(t)?null==a&&(a=1):a=0)},i,t,10):a=n?1:0,r||1!==a||(r=[],Nu(function(t,e){r[e]=null!=t?t:""},i,t)),o=r?r.length:i===ku?t.length:t[0]?t[0].length:null;else if(e===Iu)r=r||function(t){var e,i=0;for(;i<t.length&&!(e=t[i++]););if(e){var n=[];return D(e,function(t,e){n.push(e)}),n}}(t);else if(e===Cu)r||(r=[],D(t,function(t,e){r.push(e)}));else if(e===Su){var s=Er(t[0]);o=O(s)&&s.length||1}return{startIndex:a,dimensionsDefine:Eu(r),dimensionsDetectCount:o}}(i,n,o,a,s);Ou(t).source=new Pu({data:i,fromDataset:r,seriesLayoutBy:o,sourceFormat:n,dimensionsDefine:h.dimensionsDefine,startIndex:h.startIndex,dimensionsDetectCount:h.dimensionsDetectCount,encodeDefine:e.encode})}function Eu(t){if(t){var n=Q();return P(t,function(t,e){if(null==(t=k({},N(t)?t:{name:t})).name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var i=n.get(t.name);return i?t.name+="-"+i.count++:n.set(t.name,{count:1}),t})}}function Nu(t,e,i,n){if(null==n&&(n=1/0),e===ku)for(var r=0;r<i.length&&r<n;r++)t(i[r]?i[r][0]:null,r);else{var o=i[0]||[];for(r=0;r<o.length&&r<n;r++)t(o[r],r)}}function Ru(i,t,e){var o={},n=Vu(t);if(!n||!i)return o;var a,r,s=[],l=[],u=t.ecModel,h=Ou(u).datasetMap,c=n.uid+"_"+e.seriesLayoutBy;D(i=i.slice(),function(t,e){N(t)||(i[e]={name:t}),"ordinal"===t.type&&null==a&&(r=p(i[a=e])),o[t.name]=[]});var d=h.get(c)||h.set(c,{categoryWayDim:r,valueWayDim:0});function f(t,e,i){for(var n=0;n<i;n++)t.push(e+n)}function p(t){var e=t.dimsDef;return e?e.length:1}return D(i,function(t,e){var i=t.name,n=p(t);if(null==a){var r=d.valueWayDim;f(o[i],r,n),f(l,r,n),d.valueWayDim+=n}else if(a===e)f(o[i],0,n),f(s,0,n);else{r=d.categoryWayDim;f(o[i],r,n),f(l,r,n),d.categoryWayDim+=n}}),s.length&&(o.itemName=s),l.length&&(o.seriesName=l),o}function Bu(t,l,u){var e={};if(!Vu(t))return e;var h,c=l.sourceFormat,d=l.dimensionsDefine;c!==Iu&&c!==Cu||D(d,function(t,e){"name"===(N(t)?t.name:t)&&(h=e)});var i=function(){for(var t={},e={},i=[],n=0,r=Math.min(5,u);n<r;n++){var o=Fu(l.data,c,l.seriesLayoutBy,d,l.startIndex,n);i.push(o);var a=o===Lu.Not;if(a&&null==t.v&&n!==h&&(t.v=n),null!=t.n&&t.n!==t.v&&(a||i[t.n]!==Lu.Not)||(t.n=n),s(t)&&i[t.n]!==Lu.Not)return t;a||(o===Lu.Might&&null==e.v&&n!==h&&(e.v=n),null!=e.n&&e.n!==e.v||(e.n=n))}function s(t){return null!=t.v&&null!=t.n}return s(t)?t:s(e)?e:null}();if(i){e.value=i.v;var n=null!=h?h:i.n;e.itemName=[n],e.seriesName=[n]}return e}function Vu(t){var e=t.option;if(!e.data)return t.ecModel.getComponent("dataset",e.datasetIndex||0)}function Fu(t,e,i,n,r,o){var a,s,l;if(B(t))return Lu.Not;if(n){var u=n[o];N(u)?(s=u.name,l=u.type):E(u)&&(s=u)}if(null!=l)return"ordinal"===l?Lu.Must:Lu.Not;if(e===Mu)if(i===ku){for(var h=t[o],c=0;c<(h||[]).length&&c<5;c++)if(null!=(a=g(h[r+c])))return a}else for(c=0;c<t.length&&c<5;c++){var d=t[r+c];if(d&&null!=(a=g(d[o])))return a}else if(e===Iu){if(!s)return Lu.Not;for(c=0;c<t.length&&c<5;c++){if((f=t[c])&&null!=(a=g(f[s])))return a}}else if(e===Cu){if(!s)return Lu.Not;if(!(h=t[s])||B(h))return Lu.Not;for(c=0;c<h.length&&c<5;c++)if(null!=(a=g(h[c])))return a}else if(e===Su)for(c=0;c<t.length&&c<5;c++){var f,p=Er(f=t[c]);if(!O(p))return Lu.Not;if(null!=(a=g(p[o])))return a}function g(t){var e=E(t);return null!=t&&isFinite(t)&&""!==t?e?Lu.Might:Lu.Not:e&&"-"!==t?Lu.Must:void 0}return Lu.Not}var Hu="\0_ec_inner",Wu=_l.extend({init:function(t,e,i,n){i=i||{},this.option=null,this._theme=new _l(i),this._optionManager=n},setOption:function(t,e){X(!(Hu in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,i=this._optionManager;if(!t||"recreate"===t){var n=i.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(n)):function(t){t=t,this.option={},this.option[Hu]=1,this._componentsMap=Q({series:[]}),this._seriesIndices,this._seriesIndicesMap,function(i,t){var n=i.color&&!i.colorLayer;D(t,function(t,e){"colorLayer"===e&&n||vu.hasClass(e)||("object"==typeof t?i[e]=i[e]?m(i[e],t,!1):b(t):null==i[e]&&(i[e]=t))})}(t,this._theme.option),m(t,xu,!1),this.mergeOption(t)}.call(this,n),e=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=i.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var o=i.getMediaOption(this,this._api);o.length&&D(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(n){var l=this.option,u=this._componentsMap,i=[];!function(t){Ou(t).datasetMap=Q()}(this),D(n,function(t,e){null!=t&&(vu.hasClass(e)?e&&i.push(e):l[e]=null==l[e]?b(t):m(l[e],t,!0))}),vu.topologicalTravel(i,vu.getAllClassMainTypes(),function(a,t){var e=Lr(n[a]),i=Nr(u.get(a),e);Rr(i),D(i,function(t,e){var i=t.option;N(i)&&(t.keyInfo.mainType=a,t.keyInfo.subType=function(t,e,i){return e.type?e.type:i?i.subType:vu.determineSubType(t,e)}(a,i,t.exist))});var s=function(e,t){O(t)||(t=t?[t]:[]);var i={};return D(t,function(t){i[t]=(e.get(t)||[]).slice()}),i}(u,t);l[a]=[],u.set(a,[]),D(i,function(t,e){var i=t.exist,n=t.option;if(X(N(n)||i,"Empty component definition"),n){var r=vu.getClass(a,t.keyInfo.subType,!0);if(i&&i.constructor===r)i.name=t.keyInfo.name,i.mergeOption(n,this),i.optionUpdated(n,!1);else{var o=k({dependentModels:s,componentIndex:e},t.keyInfo);k(i=new r(n,this,this,o),o),i.init(n,this,this,o),i.optionUpdated(null,!0)}}else i.mergeOption({},this),i.optionUpdated({},!1);u.get(a)[e]=i,l[a][e]=i.option},this),"series"===a&&Gu(this,u.get("series"))},this),this._seriesIndicesMap=Q(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var n=b(this.option);return D(n,function(t,e){if(vu.hasClass(e)){for(var i=(t=Lr(t)).length-1;0<=i;i--)Vr(t[i])&&t.splice(i,1);n[e]=t}}),delete n[Hu],n},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap.get(t);if(i)return i[e||0]},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i,n=t.index,r=t.id,o=t.name,a=this._componentsMap.get(e);if(!a||!a.length)return[];if(null!=n)O(n)||(n=[n]),i=I(P(n,function(t){return a[t]}),function(t){return!!t});else if(null!=r){var s=O(r);i=I(a,function(t){return s&&0<=x(r,t.id)||!s&&t.id===r})}else if(null!=o){var l=O(o);i=I(a,function(t){return l&&0<=x(o,t.name)||!l&&t.name===o})}else i=a.slice();return Zu(i,t)},findComponents:function(t){var e,i,n,r,o,a=t.query,s=t.mainType,l=(i=s+"Index",n=s+"Id",r=s+"Name",!(e=a)||null==e[i]&&null==e[n]&&null==e[r]?null:{mainType:s,index:e[i],id:e[n],name:e[r]}),u=l?this.queryComponents(l):this._componentsMap.get(s);return o=Zu(u,t),t.filter?I(o,t.filter):o},eachComponent:function(t,n,r){var e=this._componentsMap;if("function"==typeof t)r=n,n=t,e.each(function(t,i){D(t,function(t,e){n.call(r,i,t,e)})});else if(E(t))D(e.get(t),n,r);else if(N(t)){D(this.findComponents(t),n,r)}},getSeriesByName:function(e){return I(this._componentsMap.get("series"),function(t){return t.name===e})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(e){return I(this._componentsMap.get("series"),function(t){return t.subType===e})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(i,n){D(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];i.call(n,e,t)},this)},eachRawSeries:function(t,e){D(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(i,n,r){D(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===i&&n.call(r,e,t)},this)},eachRawSeriesByType:function(t,e,i){return D(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){var i=I(this._componentsMap.get("series"),t,e);Gu(this,i)},restoreData:function(i){var n=this._componentsMap;Gu(this,n.get("series"));var r=[];n.each(function(t,e){r.push(e)}),vu.topologicalTravel(r,vu.getAllClassMainTypes(),function(e,t){D(n.get(e),function(t){"series"===e&&function(t,e){if(e){var i=e.seiresIndex,n=e.seriesId,r=e.seriesName;return null!=i&&t.componentIndex!==i||null!=n&&t.id!==n||null!=r&&t.name!==r}}(t,i)||t.restoreData()})})}});function Gu(t,e){t._seriesIndicesMap=Q(t._seriesIndices=P(e,function(t){return t.componentIndex})||[])}function Zu(t,e){return e.hasOwnProperty("subType")?I(t,function(t){return t.subType===e.subType}):t}S(Wu,bu);var Uu=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"];function Xu(e){D(Uu,function(t){this[t]=C(e[t],e)},this)}var Yu={};function ju(){this._coordinateSystems=[]}ju.prototype={constructor:ju,create:function(n,r){var o=[];D(Yu,function(t,e){var i=t.create(n,r);o=o.concat(i||[])}),this._coordinateSystems=o},update:function(e,i){D(this._coordinateSystems,function(t){t.update&&t.update(e,i)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},ju.register=function(t,e){Yu[t]=e},ju.get=function(t){return Yu[t]};var qu=D,$u=b,Ku=P,Qu=m,Ju=/^(min|max)?(.+)$/;function th(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function eh(t,e,i){var o={width:e,height:i,aspectratio:e/i},a=!0;return D(t,function(t,e){var i=e.match(Ju);if(i&&i[1]&&i[2]){var n=i[1],r=i[2].toLowerCase();!function(t,e,i){return"min"===i?e<=t:"max"===i?t<=e:t===e}(o[r],t,n)&&(a=!1)}}),a}th.prototype={constructor:th,setOption:function(t,e){t&&D(Lr(t.series),function(t){t&&t.data&&B(t.data)&&q(t.data)}),t=$u(t);var i=this._optionBackup,n=function(t,i,n){var e,r,o=[],a=[],s=t.timeline;t.baseOption&&(r=t.baseOption);(s||t.options)&&(r=r||{},o=(t.options||[]).slice());if(t.media){r=r||{};var l=t.media;qu(l,function(t){t&&t.option&&(t.query?a.push(t):e=e||t)})}r=r||t;r.timeline||(r.timeline=s);return qu([r].concat(o).concat(P(a,function(t){return t.option})),function(e){qu(i,function(t){t(e,n)})}),{baseOption:r,timelineOptions:o,mediaDefault:e,mediaList:a}}.call(this,t,e,!i);this._newBaseOption=n.baseOption,i?(function(r,t){qu(t=t||{},function(t,e){if(null!=t){var i=r[e];if(vu.hasClass(e)){t=Lr(t);var n=Nr(i=Lr(i),t);r[e]=Ku(n,function(t){return t.option&&t.exist?Qu(t.exist,t.option,!0):t.exist||t.option})}else r[e]=Qu(i,t,!0)}})}(i.baseOption,n.baseOption),n.timelineOptions.length&&(i.timelineOptions=n.timelineOptions),n.mediaList.length&&(i.mediaList=n.mediaList),n.mediaDefault&&(i.mediaDefault=n.mediaDefault)):this._optionBackup=n},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=Ku(e.timelineOptions,$u),this._mediaList=Ku(e.mediaList,$u),this._mediaDefault=$u(e.mediaDefault),this._currentMediaIndices=[],$u(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=$u(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(t){var e=this._api.getWidth(),i=this._api.getHeight(),n=this._mediaList,r=this._mediaDefault,o=[],a=[];if(!n.length&&!r)return a;for(var s=0,l=n.length;s<l;s++)eh(n[s].query,e,i)&&o.push(s);return!o.length&&r&&(o=[-1]),o.length&&!function(t,e){return t.join(",")===e.join(",")}(o,this._currentMediaIndices)&&(a=Ku(o,function(t){return $u(-1===t?r.option:n[t].option)})),this._currentMediaIndices=o,a}};var ih=D,nh=N,rh=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function oh(t){var e=t&&t.itemStyle;if(e)for(var i=0,n=rh.length;i<n;i++){var r=rh[i],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?m(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?m(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function ah(t,e,i){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var n=t[e].normal,r=t[e].emphasis;n&&(i?(t[e].normal=t[e].emphasis=null,A(t[e],n)):t[e]=n),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function sh(t){ah(t,"itemStyle"),ah(t,"lineStyle"),ah(t,"areaStyle"),ah(t,"label"),ah(t,"labelLine"),ah(t,"upperLabel"),ah(t,"edgeLabel")}function lh(t,e){var i=nh(t)&&t[e],n=nh(i)&&i.textStyle;if(n)for(var r=0,o=zr.length;r<o;r++){e=zr[r];n.hasOwnProperty(e)&&(i[e]=n[e])}}function uh(t){t&&(sh(t),lh(t,"label"),t.emphasis&&lh(t.emphasis,"label"))}function hh(t){return O(t)?t:t?[t]:[]}function ch(t){return(O(t)?t[0]:t)||{}}function dh(e,t){ih(hh(e.series),function(t){nh(t)&&function(t){if(nh(t)){oh(t),sh(t),lh(t,"label"),lh(t,"upperLabel"),lh(t,"edgeLabel"),t.emphasis&&(lh(t.emphasis,"label"),lh(t.emphasis,"upperLabel"),lh(t.emphasis,"edgeLabel")),(i=t.markPoint)&&(oh(i),uh(i)),(n=t.markLine)&&(oh(n),uh(n));var e=t.markArea;e&&uh(e);var i,n,r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!B(o))for(var a=0;a<o.length;a++)uh(o[a]);D(t.categories,function(t){sh(t)})}if(r&&!B(r))for(a=0;a<r.length;a++)uh(r[a]);if((i=t.markPoint)&&i.data){var s=i.data;for(a=0;a<s.length;a++)uh(s[a])}if((n=t.markLine)&&n.data){var l=n.data;for(a=0;a<l.length;a++)O(l[a])?(uh(l[a][0]),uh(l[a][1])):uh(l[a])}"gauge"===t.type?(lh(t,"axisLabel"),lh(t,"title"),lh(t,"detail")):"treemap"===t.type?(ah(t.breadcrumb,"itemStyle"),D(t.levels,function(t){sh(t)})):"tree"===t.type&&sh(t.leaves)}}(t)});var i=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&i.push("valueAxis","categoryAxis","logAxis","timeAxis"),ih(i,function(t){ih(hh(e[t]),function(t){t&&(lh(t,"axisLabel"),lh(t.axisPointer,"label"))})}),ih(hh(e.parallel),function(t){var e=t&&t.parallelAxisDefault;lh(e,"axisLabel"),lh(e&&e.axisPointer,"label")}),ih(hh(e.calendar),function(t){ah(t,"itemStyle"),lh(t,"dayLabel"),lh(t,"monthLabel"),lh(t,"yearLabel")}),ih(hh(e.radar),function(t){lh(t,"name")}),ih(hh(e.geo),function(t){nh(t)&&(uh(t),ih(hh(t.regions),function(t){uh(t)}))}),ih(hh(e.timeline),function(t){uh(t),ah(t,"label"),ah(t,"itemStyle"),ah(t,"controlStyle",!0);var e=t.data;O(e)&&D(e,function(t){N(t)&&(ah(t,"label"),ah(t,"itemStyle"))})}),ih(hh(e.toolbox),function(t){ah(t,"iconStyle"),ih(t.feature,function(t){ah(t,"iconStyle")})}),lh(ch(e.axisPointer),"label"),lh(ch(e.tooltip).axisPointer,"label")}function fh(e){D(ph,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var ph=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],gh=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],mh=function(i,t){dh(i,t),i.series=Lr(i.series),D(i.series,function(t){if(N(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e)null!=t.clockWise&&(t.clockwise=t.clockWise);else if("gauge"===e){var i=function(t,e){e=e.split(",");for(var i=t,n=0;n<e.length&&null!=(i=i&&i[e[n]]);n++);return i}(t,"pointer.color");null!=i&&function(t,e,i,n){e=e.split(",");for(var r,o=t,a=0;a<e.length-1;a++)null==o[r=e[a]]&&(o[r]={}),o=o[r];!n&&null!=o[e[a]]||(o[e[a]]=i)}(t,"itemStyle.color",i)}fh(t)}}),i.dataRange&&(i.visualMap=i.dataRange),D(gh,function(t){var e=i[t];e&&(O(e)||(e=[e]),D(e,function(t){fh(t)}))})};function vh(m){D(m,function(h,c){var d=[],f=[NaN,NaN],t=[h.stackResultDimension,h.stackedOverDimension],p=h.data,g=h.isStackedByIndex,e=p.map(t,function(t,e,i){var n,r,o=p.get(h.stackedDimension,i);if(isNaN(o))return f;g?r=p.getRawIndex(i):n=p.get(h.stackedByDimension,i);for(var a=NaN,s=c-1;0<=s;s--){var l=m[s];if(g||(r=l.data.rawIndexOf(l.stackedByDimension,n)),0<=r){var u=l.data.getByRawIndex(l.stackResultDimension,r);if(0<=o&&0<u||o<=0&&u<0){o+=u,a=u;break}}}return d[0]=o,d[1]=a,d});p.hostModel.setData(e),h.data=e})}function yh(t,e){Pu.isInstance(t)||(t=Pu.seriesDataToSource(t)),this._source=t;var i=this._data=t.data,n=t.sourceFormat;n===Au&&(this._offset=0,this._dimSize=e,this._data=i),k(this,xh[n===Mu?n+"_"+t.seriesLayoutBy:n])}var _h=yh.prototype;_h.pure=!1;var xh={arrayRows_column:{pure:_h.persistent=!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:Sh},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],i=this._data,n=0;n<i.length;n++){var r=i[n];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:wh,getItem:bh,appendData:Sh},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],i=this._source.dimensionsDefine,n=0;n<i.length;n++){var r=this._data[i[n].name];e.push(r?r[t]:null)}return e},appendData:function(t){var r=this._data;D(t,function(t,e){for(var i=r[e]||(r[e]=[]),n=0;n<(t||[]).length;n++)i.push(t[n])})}},original:{count:wh,getItem:bh,appendData:Sh},typedArray:{persistent:!(_h.getSource=function(){return this._source}),pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var i=this._dimSize*t,n=0;n<this._dimSize;n++)e[n]=this._data[i+n];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}};function wh(){return this._data.length}function bh(t){return this._data[t]}function Sh(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}var Mh={arrayRows:Ih,objectRows:function(t,e,i,n){return null!=i?t[n]:t},keyedColumns:Ih,original:function(t,e,i,n){var r=Er(t);return null!=i&&r instanceof Array?r[i]:r},typedArray:Ih};function Ih(t,e,i,n){return null!=i?t[i]:t}var Ch={arrayRows:Th,objectRows:function(t,e,i,n){return Ah(t[e],this._dimensionInfos[e])},keyedColumns:Th,original:function(t,e,i,n){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&function(t){return Dr(t)&&!(t instanceof Array)}(t)&&(this.hasItemOption=!0),Ah(r instanceof Array?r[n]:r,this._dimensionInfos[e])},typedArray:function(t,e,i,n){return t[n]}};function Th(t,e,i,n){return Ah(t[n],this._dimensionInfos[e])}function Ah(t,e){var i=e&&e.type;if("ordinal"!==i)return"time"===i&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+Nl(t)),null==t||""===t?NaN:+t;var n=e&&e.ordinalMeta;return n?n.parseAndCollect(t):t}function Dh(t,e,i){if(t){var n=t.getRawDataItem(e);if(null!=n){var r,o,a=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(i);return s&&(r=s.name,o=s.index),Mh[a](n,e,o,r)}}}function kh(t,e,i){if(t){var n=t.getProvider().getSource().sourceFormat;if(n===Su||n===Iu){var r=t.getRawDataItem(e);return n!==Su||N(r)||(r=null),r?r[i]:void 0}}}var Ph=/\{@(.+?)\}/g,Lh={getDataParams:function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),r=i.getRawIndex(t),o=i.getName(t),a=i.getRawDataItem(t),s=i.getItemVisual(t,"color"),l=i.getItemVisual(t,"borderColor"),u=this.ecModel.getComponent("tooltip"),h=Xr(u&&u.get("renderMode")),c=this.mainType,d="series"===c,f=i.userOutput;return{componentType:c,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:d?this.subType:null,seriesIndex:this.seriesIndex,seriesId:d?this.id:null,seriesName:d?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:n,color:s,borderColor:l,dimensionNames:f?f.dimensionNames:null,encode:f?f.encode:null,marker:$l({color:s,renderMode:h}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(n,t,e,i,r){t=t||"normal";var o=this.getData(e),a=o.getItemModel(n),s=this.getDataParams(n,e);null!=i&&s.value instanceof Array&&(s.value=s.value[i]);var l=a.get("normal"===t?[r||"label","formatter"]:[t,r||"label","formatter"]);return"function"==typeof l?(s.status=t,s.dimensionIndex=i,l(s)):"string"==typeof l?ql(l,s).replace(Ph,function(t,e){var i=e.length;return"["===e.charAt(0)&&"]"===e.charAt(i-1)&&(e=+e.slice(1,i-1)),Dh(o,n,e)}):void 0},getRawValue:function(t,e){return Dh(this.getData(e),t)},formatTooltip:function(){}};function Oh(t){return new zh(t)}function zh(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}var Eh=zh.prototype;Eh.perform=function(t){var e,i=this._upstream,n=t&&t.skip;if(this._dirty&&i){var r=this.context;r.data=r.outputData=i.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!n&&(e=this._plan(this.context));var o,a=h(this._modBy),s=this._modDataCount||0,l=h(t&&t.modBy),u=t&&t.modDataCount||0;function h(t){return 1<=t||(t=1),t}a===l&&s===u||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,o=function(t,e){var i,n;t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null,!e&&t._reset&&((i=t._reset(t.context))&&i.progress&&(n=i.forceFirstProgress,i=i.progress),O(i)&&!i.length&&(i=null));t._progress=i,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),n}(this,n)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!n&&(o||d<f)){var p=this._progress;if(O(p))for(var g=0;g<p.length;g++)Uh(this,p[g],d,f,l,u);else Uh(this,p,d,f,l,u)}this._dueIndex=f;var m=null!=this._settedOutputEnd?this._settedOutputEnd:f;this._outputDueEnd=m}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var Nh,Rh,Bh,Vh,Fh,Hh,Wh=Hh={reset:function(t,e,i,n){Rh=t,Nh=e,Bh=i,Vh=n,Fh=Math.ceil(Vh/Bh),Hh.next=1<Bh&&0<Vh?Zh:Gh}};function Gh(){return Rh<Nh?Rh++:null}function Zh(){var t=Rh%Fh*Bh+Math.ceil(Rh/Fh),e=Nh<=Rh?null:t<Vh?t:Rh;return Rh++,e}function Uh(t,e,i,n,r,o){Wh.reset(i,n,r,o),t._callingProgress=e,t._callingProgress({start:i,end:n,count:n-i,next:Wh.next},t.context)}Eh.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},Eh.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},Eh.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},Eh.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},Eh.getUpstream=function(){return this._upstream},Eh.getDownstream=function(){return this._downstream},Eh.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Xh=Hr(),Yh=vu.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,i,n){this.seriesIndex=this.componentIndex,this.dataTask=Oh({count:qh,reset:$h}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,i),zu(this);var r=this.getInitialData(t,i);Qh(r,this),this.dataTask.context.data=r,Xh(this).dataBeforeProcessed=r,jh(this)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?cu(t):{},r=this.subType;vu.hasClass(r)&&(r+="Series"),m(t,e.getTheme().get(this.subType)),m(t,this.getDefaultOption()),Or(t,"label",["show"]),this.fillDataTextStyle(t.data),i&&hu(t,n,i)},mergeOption:function(t,e){t=m(this.option,t,!0),this.fillDataTextStyle(t.data);var i=this.layoutMode;i&&hu(this.option,t,i),zu(this);var n=this.getInitialData(t,e);Qh(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,Xh(this).dataBeforeProcessed=n,jh(this)},fillDataTextStyle:function(t){if(t&&!B(t))for(var e=["show"],i=0;i<t.length;i++)t[i]&&t[i].label&&Or(t[i],"label",e)},getInitialData:function(){},appendData:function(t){this.getRawData().appendData(t.data)},getData:function(t){var e=tc(this);if(e){var i=e.context.data;return null==t?i:i.getLinkedData(t)}return Xh(this).data},setData:function(t){var e=tc(this);if(e){var i=e.context;i.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),i.outputData=t,e!==this.dataTask&&(i.data=t)}Xh(this).data=t},getSource:function(){return function(t){return Ou(t).source}(this)},getRawData:function(){return Xh(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(r,h,t,c){var d=this,e="html"===(c=c||"html")?"<br/>":"\n",f="richText"===c,p={},g=0;function i(t){return{renderMode:c,content:Xl(Hl(t)),style:p}}var m=this.getData(),o=m.mapDimension("defaultedTooltip",!0),n=o.length,a=this.getRawValue(r),s=O(a),v=m.getItemVisual(r,"color");N(v)&&v.colorStops&&(v=(v.colorStops[0]||{}).color),v=v||"transparent";var l=(1<n||s&&!n?function(t){var l=M(t,function(t,e,i){var n=m.getDimensionInfo(i);return t|(n&&!1!==n.tooltip&&null!=n.displayName)},0),u=[];function e(t,e){var i=m.getDimensionInfo(e);if(i&&!1!==i.otherDims.tooltip){var n=i.type,r="sub"+d.seriesIndex+"at"+g,o=$l({color:v,type:"subItem",renderMode:c,markerId:r}),a="string"==typeof o?o:o.content,s=(l?a+Xl(i.displayName||"-")+": ":"")+Xl("ordinal"===n?t+"":"time"===n?h?"":Ql("yyyy/MM/dd hh:mm:ss",t):Hl(t));s&&u.push(s),f&&(p[r]=v,++g)}}o.length?D(o,function(t){e(Dh(m,r,t),t)}):D(t,e);var i=l?f?"\n":"<br/>":"",n=i+u.join(i||", ");return{renderMode:c,content:n,style:p}}(a):i(n?Dh(m,r,o[0]):s?a[0]:a)).content,u=d.seriesIndex+"at"+g,y=$l({color:v,type:"item",renderMode:c,markerId:u});p[u]=v,++g;var _=m.getName(r),x=this.name;Br(this)||(x=""),x=x?Xl(x)+(h?": ":e):"";var w="string"==typeof y?y:y.content;return{html:h?w+x+l:x+w+(_?Xl(_)+": "+l:l),markers:p}},isAnimationEnabled:function(){if(v.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,i){var n=this.ecModel,r=bu.getColorFromPalette.call(this,t,e,i);return r=r||n.getColorFromPalette(t,e,i)},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});function jh(t){var e=t.name;Br(t)||(t.name=function(t){var i=t.getRawData(),e=i.mapDimension("seriesName",!0),n=[];return D(e,function(t){var e=i.getDimensionInfo(t);e.displayName&&n.push(e.displayName)}),n.join(" ")}(t)||e)}function qh(t){return t.model.getRawData().count()}function $h(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),Kh}function Kh(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Qh(e,i){D(e.CHANGABLE_METHODS,function(t){e.wrapMethod(t,T(Jh,i))})}function Jh(t){var e=tc(t);e&&e.setOutputEnd(this.count())}function tc(t){var e=(t.ecModel||{}).scheduler,i=e&&e.getPipeline(t.uid);if(i){var n=i.currentTask;if(n){var r=n.agentStubMap;r&&(n=r.get(t.uid))}return n}}S(Yh,Lh),S(Yh,bu);var ec=function(){this.group=new Si,this.uid=Sl("viewComponent")};ec.prototype={constructor:ec,init:function(t,e){},render:function(t,e,i,n){},dispose:function(){},filterForExposedEvent:null};var ic=ec.prototype;ic.updateView=ic.updateLayout=ic.updateVisual=function(t,e,i,n){},$r(ec),eo(ec,{registerWhenExtend:!0});function nc(){var s=Hr();return function(t){var e=s(t),i=t.pipelineContext,n=e.large,r=e.progressiveRender,o=e.large=i&&i.large,a=e.progressiveRender=i&&i.progressiveRender;return!!(n^o||r^a)&&"reset"}}var rc=Hr(),oc=nc();function ac(){this.group=new Si,this.uid=Sl("viewChart"),this.renderTask=Oh({plan:hc,reset:cc}),this.renderTask.context={view:this}}var sc=ac.prototype={type:"chart",init:function(t,e){},render:function(t,e,i,n){},highlight:function(t,e,i,n){uc(t.getData(),n,"emphasis")},downplay:function(t,e,i,n){uc(t.getData(),n,"normal")},remove:function(t,e){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};function lc(t,e,i){if(t&&(t.trigger(e,i),t.isGroup&&!Us(t)))for(var n=0,r=t.childCount();n<r;n++)lc(t.childAt(n),e,i)}function uc(e,t,i){var n=Fr(e,t),r=t&&null!=t.highlightKey?Xs(t.highlightKey):null;null!=n?D(Lr(n),function(t){lc(e.getItemGraphicEl(t),i,r)}):e.eachItemGraphicEl(function(t){lc(t,i,r)})}function hc(t){return oc(t.model)}function cc(t){var e=t.model,i=t.ecModel,n=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&rc(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,i,n,r),dc[l]}sc.updateView=sc.updateLayout=sc.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},$r(ac),eo(ac,{registerWhenExtend:!0}),ac.markUpdateMethod=function(t,e){rc(t).updateMethod=e};var dc={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},fc="\0__throttleOriginMethod",pc="\0__throttleRate",gc="\0__throttleType";function mc(t,i,n){var r,o,a,s,l,u=0,h=0,c=null;function d(){h=(new Date).getTime(),c=null,t.apply(a,s||[])}i=i||0;function e(){r=(new Date).getTime(),a=this,s=arguments;var t=l||i,e=l||n;l=null,o=r-(e?u:h)-t,clearTimeout(c),e?c=setTimeout(d,t):0<=o?d():c=setTimeout(d,-o),u=r}return e.clear=function(){c&&(clearTimeout(c),c=null)},e.debounceNextCall=function(t){l=t},e}function vc(t,e,i,n){var r=t[e];if(r){var o=r[fc]||r,a=r[gc];if(r[pc]!==i||a!==n){if(null==i||!n)return t[e]=o;(r=t[e]=mc(o,i,"debounce"===n))[fc]=o,r[gc]=n,r[pc]=i}return r}}function yc(t,e){var i=t[e];i&&i[fc]&&(t[e]=i[fc])}var _c={createOnAllSeries:!0,performRawSeries:!0,reset:function(e,t){var i=e.getData(),o=(e.visualColorAccessPath||"itemStyle.color").split("."),n=e.get(o),r=!z(n)||n instanceof rs?null:n;n&&!r||(n=e.getColorFromPalette(e.name,null,t.getSeriesCount())),i.setVisual("color",n);var a=(e.visualBorderColorAccessPath||"itemStyle.borderColor").split("."),s=e.get(a);if(i.setVisual("borderColor",s),!t.isSeriesFiltered(e)){r&&i.each(function(t){i.setItemVisual(t,"color",r(e.getDataParams(t)))});return{dataEach:i.hasItemOption?function(t,e){var i=t.getItemModel(e),n=i.get(o,!0),r=i.get(a,!0);null!=n&&t.setItemVisual(e,"color",n),null!=r&&t.setItemVisual(e,"borderColor",r)}:null}}}},xc={legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},wc=function(t,e){var o=e.getModel("aria");if(o.get("show"))if(o.get("description"))t.setAttribute("aria-label",o.get("description"));else{var h=0;e.eachSeries(function(t,e){++h},this);var i,c=o.get("data.maxCount")||10,n=o.get("series.maxCount")||10,d=Math.min(h,n);if(!(h<1)){var r=function(){var t=e.getModel("title").option;t&&t.length&&(t=t[0]);return t&&t.text}();i=r?p(g("general.withTitle"),{title:r}):g("general.withoutTitle");var f=[];i+=p(g(1<h?"series.multiple.prefix":"series.single.prefix"),{seriesCount:h}),e.eachSeries(function(t,e){if(e<d){var i,n=t.get("name"),r="series."+(1<h?"multiple":"single")+".";i=p(i=g(n?r+"withName":r+"withoutName"),{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:function(t){return xc.series.typeNames[t]||"自定义图"}(t.subType)});var o=t.getData();(window.data=o).count()>c?i+=p(g("data.partialData"),{displayCnt:c}):i+=g("data.allData");for(var a=[],s=0;s<o.count();s++)if(s<c){var l=o.getName(s),u=Dh(o,s);a.push(p(g(l?"data.withName":"data.withoutName"),{name:l,value:u}))}i+=a.join(g("data.separator.middle"))+g("data.separator.end"),f.push(i)}}),i+=f.join(g("series.multiple.separator.middle"))+g("series.multiple.separator.end"),t.setAttribute("aria-label",i)}}function p(t,e){if("string"!=typeof t)return t;var i=t;return D(e,function(t,e){i=i.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),i}function g(t){var e=o.get(t);if(null!=e)return e;for(var i=t.split("."),n=xc.aria,r=0;r<i.length;++r)n=n[i[r]];return n}},bc=Math.PI;function Sc(t,e,i,n){this.ecInstance=t,this.api=e,this.unfinished;i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice();this._allHandlers=i.concat(n),this._stageTaskMap=Q()}var Mc=Sc.prototype;function Ic(l,t,u,h,c){var d;function f(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}c=c||{},D(t,function(n,t){if(!c.visualType||c.visualType===n.visualType){var e=l._stageTaskMap.get(n.uid),i=e.seriesTaskMap,r=e.overallTask;if(r){var o,a=r.agentStubMap;a.each(function(t){f(c,t)&&(t.dirty(),o=!0)}),o&&r.dirty(),Cc(r,h);var s=l.getPerformArgs(r,c.block);a.each(function(t){t.perform(s)}),d|=r.perform(s)}else i&&i.each(function(t,e){f(c,t)&&t.dirty();var i=l.getPerformArgs(t,c.block);i.skip=!n.performRawSeries&&u.isSeriesFiltered(t.context.model),Cc(t,h),d|=t.perform(i)})}}),l.unfinished|=d}Mc.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},Mc.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,r=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex?i.step:null,o=n&&n.modDataCount;return{step:r,modBy:null!=o?Math.ceil(o/r):null,modDataCount:o}}},Mc.getPipeline=function(t){return this._pipelineMap.get(t)},Mc.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData().count(),r=i.progressiveEnabled&&e.incrementalPrepareRender&&n>=i.threshold,o=t.get("large")&&n>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?n:null;t.pipelineContext=i.context={progressiveRender:r,modDataCount:a,large:o}},Mc.restorePipelines=function(t){var n=this,r=n._pipelineMap=Q();t.eachSeries(function(t){var e=t.getProgressive(),i=t.uid;r.set(i,{id:i,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),Nc(n,t,t.dataTask)})},Mc.prepareStageTasks=function(){var i=this._stageTaskMap,n=this.ecInstance.getModel(),r=this.api;D(this._allHandlers,function(t){var e=i.get(t.uid)||i.set(t.uid,[]);t.reset&&function(n,r,t,o,a){var s=t.seriesTaskMap||(t.seriesTaskMap=Q()),e=r.seriesType,i=r.getTargetSeries;r.createOnAllSeries?o.eachRawSeries(l):e?o.eachRawSeriesByType(e,l):i&&i(o,a).each(l);function l(t){var e=t.uid,i=s.get(e)||s.set(e,Oh({plan:Pc,reset:Lc,count:Ec}));i.context={model:t,ecModel:o,api:a,useClearVisual:r.isVisual&&!r.isLayout,plan:r.plan,reset:r.reset,scheduler:n},Nc(n,t,i)}var u=n._pipelineMap;s.each(function(t,e){u.get(e)||(t.dispose(),s.removeKey(e))})}(this,t,e,n,r),t.overallReset&&function(n,t,e,i,r){var o=e.overallTask=e.overallTask||Oh({reset:Tc});o.context={ecModel:i,api:r,overallReset:t.overallReset,scheduler:n};var a=o.agentStubMap=o.agentStubMap||Q(),s=t.seriesType,l=t.getTargetSeries,u=!0,h=t.modifyOutputEnd;s?i.eachRawSeriesByType(s,c):l?l(i,r).each(c):(u=!1,D(i.getSeries(),c));function c(t){var e=t.uid,i=a.get(e);i||(i=a.set(e,Oh({reset:Ac,onDirty:kc})),o.dirty()),i.context={model:t,overallProgress:u,modifyOutputEnd:h},i.agent=o,i.__block=u,Nc(n,t,i)}var d=n._pipelineMap;a.each(function(t,e){d.get(e)||(t.dispose(),o.dirty(),a.removeKey(e))})}(this,t,e,n,r)},this)},Mc.prepareView=function(t,e,i,n){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=i,o.api=n,r.__block=!t.incrementalPrepareRender,Nc(this,e,r)},Mc.performDataProcessorTasks=function(t,e){Ic(this,this._dataProcessorHandlers,t,e,{block:!0})},Mc.performVisualTasks=function(t,e,i){Ic(this,this._visualHandlers,t,e,i)},Mc.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},Mc.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var Cc=Mc.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)};function Tc(t){t.overallReset(t.ecModel,t.api,t.payload)}function Ac(t,e){return t.overallProgress&&Dc}function Dc(){this.agent.dirty(),this.getDownstream().dirty()}function kc(){this.agent&&this.agent.dirty()}function Pc(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function Lc(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=Lr(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<e.length?P(e,function(t,e){return zc(e)}):Oc}var Oc=zc(0);function zc(o){return function(t,e){var i=e.data,n=e.resetDefines[o];if(n&&n.dataEach)for(var r=t.start;r<t.end;r++)n.dataEach(i,r);else n&&n.progress&&n.progress(t,i)}}function Ec(t){return t.data.count()}function Nc(t,e,i){var n=e.uid,r=t._pipelineMap.get(n);r.head||(r.head=i),r.tail&&r.tail.pipe(i),(r.tail=i).__idxInPipeline=r.count++,i.__pipeline=r}Sc.wrapStageHandler=function(t,e){return z(t)&&(t={overallReset:t,seriesType:function(t){Rc=null;try{t(Bc,Vc)}catch(t){}return Rc}(t)}),t.uid=Sl("stageHandler"),e&&(t.visualType=e),t};var Rc,Bc={},Vc={};function Fc(t,e){for(var i in e.prototype)t[i]=J}Fc(Bc,Wu),Fc(Vc,Xu),Bc.eachSeriesByType=Bc.eachRawSeriesByType=function(t){Rc=t},Bc.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Rc=t.subType)};function Hc(){return{axisLine:{lineStyle:{color:Zc}},axisTick:{lineStyle:{color:Zc}},axisLabel:{textStyle:{color:Zc}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:Zc}}}}var Wc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Gc={color:Wc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Wc]},Zc="#eee",Uc=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],Xc={color:Uc,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:Zc},crossStyle:{color:Zc},label:{color:"#000"}}},legend:{textStyle:{color:Zc}},textStyle:{color:Zc},title:{textStyle:{color:Zc}},toolbox:{iconStyle:{normal:{borderColor:Zc}}},dataZoom:{textStyle:{color:Zc}},visualMap:{textStyle:{color:Zc}},timeline:{lineStyle:{color:Zc},itemStyle:{normal:{color:Uc[1]}},label:{normal:{textStyle:{color:Zc}}},controlStyle:{normal:{color:Zc,borderColor:Zc}}},timeAxis:Hc(),logAxis:Hc(),valueAxis:Hc(),categoryAxis:Hc(),line:{symbol:"circle"},graph:{color:Uc},gauge:{title:{textStyle:{color:Zc}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};Xc.categoryAxis.splitLine.show=!1,vu.extend({type:"dataset",defaultOption:{seriesLayoutBy:Du,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){!function(t){var e=t.option.source,i=Tu;if(B(e))i=Au;else if(O(e)){0===e.length&&(i=Mu);for(var n=0,r=e.length;n<r;n++){var o=e[n];if(null!=o){if(O(o)){i=Mu;break}if(N(o)){i=Iu;break}}}}else if(N(e)){for(var a in e)if(e.hasOwnProperty(a)&&L(e[a])){i=Cu;break}}else if(null!=e)throw new Error("Invalid data");Ou(t).sourceFormat=i}(this)}}),ec.extend({type:"dataset"});var Yc=xa.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.rx,o=e.ry,a=.5522848*r,s=.5522848*o;t.moveTo(i-r,n),t.bezierCurveTo(i-r,n-s,i-a,n-o,i,n-o),t.bezierCurveTo(i+a,n-o,i+r,n-s,i+r,n),t.bezierCurveTo(i+r,n+s,i+a,n+o,i,n+o),t.bezierCurveTo(i-a,n+o,i-r,n+s,i-r,n),t.closePath()}}),jc=/[\s,]+/;function qc(t){E(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));for(9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}var $c={g:function(t,e){var i=new Si;return Qc(e,i),ed(t,i,this._defs),i},rect:function(t,e){var i=new Ja;return Qc(e,i),ed(t,i,this._defs),i.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),i},circle:function(t,e){var i=new Fa;return Qc(e,i),ed(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),i},line:function(t,e){var i=new es;return Qc(e,i),ed(t,i,this._defs),i.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),i},ellipse:function(t,e){var i=new Yc;return Qc(e,i),ed(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),i},polygon:function(t,e){var i=t.getAttribute("points");i=i&&Jc(i);var n=new Xa({shape:{points:i||[]}});return Qc(e,n),ed(t,n,this._defs),n},polyline:function(t,e){var i=new xa;Qc(e,i),ed(t,i,this._defs);var n=t.getAttribute("points");return n=n&&Jc(n),new Ya({shape:{points:n||[]}})},image:function(t,e){var i=new Yn;return Qc(e,i),ed(t,i,this._defs),i.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),i},text:function(t,e){var i=t.getAttribute("x")||0,n=t.getAttribute("y")||0,r=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0;this._textX=parseFloat(i)+parseFloat(r),this._textY=parseFloat(n)+parseFloat(o);var a=new Si;return Qc(e,a),ed(t,a,this._defs),a},tspan:function(t,e){var i=t.getAttribute("x"),n=t.getAttribute("y");null!=i&&(this._textX=parseFloat(i)),null!=n&&(this._textY=parseFloat(n));var r=t.getAttribute("dx")||0,o=t.getAttribute("dy")||0,a=new Si;return Qc(e,a),ed(t,a,this._defs),this._textX+=r,this._textY+=o,a},path:function(t,e){var i=Ra(t.getAttribute("d")||"");return Qc(e,i),ed(t,i,this._defs),i}},Kc={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),i=parseInt(t.getAttribute("y1")||0,10),n=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),o=new ls(e,i,n,r);return function(t,e){var i=t.firstChild;for(;i;){if(1===i.nodeType){var n=i.getAttribute("offset");n=0<n.indexOf("%")?parseInt(n,10)/100:n?parseFloat(n):0;var r=i.getAttribute("stop-color")||"#000000";e.addColorStop(n,r)}i=i.nextSibling}}(t,o),o},radialgradient:function(t){}};function Qc(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),A(e.__inheritedStyle,t.__inheritedStyle))}function Jc(t){for(var e=Y(t).split(jc),i=[],n=0;n<e.length;n+=2){var r=parseFloat(e[n]),o=parseFloat(e[n+1]);i.push([r,o])}return i}var td={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"};function ed(t,e,i,n){var r=e.__inheritedStyle||{},o="text"===e.type;if(1===t.nodeType&&(function(t,e){var i=t.getAttribute("transform");if(i){i=i.replace(/,/g," ");var n=null,r=[];i.replace(rd,function(t,e,i){r.push(e,i)});for(var o=r.length-1;0<o;o-=2){var a=r[o],s=r[o-1];switch(n=n||ee(),s){case"translate":a=Y(a).split(jc),oe(n,n,[parseFloat(a[0]),parseFloat(a[1]||0)]);break;case"scale":a=Y(a).split(jc),se(n,n,[parseFloat(a[0]),parseFloat(a[1]||a[0])]);break;case"rotate":a=Y(a).split(jc),ae(n,n,parseFloat(a[0]));break;case"skew":a=Y(a).split(jc),console.warn("Skew transform is not supported yet");break;case"matrix":a=Y(a).split(jc);n[0]=parseFloat(a[0]),n[1]=parseFloat(a[1]),n[2]=parseFloat(a[2]),n[3]=parseFloat(a[3]),n[4]=parseFloat(a[4]),n[5]=parseFloat(a[5])}}e.setLocalTransform(n)}}(t,e),k(r,function(t){var e=t.getAttribute("style"),i={};if(!e)return i;var n,r={};od.lastIndex=0;for(;null!=(n=od.exec(e));)r[n[1]]=n[2];for(var o in td)td.hasOwnProperty(o)&&null!=r[o]&&(i[td[o]]=r[o]);return i}(t)),!n))for(var a in td)if(td.hasOwnProperty(a)){var s=t.getAttribute(a);null!=s&&(r[td[a]]=s)}var l=o?"textFill":"fill",u=o?"textStroke":"stroke";e.style=e.style||new Vi;var h=e.style;null!=r.fill&&h.set(l,nd(r.fill,i)),null!=r.stroke&&h.set(u,nd(r.stroke,i)),D(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&o?"textStrokeWidth":t;null!=r[t]&&h.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),D(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&h.set(t,r[t])}),r.lineDash&&(e.style.lineDash=Y(r.lineDash).split(jc)),h[u]&&"none"!==h[u]&&(e[u]=!0),e.__inheritedStyle=r}var id=/url\(\s*#(.*?)\)/;function nd(t,e){var i=e&&t&&t.match(id);return i?e[Y(i[1])]:t}var rd=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g;var od=/([^\s:;]+)\s*:\s*([^:;]+)/g;var ad=Q(),sd=function(t,e,i){var n;return D(n=O(e)?e:e.svg?[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(i=e.specialAreas,e=e.geoJson),[{type:"geoJSON",source:e,specialAreas:i}]),function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON"),(0,ud[e])(t)}),ad.set(t,n)},ld=function(t){return ad.get(t)},ud={geoJSON:function(t){var e=t.source;t.geoJSON=E(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=qc(t.source)}},hd=X,cd=D,dd=z,fd=N,pd=vu.parseClassType,gd={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:3500,COMPONENT:4e3,BRUSH:5e3}},md="__flagInMainProcess",vd="__optionUpdated",yd=/^[a-zA-Z0-9_]+$/;function _d(n,r){return function(t,e,i){!r&&this._disposed||(t=t&&t.toLowerCase(),It.prototype[n].call(this,t,e,i))}}function xd(){It.call(this)}function wd(t,e,i){i=i||{},"string"==typeof e&&(e=Ud[e]),this.id,this.group,this._dom=t;var n=this._zr=Mr(t,{renderer:i.renderer||"canvas",devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height});this._throttledZrFlush=mc(C(n.flush,n),17),(e=b(e))&&mh(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new ju;var r=this._api=function(i){var t=i._coordSysMgr;return k(new Xu(i),{getCoordinateSystems:C(t.getCoordinateSystems,t),getComponentByElement:function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return i._model.getComponent(e.mainType,e.index);t=t.parent}}})}(this);function o(t,e){return t.__prio-e.__prio}Pi(Zd,o),Pi(Hd,o),this._scheduler=new Sc(this,r,Hd,Zd),It.call(this,this._ecEventProcessor=new Bd),this._messageCenter=new xd,this._initEvents(),this.resize=C(this.resize,this),this._pendingActions=[],n.animation.on("frame",this._onframe,this),function(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[vd]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}(n,this),q(this)}xd.prototype.on=_d("on",!0),xd.prototype.off=_d("off",!0),xd.prototype.one=_d("one",!0),S(xd,It);var bd=wd.prototype;function Sd(t,e,i){if(!this._disposed){var n,r=this._model,o=this._coordSysMgr.getCoordinateSystems();e=Gr(r,e);for(var a=0;a<o.length;a++){var s=o[a];if(s[t]&&null!=(n=s[t](r,e,i)))return n}}}bd._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[vd]){var e=this[vd].silent;this[md]=!0,Id(this),Md.update.call(this),this[md]=!1,this[vd]=!1,Dd.call(this,e),kd.call(this,e)}else if(t.unfinished){var i=1,n=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(n),t.performDataProcessorTasks(n),Td(this,n),t.performVisualTasks(n),zd(this,this._model,r,"remain"),i-=+new Date-o}while(0<i&&t.unfinished);t.unfinished||this._zr.flush()}}},bd.getDom=function(){return this._dom},bd.getZr=function(){return this._zr},bd.setOption=function(t,e,i){if(!this._disposed){var n;if(fd(e)&&(i=e.lazyUpdate,n=e.silent,e=e.notMerge),this[md]=!0,!this._model||e){var r=new th(this._api),o=this._theme,a=this._model=new Wu;a.scheduler=this._scheduler,a.init(null,null,o,r)}this._model.setOption(t,Wd),i?(this[vd]={silent:n},this[md]=!1):(Id(this),Md.update.call(this),this._zr.flush(),this[vd]=!1,this[md]=!1,Dd.call(this,n),kd.call(this,n))}},bd.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},bd.getModel=function(){return this._model},bd.getOption=function(){return this._model&&this._model.getOption()},bd.getWidth=function(){return this._zr.getWidth()},bd.getHeight=function(){return this._zr.getHeight()},bd.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},bd.getRenderedCanvas=function(t){if(v.canvasSupported)return(t=t||{}).pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor"),this._zr.painter.getRenderedCanvas(t)},bd.getSvgDataURL=function(){if(v.svgSupported){var t=this._zr;return D(t.storage.getDisplayList(),function(t){t.stopAnimation(!0)}),t.painter.toDataURL()}},bd.getDataURL=function(t){if(!this._disposed){var e=(t=t||{}).excludeComponents,i=this._model,n=[],r=this;cd(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(n.push(e),e.group.ignore=!0)})});var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return cd(n,function(t){t.group.ignore=!1}),o}},bd.getConnectedDataURL=function(r){if(!this._disposed&&v.canvasSupported){var o="svg"===r.type,a=this.group,s=Math.min,l=Math.max;if(jd[a]){var u=1/0,h=1/0,c=-1/0,d=-1/0,f=[],i=r&&r.pixelRatio||1;D(Yd,function(t,e){if(t.group===a){var i=o?t.getZr().painter.getSvgDom().innerHTML:t.getRenderedCanvas(b(r)),n=t.getDom().getBoundingClientRect();u=s(n.left,u),h=s(n.top,h),c=l(n.right,c),d=l(n.bottom,d),f.push({dom:i,left:n.left,top:n.top})}});var t=(c*=i)-(u*=i),e=(d*=i)-(h*=i),n=y(),p=Mr(n,{renderer:o?"svg":"canvas"});if(p.resize({width:t,height:e}),o){var g="";return cd(f,function(t){var e=t.left-u,i=t.top-h;g+='<g transform="translate('+e+","+i+')">'+t.dom+"</g>"}),p.painter.getSvgRoot().innerHTML=g,r.connectedBackgroundColor&&p.painter.setBackgroundColor(r.connectedBackgroundColor),p.refreshImmediately(),p.painter.toDataURL()}return r.connectedBackgroundColor&&p.add(new Ja({shape:{x:0,y:0,width:t,height:e},style:{fill:r.connectedBackgroundColor}})),cd(f,function(t){var e=new Yn({style:{x:t.left*i-u,y:t.top*i-h,image:t.dom}});p.add(e)}),p.refreshImmediately(),n.toDataURL("image/"+(r&&r.type||"png"))}return this.getDataURL(r)}},bd.convertToPixel=T(Sd,"convertToPixel"),bd.convertFromPixel=T(Sd,"convertFromPixel"),bd.containPixel=function(t,r){var o;if(!this._disposed)return D(t=Gr(this._model,t),function(t,n){0<=n.indexOf("Models")&&D(t,function(t){var e=t.coordinateSystem;if(e&&e.containPoint)o|=!!e.containPoint(r);else if("seriesModels"===n){var i=this._chartsMap[t.__viewId];i&&i.containPoint&&(o|=i.containPoint(r,t))}},this)},this),!!o},bd.getVisual=function(t,e){var i=(t=Gr(this._model,t,{defaultMainType:"series"})).seriesModel.getData(),n=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?i.indexOfRawIndex(t.dataIndex):null;return null!=n?i.getItemVisual(n,e):i.getVisual(e)},bd.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},bd.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var Md={prepareAndUpdate:function(t){Id(this),Md.update.call(this,t)},update:function(t){var e=this._model,i=this._api,n=this._zr,r=this._coordSysMgr,o=this._scheduler;if(e){o.restoreData(e,t),o.performSeriesTasks(e),r.create(e,i),o.performDataProcessorTasks(e,t),Td(this,e),r.update(e,i),Ld(e),o.performVisualTasks(e,t),Od(this,e,i,t);var a=e.get("backgroundColor")||"transparent";if(v.canvasSupported)n.setBackgroundColor(a);else{var s=Ne(a);a=Ue(s,"rgb"),0===s[3]&&(a="transparent")}Ed(e,i)}},updateTransform:function(r){var o=this._model,a=this,s=this._api;if(o){var l=[];o.eachComponent(function(t,e){var i=a.getViewOfComponentModel(e);if(i&&i.__alive)if(i.updateTransform){var n=i.updateTransform(e,o,s,r);n&&n.update&&l.push(i)}else l.push(i)});var n=Q();o.eachSeries(function(t){var e=a._chartsMap[t.__viewId];if(e.updateTransform){var i=e.updateTransform(t,o,s,r);i&&i.update&&n.set(t.uid,1)}else n.set(t.uid,1)}),Ld(o),this._scheduler.performVisualTasks(o,r,{setDirty:!0,dirtyMap:n}),zd(a,o,s,r,n),Ed(o,this._api)}},updateView:function(t){var e=this._model;e&&(ac.markUpdateMethod(t,"updateView"),Ld(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Od(this,this._model,this._api,t),Ed(e,this._api))},updateVisual:function(t){Md.update.call(this,t)},updateLayout:function(t){Md.update.call(this,t)}};function Id(t){var e=t._model,i=t._scheduler;i.restorePipelines(e),i.prepareStageTasks(),Pd(t,"component",e,i),Pd(t,"chart",e,i),i.plan()}function Cd(e,i,n,r,t){var o=e._model;if(r){var a={};a[r+"Id"]=n[r+"Id"],a[r+"Index"]=n[r+"Index"],a[r+"Name"]=n[r+"Name"];var s={mainType:r,query:a};t&&(s.subType=t);var l=n.excludeSeriesId;null!=l&&(l=Q(Lr(l))),o&&o.eachComponent(s,function(t){l&&null!=l.get(t.id)||u(e["series"===r?"_chartsMap":"_componentsMap"][t.__viewId])},e)}else cd(e._componentsViews.concat(e._chartsViews),u);function u(t){t&&t.__alive&&t[i]&&t[i](t.__model,o,e._api,n)}}function Td(t,e){var i=t._chartsMap,n=t._scheduler;e.eachSeries(function(t){n.updateStreamModes(t,i[t.__viewId])})}function Ad(e,t){var i=e.type,n=e.escapeConnect,r=Vd[i],o=r.actionInfo,a=(o.update||"update").split(":"),s=a.pop();a=null!=a[0]&&pd(a[0]),this[md]=!0;var l=[e],u=!1;e.batch&&(u=!0,l=P(e.batch,function(t){return(t=A(k({},t),e)).batch=null,t}));var h,c=[],d="highlight"===i||"downplay"===i;cd(l,function(t){(h=(h=r.action(t,this._model,this._api))||k({},t)).type=o.event||h.type,c.push(h),d?Cd(this,s,t,"series"):a&&Cd(this,s,t,a.main,a.sub)},this),"none"===s||d||a||(this[vd]?(Id(this),Md.update.call(this,e),this[vd]=!1):Md[s].call(this,e)),h=u?{type:o.event||i,escapeConnect:n,batch:c}:c[0],this[md]=!1,t||this._messageCenter.trigger(h.type,h)}function Dd(t){for(var e=this._pendingActions;e.length;){var i=e.shift();Ad.call(this,i,t)}}function kd(t){t||this.trigger("updated")}function Pd(t,e,r,o){for(var a="component"===e,s=a?t._componentsViews:t._chartsViews,l=a?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,i=0;i<s.length;i++)s[i].__alive=!1;function n(t){var e="_ec_"+t.id+"_"+t.type,i=l[e];if(!i){var n=pd(t.type);(i=new(a?ec.getClass(n.main,n.sub):ac.getClass(n.sub))).init(r,h),l[e]=i,s.push(i),u.add(i.group)}t.__viewId=i.__id=e,i.__alive=!0,i.__model=t,i.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},a||o.prepareView(i,t,r,h)}a?r.eachComponent(function(t,e){"series"!==t&&n(e)}):r.eachSeries(n);for(i=0;i<s.length;){var c=s[i];c.__alive?i++:(a||c.renderTask.dispose(),u.remove(c.group),c.dispose(r,h),s.splice(i,1),delete l[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function Ld(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function Od(t,e,i,n){!function(t,i,n,r,e){cd(e||t._componentsViews,function(t){var e=t.__model;t.render(e,i,n,r),Rd(e,t)})}(t,e,i,n),cd(t._chartsViews,function(t){t.__alive=!1}),zd(t,e,i,n),cd(t._chartsViews,function(t){t.__alive||t.remove(e,i)})}function zd(n,t,e,r,o){var a,s=n._scheduler;t.eachSeries(function(t){var e=n._chartsMap[t.__viewId];e.__alive=!0;var i=e.renderTask;s.updatePayload(i,r),o&&o.get(t.uid)&&i.dirty(),a|=i.perform(s.getPerformArgs(i)),e.group.silent=!!t.get("silent"),Rd(t,e),function(t,e){var i=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.style.blend!==i&&t.setStyle("blend",i),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",i)})})}(t,e)}),s.unfinished|=a,function(i,t){var e=i._zr.storage,n=0;e.traverse(function(t){n++}),n>t.get("hoverLayerThreshold")&&!v.node&&t.eachSeries(function(t){if(!t.preventUsingHoverLayer){var e=i._chartsMap[t.__viewId];e.__alive&&e.group.traverse(function(t){t.useHoverLayer=!0})}})}(n,t),wc(n._zr.dom,t)}function Ed(e,i){cd(Gd,function(t){t(e,i)})}bd.resize=function(t){if(!this._disposed){this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var i=e.resetOption("media"),n=t&&t.silent;this[md]=!0,i&&Id(this),Md.update.call(this),this[md]=!1,Dd.call(this,n),kd.call(this,n)}}},bd.showLoading=function(t,e){if(!this._disposed&&(fd(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Xd[t])){var i=Xd[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)}},bd.hideLoading=function(){this._disposed||(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},bd.makeActionFromEvent=function(t){var e=k({},t);return e.type=Fd[t.type],e},bd.dispatchAction=function(t,e){this._disposed||(fd(e)||(e={silent:!!e}),Vd[t.type]&&this._model&&(this[md]?this._pendingActions.push(t):(Ad.call(this,t,e.silent),e.flush?this._zr.flush(!0):!1!==e.flush&&v.browser.weChat&&this._throttledZrFlush(),Dd.call(this,e.silent),kd.call(this,e.silent))))},bd.appendData=function(t){if(!this._disposed){var e=t.seriesIndex;this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0}},bd.on=_d("on",!1),bd.off=_d("off",!1),bd.one=_d("one",!1);var Nd=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function Rd(t,e){var i=t.get("z"),n=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t.zlevel=n))})}function Bd(){this.eventInfo}bd._initEvents=function(){cd(Nd,function(u){function t(t){var e,i=this.getModel(),n=t.target;if("globalout"===u)e={};else if(n&&null!=n.dataIndex){var r=n.dataModel||i.getSeriesByIndex(n.seriesIndex);e=r&&r.getDataParams(n.dataIndex,n.dataType,n)||{}}else n&&n.eventData&&(e=k({},n.eventData));if(e){var o=e.componentType,a=e.componentIndex;"markLine"!==o&&"markPoint"!==o&&"markArea"!==o||(o="series",a=e.seriesIndex);var s=o&&null!=a&&i.getComponent(o,a),l=s&&this["series"===s.mainType?"_chartsMap":"_componentsMap"][s.__viewId];e.event=t,e.type=u,this._ecEventProcessor.eventInfo={targetEl:n,packedEvent:e,model:s,view:l},this.trigger(u,e)}}t.zrEventfulCallAtLast=!0,this._zr.on(u,t,this)},this),cd(Fd,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},bd.isDisposed=function(){return this._disposed},bd.clear=function(){this._disposed||this.setOption({series:[]},!0)},bd.dispose=function(){if(!this._disposed){this._disposed=!0,Ur(this.getDom(),Kd,"");var e=this._api,i=this._model;cd(this._componentsViews,function(t){t.dispose(i,e)}),cd(this._chartsViews,function(t){t.dispose(i,e)}),this._zr.dispose(),delete Yd[this.id]}},S(wd,It),Bd.prototype={constructor:Bd,normalizeQuery:function(t){var s={},l={},u={};if(E(t)){var e=pd(t);s.mainType=e.main||null,s.subType=e.sub||null}else{var h=["Index","Name","Id"],c={name:1,dataIndex:1,dataType:1};D(t,function(t,e){for(var i=!1,n=0;n<h.length;n++){var r=h[n],o=e.lastIndexOf(r);if(0<o&&o===e.length-r.length){var a=e.slice(0,o);"data"!==a&&(s.mainType=a,s[r.toLowerCase()]=t,i=!0)}}c.hasOwnProperty(e)&&(l[e]=t,i=!0),i||(u[e]=t)})}return{cptQuery:s,dataQuery:l,otherQuery:u}},filter:function(t,e,i){var n=this.eventInfo;if(!n)return!0;var r=n.targetEl,o=n.packedEvent,a=n.model,s=n.view;if(!a||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return h(l,a,"mainType")&&h(l,a,"subType")&&h(l,a,"index","componentIndex")&&h(l,a,"name")&&h(l,a,"id")&&h(u,o,"name")&&h(u,o,"dataIndex")&&h(u,o,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,o));function h(t,e,i,n){return null==t[i]||e[n||i]===t[i]}},afterTrigger:function(){this.eventInfo=null}};var Vd={},Fd={},Hd=[],Wd=[],Gd=[],Zd=[],Ud={},Xd={},Yd={},jd={},qd=new Date-0,$d=new Date-0,Kd="_echarts_instance_";function Qd(t){jd[t]=!1}var Jd=Qd;function tf(t){return Yd[function(t,e){return t.getAttribute?t.getAttribute(e):t[e]}(t,Kd)]}function ef(t,e){Ud[t]=e}function nf(t){Wd.push(t)}function rf(t,e){lf(Hd,t,e,1e3)}function of(t,e,i){"function"==typeof e&&(i=e,e="");var n=fd(t)?t.type:[t,t={event:e}][0];t.event=(t.event||n).toLowerCase(),e=t.event,hd(yd.test(n)&&yd.test(e)),Vd[n]||(Vd[n]={action:i,actionInfo:t}),Fd[e]=n}function af(t,e){lf(Zd,t,e,1e3,"layout")}function sf(t,e){lf(Zd,t,e,3e3,"visual")}function lf(t,e,i,n,r){(dd(e)||fd(e))&&(i=e,e=n);var o=Sc.wrapStageHandler(i,r);return o.__prio=e,o.__raw=i,t.push(o),o}function uf(t,e){Xd[t]=e}function hf(t){return vu.extend(t)}function cf(t){return ec.extend(t)}function df(t){return Yh.extend(t)}function ff(t){return ac.extend(t)}sf(2e3,_c),nf(mh),rf(900,function(t){var o=Q();t.eachSeries(function(t){var e=t.get("stack");if(e){var i=o.get(e)||o.set(e,[]),n=t.getData(),r={stackResultDimension:n.getCalculationInfo("stackResultDimension"),stackedOverDimension:n.getCalculationInfo("stackedOverDimension"),stackedDimension:n.getCalculationInfo("stackedDimension"),stackedByDimension:n.getCalculationInfo("stackedByDimension"),isStackedByIndex:n.getCalculationInfo("isStackedByIndex"),data:n,seriesModel:t};if(!r.stackedDimension||!r.isStackedByIndex&&!r.stackedByDimension)return;i.length&&n.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(r)}}),o.each(vh)}),uf("default",function(r,o){A(o=o||{},{text:"loading",textColor:"#000",fontSize:"12px",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#c23531",spinnerRadius:10,lineWidth:5,zlevel:0});var t=new Si,a=new Ja({style:{fill:o.maskColor},zlevel:o.zlevel,z:1e4});t.add(a);var s=o.fontSize+" sans-serif",l=new Ja({style:{fill:"none",text:o.text,font:s,textPosition:"right",textDistance:10,textFill:o.textColor},zlevel:o.zlevel,z:10001});if(t.add(l),o.showSpinner){var u=new as({shape:{startAngle:-bc/2,endAngle:-bc/2+.1,r:o.spinnerRadius},style:{stroke:o.color,lineCap:"round",lineWidth:o.lineWidth},zlevel:o.zlevel,z:10001});u.animateShape(!0).when(1e3,{endAngle:3*bc/2}).start("circularInOut"),u.animateShape(!0).when(1e3,{startAngle:3*bc/2}).delay(300).start("circularInOut"),t.add(u)}return t.resize=function(){var t=ln(o.text,s),e=o.showSpinner?o.spinnerRadius:0,i=(r.getWidth()-2*e-(o.showSpinner&&t?10:0)-t)/2-(o.showSpinner?0:t/2),n=r.getHeight()/2;o.showSpinner&&u.setShape({cx:i,cy:n}),l.setShape({x:i-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},t.resize(),t}),of({type:"highlight",event:"highlight",update:"highlight"},J),of({type:"downplay",event:"downplay",update:"downplay"},J),ef("light",Gc),ef("dark",Xc);function pf(t){return t}function gf(t,e,i,n,r){this._old=t,this._new=e,this._oldKeyGetter=i||pf,this._newKeyGetter=n||pf,this.context=r}function mf(t,e,i,n,r){for(var o=0;o<t.length;o++){var a="_ec_"+r[n](t[o],o),s=e[a];null==s?(i.push(a),e[a]=o):(s.length||(e[a]=s=[s]),s.push(o))}}gf.prototype={constructor:gf,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t=this._old,e=this._new,i={},n=[],r=[];for(mf(t,{},n,"_oldKeyGetter",this),mf(e,i,r,"_newKeyGetter",this),o=0;o<t.length;o++){if(null!=(s=i[a=n[o]]))(u=s.length)?(1===u&&(i[a]=null),s=s.shift()):i[a]=null,this._update&&this._update(s,o);else this._remove&&this._remove(o)}for(var o=0;o<r.length;o++){var a=r[o];if(i.hasOwnProperty(a)){var s;if(null==(s=i[a]))continue;if(s.length)for(var l=0,u=s.length;l<u;l++)this._add&&this._add(s[l]);else this._add&&this._add(s)}}}};var vf=Q(["tooltip","label","itemName","itemId","seriesName"]);function yf(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function _f(t){null!=t&&k(this,t),this.otherDims={}}var xf=N,wf="undefined",bf={float:typeof Float64Array==wf?Array:Float64Array,int:typeof Int32Array==wf?Array:Int32Array,ordinal:Array,number:Array,time:Array},Sf=typeof Uint32Array==wf?Array:Uint32Array,Mf=typeof Int32Array==wf?Array:Int32Array,If=typeof Uint16Array==wf?Array:Uint16Array;function Cf(t){return 65535<t._rawCount?Sf:If}var Tf=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],Af=["_extent","_approximateExtent","_rawExtent"];function Df(e,i){D(Tf.concat(i.__wrappedMethods||[]),function(t){i.hasOwnProperty(t)&&(e[t]=i[t])}),e.__wrappedMethods=i.__wrappedMethods,D(Af,function(t){e[t]=b(i[t])}),e._calculationInfo=k(i._calculationInfo)}var kf=function(t,e){t=t||["x","y"];for(var i={},n=[],r={},o=0;o<t.length;o++){var a=t[o];E(a)?a=new _f({name:a}):a instanceof _f||(a=new _f(a));var s=a.name;a.type=a.type||"float",a.coordDim||(a.coordDim=s,a.coordDimIndex=0),a.otherDims=a.otherDims||{},n.push(s),(i[s]=a).index=o,a.createInvertedIndices&&(r[s]=[])}this.dimensions=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=function(n){var t={},o=t.encode={},a=Q(),s=[],l=[],u=t.userOutput={dimensionNames:n.dimensions.slice(),encode:{}};D(n.dimensions,function(t){var r=n.getDimensionInfo(t),e=r.coordDim;if(e){var i=r.coordDimIndex;yf(o,e)[i]=t,r.isExtraCoord||(a.set(e,1),function(t){return!("ordinal"===t||"time"===t)}(r.type)&&(s[0]=t),yf(u.encode,e)[i]=r.index),r.defaultTooltip&&l.push(t)}vf.each(function(t,e){var i=yf(o,e),n=r.otherDims[e];null!=n&&!1!==n&&(i[n]=r.name)})});var r=[],h={};a.each(function(t,e){var i=o[e];h[e]=i[0],r=r.concat(i)}),t.dataDimsOnCoord=r,t.encodeFirstDimNotExtra=h;var e=o.label;e&&e.length&&(s=e.slice());var i=o.tooltip;return i&&i.length?l=i.slice():l.length||(l=s.slice()),o.defaultedLabel=s,o.defaultedTooltip=l,t}(this),this._invertedIndicesMap=r,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},Pf=kf.prototype;function Lf(t,e,i,n,r){var o=bf[e.type],a=n-1,s=e.name,l=t[s][a];if(l&&l.length<i){for(var u=new o(Math.min(r-a*i,i)),h=0;h<l.length;h++)u[h]=l[h];t[s][a]=u}for(var c=n*i;c<r;c+=i)t[s].push(new o(Math.min(r-c,i)))}function Of(r){var o=r._invertedIndicesMap;D(o,function(t,e){var i=r._dimensionInfos[e].ordinalMeta;if(i){t=o[e]=new Mf(i.categories.length);for(var n=0;n<t.length;n++)t[n]=-1;for(n=0;n<r._count;n++)t[r.get(e,n)]=n}})}function zf(t,e,i){var n;if(null!=e){var r=t._chunkSize,o=Math.floor(i/r),a=i%r,s=t.dimensions[e],l=t._storage[s][o];if(l){n=l[a];var u=t._dimensionInfos[s].ordinalMeta;u&&u.categories.length&&(n=u.categories[n])}}return n}function Ef(t){return t}function Nf(t){return t<this._count&&0<=t?this._indices[t]:-1}function Rf(t,e){var i=t._idList[e];return null==i&&(i=zf(t,t._idDimIdx,e)),null==i&&(i="e\0\0"+e),i}function Bf(t){return O(t)||(t=[t]),t}function Vf(t,e){var i=t.dimensions,n=new kf(P(i,t.getDimensionInfo,t),t.hostModel);Df(n,t);for(var r=n._storage={},o=t._storage,a=0;a<i.length;a++){var s=i[a];o[s]&&(0<=x(e,s)?(r[s]=Ff(o[s]),n._rawExtent[s]=Hf(),n._extent[s]=null):r[s]=o[s])}return n}function Ff(t){for(var e,i,n=new Array(t.length),r=0;r<t.length;r++)n[r]=(e=t[r],i=void 0,(i=e.constructor)===Array?e.slice():new i(e));return n}function Hf(){return[1/0,-1/0]}Pf.type="list",Pf.hasItemOption=!0,Pf.getDimension=function(t){return"number"!=typeof t&&(isNaN(t)||this._dimensionInfos.hasOwnProperty(t))||(t=this.dimensions[t]),t},Pf.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},Pf.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},Pf.mapDimension=function(t,e){var i=this._dimensionsSummary;if(null==e)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return!0===e?(n||[]).slice():n&&n[e]},Pf.initData=function(t,e,i){(Pu.isInstance(t)||L(t))&&(t=new yh(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},i||(this.hasItemOption=!1),this.defaultDimValueGetter=Ch[this._rawData.getSource().sourceFormat],this._dimValueGetter=i=i||this.defaultDimValueGetter,this._dimValueGetterArrayRows=Ch.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},Pf.getProvider=function(){return this._rawData},Pf.appendData=function(t){var e=this._rawData,i=this.count();e.appendData(t);var n=e.count();e.persistent||(n+=i),this._initDataFromProvider(i,n)},Pf.appendValues=function(t,e){for(var i=this._chunkSize,n=this._storage,r=this.dimensions,o=r.length,a=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),u=this._chunkCount,h=0;h<o;h++){a[v=r[h]]||(a[v]=Hf()),n[v]||(n[v]=[]),Lf(n,this._dimensionInfos[v],i,u,l),this._chunkCount=n[v].length}for(var c=new Array(o),d=s;d<l;d++){for(var f=d-s,p=Math.floor(d/i),g=d%i,m=0;m<o;m++){var v=r[m],y=this._dimValueGetterArrayRows(t[f]||c,v,f,m);n[v][p][g]=y;var _=a[v];y<_[0]&&(_[0]=y),y>_[1]&&(_[1]=y)}e&&(this._nameList[d]=e[f])}this._rawCount=this._count=l,this._extent={},Of(this)},Pf._initDataFromProvider=function(t,e){if(!(e<=t)){for(var i,n=this._chunkSize,r=this._rawData,o=this._storage,a=this.dimensions,s=a.length,l=this._dimensionInfos,u=this._nameList,h=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;p<s;p++){c[w=a[p]]||(c[w]=Hf());var g=l[w];0===g.otherDims.itemName&&(i=this._nameDimIdx=p),0===g.otherDims.itemId&&(this._idDimIdx=p),o[w]||(o[w]=[]),Lf(o,g,n,f,e),this._chunkCount=o[w].length}for(var m=new Array(s),v=t;v<e;v++){m=r.getItem(v,m);for(var y=Math.floor(v/n),_=v%n,x=0;x<s;x++){var w,b=o[w=a[x]][y],S=this._dimValueGetter(m,w,v,x);b[_]=S;var M=c[w];S<M[0]&&(M[0]=S),S>M[1]&&(M[1]=S)}if(!r.pure){var I=u[v];if(m&&null==I)if(null!=m.name)u[v]=I=m.name;else if(null!=i){var C=a[i],T=o[C][y];if(T){I=T[_];var A=l[C].ordinalMeta;A&&A.categories.length&&(I=A.categories[I])}}var D=null==m?null:m.id;null==D&&null!=I&&(d[I]=d[I]||0,0<d[D=I]&&(D+="__ec__"+d[I]),d[I]++),null!=D&&(h[v]=D)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},Of(this)}},Pf.count=function(){return this._count},Pf.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,i=this._count;if(e===Array){r=new e(i);for(var n=0;n<i;n++)r[n]=t[n]}else r=new e(t.buffer,0,i)}else{var r=new(e=Cf(this))(this.count());for(n=0;n<r.length;n++)r[n]=n}return r},Pf.get=function(t,e){if(!(0<=e&&e<this._count))return NaN;var i=this._storage;if(!i[t])return NaN;e=this.getRawIndex(e);var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize;return i[t][n][r]},Pf.getByRawIndex=function(t,e){if(!(0<=e&&e<this._rawCount))return NaN;var i=this._storage[t];if(!i)return NaN;var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize;return i[n][r]},Pf._getFast=function(t,e){var i=Math.floor(e/this._chunkSize),n=e%this._chunkSize;return this._storage[t][i][n]},Pf.getValues=function(t,e){var i=[];O(t)||(e=t,t=this.dimensions);for(var n=0,r=t.length;n<r;n++)i.push(this.get(t[n],e));return i},Pf.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,i=0,n=e.length;i<n;i++)if(isNaN(this.get(e[i],t)))return!1;return!0},Pf.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],i=Hf();if(!e)return i;var n,r=this.count();if(!this._indices)return this._rawExtent[t].slice();if(n=this._extent[t])return n.slice();for(var o=(n=i)[0],a=n[1],s=0;s<r;s++){var l=this._getFast(t,this.getRawIndex(s));l<o&&(o=l),a<l&&(a=l)}return n=[o,a],this._extent[t]=n},Pf.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},Pf.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},Pf.getCalculationInfo=function(t){return this._calculationInfo[t]},Pf.setCalculationInfo=function(t,e){xf(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},Pf.getSum=function(t){var e=0;if(this._storage[t])for(var i=0,n=this.count();i<n;i++){var r=this.get(t,i);isNaN(r)||(e+=r)}return e},Pf.getMedian=function(t){var i=[];this.each(t,function(t,e){isNaN(t)||i.push(t)});var e=[].concat(i).sort(function(t,e){return t-e}),n=this.count();return 0===n?0:n%2==1?e[(n-1)/2]:(e[n/2]+e[n/2-1])/2},Pf.rawIndexOf=function(t,e){var i=(t&&this._invertedIndicesMap[t])[e];return null==i||isNaN(i)?-1:i},Pf.indexOfName=function(t){for(var e=0,i=this.count();e<i;e++)if(this.getName(e)===t)return e;return-1},Pf.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(null!=i&&i<this._count&&i===t)return t;for(var n=0,r=this._count-1;n<=r;){var o=(n+r)/2|0;if(e[o]<t)n=1+o;else{if(!(e[o]>t))return o;r=o-1}}return-1},Pf.indicesOfNearest=function(t,e,i){var n=[];if(!this._storage[t])return n;null==i&&(i=1/0);for(var r=1/0,o=-1,a=0,s=0,l=this.count();s<l;s++){var u=e-this.get(t,s),h=Math.abs(u);h<=i&&((h<r||h===r&&0<=u&&o<0)&&(r=h,o=u,a=0),u===o&&(n[a++]=s))}return n.length=a,n},Pf.getRawIndex=Ef,Pf.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],i=0;i<this.dimensions.length;i++){var n=this.dimensions[i];e.push(this.get(n,t))}return e},Pf.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||zf(this,this._nameDimIdx,e)||""},Pf.getId=function(t){return Rf(this,this.getRawIndex(t))},Pf.each=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this;for(var r=(t=P(Bf(t),this.getDimension,this)).length,o=0;o<this.count();o++)switch(r){case 0:e.call(i,o);break;case 1:e.call(i,this.get(t[0],o),o);break;case 2:e.call(i,this.get(t[0],o),this.get(t[1],o),o);break;default:for(var a=0,s=[];a<r;a++)s[a]=this.get(t[a],o);s[a]=o,e.apply(i,s)}}},Pf.filterSelf=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=P(Bf(t),this.getDimension,this);for(var r=this.count(),o=new(Cf(this))(r),a=[],s=t.length,l=0,u=t[0],h=0;h<r;h++){var c,d=this.getRawIndex(h);if(0===s)c=e.call(i,h);else if(1===s){var f=this._getFast(u,d);c=e.call(i,f,h)}else{for(var p=0;p<s;p++)a[p]=this._getFast(u,d);a[p]=h,c=e.apply(i,a)}c&&(o[l++]=d)}return l<r&&(this._indices=o),this._count=l,this._extent={},this.getRawIndex=this._indices?Nf:Ef,this}},Pf.selectRange=function(t){if(this._count){var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(i);var n=e.length;if(n){var r=this.count(),o=new(Cf(this))(r),a=0,s=e[0],l=t[s][0],u=t[s][1],h=!1;if(!this._indices){var c=0;if(1===n){for(var d=this._storage[e[0]],f=0;f<this._chunkCount;f++)for(var p=d[f],g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++){(l<=(w=p[m])&&w<=u||isNaN(w))&&(o[a++]=c),c++}h=!0}else if(2===n){d=this._storage[s];var v=this._storage[e[1]],y=t[e[1]][0],_=t[e[1]][1];for(f=0;f<this._chunkCount;f++){p=d[f];var x=v[f];for(g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++){var w=p[m],b=x[m];(l<=w&&w<=u||isNaN(w))&&(y<=b&&b<=_||isNaN(b))&&(o[a++]=c),c++}}h=!0}}if(!h)if(1===n)for(m=0;m<r;m++){var S=this.getRawIndex(m);(l<=(w=this._getFast(s,S))&&w<=u||isNaN(w))&&(o[a++]=S)}else for(m=0;m<r;m++){var M=!0;for(S=this.getRawIndex(m),f=0;f<n;f++){var I=e[f];((w=this._getFast(i,S))<t[I][0]||w>t[I][1])&&(M=!1)}M&&(o[a++]=this.getRawIndex(m))}return a<r&&(this._indices=o),this._count=a,this._extent={},this.getRawIndex=this._indices?Nf:Ef,this}}},Pf.mapArray=function(t,e,i,n){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i),r},Pf.map=function(t,e,i,n){i=i||n||this;var r=Vf(this,t=P(Bf(t),this.getDimension,this));r._indices=this._indices,r.getRawIndex=r._indices?Nf:Ef;for(var o=r._storage,a=[],s=this._chunkSize,l=t.length,u=this.count(),h=[],c=r._rawExtent,d=0;d<u;d++){for(var f=0;f<l;f++)h[f]=this.get(t[f],d);h[l]=d;var p=e&&e.apply(i,h);if(null!=p){"object"!=typeof p&&(a[0]=p,p=a);for(var g=this.getRawIndex(d),m=Math.floor(g/s),v=g%s,y=0;y<p.length;y++){var _=t[y],x=p[y],w=c[_],b=o[_];b&&(b[m][v]=x),x<w[0]&&(w[0]=x),x>w[1]&&(w[1]=x)}}}return r},Pf.downSample=function(t,e,i,n){for(var r=Vf(this,[t]),o=r._storage,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=this._chunkSize,c=r._rawExtent[t],d=new(Cf(this))(u),f=0,p=0;p<u;p+=s){u-p<s&&(s=u-p,a.length=s);for(var g=0;g<s;g++){var m=this.getRawIndex(p+g),v=Math.floor(m/h),y=m%h;a[g]=l[v][y]}var _=i(a),x=this.getRawIndex(Math.min(p+n(a,_)||0,u-1)),w=x%h;(l[Math.floor(x/h)][w]=_)<c[0]&&(c[0]=_),_>c[1]&&(c[1]=_),d[f++]=x}return r._count=f,r._indices=d,r.getRawIndex=Nf,r},Pf.getItemModel=function(t){var e=this.hostModel;return new _l(this.getRawDataItem(t),e,e&&e.ecModel)},Pf.diff=function(e){var i=this;return new gf(e?e.getIndices():[],this.getIndices(),function(t){return Rf(e,t)},function(t){return Rf(i,t)})},Pf.getVisual=function(t){var e=this._visual;return e&&e[t]},Pf.setVisual=function(t,e){if(xf(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},Pf.setLayout=function(t,e){if(xf(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},Pf.getLayout=function(t){return this._layout[t]},Pf.getItemLayout=function(t){return this._itemLayouts[t]},Pf.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?k(this._itemLayouts[t]||{},e):e},Pf.clearItemLayouts=function(){this._itemLayouts.length=0},Pf.getItemVisual=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)},Pf.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=n,xf(e))for(var o in e)e.hasOwnProperty(o)&&(n[o]=e[o],r[o]=!0);else n[e]=i,r[e]=!0},Pf.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};function Wf(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType}function Gf(t,e,i){Pu.isInstance(e)||(e=Pu.seriesDataToSource(e)),i=i||{},t=(t||[]).slice();for(var n=(i.dimsDef||[]).slice(),r=Q(),o=Q(),l=[],a=function(t,e,i,n){var r=Math.max(t.dimensionsDetectCount||1,e.length,i.length,n||0);return D(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}(e,t,n,i.dimCount),s=0;s<a;s++){var u=n[s]=k({},N(n[s])?n[s]:{name:n[s]}),h=u.name,c=l[s]=new _f;null!=h&&null==r.get(h)&&(c.name=c.displayName=h,r.set(h,s)),null!=u.type&&(c.type=u.type),null!=u.displayName&&(c.displayName=u.displayName)}var d=i.encodeDef;!d&&i.encodeDefaulter&&(d=i.encodeDefaulter(e,a)),(d=Q(d)).each(function(t,i){if(1===(t=Lr(t).slice()).length&&!E(t[0])&&t[0]<0)d.set(i,!1);else{var n=d.set(i,[]);D(t,function(t,e){E(t)&&(t=r.get(t)),null!=t&&t<a&&(n[e]=t,p(l[t],i,e))})}});var f=0;function p(t,e,i){null!=vf.get(e)?t.otherDims[e]=i:(t.coordDim=e,t.coordDimIndex=i,o.set(e,!0))}D(t,function(r,t){var o,a,s;if(E(r))o=r,r={};else{o=r.name;var e=r.ordinalMeta;r.ordinalMeta=null,(r=b(r)).ordinalMeta=e,a=r.dimsDef,s=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}if(!1!==(i=d.get(o))){var i;if(!(i=Lr(i)).length)for(var n=0;n<(a&&a.length||1);n++){for(;f<l.length&&null!=l[f].coordDim;)f++;f<l.length&&i.push(f++)}D(i,function(t,e){var i=l[t];if(p(A(i,r),o,e),null==i.name&&a){var n=a[e];N(n)||(n={name:n}),i.name=i.displayName=n.name,i.defaultTooltip=n.defaultTooltip}s&&A(i.otherDims,s)})}});var g=i.generateCoord,m=i.generateCoordCount,v=null!=m;m=g?m||1:0;for(var y,_,x=g||"value",w=0;w<a;w++){null==(c=l[w]=l[w]||new _f).coordDim&&(c.coordDim=Zf(x,o,v),c.coordDimIndex=0,(!g||m<=0)&&(c.isExtraCoord=!0),m--),null==c.name&&(c.name=Zf(c.coordDim,r)),null==c.type&&(y=e,_=w,c.name,Fu(y.data,y.sourceFormat,y.seriesLayoutBy,y.dimensionsDefine,y.startIndex,_)===Lu.Must||c.isExtraCoord&&(null!=c.otherDims.itemName||null!=c.otherDims.seriesName))&&(c.type="ordinal")}return l}function Zf(t,e,i){if(i||null!=e.get(t)){for(var n=0;null!=e.get(t+n);)n++;t+=n}return e.set(t,!0),t}Pf.setItemGraphicEl=function(t,e){var i=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=i&&i.seriesIndex,"group"===e.type&&e.traverse(Wf,e)),this._graphicEls[t]=e},Pf.getItemGraphicEl=function(t){return this._graphicEls[t]},Pf.eachItemGraphicEl=function(i,n){D(this._graphicEls,function(t,e){t&&i&&i.call(n,t,e)})},Pf.cloneShallow=function(t){if(!t){var e=P(this.dimensions,this.getDimensionInfo,this);t=new kf(e,this.hostModel)}if(t._storage=this._storage,Df(t,this),this._indices){var i=this._indices.constructor;t._indices=new i(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?Nf:Ef,t},Pf.wrapMethod=function(t,e){var i=this[t];"function"==typeof i&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t].concat(Z(arguments)))})},Pf.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],Pf.CHANGABLE_METHODS=["filterSelf","selectRange"];var Uf=function(t,e){return Gf((e=e||{}).coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})};function Xf(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=Q(),this.categoryAxisMap=Q(),this.firstCategoryDimIndex=null}var Yf={cartesian2d:function(t,e,i,n){var r=t.getReferringComponents("xAxis")[0],o=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],i.set("x",r),i.set("y",o),jf(r)&&(n.set("x",r),e.firstCategoryDimIndex=0),jf(o)&&(n.set("y",o),e.firstCategoryDimIndex,e.firstCategoryDimIndex=1)},singleAxis:function(t,e,i,n){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],i.set("single",r),jf(r)&&(n.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,i,n){var r=t.getReferringComponents("polar")[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],i.set("radius",o),i.set("angle",a),jf(o)&&(n.set("radius",o),e.firstCategoryDimIndex=0),jf(a)&&(n.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,i,n){e.coordSysDims=["lng","lat"]},parallel:function(t,r,o,a){var s=t.ecModel,e=s.getComponent("parallel",t.get("parallelIndex")),l=r.coordSysDims=e.dimensions.slice();D(e.parallelAxisIndex,function(t,e){var i=s.getComponent("parallelAxis",t),n=l[e];o.set(n,i),jf(i)&&null==r.firstCategoryDimIndex&&(a.set(n,i),r.firstCategoryDimIndex=e)})}};function jf(t){return"category"===t.get("type")}function qf(t,i,e){var n,r,o,a,s=(e=e||{}).byIndex,l=e.stackedCoordDimension,u=!(!t||!t.get("stack"));if(D(i,function(t,e){E(t)&&(i[e]=t={name:t}),u&&!t.isExtraCoord&&(s||n||!t.ordinalMeta||(n=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),!r||s||n||(s=!0),r){o="__\0ecstackresult",a="__\0ecstackedover",n&&(n.createInvertedIndices=!0);var h=r.coordDim,c=r.type,d=0;D(i,function(t){t.coordDim===h&&d++}),i.push({name:o,coordDim:h,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0}),d++,i.push({name:a,coordDim:a,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:n&&n.name,isStackedByIndex:s,stackedOverDimension:a,stackResultDimension:o}}function $f(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function Kf(t,e){return $f(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Qf(t,e,i){i=i||{},Pu.isInstance(t)||(t=Pu.seriesDataToSource(t));var n,r=e.get("coordinateSystem"),o=ju.get(r),a=function(t){var e=t.get("coordinateSystem"),i=new Xf(e),n=Yf[e];if(n)return n(t,i,i.axisMap,i.categoryAxisMap),i}(e);a&&(n=P(a.coordSysDims,function(t){var e={name:t},i=a.axisMap.get(t);if(i){var n=i.get("type");e.type=function(t){return"category"===t?"ordinal":"time"===t?"time":"float"}(n)}return e})),n=n||(o&&(o.getDimensionsInfo?o.getDimensionsInfo():o.dimensions.slice())||["x","y"]);var s,l,u=Uf(t,{coordDimensions:n,generateCoord:i.generateCoord,encodeDefaulter:i.useEncodeDefaulter?T(Ru,n,e):null});a&&D(u,function(t,e){var i=t.coordDim,n=a.categoryAxisMap.get(i);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(u[s].otherDims.itemName=0);var h=qf(e,u),c=new kf(u,e);c.setCalculationInfo(h);var d=null!=s&&function(t){if(t.sourceFormat===Su){var e=function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[]);return null!=e&&!O(Er(e))}}(t)?function(t,e,i,n){return n===s?i:this.defaultDimValueGetter(t,e,i,n)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function Jf(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function tp(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}Jf.prototype.parse=function(t){return t},Jf.prototype.getSetting=function(t){return this._setting[t]},Jf.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},Jf.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},Jf.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},Jf.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Jf.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Jf.prototype.getExtent=function(){return this._extent.slice()},Jf.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},Jf.prototype.isBlank=function(){return this._isBlank},Jf.prototype.setBlank=function(t){this._isBlank=t},Jf.prototype.getLabel=null,$r(Jf),eo(Jf,{registerWhenExtend:!0}),tp.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&P(i,np);return new tp({categories:n,needCollect:!n,deduplication:!1!==e.dedplication})};var ep=tp.prototype;function ip(t){return t._map||(t._map=Q(t.categories))}function np(t){return N(t)&&null!=t.value?t.value:t+""}ep.getOrdinal=function(t){return ip(this).get(t)},ep.parseAndCollect=function(t){var e,i=this._needCollect;if("string"!=typeof t&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=ip(this);return null==(e=n.get(t))&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=NaN),e};var rp=Jf.prototype,op=Jf.extend({type:"ordinal",init:function(t,e){t&&!O(t)||(t=new tp({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),rp.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return rp.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(rp.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){if(!this.isBlank())return this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:J,niceExtent:J});op.create=function(){return new op};var ap=Tl;function sp(t){return kl(t)+2}function lp(t,e,i){t[e]=Math.max(Math.min(t[e],i[1]),i[0])}function up(t,e){isFinite(t[0])||(t[0]=e[0]),isFinite(t[1])||(t[1]=e[1]),lp(t,0,e),lp(t,1,e),t[0]>t[1]&&(t[0]=t[1])}var hp=Tl,cp=Jf.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),cp.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=sp(t)},getTicks:function(t){var e=this._interval,i=this._extent,n=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;i[0]<n[0]&&(t?o.push(hp(n[0]-e,r)):o.push(i[0]));for(var a=n[0];a<=n[1]&&(o.push(a),(a=hp(a+e,r))!==o[o.length-1]);)if(1e4<o.length)return[];var s=o.length?o[o.length-1]:n[1];return i[1]>s&&(t?o.push(hp(s+e,r)):o.push(i[1])),o},getMinorTicks:function(t){for(var e=this.getTicks(!0),i=[],n=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o-a)/t;s<t-1;){var h=Tl(a+(s+1)*u);h>n[0]&&h<n[1]&&l.push(h),s++}i.push(l)}return i},getLabel:function(t,e){if(null==t)return"";var i=e&&e.precision;return null==i?i=kl(t)||0:"auto"===i&&(i=this._intervalPrecision),Hl(t=hp(t,i,!0))},niceTicks:function(t,e,i){t=t||5;var n=this._extent,r=n[1]-n[0];if(isFinite(r)){r<0&&(r=-r,n.reverse());var o=function(t,e,i,n){var r={},o=t[1]-t[0],a=r.interval=Vl(o/e,!0);null!=i&&a<i&&(a=r.interval=i),null!=n&&n<a&&(a=r.interval=n);var s=r.intervalPrecision=sp(a);return up(r.niceTickExtent=[ap(Math.ceil(t[0]/a)*a,s),ap(Math.floor(t[1]/a)*a,s)],t),r}(n,t,e,i);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var i=e[0];t.fixMax||(e[1]+=i/2),e[0]-=i/2}else e[1]=1;var n=e[1]-e[0];isFinite(n)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=hp(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=hp(Math.ceil(e[1]/r)*r))}});cp.create=function(){return new cp};var dp="__ec_stack_",fp="undefined"!=typeof Float32Array?Float32Array:Array;function pp(t){return t.get("stack")||dp+t.seriesIndex}function gp(t){return t.dim+t.index}function mp(t,e){var i=[];return e.eachSeriesByType(t,function(t){xp(t)&&!wp(t)&&i.push(t)}),i}function vp(t){var g=function(t){var l={};D(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var i=t.getData(),n=e.dim+"_"+e.index,r=i.mapDimension(e.dim),o=0,a=i.count();o<a;++o){var s=i.get(r,o);l[n]?l[n].push(s):l[n]=[s]}});var e=[];for(var i in l)if(l.hasOwnProperty(i)){var n=l[i];if(n){n.sort(function(t,e){return t-e});for(var r=null,o=1;o<n.length;++o){var a=n[o]-n[o-1];0<a&&(r=null===r?a:Math.min(r,a))}e[i]=r}}return e}(t),m=[];return D(t,function(t){var e,i=t.coordinateSystem.getBaseAxis(),n=i.getExtent();if("category"===i.type)e=i.getBandWidth();else if("value"===i.type||"time"===i.type){var r=i.dim+"_"+i.index,o=g[r],a=Math.abs(n[1]-n[0]),s=i.scale.getExtent(),l=Math.abs(s[1]-s[0]);e=o?a/l*o:a}else{var u=t.getData();e=Math.abs(n[1]-n[0])/u.count()}var h=Cl(t.get("barWidth"),e),c=Cl(t.get("barMaxWidth"),e),d=Cl(t.get("barMinWidth")||1,e),f=t.get("barGap"),p=t.get("barCategoryGap");m.push({bandWidth:e,barWidth:h,barMaxWidth:c,barMinWidth:d,barGap:f,barCategoryGap:p,axisKey:gp(i),stackId:pp(t)})}),function(t){var d={};D(t,function(t,e){var i=t.axisKey,n=t.bandWidth,r=d[i]||{bandWidth:n,remainedWidth:n,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=r.stacks;d[i]=r;var a=t.stackId;o[a]||r.autoWidthCount++,o[a]=o[a]||{width:0,maxWidth:0};var s=t.barWidth;s&&!o[a].width&&(o[a].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(o[a].maxWidth=l);var u=t.barMinWidth;u&&(o[a].minWidth=u);var h=t.barGap;null!=h&&(r.gap=h);var c=t.barCategoryGap;null!=c&&(r.categoryGap=c)});var f={};return D(d,function(t,i){f[i]={};var e=t.stacks,n=t.bandWidth,r=Cl(t.categoryGap,n),o=Cl(t.gap,1),a=t.remainedWidth,s=t.autoWidthCount,l=(a-r)/(s+(s-1)*o);l=Math.max(l,0),D(e,function(t){var e=t.maxWidth,i=t.minWidth;if(t.width){n=t.width;e&&(n=Math.min(n,e)),i&&(n=Math.max(n,i)),t.width=n,a-=n+o*n,s--}else{var n=l;e&&e<n&&(n=Math.min(e,a)),i&&n<i&&(n=i),n!==l&&(t.width=n,a-=n+o*n,s--)}}),l=(a-r)/(s+(s-1)*o),l=Math.max(l,0);var u,h=0;D(e,function(t,e){t.width||(t.width=l),h+=(u=t).width*(1+o)}),u&&(h-=u.width*o);var c=-h/2;D(e,function(t,e){f[i][e]=f[i][e]||{bandWidth:n,offset:c,width:t.width},c+=t.width*(1+o)})}),f}(m)}function yp(t,e,i){if(t&&e){var n=t[gp(e)];return null!=n&&null!=i&&(n=n[pp(i)]),n}}var _p={seriesType:"bar",plan:nc(),reset:function(t){if(xp(t)&&wp(t)){var e=t.getData(),c=t.coordinateSystem,d=c.grid.getRect(),f=c.getBaseAxis(),p=c.getOtherAxis(f),g=e.mapDimension(p.dim),m=e.mapDimension(f.dim),v=p.isHorizontal(),y=v?0:1,_=yp(vp([t]),f,t).width;return.5<_||(_=.5),{progress:function(t,e){var i,n=t.count,r=new fp(2*n),o=new fp(2*n),a=new fp(n),s=[],l=[],u=0,h=0;for(;null!=(i=t.next());)l[y]=e.get(g,i),l[1-y]=e.get(m,i),s=c.dataToPoint(l,null,s),o[u]=v?d.x+d.width:s[0],r[u++]=s[0],o[u]=v?s[1]:d.y+d.height,r[u++]=s[1],a[h++]=i;e.setLayout({largePoints:r,largeDataIndices:a,largeBackgroundPoints:o,barWidth:_,valueAxisStart:bp(f,p,!1),backgroundStart:v?d.x:d.y,valueAxisHorizontal:v})}}}}};function xp(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function wp(t){return t.pipelineContext&&t.pipelineContext.large}function bp(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}var Sp=cp.prototype,Mp=Math.ceil,Ip=Math.floor,Cp=36e5,Tp=864e5,Ap=cp.extend({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return Ql(e[0],i,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=Tp,e[1]+=Tp),e[1]===-1/0&&e[0]===1/0){var i=new Date;e[1]=+new Date(i.getFullYear(),i.getMonth(),i.getDate()),e[0]=e[1]-Tp}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var n=this._interval;t.fixMin||(e[0]=Tl(Ip(e[0]/n)*n)),t.fixMax||(e[1]=Tl(Mp(e[1]/n)*n))},niceTicks:function(t,e,i){t=t||10;var n=this._extent,r=n[1]-n[0],o=r/t;null!=e&&o<e&&(o=e),null!=i&&i<o&&(o=i);var a=Dp.length,s=function(t,e,i,n){for(;i<n;){var r=i+n>>>1;t[r][1]<e?i=1+r:n=r}return i}(Dp,o,0,a),l=Dp[Math.min(s,a-1)],u=l[1];"year"===l[0]&&(u*=Vl(r/u/t,!0));var h=this.getSetting("useUTC")?0:60*new Date(+n[0]||+n[1]).getTimezoneOffset()*1e3,c=[Math.round(Mp((n[0]-h)/u)*u+h),Math.round(Ip((n[1]-h)/u)*u+h)];up(c,n),this._stepLvl=l,this._interval=u,this._niceExtent=c},parse:function(t){return+Nl(t)}});D(["contain","normalize"],function(e){Ap.prototype[e]=function(t){return Sp[e].call(this,this.parse(t))}});var Dp=[["hh:mm:ss",1e3],["hh:mm:ss",5e3],["hh:mm:ss",1e4],["hh:mm:ss",15e3],["hh:mm:ss",3e4],["hh:mm\nMM-dd",6e4],["hh:mm\nMM-dd",3e5],["hh:mm\nMM-dd",6e5],["hh:mm\nMM-dd",9e5],["hh:mm\nMM-dd",18e5],["hh:mm\nMM-dd",Cp],["hh:mm\nMM-dd",72e5],["hh:mm\nMM-dd",6*Cp],["hh:mm\nMM-dd",432e5],["MM-dd\nyyyy",Tp],["MM-dd\nyyyy",2*Tp],["MM-dd\nyyyy",3*Tp],["MM-dd\nyyyy",4*Tp],["MM-dd\nyyyy",5*Tp],["MM-dd\nyyyy",6*Tp],["week",7*Tp],["MM-dd\nyyyy",864e6],["week",14*Tp],["week",21*Tp],["month",31*Tp],["week",42*Tp],["month",62*Tp],["week",70*Tp],["quarter",95*Tp],["month",31*Tp*4],["month",13392e6],["half-year",16416e6],["month",31*Tp*8],["month",26784e6],["year",380*Tp]];Ap.create=function(t){return new Ap({useUTC:t.ecModel.get("useUTC")})};var kp=Jf.prototype,Pp=cp.prototype,Lp=kl,Op=Tl,zp=Math.floor,Ep=Math.ceil,Np=Math.pow,Rp=Math.log,Bp=Jf.extend({type:"log",base:10,$constructor:function(){Jf.apply(this,arguments),this._originalScale=new cp},getTicks:function(t){var i=this._originalScale,n=this._extent,r=i.getExtent();return P(Pp.getTicks.call(this,t),function(t){var e=Tl(Np(this.base,t));return e=t===n[0]&&i.__fixMin?Vp(e,r[0]):e,e=t===n[1]&&i.__fixMax?Vp(e,r[1]):e},this)},getMinorTicks:Pp.getMinorTicks,getLabel:Pp.getLabel,scale:function(t){return t=kp.scale.call(this,t),Np(this.base,t)},setExtent:function(t,e){var i=this.base;t=Rp(t)/Rp(i),e=Rp(e)/Rp(i),Pp.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=kp.getExtent.call(this);e[0]=Np(t,e[0]),e[1]=Np(t,e[1]);var i=this._originalScale,n=i.getExtent();return i.__fixMin&&(e[0]=Vp(e[0],n[0])),i.__fixMax&&(e[1]=Vp(e[1],n[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Rp(t[0])/Rp(e),t[1]=Rp(t[1])/Rp(e),kp.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(i==1/0||i<=0)){var n=Rl(i);for(t/i*n<=.5&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&0<Math.abs(n);)n*=10;var r=[Tl(Ep(e[0]/n)*n),Tl(zp(e[1]/n)*n)];this._interval=n,this._niceExtent=r}},niceExtent:function(t){Pp.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});function Vp(t,e){return Op(t,Lp(e))}function Fp(t,e){var i,n,r,o=t.type,a=e.getMin(),s=e.getMax(),l=t.getExtent();"ordinal"===o?i=e.getCategories().length:(O(n=e.get("boundaryGap"))||(n=[n||0,n||0]),"boolean"==typeof n[0]&&(n=[0,0]),n[0]=Cl(n[0],1),n[1]=Cl(n[1],1),r=l[1]-l[0]||Math.abs(l[0])),"dataMin"===a?a=l[0]:"function"==typeof a&&(a=a({min:l[0],max:l[1]})),"dataMax"===s?s=l[1]:"function"==typeof s&&(s=s({min:l[0],max:l[1]}));var u=null!=a,h=null!=s;null==a&&(a="ordinal"===o?i?0:NaN:l[0]-n[0]*r),null==s&&(s="ordinal"===o?i?i-1:NaN:l[1]+n[1]*r),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),t.setBlank(F(a)||F(s)||"ordinal"===o&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(0<a&&0<s&&!u&&(a=0),a<0&&s<0&&!h&&(s=0));var c=e.ecModel;if(c&&"time"===o){var d,f=mp("bar",c);if(D(f,function(t){d|=t.getBaseAxis()===e.axis}),d){var p=vp(f),g=function(t,e,i,n){var r=i.axis.getExtent(),o=r[1]-r[0],a=yp(n,i.axis);if(void 0===a)return{min:t,max:e};var s=1/0;D(a,function(t){s=Math.min(t.offset,s)});var l=-1/0;D(a,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=h/(1-(s+l)/o)-h;return{min:t-=s/u*c,max:e+=l/u*c}}(a,s,e,p);a=g.min,s=g.max}}return{extent:[a,s],fixMin:u,fixMax:h}}function Hp(t,e){var i=Fp(t,e),n=i.extent,r=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(n[0],n[1]),t.niceExtent({splitNumber:r,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var a=e.get("interval");null!=a&&t.setInterval&&t.setInterval(a)}function Wp(t,e){if(e=e||t.get("type"))switch(e){case"category":return new op(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new cp;default:return(Jf.getClass(e)||cp).create(t)}}function Gp(i){var e,n=i.getLabelModel().get("formatter"),r="category"===i.type?i.scale.getExtent()[0]:null;return"string"==typeof n?(e=n,n=function(t){return t=i.scale.getLabel(t),e.replace("{value}",null!=t?t:"")}):"function"==typeof n?function(t,e){return null!=r&&(e=t-r),n(Zp(i,t),e)}:function(t){return i.scale.getLabel(t)}}function Zp(t,e){return"category"===t.type?t.scale.getLabel(e):e}function Up(t){var e=t.get("interval");return null==e?"auto":e}function Xp(t){return"category"===t.type&&0===Up(t.getLabelModel())}D(["contain","normalize"],function(e){Bp.prototype[e]=function(t){return t=Rp(t)/Rp(this.base),kp[e].call(this,t)}}),Bp.create=function(){return new Bp};var Yp={getMin:function(t){var e=this.option,i=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=i&&"dataMin"!==i&&"function"!=typeof i&&!F(i)&&(i=this.axis.scale.parse(i)),i},getMax:function(t){var e=this.option,i=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=i&&"dataMax"!==i&&"function"!=typeof i&&!F(i)&&(i=this.axis.scale.parse(i)),i},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:J,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},jp=ws({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,o=e.height/2;t.moveTo(i,n-o),t.lineTo(i+r,n+o),t.lineTo(i-r,n+o),t.closePath()}}),qp=ws({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,o=e.height/2;t.moveTo(i,n-o),t.lineTo(i+r,n),t.lineTo(i,n+o),t.lineTo(i-r,n),t.closePath()}}),$p=ws({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=n-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),d=Math.cos(u),f=.6*a,p=.7*a;t.moveTo(i-h,l+s),t.arc(i,l,a,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(i+h-c*f,l+s+d*f,i,n-p,i,n),t.bezierCurveTo(i,n-p,i-h+c*f,l+s+d*f,i-h,l+s),t.closePath()}}),Kp=ws({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.height,n=e.width,r=e.x,o=e.y,a=n/3*2;t.moveTo(r,o),t.lineTo(r+a,o+i),t.lineTo(r,o+i/4*3),t.lineTo(r-a,o+i),t.lineTo(r,o),t.closePath()}}),Qp={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var o=Math.min(i,n);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n}},Jp={};D({line:es,rect:Ja,roundRect:Ja,square:Ja,circle:Fa,diamond:qp,pin:$p,arrow:Kp,triangle:jp},function(t,e){Jp[e]=new t});var tg=ws({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,i){var n=dn(t,e,i),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.textPosition&&(n.y=i.y+.4*i.height),n},buildPath:function(t,e,i){var n=e.symbolType;if("none"!==n){var r=Jp[n];r=r||Jp[n="rect"],Qp[n](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,i)}}});function eg(t,e){if("image"!==this.type){var i=this.style,n=this.shape;n&&"line"===n.symbolType?i.stroke=t:this.__isEmptyBrush?(i.stroke=t,i.fill=e||"#fff"):(i.fill&&(i.fill=t),i.stroke&&(i.stroke=t)),this.dirty(!1)}}function ig(t,e,i,n,r,o,a){var s,l=0===t.indexOf("empty");return l&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?Is(t.slice(8),new bi(e,i,n,r),a?"center":"cover"):0===t.indexOf("path://")?Ms(t.slice(7),{},new bi(e,i,n,r),a?"center":"cover"):new tg({shape:{symbolType:t,x:e,y:i,width:n,height:r}})).__isEmptyBrush=l,s.setColor=eg,s.setColor(o),s}var ng={isDimensionStacked:$f,enableDataStack:qf,getStackedDimension:Kf};var rg=(Object.freeze||Object)({createList:function(t){return Qf(t.getSource(),t)},getLayoutRect:lu,dataStack:ng,createScale:function(t,e){var i=e;_l.isInstance(e)||S(i=new _l(e),Yp);var n=Wp(i);return n.setExtent(t[0],t[1]),Hp(n,i),n},mixinAxisModelCommonMethods:function(t){S(t,Yp)},completeDimensions:Gf,createDimensions:Uf,createSymbol:ig}),og=1e-8;function ag(t,e){return Math.abs(t-e)<og}function sg(t,e,i){var n=0,r=t[0];if(!r)return!1;for(var o=1;o<t.length;o++){var a=t[o];n+=sa(r[0],r[1],a[0],a[1],e,i),r=a}var s=t[0];return ag(r[0],s[0])&&ag(r[1],s[1])||(n+=sa(r[0],r[1],s[0],s[1],e,i)),0!==n}function lg(t,e,i){if(this.name=t,this.geometries=e,i)i=[i[0],i[1]];else{var n=this.getBoundingRect();i=[n.x+n.width/2,n.y+n.height/2]}this.center=i}function ug(t,e,i){for(var n=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),r=s+=r,o=l+=o,n.push([s/i,l/i])}return n}lg.prototype={constructor:lg,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,i=[e,e],n=[-e,-e],r=[],o=[],a=this.geometries,s=0;s<a.length;s++){if("polygon"===a[s].type)Eo(a[s].exterior,r,o),_t(i,i,r),xt(n,n,o)}return 0===s&&(i[0]=i[1]=n[0]=n[1]=0),this._rect=new bi(i[0],i[1],n[0]-i[0],n[1]-i[1])},contain:function(t){var e=this.getBoundingRect(),i=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var n=0,r=i.length;n<r;n++)if("polygon"===i[n].type){var o=i[n].exterior,a=i[n].interiors;if(sg(o,t[0],t[1])){for(var s=0;s<(a?a.length:0);s++)if(sg(a[s]))continue t;return!0}}return!1},transformTo:function(t,e,i,n){var r=this.getBoundingRect(),o=r.width/r.height;i?n=n||i/o:i=o*n;for(var a=new bi(t,e,i,n),s=r.calculateTransform(a),l=this.geometries,u=0;u<l.length;u++)if("polygon"===l[u].type){for(var h=l[u].exterior,c=l[u].interiors,d=0;d<h.length;d++)yt(h[d],h[d],s);for(var f=0;f<(c?c.length:0);f++)for(d=0;d<c[f].length;d++)yt(c[f][d],c[f][d],s)}(r=this._rect).copy(a),this.center=[r.x+r.width/2,r.y+r.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new lg(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};function hg(t,a){return function(t){if(!t.UTF8Encoding)return;var e=t.UTF8Scale;null==e&&(e=1024);for(var i=t.features,n=0;n<i.length;n++)for(var r=i[n].geometry,o=r.coordinates,a=r.encodeOffsets,s=0;s<o.length;s++){var l=o[s];if("Polygon"===r.type)o[s]=ug(l,a[s],e);else if("MultiPolygon"===r.type)for(var u=0;u<l.length;u++){var h=l[u];l[u]=ug(h,a[s][u],e)}}t.UTF8Encoding=!1}(t),P(I(t.features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,i=t.geometry,n=i.coordinates,r=[];"Polygon"===i.type&&r.push({type:"polygon",exterior:n[0],interiors:n.slice(1)}),"MultiPolygon"===i.type&&D(n,function(t){t[0]&&r.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var o=new lg(e[a||"name"],r,e.cp);return o.properties=e,o})}var cg=Hr();function dg(t){return"category"===t.type?function(t){var e=t.getLabelModel(),i=pg(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:i.labelCategoryInterval}:i}(t):function(i){var t=i.scale.getTicks(),n=Gp(i);return{labels:P(t,function(t,e){return{formattedLabel:n(t,e),rawLabel:i.scale.getLabel(t),tickValue:t}})}}(t)}function fg(t,e){return"category"===t.type?function(t,e){var i,n,r=gg(t,"ticks"),o=Up(e),a=mg(r,o);if(a)return a;e.get("show")&&!t.scale.isBlank()||(i=[]);if(z(o))i=_g(t,o,!0);else if("auto"===o){var s=pg(t,t.getLabelModel());n=s.labelCategoryInterval,i=P(s.labels,function(t){return t.tickValue})}else i=yg(t,n=o,!0);return vg(r,o,{ticks:i,tickCategoryInterval:n})}(t,e):{ticks:t.scale.getTicks()}}function pg(t,e){var i,n=gg(t,"labels"),r=Up(e),o=mg(n,r);return o||vg(n,r,{labels:z(r)?_g(t,r):yg(t,i="auto"===r?function(t){var e=cg(t).autoInterval;return null!=e?e:cg(t).autoInterval=t.calculateCategoryInterval()}(t):r),labelCategoryInterval:i})}function gg(t,e){return cg(t)[e]||(cg(t)[e]=[])}function mg(t,e){for(var i=0;i<t.length;i++)if(t[i].key===e)return t[i].value}function vg(t,e,i){return t.push({key:e,value:i}),i}function yg(t,e,i){var n=Gp(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=o[0],h=r.count();0!==u&&1<l&&2<h/l&&(u=Math.round(Math.ceil(u/l)*l));var c=Xp(t),d=a.get("showMinLabel")||c,f=a.get("showMaxLabel")||c;d&&u!==o[0]&&g(o[0]);for(var p=u;p<=o[1];p+=l)g(p);function g(t){s.push(i?t:{formattedLabel:n(t),rawLabel:r.getLabel(t),tickValue:t})}return f&&p-l!==o[1]&&g(o[1]),s}function _g(t,i,n){var r=t.scale,o=Gp(t),a=[];return D(r.getTicks(),function(t){var e=r.getLabel(t);i(t,e)&&a.push(n?t:{formattedLabel:o(t),rawLabel:e,tickValue:t})}),a}function xg(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1}var wg=[0,1];function bg(t,e){var i=(t[1]-t[0])/e/2;t[0]+=i,t[1]-=i}xg.prototype={constructor:xg,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return i<=t&&t<=n},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return Pl(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&"ordinal"===n.type&&bg(i=i.slice(),n.count()),Il(t,wg,i,e)},coordToData:function(t,e){var i=this._extent,n=this.scale;this.onBand&&"ordinal"===n.type&&bg(i=i.slice(),n.count());var r=Il(t,i,wg,e);return this.scale.scale(r)},pointToData:function(t,e){},getTicksCoords:function(t){var e=(t=t||{}).tickModel||this.getTickModel(),i=P(fg(this,e).ticks,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this);return function(t,e,i,n){var r=e.length;if(!t.onBand||i||!r)return;var o,a,s=t.getExtent();if(1===r)e[0].coord=s[0],o=e[1]={coord:s[0]};else{var l=e[r-1].tickValue-e[0].tickValue,u=(e[r-1].coord-e[0].coord)/l;D(e,function(t){t.coord-=u/2});var h=t.scale.getExtent();a=1+h[1]-e[r-1].tickValue,o={coord:e[r-1].coord+u*a},e.push(o)}var c=s[0]>s[1];d(e[0].coord,s[0])&&(n?e[0].coord=s[0]:e.shift());n&&d(s[0],e[0].coord)&&e.unshift({coord:s[0]});d(s[1],o.coord)&&(n?o.coord=s[1]:e.pop());n&&d(o.coord,s[1])&&e.push({coord:s[1]});function d(t,e){return t=Tl(t),e=Tl(e),c?e<t:t<e}}(this,i,e.get("alignWithLabel"),t.clamp),i},getMinorTicksCoords:function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");return 0<t&&t<100||(t=5),P(this.scale.getMinorTicks(t),function(t){return P(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this)},getViewLabels:function(){return dg(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return function(t){var e=function(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}(t),i=Gp(t),n=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,o=r.getExtent(),a=r.count();if(o[1]-o[0]<1)return 0;var s=1;40<a&&(s=Math.max(1,Math.floor(a/40)));for(var l=o[0],u=t.dataToCoord(l+1)-t.dataToCoord(l),h=Math.abs(u*Math.cos(n)),c=Math.abs(u*Math.sin(n)),d=0,f=0;l<=o[1];l+=s){var p,g,m=un(i(l),e.font,"center","top");p=1.3*m.width,g=1.3*m.height,d=Math.max(d,p,7),f=Math.max(f,g,7)}var v=d/h,y=f/c;isNaN(v)&&(v=1/0),isNaN(y)&&(y=1/0);var _=Math.max(0,Math.floor(Math.min(v,y))),x=cg(t.model),w=t.getExtent(),b=x.lastAutoInterval,S=x.lastTickCount;return null!=b&&null!=S&&Math.abs(b-_)<=1&&Math.abs(S-a)<=1&&_<b&&x.axisExtend0===w[0]&&x.axisExtend1===w[1]?_=b:(x.lastTickCount=a,x.lastAutoInterval=_,x.axisExtend0=w[0],x.axisExtend1=w[1]),_}(this)}};var Sg=hg,Mg={};D(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){Mg[t]=tt[t]});var Ig={};function Cg(t,e){var i=t.mapDimension("defaultedLabel",!0),n=i.length;if(1===n)return Dh(t,e,i[0]);if(n){for(var r=[],o=0;o<i.length;o++){var a=Dh(t,e,i[o]);r.push(a)}return r.join(" ")}}function Tg(t,e,i){Si.call(this),this.updateData(t,e,i)}D(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){Ig[t]=dl[t]}),Yh.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,e){return Qf(this.getSource(),this,{useEncodeDefaulter:!0})},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clip:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var Ag=Tg.prototype,Dg=Tg.getSymbolSize=function(t,e){var i=t.getItemVisual(e,"symbolSize");return i instanceof Array?i.slice():[+i,+i]};function kg(t){return[t[0]/2,t[1]/2]}function Pg(t,e){this.parent.drift(t,e)}Ag._createSymbol=function(t,e,i,n,r){this.removeAll();var o=ig(t,-1,-1,2,2,e.getItemVisual(i,"color"),r);o.attr({z2:100,culling:!0,scale:kg(n)}),o.drift=Pg,this._symbolType=t,this.add(o)},Ag.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},Ag.getSymbolPath=function(){return this.childAt(0)},Ag.getScale=function(){return this.childAt(0).scale},Ag.highlight=function(){this.childAt(0).trigger("emphasis")},Ag.downplay=function(){this.childAt(0).trigger("normal")},Ag.setZ=function(t,e){var i=this.childAt(0);i.zlevel=t,i.z=e},Ag.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},Ag.updateData=function(t,e,i){this.silent=!1;var n=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,o=Dg(t,e),a=n!==this._symbolType;if(a){var s=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(n,t,e,o,s)}else{(l=this.childAt(0)).silent=!1,il(l,{scale:kg(o)},r,e)}if(this._updateCommon(t,e,o,i),a){var l=this.childAt(0),u=i&&i.fadeIn,h={scale:l.scale.slice()};u&&(h.style={opacity:l.style.opacity}),l.scale=[0,0],u&&(l.style.opacity=0),nl(l,h,r,e)}this._seriesModel=r};var Lg=["itemStyle"],Og=["emphasis","itemStyle"],zg=["label"],Eg=["emphasis","label"];function Ng(t,e){if(!this.incremental&&!this.useHoverLayer)if("emphasis"===e){var i=this.__symbolOriginalScale,n=i[1]/i[0],r={scale:[Math.max(1.1*i[0],i[0]+3),Math.max(1.1*i[1],i[1]+3*n)]};this.animateTo(r,400,"elasticOut")}else"normal"===e&&this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut")}function Rg(t){this.group=new Si,this._symbolCtor=t||Tg}Ag._updateCommon=function(i,t,e,n){var r=this.childAt(0),o=i.hostModel,a=i.getItemVisual(t,"color");"image"!==r.type?r.useStyle({strokeNoScale:!0}):r.setStyle({opacity:1,shadowBlur:null,shadowOffsetX:null,shadowOffsetY:null,shadowColor:null});var s=n&&n.itemStyle,l=n&&n.hoverItemStyle,u=n&&n.symbolOffset,h=n&&n.labelModel,c=n&&n.hoverLabelModel,d=n&&n.hoverAnimation,f=n&&n.cursorStyle;if(!n||i.hasItemOption){var p=n&&n.itemModel?n.itemModel:i.getItemModel(t);s=p.getModel(Lg).getItemStyle(["color"]),l=p.getModel(Og).getItemStyle(),u=p.getShallow("symbolOffset"),h=p.getModel(zg),c=p.getModel(Eg),d=p.getShallow("hoverAnimation"),f=p.getShallow("cursor")}else l=k({},l);var g=r.style,m=i.getItemVisual(t,"symbolRotate");r.attr("rotation",(m||0)*Math.PI/180||0),u&&r.attr("position",[Cl(u[0],e[0]),Cl(u[1],e[1])]),f&&r.attr("cursor",f),r.setColor(a,n&&n.symbolInnerColor),r.setStyle(s);var v=i.getItemVisual(t,"opacity");null!=v&&(g.opacity=v);var y=i.getItemVisual(t,"liftZ"),_=r.__z2Origin;null!=y?null==_&&(r.__z2Origin=r.z2,r.z2+=y):null!=_&&(r.z2=_,r.__z2Origin=null);var x=n&&n.useNameLabel;Ys(g,l,h,c,{labelFetcher:o,labelDataIndex:t,defaultText:function(t,e){return x?i.getName(t):Cg(i,t)},isRectText:!0,autoColor:a}),r.__symbolOriginalScale=kg(e),r.hoverStyle=l,r.highDownOnUpdate=d&&o.isAnimationEnabled()?Ng:null,Gs(r)},Ag.fadeOut=function(t,e){var i=this.childAt(0);this.silent=i.silent=!0,e&&e.keepLabel||(i.style.text=null),il(i,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},w(Tg,Si);var Bg=Rg.prototype;function Vg(t,e,i,n){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(n.isIgnore&&n.isIgnore(i))&&!(n.clipShape&&!n.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(i,"symbol")}function Fg(t){return null==t||N(t)||(t={isIgnore:t}),t||{}}function Hg(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}function Wg(t,e,i){var n,r=t.getBaseAxis(),o=t.getOtherAxis(r),a=function(t,e){var i=0,n=t.scale.getExtent();"start"===e?i=n[0]:"end"===e?i=n[1]:0<n[0]?i=n[0]:n[1]<0&&(i=n[1]);return i}(o,i),s=r.dim,l=o.dim,u=e.mapDimension(l),h=e.mapDimension(s),c="x"===l||"radius"===l?1:0,d=P(t.dimensions,function(t){return e.mapDimension(t)}),f=e.getCalculationInfo("stackResultDimension");return(n|=$f(e,d[0]))&&(d[0]=f),(n|=$f(e,d[1]))&&(d[1]=f),{dataDimsForPoint:d,valueStart:a,valueAxisDim:l,baseAxisDim:s,stacked:!!n,valueDim:u,baseDim:h,baseDataOffset:c,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function Gg(t,e,i,n){var r=NaN;t.stacked&&(r=i.get(i.getCalculationInfo("stackedOverDimension"),n)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=i.get(t.baseDim,n),a[1-o]=r,e.dataToPoint(a)}Bg.updateData=function(r,o){o=Fg(o);var a=this.group,s=r.hostModel,l=this._data,u=this._symbolCtor,h=Hg(r);l||a.removeAll(),r.diff(l).add(function(t){var e=r.getItemLayout(t);if(Vg(r,e,t,o)){var i=new u(r,t,h);i.attr("position",e),r.setItemGraphicEl(t,i),a.add(i)}}).update(function(t,e){var i=l.getItemGraphicEl(e),n=r.getItemLayout(t);Vg(r,n,t,o)?(i?(i.updateData(r,t,h),il(i,{position:n},s)):(i=new u(r,t)).attr("position",n),a.add(i),r.setItemGraphicEl(t,i)):a.remove(i)}).remove(function(t){var e=l.getItemGraphicEl(t);e&&e.fadeOut(function(){a.remove(e)})}).execute(),this._data=r},Bg.isPersistent=function(){return!0},Bg.updateLayout=function(){var n=this._data;n&&n.eachItemGraphicEl(function(t,e){var i=n.getItemLayout(e);t.attr("position",i)})},Bg.incrementalPrepareUpdate=function(t){this._seriesScope=Hg(t),this._data=null,this.group.removeAll()},Bg.incrementalUpdate=function(t,e,i){function n(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}i=Fg(i);for(var r=t.start;r<t.end;r++){var o=e.getItemLayout(r);if(Vg(e,o,r,i)){var a=new this._symbolCtor(e,r,this._seriesScope);a.traverse(n),a.attr("position",o),this.group.add(a),e.setItemGraphicEl(r,a)}}},Bg.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var Zg=_t,Ug=xt,Xg=at,Yg=nt,jg=[],qg=[],$g=[];function Kg(t){return isNaN(t[0])||isNaN(t[1])}function Qg(t,e,i,n,r,o,a,s,l,u){return"none"!==u&&u?function(t,e,i,n,r,o,a,s,l,u,h){for(var c=0,d=i,f=0;f<n;f++){var p=e[d];if(r<=d||d<0)break;if(Kg(p)){if(h){d+=o;continue}break}if(d===i)t[0<o?"moveTo":"lineTo"](p[0],p[1]);else if(0<l){var g=e[c],m="y"===u?1:0,v=(p[m]-g[m])*l;Yg(qg,g),qg[m]=g[m]+v,Yg($g,p),$g[m]=p[m]-v,t.bezierCurveTo(qg[0],qg[1],$g[0],$g[1],p[0],p[1])}else t.lineTo(p[0],p[1]);c=d,d+=o}return f}.apply(this,arguments):function(t,e,i,n,r,o,a,s,l,u,h){for(var c=0,d=i,f=0;f<n;f++){var p=e[d];if(r<=d||d<0)break;if(Kg(p)){if(h){d+=o;continue}break}if(d===i)t[0<o?"moveTo":"lineTo"](p[0],p[1]),Yg(qg,p);else if(0<l){var g=d+o,m=e[g];if(h)for(;m&&Kg(e[g]);)m=e[g+=o];var v=.5,y=e[c];if(!(m=e[g])||Kg(m))Yg($g,p);else{var _,x;if(Kg(m)&&!h&&(m=p),st(jg,m,y),"x"===u||"y"===u){var w="x"===u?0:1;_=Math.abs(p[w]-y[w]),x=Math.abs(p[w]-m[w])}else _=gt(p,y),x=gt(p,m);Xg($g,p,jg,-l*(1-(v=x/(x+_))))}Zg(qg,qg,s),Ug(qg,qg,a),Zg($g,$g,s),Ug($g,$g,a),t.bezierCurveTo(qg[0],qg[1],$g[0],$g[1],p[0],p[1]),Xg(qg,p,jg,l*v)}else t.lineTo(p[0],p[1]);c=d,d+=o}return f}.apply(this,arguments)}function Jg(t,e){var i=[1/0,1/0],n=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var o=t[r];o[0]<i[0]&&(i[0]=o[0]),o[1]<i[1]&&(i[1]=o[1]),o[0]>n[0]&&(n[0]=o[0]),o[1]>n[1]&&(n[1]=o[1])}return{min:e?i:n,max:e?n:i}}var tm=xa.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:Va(xa.prototype.brush),buildPath:function(t,e){var i=e.points,n=0,r=i.length,o=Jg(i,e.smoothConstraint);if(e.connectNulls){for(;0<r&&Kg(i[r-1]);r--);for(;n<r&&Kg(i[n]);n++);}for(;n<r;)n+=Qg(t,i,n,r,r,1,o.min,o.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),em=xa.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:Va(xa.prototype.brush),buildPath:function(t,e){var i=e.points,n=e.stackedOnPoints,r=0,o=i.length,a=e.smoothMonotone,s=Jg(i,e.smoothConstraint),l=Jg(n,e.smoothConstraint);if(e.connectNulls){for(;0<o&&Kg(i[o-1]);o--);for(;r<o&&Kg(i[r]);r++);}for(;r<o;){var u=Qg(t,i,r,o,o,1,s.min,s.max,e.smooth,a,e.connectNulls);Qg(t,n,r+u-1,u,o,-1,l.min,l.max,e.stackedOnSmooth,a,e.connectNulls),r+=u+1,t.closePath()}}});function im(t,e,i){var n=t.getArea(),r=t.getBaseAxis().isHorizontal(),o=n.x,a=n.y,s=n.width,l=n.height,u=i.get("lineStyle.width")||2;o-=u/2,a-=u/2,s+=u,l+=u,o=Math.floor(o),s=Math.round(s);var h=new Ja({shape:{x:o,y:a,width:s,height:l}});return e&&(h.shape[r?"width":"height"]=0,nl(h,{shape:{width:s,height:l}},i)),h}function nm(t,e,i){var n=t.getArea(),r=new Wa({shape:{cx:Tl(t.cx,1),cy:Tl(t.cy,1),r0:Tl(n.r0,1),r:Tl(n.r,1),startAngle:n.startAngle,endAngle:n.endAngle,clockwise:n.clockwise}});return e&&(r.shape.endAngle=n.startAngle,nl(r,{shape:{endAngle:n.endAngle}},i)),r}function rm(t,e){if(t.length===e.length){for(var i=0;i<t.length;i++){var n=t[i],r=e[i];if(n[0]!==r[0]||n[1]!==r[1])return}return!0}}function om(t,e){var i=[],n=[],r=[],o=[];return Eo(t,i,n),Eo(e,r,o),Math.max(Math.abs(i[0]-r[0]),Math.abs(i[1]-r[1]),Math.abs(n[0]-o[0]),Math.abs(n[1]-o[1]))}function am(t){return"number"==typeof t?t:t?.5:0}function sm(t,e,i){for(var n=e.getBaseAxis(),r="x"===n.dim||"radius"===n.dim?0:1,o=[],a=0;a<t.length-1;a++){var s=t[a+1],l=t[a];o.push(l);var u=[];switch(i){case"end":u[r]=s[r],u[1-r]=l[1-r],o.push(u);break;case"middle":var h=(l[r]+s[r])/2,c=[];u[r]=c[r]=h,u[1-r]=l[1-r],c[1-r]=s[1-r],o.push(u),o.push(c);break;default:u[r]=l[r],u[1-r]=s[1-r],o.push(u)}}return t[a]&&o.push(t[a]),o}function lm(t,e,i){var n=t.get("showAllSymbol"),r="auto"===n;if(!n||r){var o=i.getAxesByScale("ordinal")[0];if(o&&(!r||!function(t,e){var i=t.getExtent(),n=Math.abs(i[1]-i[0])/t.scale.count();isNaN(n)&&(n=0);for(var r=e.count(),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*Tg.getSymbolSize(e,a)[t.isHorizontal()?1:0]>n)return!1;return!0}(o,e))){var a=e.mapDimension(o.dim),s={};return D(o.getViewLabels(),function(t){s[t.tickValue]=1}),function(t){return!s.hasOwnProperty(e.get(a,t))}}}}function um(t,e,i){if("cartesian2d"!==t.type)return nm(t,e,i);var n=t.getBaseAxis().isHorizontal(),r=im(t,e,i);if(!i.get("clip",!0)){var o=r.shape,a=Math.max(o.width,o.height);n?(o.y-=a,o.height+=2*a):(o.x-=a,o.width+=2*a)}return r}ac.extend({type:"line",init:function(){var t=new Si,e=new Rg;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,i){var n=t.coordinateSystem,r=this.group,o=t.getData(),a=t.getModel("lineStyle"),s=t.getModel("areaStyle"),l=o.mapArray(o.getItemLayout),u="polar"===n.type,h=this._coordSys,c=this._symbolDraw,d=this._polyline,f=this._polygon,p=this._lineGroup,g=t.get("animation"),m=!s.isEmpty(),v=s.get("origin"),y=function(t,e,i){if(!i.valueDim)return[];for(var n=[],r=0,o=e.count();r<o;r++)n.push(Gg(i,t,e,r));return n}(n,o,Wg(n,o,v)),_=t.get("showSymbol"),x=_&&!u&&lm(t,o,n),w=this._data;w&&w.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),w.setItemGraphicEl(e,null))}),_||c.remove(),r.add(p);var b,S=!u&&t.get("step");n&&n.getArea&&t.get("clip",!0)&&(null!=(b=n.getArea()).width?(b.x-=.1,b.y-=.1,b.width+=.2,b.height+=.2):b.r0&&(b.r0-=.5,b.r1+=.5)),this._clipShapeForSymbol=b,d&&h.type===n.type&&S===this._step?(m&&!f?f=this._newPolygon(l,y,n,g):f&&!m&&(p.remove(f),f=this._polygon=null),p.setClipPath(um(n,!1,t)),_&&c.updateData(o,{isIgnore:x,clipShape:b}),o.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),rm(this._stackedOnPoints,y)&&rm(this._points,l)||(g?this._updateAnimation(o,y,n,i,S,v):(S&&(l=sm(l,n,S),y=sm(y,n,S)),d.setShape({points:l}),f&&f.setShape({points:l,stackedOnPoints:y})))):(_&&c.updateData(o,{isIgnore:x,clipShape:b}),S&&(l=sm(l,n,S),y=sm(y,n,S)),d=this._newPolyline(l,n,g),m&&(f=this._newPolygon(l,y,n,g)),p.setClipPath(um(n,!0,t)));var M=function(t,e){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var n,r,o=i.length-1;0<=o;o--){var a=i[o].dimension,s=t.dimensions[a],l=t.getDimensionInfo(s);if("x"===(n=l&&l.coordDim)||"y"===n){r=i[o];break}}if(r){var u=e.getAxis(n),h=P(r.stops,function(t){return{coord:u.toGlobalCoord(u.dataToCoord(t.value)),color:t.color}}),c=h.length,d=r.outerColors.slice();c&&h[0].coord>h[c-1].coord&&(h.reverse(),d.reverse());var f=h[0].coord-10,p=h[c-1].coord+10,g=p-f;if(g<.001)return"transparent";D(h,function(t){t.offset=(t.coord-f)/g}),h.push({offset:c?h[c-1].offset:.5,color:d[1]||"transparent"}),h.unshift({offset:c?h[0].offset:.5,color:d[0]||"transparent"});var m=new ls(0,0,0,0,h,!0);return m[n]=f,m[n+"2"]=p,m}}}(o,n)||o.getVisual("color");d.useStyle(A(a.getLineStyle(),{fill:"none",stroke:M,lineJoin:"bevel"}));var I=t.get("smooth");if(I=am(t.get("smooth")),d.setShape({smooth:I,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),f){var C=o.getCalculationInfo("stackedOnSeries"),T=0;f.useStyle(A(s.getAreaStyle(),{fill:M,opacity:.7,lineJoin:"bevel"})),C&&(T=am(C.get("smooth"))),f.setShape({smooth:I,stackedOnSmooth:T,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=o,this._coordSys=n,this._stackedOnPoints=y,this._points=l,this._step=S,this._valueOrigin=v},dispose:function(){},highlight:function(t,e,i,n){var r=t.getData(),o=Fr(r,n);if(!(o instanceof Array)&&null!=o&&0<=o){var a=r.getItemGraphicEl(o);if(!a){var s=r.getItemLayout(o);if(!s)return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s[0],s[1]))return;(a=new Tg(r,o)).position=s,a.setZ(t.get("zlevel"),t.get("z")),a.ignore=isNaN(s[0])||isNaN(s[1]),a.__temp=!0,r.setItemGraphicEl(o,a),a.stopSymbolAnimation(!0),this.group.add(a)}a.highlight()}else ac.prototype.highlight.call(this,t,e,i,n)},downplay:function(t,e,i,n){var r=t.getData(),o=Fr(r,n);if(null!=o&&0<=o){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else ac.prototype.downplay.call(this,t,e,i,n)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new tm({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e},_newPolygon:function(t,e){var i=this._polygon;return i&&this._lineGroup.remove(i),i=new em({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(i),this._polygon=i},_updateAnimation:function(t,e,i,n,r,o){var a=this._polyline,s=this._polygon,l=t.hostModel,u=function(t,e,i,n,r,o,a,s){for(var l=function(t,e){var i=[];return e.diff(t).add(function(t){i.push({cmd:"+",idx:t})}).update(function(t,e){i.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){i.push({cmd:"-",idx:t})}).execute(),i}(t,e),u=[],h=[],c=[],d=[],f=[],p=[],g=[],m=Wg(r,e,a),v=Wg(o,t,s),y=0;y<l.length;y++){var _=l[y],x=!0;switch(_.cmd){case"=":var w=t.getItemLayout(_.idx),b=e.getItemLayout(_.idx1);(isNaN(w[0])||isNaN(w[1]))&&(w=b.slice()),u.push(w),h.push(b),c.push(i[_.idx]),d.push(n[_.idx1]),g.push(e.getRawIndex(_.idx1));break;case"+":var S=_.idx;u.push(r.dataToPoint([e.get(m.dataDimsForPoint[0],S),e.get(m.dataDimsForPoint[1],S)])),h.push(e.getItemLayout(S).slice()),c.push(Gg(m,r,e,S)),d.push(n[S]),g.push(e.getRawIndex(S));break;case"-":S=_.idx;var M=t.getRawIndex(S);M!==S?(u.push(t.getItemLayout(S)),h.push(o.dataToPoint([t.get(v.dataDimsForPoint[0],S),t.get(v.dataDimsForPoint[1],S)])),c.push(i[S]),d.push(Gg(v,o,t,S)),g.push(M)):x=!1}x&&(f.push(_),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});var I=[],C=[],T=[],A=[],D=[];for(y=0;y<p.length;y++){S=p[y];I[y]=u[S],C[y]=h[S],T[y]=c[S],A[y]=d[S],D[y]=f[S]}return{current:I,next:C,stackedOnCurrent:T,stackedOnNext:A,status:D}}(this._data,t,this._stackedOnPoints,e,this._coordSys,i,this._valueOrigin,o),h=u.current,c=u.stackedOnCurrent,d=u.next,f=u.stackedOnNext;if(r&&(h=sm(u.current,i,r),c=sm(u.stackedOnCurrent,i,r),d=sm(u.next,i,r),f=sm(u.stackedOnNext,i,r)),3e3<om(h,d)||s&&3e3<om(c,f))return a.setShape({points:d}),void(s&&s.setShape({points:d,stackedOnPoints:f}));a.shape.__points=u.current,a.shape.points=h,il(a,{shape:{points:d}},l),s&&(s.setShape({points:h,stackedOnPoints:c}),il(s,{shape:{points:d,stackedOnPoints:f}},l));for(var p=[],g=u.status,m=0;m<g.length;m++){if("="===g[m].cmd){var v=t.getItemGraphicEl(g[m].idx1);v&&p.push({el:v,ptIdx:m})}}a.animators&&a.animators.length&&a.animators[0].during(function(){for(var t=0;t<p.length;t++){p[t].el.attr("position",a.shape.__points[p[t].ptIdx])}})},remove:function(t){var i=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(t,e){t.__temp&&(i.remove(t),n.setItemGraphicEl(e,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});function hm(t,a,s){return{seriesType:t,performRawSeries:!0,reset:function(u,t,e){var i=u.getData(),h=u.get("symbol"),c=u.get("symbolSize"),n=u.get("symbolKeepAspect"),d=u.get("symbolRotate"),f=z(h),p=z(c),g=z(d),m=f||p||g,r=!f&&h?h:a,o=p?null:c;if(i.setVisual({legendSymbol:s||r,symbol:r,symbolSize:o,symbolKeepAspect:n,symbolRotate:d}),!t.isSeriesFiltered(u))return{dataEach:i.hasItemOption||m?function(t,e){if(m){var i=u.getRawValue(e),n=u.getDataParams(e);f&&t.setItemVisual(e,"symbol",h(i,n)),p&&t.setItemVisual(e,"symbolSize",c(i,n)),g&&t.setItemVisual(e,"symbolRotate",d(i,n))}if(t.hasItemOption){var r=t.getItemModel(e),o=r.getShallow("symbol",!0),a=r.getShallow("symbolSize",!0),s=r.getShallow("symbolRotate",!0),l=r.getShallow("symbolKeepAspect",!0);null!=o&&t.setItemVisual(e,"symbol",o),null!=a&&t.setItemVisual(e,"symbolSize",a),null!=s&&t.setItemVisual(e,"symbolRotate",s),null!=l&&t.setItemVisual(e,"symbolKeepAspect",l)}}:null}}}}function cm(t){return{seriesType:t,plan:nc(),reset:function(t){var e=t.getData(),c=t.coordinateSystem,d=t.pipelineContext.large;if(c){var f=P(c.dimensions,function(t){return e.mapDimension(t)}).slice(0,2),p=f.length,i=e.getCalculationInfo("stackResultDimension");return $f(e,f[0])&&(f[0]=i),$f(e,f[1])&&(f[1]=i),p&&{progress:function(t,e){for(var i=t.end-t.start,n=d&&new Float32Array(i*p),r=t.start,o=0,a=[],s=[];r<t.end;r++){var l;if(1===p){var u=e.get(f[0],r);l=!isNaN(u)&&c.dataToPoint(u,null,s)}else{u=a[0]=e.get(f[0],r);var h=a[1]=e.get(f[1],r);l=!isNaN(u)&&!isNaN(h)&&c.dataToPoint(a,null,s)}d?(n[o++]=l?l[0]:NaN,n[o++]=l?l[1]:NaN):e.setItemLayout(r,l&&l.slice()||[NaN,NaN])}d&&e.setLayout("symbolPoints",n)}}}}}}function dm(t,e){return Math.round(t.length/2)}var fm={average:function(t){for(var e=0,i=0,n=0;n<t.length;n++)isNaN(t[n])||(e+=t[n],i++);return 0===i?NaN:e/i},sum:function(t){for(var e=0,i=0;i<t.length;i++)e+=t[i]||0;return e},max:function(t){for(var e=-1/0,i=0;i<t.length;i++)t[i]>e&&(e=t[i]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,i=0;i<t.length;i++)t[i]<e&&(e=t[i]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}};function pm(t){return this._axes[t]}function gm(t){this._axes={},this._dimList=[],this.name=t||""}function mm(t){gm.call(this,t)}gm.prototype={constructor:gm,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return P(this._dimList,pm,this)},getAxesByScale:function(e){return e=e.toLowerCase(),I(this.getAxes(),function(t){return t.scale.type===e})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i.length;r++){var o=i[r],a=this._axes[o];n[o]=a[e](t[o])}return n}},mm.prototype={constructor:mm,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),i=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&i.contain(i.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,i){var n=this.getAxis("x"),r=this.getAxis("y");return(i=i||[])[0]=n.toGlobalCoord(n.dataToCoord(t[0])),i[1]=r.toGlobalCoord(r.dataToCoord(t[1])),i},clampData:function(t,e){var i=this.getAxis("x").scale,n=this.getAxis("y").scale,r=i.getExtent(),o=n.getExtent(),a=i.parse(t[0]),s=n.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},pointToData:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return(e=e||[])[0]=i.coordToData(i.toLocalCoord(t[0])),e[1]=n.coordToData(n.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),i=Math.min(t[0],t[1]),n=Math.min(e[0],e[1]);return new bi(i,n,Math.max(t[0],t[1])-i,Math.max(e[0],e[1])-n)}},w(mm,gm);function vm(t,e,i,n,r){xg.call(this,t,e,i),this.type=n||"value",this.position=r||"bottom"}vm.prototype={constructor:vm,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},w(vm,xg);var ym={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},_m={};_m.categoryAxis=m({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},ym),_m.valueAxis=m({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},ym),_m.timeAxis=A({scale:!0,min:"dataMin",max:"dataMax"},_m.valueAxis),_m.logAxis=A({scale:!0,logBase:10},_m.valueAxis);function xm(o,t,a,e){D(wm,function(r){t.extend({type:o+"Axis."+r,mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?cu(t):{};m(t,e.getTheme().get(r+"Axis")),m(t,this.getDefaultOption()),t.type=a(o,t),i&&hu(t,n,i)},optionUpdated:function(){"category"===this.option.type&&(this.__ordinalMeta=tp.createByAxisModel(this))},getCategories:function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:p([{},_m[r+"Axis"],e],!0)})}),vu.registerSubTypeDefaulter(o+"Axis",T(a,o))}var wm=["value","category","time","log"],bm=vu.extend({type:"cartesian2dAxis",axis:null,init:function(){bm.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){bm.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){bm.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});function Sm(t,e){return e.type||(e.data?"category":"value")}m(bm.prototype,Yp);var Mm={offset:0};function Im(t,e){return t.getCoordSysModel()===e}function Cm(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this.model=t}xm("x",bm,Sm,Mm),xm("y",bm,Sm,Mm),vu.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var Tm=Cm.prototype;function Am(t,e,i,n){i.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],a=i.model,s=a.get("axisLine.onZero"),l=a.get("axisLine.onZeroAxisIndex");if(s){if(null!=l)Dm(o[l])&&(r=o[l]);else for(var u in o)if(o.hasOwnProperty(u)&&Dm(o[u])&&!n[h(o[u])]){r=o[u];break}r&&(n[h(r)]=!0)}function h(t){return t.dim+"_"+t.index}}function Dm(t){return t&&"category"!==t.type&&"time"!==t.type&&function(t){var e=t.scale.getExtent(),i=e[0],n=e[1];return!(0<i&&0<n||i<0&&n<0)}(t)}Tm.type="grid",Tm.axisPointerEnabled=!0,Tm.getRect=function(){return this._rect},Tm.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model),D(i.x,function(t){Hp(t.scale,t.model)}),D(i.y,function(t){Hp(t.scale,t.model)});var n={};D(i.x,function(t){Am(i,"y",t,n)}),D(i.y,function(t){Am(i,"x",t,n)}),this.resize(this.model,e)},Tm.resize=function(t,e,i){var r=lu(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var n=this._axesList;function o(){D(n,function(t){var e=t.isHorizontal(),i=e?[0,r.width]:[0,r.height],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),function(t,e){var i=t.getExtent(),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}(t,e?r.x:r.y)})}o(),!i&&t.get("containLabel")&&(D(n,function(t){if(!t.model.get("axisLabel.inside")){var e=function(t){var e=t.model,i=t.scale;if(e.get("axisLabel.show")&&!i.isBlank()){var n,r,o="category"===t.type,a=i.getExtent();r=o?i.count():(n=i.getTicks()).length;var s,l,u,h,c,d,f,p,g,m=t.getLabelModel(),v=Gp(t),y=1;40<r&&(y=Math.ceil(r/40));for(var _=0;_<r;_+=y){var x=v(n?n[_]:a[0]+_),w=m.getTextRect(x),b=(l=w,u=m.get("rotate")||0,void 0,h=u*Math.PI/180,c=l.plain(),d=c.width,f=c.height,p=d*Math.abs(Math.cos(h))+Math.abs(f*Math.sin(h)),g=d*Math.abs(Math.sin(h))+Math.abs(f*Math.cos(h)),new bi(c.x,c.y,p,g));s?s.union(b):s=b}return s}}(t);if(e){var i=t.isHorizontal()?"height":"width",n=t.model.get("axisLabel.margin");r[i]-=e[i]+n,"top"===t.position?r.y+=e.height+n:"left"===t.position&&(r.x+=e.width+n)}}}),o())},Tm.getAxis=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)if(i.hasOwnProperty(n))return i[n];return i[e]}},Tm.getAxes=function(){return this._axesList.slice()},Tm.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}N(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,r=this._coordsList;n<r.length;n++)if(r[n].getAxis("x").index===t||r[n].getAxis("y").index===e)return r[n]},Tm.getCartesians=function(){return this._coordsList.slice()},Tm.convertToPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},Tm.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},Tm._findConvertTarget=function(t,e){var i,n,r=e.seriesModel,o=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],a=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)x(l,i=r.coordinateSystem)<0&&(i=null);else if(o&&a)i=this.getCartesian(o.componentIndex,a.componentIndex);else if(o)n=this.getAxis("x",o.componentIndex);else if(a)n=this.getAxis("y",a.componentIndex);else if(s){s.coordinateSystem===this&&(i=this._coordsList[0])}return{cartesian:i,axis:n}},Tm.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},Tm._initCartesian=function(a,t,e){var s={left:!1,right:!1,top:!1,bottom:!1},l={x:{},y:{}},u={x:0,y:0};if(t.eachComponent("xAxis",i("x"),this),t.eachComponent("yAxis",i("y"),this),!u.x||!u.y)return this._axesMap={},void(this._axesList=[]);function i(o){return function(t,e){if(Im(t,a)){var i=t.get("position");"x"===o?"top"!==i&&"bottom"!==i&&(i=s.bottom?"top":"bottom"):"left"!==i&&"right"!==i&&(i=s.left?"right":"left"),s[i]=!0;var n=new vm(o,Wp(t),[0,0],t.get("type"),i),r="category"===n.type;n.onBand=r&&t.get("boundaryGap"),n.inverse=t.get("inverse"),(t.axis=n).model=t,n.grid=this,n.index=e,this._axesList.push(n),l[o][e]=n,u[o]++}}}D((this._axesMap=l).x,function(r,o){D(l.y,function(t,e){var i="x"+o+"y"+e,n=new mm(i);n.grid=this,n.model=a,this._coordsMap[i]=n,this._coordsList.push(n),n.addAxis(r),n.addAxis(t)},this)},this)},Tm._updateScale=function(l,u){function h(e,i){D(e.mapDimension(i.dim,!0),function(t){i.scale.unionExtentFromData(e,Kf(e,t))})}D(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),l.eachSeries(function(t){if(Lm(t)){var e=Pm(t,l),i=e[0],n=e[1];if(!Im(i,u)||!Im(n,u))return;var r=this.getCartesian(i.componentIndex,n.componentIndex),o=t.getData(),a=r.getAxis("x"),s=r.getAxis("y");"list"===o.type&&(h(o,a,t),h(o,s,t))}},this)},Tm.getTooltipAxes=function(n){var r=[],o=[];return D(this.getCartesians(),function(t){var e=null!=n&&"auto"!==n?t.getAxis(n):t.getBaseAxis(),i=t.getOtherAxis(e);x(r,e)<0&&r.push(e),x(o,i)<0&&o.push(i)}),{baseAxes:r,otherAxes:o}};var km=["xAxis","yAxis"];function Pm(e){return P(km,function(t){return e.getReferringComponents(t)[0]})}function Lm(t){return"cartesian2d"===t.get("coordinateSystem")}Cm.create=function(n,r){var o=[];return n.eachComponent("grid",function(t,e){var i=new Cm(t,n,r);i.name="grid_"+e,i.resize(t,r,!0),t.coordinateSystem=i,o.push(i)}),n.eachSeries(function(t){if(Lm(t)){var e=Pm(t),i=e[0],n=e[1],r=i.getCoordSysModel().coordinateSystem;t.coordinateSystem=r.getCartesian(i.componentIndex,n.componentIndex)}}),o},Cm.dimensions=Cm.prototype.dimensions=mm.prototype.dimensions,ju.register("cartesian2d",Cm);function Om(t,e){this.opt=e,this.axisModel=t,A(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new Si;var i=new Si({position:e.position.slice(),rotation:e.rotation});i.updateTransform(),this._transform=i.transform,this._dumbGroup=i}var zm=Math.PI;Om.prototype={constructor:Om,hasBuilder:function(t){return!!Em[t]},add:function(t){Em[t].call(this)},getGroup:function(){return this.group}};var Em={axisLine:function(){var o=this.opt,t=this.axisModel;if(t.get("axisLine.show")){var e=this.axisModel.axis.getExtent(),i=this._transform,a=[e[0],0],n=[e[1],0];i&&(yt(a,a,i),yt(n,n,i));var s=k({lineCap:"round"},t.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new es({anid:"line",subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:n[0],y2:n[1]},style:s,strokeContainThreshold:o.strokeContainThreshold||5,silent:!0,z2:1}));var l=t.get("axisLine.symbol"),r=t.get("axisLine.symbolSize"),u=t.get("axisLine.symbolOffset")||0;if("number"==typeof u&&(u=[u,u]),null!=l){"string"==typeof l&&(l=[l,l]),"string"!=typeof r&&"number"!=typeof r||(r=[r,r]);var h=r[0],c=r[1];D([{rotate:o.rotation+Math.PI/2,offset:u[0],r:0},{rotate:o.rotation-Math.PI/2,offset:u[1],r:Math.sqrt((a[0]-n[0])*(a[0]-n[0])+(a[1]-n[1])*(a[1]-n[1]))}],function(t,e){if("none"!==l[e]&&null!=l[e]){var i=ig(l[e],-h/2,-c/2,h,c,s.stroke,!0),n=t.r+t.offset,r=[a[0]+n*Math.cos(o.rotation),a[1]-n*Math.sin(o.rotation)];i.attr({rotation:t.rotate,position:r,silent:!0,z2:11}),this.group.add(i)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,i=function(t,e,i){var n=e.axis,r=e.getModel("axisTick");if(!r.get("show")||n.scale.isBlank())return;for(var o=r.getModel("lineStyle"),a=i.tickDirection*r.get("length"),s=Wm(n.getTicksCoords(),t._transform,a,A(o.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),l=0;l<s.length;l++)t.group.add(s[l]);return s}(this,t,e);!function(t,e,i){if(Xp(t.axis))return;var n=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");i=i||[];var o=(e=e||[])[0],a=e[1],s=e[e.length-1],l=e[e.length-2],u=i[0],h=i[1],c=i[i.length-1],d=i[i.length-2];!1===n?(Vm(o),Vm(u)):Fm(o,a)&&(n?(Vm(a),Vm(h)):(Vm(o),Vm(u)));!1===r?(Vm(s),Vm(c)):Fm(l,s)&&(r?(Vm(l),Vm(d)):(Vm(s),Vm(c)))}(t,function(u,h,c){var d=h.axis;if(!H(c.axisLabelShow,h.get("axisLabel.show"))||d.scale.isBlank())return;var f=h.getModel("axisLabel"),p=f.get("margin"),t=d.getViewLabels(),e=(H(c.labelRotate,f.get("rotate"))||0)*zm/180,g=Rm(c.rotation,e,c.labelDirection),m=h.getCategories&&h.getCategories(!0),v=[],y=Bm(h),_=h.get("triggerEvent");return D(t,function(t,e){var i=t.tickValue,n=t.formattedLabel,r=t.rawLabel,o=f;m&&m[i]&&m[i].textStyle&&(o=new _l(m[i].textStyle,f,h.ecModel));var a=o.getTextColor()||h.get("axisLine.lineStyle.color"),s=[d.dataToCoord(i),c.labelOffset+c.labelDirection*p],l=new Ba({anid:"label_"+i,position:s,rotation:g.rotation,silent:y,z2:10});js(l.style,o,{text:n,textAlign:o.getShallow("align",!0)||g.textAlign,textVerticalAlign:o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||g.textVerticalAlign,textFill:"function"==typeof a?a("category"===d.type?r:"value"===d.type?i+"":i,e):a}),_&&(l.eventData=Nm(h),l.eventData.targetType="axisLabel",l.eventData.value=r),u._dumbGroup.add(l),l.updateTransform(),v.push(l),u.group.add(l),l.decomposeTransform()}),v}(this,t,e),i),function(t,e,i){var n=e.axis,r=e.getModel("minorTick");if(!r.get("show")||n.scale.isBlank())return;var o=n.getMinorTicksCoords();if(!o.length)return;for(var a=r.getModel("lineStyle"),s=i.tickDirection*r.get("length"),l=A(a.getLineStyle(),A(e.getModel("axisTick").getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")})),u=0;u<o.length;u++)for(var h=Wm(o[u],t._transform,s,l,"minorticks_"+u),c=0;c<h.length;c++)t.group.add(h[c])}(this,t,e)},axisName:function(){var t=this.opt,e=this.axisModel,i=H(t.axisName,e.get("name"));if(i){var n,r,o=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,u=this.axisModel.axis.getExtent(),h=u[0]>u[1]?-1:1,c=["start"===o?u[0]-h*l:"end"===o?u[1]+h*l:(u[0]+u[1])/2,Hm(o)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*zm/180),Hm(o)?n=Rm(t.rotation,null!=d?d:t.rotation,a):(n=function(t,e,i,n){var r,o,a=Ol(i-t.rotation),s=n[0]>n[1],l="start"===e&&!s||"start"!==e&&s;r=zl(a-zm/2)?(o=l?"bottom":"top","center"):zl(a-1.5*zm)?(o=l?"top":"bottom","center"):(o="middle",a<1.5*zm&&zm/2<a?l?"left":"right":l?"right":"left");return{rotation:a,textAlign:r,textVerticalAlign:o}}(t,o,d||0,u),null!=(r=t.axisNameAvailableWidth)&&(r=Math.abs(r/Math.sin(n.rotation)),isFinite(r)||(r=null)));var f=s.getFont(),p=e.get("nameTruncate",!0)||{},g=p.ellipsis,m=H(t.nameTruncateMaxWidth,p.maxWidth,r),v=null!=g&&null!=m?tu(i,m,f,g,{minChar:2,placeholder:p.placeholder}):i,y=e.get("tooltip",!0),_=e.mainType,x={componentType:_,name:i,$vars:["name"]};x[_+"Index"]=e.componentIndex;var w=new Ba({anid:"name",__fullText:i,__truncatedText:v,position:c,rotation:n.rotation,silent:Bm(e),z2:1,tooltip:y&&y.show?k({content:i,formatter:function(){return i},formatterParams:x},y):null});js(w.style,s,{text:v,textFont:f,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:s.get("align")||n.textAlign,textVerticalAlign:s.get("verticalAlign")||n.textVerticalAlign}),e.get("triggerEvent")&&(w.eventData=Nm(e),w.eventData.targetType="axisName",w.eventData.name=i),this._dumbGroup.add(w),w.updateTransform(),this.group.add(w),w.decomposeTransform()}}},Nm=Om.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},Rm=Om.innerTextLayout=function(t,e,i){var n,r=Ol(e-t);return{rotation:r,textAlign:zl(r)?(n=0<i?"top":"bottom","center"):zl(r-zm)?(n=0<i?"bottom":"top","center"):(n="middle",0<r&&r<zm?0<i?"right":"left":0<i?"left":"right"),textVerticalAlign:n}};var Bm=Om.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)};function Vm(t){t&&(t.ignore=!0)}function Fm(t,e){var i=t&&t.getBoundingRect().clone(),n=e&&e.getBoundingRect().clone();if(i&&n){var r=ie([]);return ae(r,r,-t.rotation),i.applyTransform(re([],r,t.getLocalTransform())),n.applyTransform(re([],r,e.getLocalTransform())),i.intersect(n)}}function Hm(t){return"middle"===t||"center"===t}function Wm(t,e,i,n,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;a[0]=u,s[a[1]=0]=u,s[1]=i,e&&(yt(a,a,e),yt(s,s,e));var h=new es({anid:r+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:n,z2:2,silent:!0});o.push(h)}return o}var Gm=D,Zm=T;function Um(t,e){var i={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return function(p,g,t){var o=g.getComponent("tooltip"),m=g.getComponent("axisPointer"),v=m.get("link",!0)||[],y=[];Gm(t.getCoordinateSystems(),function(c){if(c.axisPointerEnabled){var t=qm(c.model),d=p.coordSysAxesInfo[t]={},f=(p.coordSysMap[t]=c).model.getModel("tooltip",o);if(Gm(c.getAxes(),Zm(r,!1,null)),c.getTooltipAxes&&o&&f.get("show")){var e="axis"===f.get("trigger"),i="cross"===f.get("axisPointer.type"),n=c.getTooltipAxes(f.get("axisPointer.axis"));(e||i)&&Gm(n.baseAxes,Zm(r,!i||"cross",e)),i&&Gm(n.otherAxes,Zm(r,"cross",!1))}}function r(t,e,i){var n=i.model.getModel("axisPointer",m),r=n.get("show");if(r&&("auto"!==r||t||jm(n))){null==e&&(e=n.get("triggerTooltip"));var o=(n=t?function(t,e,i,n,r,o){var a=e.getModel("axisPointer"),s={};Gm(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=b(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");var l=s.label||(s.label={});if(null==l.show&&(l.show=!1),"cross"===r){var u=a.get("label.show");if(l.show=null==u||u,!o){var h=s.lineStyle=a.get("crossStyle");h&&A(l,h.textStyle)}}return t.model.getModel("axisPointer",new _l(s,i,n))}(i,f,m,g,t,e):n).get("snap"),a=qm(i.model),s=e||o||"category"===i.type,l=p.axesInfo[a]={key:a,axis:i,coordSys:c,axisPointerModel:n,triggerTooltip:e,involveSeries:s,snap:o,useHandle:jm(n),seriesModels:[]};d[a]=l,p.seriesInvolved|=s;var u=function(t,e){for(var i=e.model,n=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(Xm(o[n+"AxisId"],i.id)||Xm(o[n+"AxisIndex"],i.componentIndex)||Xm(o[n+"AxisName"],i.name))return r}}(v,i);if(null!=u){var h=y[u]||(y[u]={axesInfo:{}});h.axesInfo[a]=l,h.mapper=v[u].mapper,l.linkGroup=h}}}})}(i,t,e),i.seriesInvolved&&function(r,t){t.eachSeries(function(i){var n=i.coordinateSystem,t=i.get("tooltip.trigger",!0),e=i.get("tooltip.show",!0);n&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==i.get("axisPointer.show",!0)&&Gm(r.coordSysAxesInfo[qm(n.model)],function(t){var e=t.axis;n.getAxis(e.dim)===e&&(t.seriesModels.push(i),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=i.getData().count())})},this)}(i,t),i}function Xm(t,e){return"all"===t||O(t)&&0<=x(t,e)||t===e}function Ym(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[qm(t)]}function jm(t){return!!t.get("handle.show")}function qm(t){return t.type+"||"+t.id}var $m=cf({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,i,n){this.axisPointerClass&&function(t){var e=Ym(t);if(e){var i=e.axisPointerModel,n=e.axis.scale,r=i.option,o=i.get("status"),a=i.get("value");null!=a&&(a=n.parse(a));var s=jm(i);null==o&&(r.status=s?"show":"hide");var l=n.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}(t),$m.superApply(this,"render",arguments),Km(this,t,e,i,n,!0)},updateAxisPointer:function(t,e,i,n,r){Km(this,t,e,i,n,!1)},remove:function(t,e){var i=this._axisPointer;i&&i.remove(e),$m.superApply(this,"remove",arguments)},dispose:function(t,e){Qm(this,e),$m.superApply(this,"dispose",arguments)}});function Km(t,e,i,n,r,o){var a=$m.getAxisPointerClass(t.axisPointerClass);if(a){var s=function(t){var e=Ym(t);return e&&e.axisPointerModel}(e);s?(t._axisPointer||(t._axisPointer=new a)).render(e,s,n,o):Qm(t,n)}}function Qm(t,e,i){var n=t._axisPointer;n&&n.dispose(e,i),t._axisPointer=null}var Jm=[];function tv(t,e,i){i=i||{};var n=t.coordinateSystem,r=e.axis,o={},a=r.getAxesOnZeroOf()[0],s=r.position,l=a?"onZero":s,u=r.dim,h=n.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===u?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(a){var g=a.toGlobalCoord(a.dataToCoord(0));p[d.onZero]=Math.max(Math.min(g,p[1]),p[0])}o.position=["y"===u?p[d[l]]:c[0],"x"===u?p[d[l]]:c[3]],o.rotation=Math.PI/2*("x"===u?0:1);o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,left:-1,right:1}[s],o.labelOffset=a?p[d[s]]-p[d.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),H(i.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var m=e.get("axisLabel.rotate");return o.labelRotate="top"===l?-m:m,o.z2=1,o}$m.registerAxisPointerClass=function(t,e){Jm[t]=e},$m.getAxisPointerClass=function(t){return t&&Jm[t]};var ev=["axisLine","axisTickLabel","axisName"],iv=["splitArea","splitLine","minorSplitLine"],nv=$m.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(e,t,i,n){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new Si,this.group.add(this._axisGroup),e.get("show")){var o=e.getCoordSysModel(),a=tv(o,e),s=new Om(e,a);D(ev,s.add,s),this._axisGroup.add(s.getGroup()),D(iv,function(t){e.get(t+".show")&&this["_"+t](e,o)},this),sl(r,this._axisGroup,e),nv.superCall(this,"render",e,t,i,n)}},remove:function(){!function(t){t.__splitAreaColors=null}(this)},_splitLine:function(t,e){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitLine"),r=n.getModel("lineStyle"),o=r.get("color");o=O(o)?o:[o];for(var a=e.coordinateSystem.getRect(),s=i.isHorizontal(),l=0,u=i.getTicksCoords({tickModel:n}),h=[],c=[],d=r.getLineStyle(),f=0;f<u.length;f++){var p=i.toGlobalCoord(u[f].coord);s?(h[0]=p,h[1]=a.y,c[0]=p,c[1]=a.y+a.height):(h[0]=a.x,h[1]=p,c[0]=a.x+a.width,c[1]=p);var g=l++%o.length,m=u[f].tickValue;this._axisGroup.add(new es({anid:null!=m?"line_"+u[f].tickValue:null,subPixelOptimize:!0,shape:{x1:h[0],y1:h[1],x2:c[0],y2:c[1]},style:A({stroke:o[g]},d),silent:!0}))}}},_minorSplitLine:function(t,e){var i=t.axis,n=t.getModel("minorSplitLine").getModel("lineStyle"),r=e.coordinateSystem.getRect(),o=i.isHorizontal(),a=i.getMinorTicksCoords();if(a.length)for(var s=[],l=[],u=n.getLineStyle(),h=0;h<a.length;h++)for(var c=0;c<a[h].length;c++){var d=i.toGlobalCoord(a[h][c].coord);o?(s[0]=d,s[1]=r.y,l[0]=d,l[1]=r.y+r.height):(s[0]=r.x,s[1]=d,l[0]=r.x+r.width,l[1]=d),this._axisGroup.add(new es({anid:"minor_line_"+a[h][c].tickValue,subPixelOptimize:!0,shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:u,silent:!0}))}},_splitArea:function(t,e){!function(t,e,i,n){var r=i.axis;if(!r.scale.isBlank()){var o=i.getModel("splitArea"),a=o.getModel("areaStyle"),s=a.get("color"),l=n.coordinateSystem.getRect(),u=r.getTicksCoords({tickModel:o,clamp:!0});if(u.length){var h=s.length,c=t.__splitAreaColors,d=Q(),f=0;if(c)for(var p=0;p<u.length;p++){var g=c.get(u[p].tickValue);if(null!=g){f=(g+(h-1)*p)%h;break}}var m=r.toGlobalCoord(u[0].coord),v=a.getAreaStyle();s=O(s)?s:[s];for(p=1;p<u.length;p++){var y,_,x,w,b=r.toGlobalCoord(u[p].coord);m=r.isHorizontal()?(y=m,_=l.y,x=b-y,w=l.height,y+x):(y=l.x,_=m,x=l.width,_+(w=b-_));var S=u[p-1].tickValue;null!=S&&d.set(S,f),e.add(new Ja({anid:null!=S?"area_"+S:null,shape:{x:y,y:_,width:x,height:w},style:A({fill:s[f]},v),silent:!0})),f=(f+1)%h}t.__splitAreaColors=d}}}(this,this._axisGroup,t,e)}});function rv(t,e){"outside"===t.textPosition&&(t.textPosition=e)}nv.extend({type:"xAxis"}),nv.extend({type:"yAxis"}),cf({type:"grid",render:function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new Ja({shape:t.coordinateSystem.getRect(),style:A({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),nf(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),sf(hm("line","circle","line")),af(cm("line")),rf(gd.PROCESSOR.STATISTIC,{seriesType:"line",modifyOutputEnd:!0,reset:function(t,e,i){var n=t.getData(),r=t.get("sampling"),o=t.coordinateSystem;if("cartesian2d"===o.type&&r){var a,s=o.getBaseAxis(),l=o.getOtherAxis(s),u=s.getExtent(),h=Math.abs(u[1]-u[0]),c=Math.round(n.count()/h);1<c&&("string"==typeof r?a=fm[r]:"function"==typeof r&&(a=r),a&&t.setData(n.downSample(n.mapDimension(l.dim),1/c,a,dm)))}}}),Yh.extend({type:"series.__base_bar__",getInitialData:function(t,e){return Qf(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var i=e.dataToPoint(e.clampData(t)),n=this.getData(),r=n.getLayout("offset"),o=n.getLayout("size");return i[e.getBaseAxis().isHorizontal()?0:1]+=r+o/2,i}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}}).extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return!!this.get("large")&&this.get("progressive")},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return t<e&&(t=e),t},defaultOption:{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1}}});var ov=io([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),av={getBarItemStyle:function(t){var e=ov(this,t);if(this.getBorderLineDash){var i=this.getBorderLineDash();i&&(e.lineDash=i)}return e}},sv=ws({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=Math.cos(l),d=Math.sin(l),f=Math.cos(u),p=Math.sin(u);(h?u-l<2*Math.PI:l-u<2*Math.PI)&&(t.moveTo(c*r+i,d*r+n),t.arc(c*s+i,d*s+n,a,-Math.PI+l,l,!h)),t.arc(i,n,o,l,u,!h),t.moveTo(f*o+i,p*o+n),t.arc(f*s+i,p*s+n,a,u-2*Math.PI,u-Math.PI,!h),0!==r&&(t.arc(i,n,r,u,l,h),t.moveTo(c*r+i,p*r+n)),t.closePath()}}),lv=["itemStyle","barBorderWidth"],uv=[0,0];k(_l.prototype,av),ff({type:"bar",render:function(t,e,i){this._updateDrawMode(t);var n=t.get("coordinateSystem");return"cartesian2d"!==n&&"polar"!==n||(this._isLargeDraw?this._renderLarge(t,e,i):this._renderNormal(t,e,i)),this.group},incrementalPrepareRender:function(t,e,i){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e,i,n){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(s,t,e){var l,u=this.group,h=s.getData(),c=this._data,d=s.coordinateSystem,i=d.getBaseAxis();"cartesian2d"===d.type?l=i.isHorizontal():"polar"===d.type&&(l="angle"===i.dim);var f=s.isAnimationEnabled()?s:null,p=s.get("clip",!0),g=function(t,e){var i=t.getArea&&t.getArea();if("cartesian2d"===t.type){var n=t.getBaseAxis();if("category"!==n.type||!n.onBand){var r=e.getLayout("bandWidth");n.isHorizontal()?(i.x-=r,i.width+=2*r):(i.y-=r,i.height+=2*r)}}return i}(d,h);u.removeClipPath();function m(t){var e=mv[d.type](h,t),i=function(t,e,i){return new("polar"===t.type?Wa:Ja)({shape:bv(e,i,t),silent:!0,z2:0})}(d,l,e);return i.useStyle(_.getBarItemStyle()),"cartesian2d"===d.type&&i.setShape("r",x),w[t]=i}var v=s.get("roundCap",!0),y=s.get("showBackground",!0),_=s.getModel("backgroundStyle"),x=_.get("barBorderRadius")||0,w=[],b=this._backgroundEls||[];h.diff(c).add(function(t){var e=h.getItemModel(t),i=mv[d.type](h,t,e);if(y&&m(t),h.hasValue(t)){if(p)if(dv[d.type](g,i))return void u.remove(n);var n=fv[d.type](t,i,l,f,!1,v);h.setItemGraphicEl(t,n),u.add(n),yv(n,h,t,e,i,s,l,"polar"===d.type)}}).update(function(t,e){var i=h.getItemModel(t),n=mv[d.type](h,t,i);if(y){var r;0===b.length?r=m(e):((r=b[e]).useStyle(_.getBarItemStyle()),"cartesian2d"===d.type&&r.setShape("r",x),w[t]=r);var o=mv[d.type](h,t);il(r,{shape:bv(l,o,d)},f,t)}var a=c.getItemGraphicEl(e);if(h.hasValue(t)){if(p)if(dv[d.type](g,n))return void u.remove(a);a?il(a,{shape:n},f,t):a=fv[d.type](t,n,l,f,!0,v),h.setItemGraphicEl(t,a),u.add(a),yv(a,h,t,i,n,s,l,"polar"===d.type)}else u.remove(a)}).remove(function(t){var e=c.getItemGraphicEl(t);"cartesian2d"===d.type?e&&pv(t,f,e):e&&gv(t,f,e)}).execute();var n=this._backgroundGroup||(this._backgroundGroup=new Si);n.removeAll();for(var r=0;r<w.length;++r)n.add(w[r]);u.add(n),this._backgroundEls=w,this._data=h},_renderLarge:function(t,e,i){this._clear(),xv(t,this.group);var n=t.get("clip",!0)?function(t,e,i){return t?"polar"===t.type?nm(t,e,i):"cartesian2d"===t.type?im(t,e,i):null:null}(t.coordinateSystem,!1,t):null;n?this.group.setClipPath(n):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){this._removeBackground(),xv(e,this.group,!0)},dispose:J,remove:function(t){this._clear(t)},_clear:function(e){var t=this.group,i=this._data;e&&e.get("animation")&&i&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],i.eachItemGraphicEl(function(t){"sector"===t.type?gv(t.dataIndex,e,t):pv(t.dataIndex,e,t)})):t.removeAll(),this._data=null},_removeBackground:function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null}});var hv=Math.max,cv=Math.min,dv={cartesian2d:function(t,e){var i=e.width<0?-1:1,n=e.height<0?-1:1;i<0&&(e.x+=e.width,e.width=-e.width),n<0&&(e.y+=e.height,e.height=-e.height);var r=hv(e.x,t.x),o=cv(e.x+e.width,t.x+t.width),a=hv(e.y,t.y),s=cv(e.y+e.height,t.y+t.height);e.x=r,e.y=a,e.width=o-r,e.height=s-a;var l=e.width<0||e.height<0;return i<0&&(e.x+=e.width,e.width=-e.width),n<0&&(e.y+=e.height,e.height=-e.height),l},polar:function(t,e){var i=e.r0<=e.r?1:-1;if(i<0){var n=e.r;e.r=e.r0,e.r0=n}n=cv(e.r,t.r);var r=hv(e.r0,t.r0),o=(e.r=n)-(e.r0=r)<0;if(i<0){n=e.r;e.r=e.r0,e.r0=n}return o}},fv={cartesian2d:function(t,e,i,n,r){var o=new Ja({shape:k({},e),z2:1});if(o.name="item",n){var a=i?"height":"width",s={};o.shape[a]=0,s[a]=e[a],dl[r?"updateProps":"initProps"](o,{shape:s},n,t)}return o},polar:function(t,e,i,n,r,o){var a=e.startAngle<e.endAngle,s=new(!i&&o?sv:Wa)({shape:A({clockwise:a},e),z2:1});if(s.name="item",n){var l=i?"r":"endAngle",u={};s.shape[l]=i?0:e.startAngle,u[l]=e[l],dl[r?"updateProps":"initProps"](s,{shape:u},n,t)}return s}};function pv(t,e,i){i.style.text=null,il(i,{shape:{width:0}},e,t,function(){i.parent&&i.parent.remove(i)})}function gv(t,e,i){i.style.text=null,il(i,{shape:{r:i.shape.r0}},e,t,function(){i.parent&&i.parent.remove(i)})}var mv={cartesian2d:function(t,e,i){var n=t.getItemLayout(e),r=i?function(t,e){var i=t.get(lv)||0,n=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),r=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(i,n,r)}(i,n):0,o=0<n.width?1:-1,a=0<n.height?1:-1;return{x:n.x+o*r/2,y:n.y+a*r/2,width:n.width-o*r,height:n.height-a*r}},polar:function(t,e,i){var n=t.getItemLayout(e);return{cx:n.cx,cy:n.cy,r0:n.r0,r:n.r,startAngle:n.startAngle,endAngle:n.endAngle}}};function vv(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function yv(t,e,i,n,r,o,a,s){var l=e.getItemVisual(i,"color"),u=e.getItemVisual(i,"opacity"),h=e.getVisual("borderColor"),c=n.getModel("itemStyle"),d=n.getModel("emphasis.itemStyle").getBarItemStyle();s||t.setShape("r",c.get("barBorderRadius")||0),t.useStyle(A({stroke:vv(r)?"none":h,fill:vv(r)?"none":l,opacity:u},c.getBarItemStyle()));var f=n.getShallow("cursor");f&&t.attr("cursor",f);a?r.height:r.width;s||function(t,e,i,n,r,o){Ys(t,e,i.getModel("label"),i.getModel("emphasis.label"),{labelFetcher:r,labelDataIndex:o,defaultText:Cg(r.getData(),o),isRectText:!0,autoColor:n}),rv(t),rv(e)}(t.style,d,n,l,o,i),vv(r)&&(d.fill=d.stroke="none"),Gs(t,d)}var _v=xa.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var i=e.points,n=this.__startPoint,r=this.__baseDimIdx,o=0;o<i.length;o+=2)n[r]=i[o+r],t.moveTo(n[0],n[1]),t.lineTo(i[o],i[o+1])}});function xv(t,e,i){var n=t.getData(),r=[],o=n.getLayout("valueAxisHorizontal")?1:0;r[1-o]=n.getLayout("valueAxisStart");var a=n.getLayout("largeDataIndices"),s=n.getLayout("barWidth"),l=t.getModel("backgroundStyle");if(t.get("showBackground",!0)){var u=n.getLayout("largeBackgroundPoints"),h=[];h[1-o]=n.getLayout("backgroundStart");var c=new _v({shape:{points:u},incremental:!!i,__startPoint:h,__baseDimIdx:o,__largeDataIndices:a,__barWidth:s,silent:!0,z2:0});!function(t,e,i){var n=e.get("borderColor")||e.get("color"),r=e.getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=n,t.style.lineWidth=i.getLayout("barWidth")}(c,l,n),e.add(c)}var d=new _v({shape:{points:n.getLayout("largePoints")},incremental:!!i,__startPoint:r,__baseDimIdx:o,__largeDataIndices:a,__barWidth:s});e.add(d),function(t,e,i){var n=i.getVisual("borderColor")||i.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=n,t.style.lineWidth=i.getLayout("barWidth")}(d,t,n),d.seriesIndex=t.seriesIndex,t.get("silent")||(d.on("mousedown",wv),d.on("mousemove",wv))}var wv=mc(function(t){var e=function(t,e,i){var n=t.__baseDimIdx,r=1-n,o=t.shape.points,a=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];uv[0]=e,uv[1]=i;for(var u=uv[n],h=uv[1-n],c=u-s,d=u+s,f=0,p=o.length/2;f<p;f++){var g=2*f,m=o[g+n],v=o[g+r];if(c<=m&&m<=d&&(l<=v?l<=h&&h<=v:v<=h&&h<=l))return a[f]}return-1}(this,t.offsetX,t.offsetY);this.dataIndex=0<=e?e:null},30,!1);function bv(t,e,i){var n,r="polar"===i.type;return n=r?i.getArea():i.grid.getRect(),r?{cx:n.cx,cy:n.cy,r0:t?n.r0:e.r0,r:t?n.r:e.r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}:{x:t?e.x:n.x,y:t?n.y:e.y,width:t?e.width:n.width,height:t?n.height:e.height}}af(gd.VISUAL.LAYOUT,T(function(t,e){var i=mp(t,e),C=vp(i),T={};D(i,function(t){var e=t.getData(),i=t.coordinateSystem,n=i.getBaseAxis(),r=pp(t),o=C[gp(n)][r],a=o.offset,s=o.width,l=i.getOtherAxis(n),u=t.get("barMinHeight")||0;T[r]=T[r]||[],e.setLayout({bandWidth:o.bandWidth,offset:a,size:s});for(var h=e.mapDimension(l.dim),c=e.mapDimension(n.dim),d=$f(e,h),f=l.isHorizontal(),p=bp(n,l,d),g=0,m=e.count();g<m;g++){var v,y,_,x,w,b=e.get(h,g),S=e.get(c,g),M=0<=b?"p":"n",I=p;if(d&&(T[r][S]||(T[r][S]={p:p,n:p}),I=T[r][S][M]),f)v=I,y=(w=i.dataToPoint([b,S]))[1]+a,_=w[0]-p,x=s,Math.abs(_)<u&&(_=(_<0?-1:1)*u),isNaN(_)||d&&(T[r][S][M]+=_);else v=(w=i.dataToPoint([S,b]))[0]+a,y=I,_=s,x=w[1]-p,Math.abs(x)<u&&(x=(x<=0?-1:1)*u),isNaN(x)||d&&(T[r][S][M]+=x);e.setItemLayout(g,{x:v,y:y,width:_,height:x})}},this)},"bar")),af(gd.VISUAL.PROGRESSIVE_LAYOUT,_p),sf({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}});var Sv={updateSelectedMap:function(t){this._targetList=O(t)?t.slice():[],this._selectTargetMap=M(t||[],function(t,e){return t.set(e.name,e),t},Q())},select:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);"single"===this.get("selectedMode")&&this._selectTargetMap.each(function(t){t.selected=!1}),i&&(i.selected=!0)},unSelect:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);i&&(i.selected=!1)},toggleSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);if(null!=i)return this[i.selected?"unSelect":"select"](t,e),i.selected},isSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return i&&i.selected}};function Mv(i,e){this.getAllNames=function(){var t=e();return t.mapArray(t.getName)},this.containName=function(t){return 0<=e().indexOfName(t)},this.indexOfName=function(t){return i().indexOfName(t)},this.getItemVisual=function(t,e){return i().getItemVisual(t,e)}}var Iv=df({type:"series.pie",init:function(t){Iv.superApply(this,"init",arguments),this.legendVisualProvider=new Mv(C(this.getData,this),C(this.getRawData,this)),this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){Iv.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(t,e){return function(t,e,i){e=O(e)&&{coordDimensions:e}||k({},e);var n=t.getSource(),r=Uf(n,e),o=new kf(r,t);return o.initData(n,i),o}(this,{coordDimensions:["value"],encodeDefaulter:T(Bu,this)})},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),i=[],n=0,r=t.count();n<r;n++)i.push({name:t.getName(n),value:t.get(e,n),selected:kh(t,n,"selected")});return i},getDataParams:function(t){var e=this.getData(),i=Iv.superCall(this,"getDataParams",t),n=[];return e.each(e.mapDimension("value"),function(t){n.push(t)}),i.percent=Ll(n,t,e.hostModel.get("percentPrecision")),i.$vars.push("percent"),i},_defaultLabelLine:function(t){Or(t,"labelLine",["show"]);var e=t.labelLine,i=t.emphasis.labelLine;e.show=e.show&&t.label.show,i.show=i.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:!1,show:!0,position:"outer",alignTo:"none",margin:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationTypeUpdate:"transition",animationEasing:"cubicOut"}});function Cv(t,e,i,n){var r=e.getData(),o=this.dataIndex,a=r.getName(o),s=e.get("selectedOffset");n.dispatchAction({type:"pieToggleSelect",from:t,name:a,seriesId:e.id}),r.each(function(t){Tv(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,i)})}function Tv(t,e,i,n,r){var o=(e.startAngle+e.endAngle)/2,a=i?n:0,s=[Math.cos(o)*a,Math.sin(o)*a];r?t.animate().when(200,{position:s}).start("bounceOut"):t.attr("position",s)}function Av(t,e){Si.call(this);var i=new Wa({z2:2}),n=new Ya,r=new Ba;this.add(i),this.add(n),this.add(r),this.updateData(t,e,!0)}S(Iv,Sv);var Dv=Av.prototype;Dv.updateData=function(t,e,i){var n=this.childAt(0),r=this.childAt(1),o=this.childAt(2),a=t.hostModel,s=t.getItemModel(e),l=t.getItemLayout(e),u=k({},l);u.label=null;var h=a.getShallow("animationTypeUpdate");i?(n.setShape(u),"scale"===a.getShallow("animationType")?(n.shape.r=l.r0,nl(n,{shape:{r:l.r}},a,e)):(n.shape.endAngle=l.startAngle,il(n,{shape:{endAngle:l.endAngle}},a,e))):"expansion"===h?n.setShape(u):il(n,{shape:u},a,e);var c=t.getItemVisual(e,"color");n.useStyle(A({lineJoin:"bevel",fill:c},s.getModel("itemStyle").getItemStyle())),n.hoverStyle=s.getModel("emphasis.itemStyle").getItemStyle();var d=s.getShallow("cursor");d&&n.attr("cursor",d),Tv(this,t.getItemLayout(e),a.isSelected(t.getName(e)),a.get("selectedOffset"),a.get("animation"));var f=!i&&"transition"===h;this._updateLabel(t,e,f),this.highDownOnUpdate=a.get("silent")?null:function(t,e){var i=a.isAnimationEnabled()&&s.get("hoverAnimation");"emphasis"===e?(r.ignore=r.hoverIgnore,o.ignore=o.hoverIgnore,i&&(n.stopAnimation(!0),n.animateTo({shape:{r:l.r+a.get("hoverOffset")}},300,"elasticOut"))):(r.ignore=r.normalIgnore,o.ignore=o.normalIgnore,i&&(n.stopAnimation(!0),n.animateTo({shape:{r:l.r}},300,"elasticOut")))},Gs(this)},Dv._updateLabel=function(t,e,i){var n=this.childAt(1),r=this.childAt(2),o=t.hostModel,a=t.getItemModel(e),s=t.getItemLayout(e).label,l=t.getItemVisual(e,"color");if(!s||isNaN(s.x)||isNaN(s.y))r.ignore=r.normalIgnore=r.hoverIgnore=n.ignore=n.normalIgnore=n.hoverIgnore=!0;else{var u={points:s.linePoints||[[s.x,s.y],[s.x,s.y],[s.x,s.y]]},h={x:s.x,y:s.y};i?(il(n,{shape:u},o,e),il(r,{style:h},o,e)):(n.attr({shape:u}),r.attr({style:h})),r.attr({rotation:s.rotation,origin:[s.x,s.y],z2:10});var c=a.getModel("label"),d=a.getModel("emphasis.label"),f=a.getModel("labelLine"),p=a.getModel("emphasis.labelLine");l=t.getItemVisual(e,"color");Ys(r.style,r.hoverStyle={},c,d,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:s.text,autoColor:l,useInsideStyle:!!s.inside},{textAlign:s.textAlign,textVerticalAlign:s.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),r.ignore=r.normalIgnore=!c.get("show"),r.hoverIgnore=!d.get("show"),n.ignore=n.normalIgnore=!f.get("show"),n.hoverIgnore=!p.get("show"),n.setStyle({stroke:l,opacity:t.getItemVisual(e,"opacity")}),n.setStyle(f.getModel("lineStyle").getLineStyle()),n.hoverStyle=p.getModel("lineStyle").getLineStyle();var g=f.get("smooth");g&&!0===g&&(g=.4),n.setShape({smooth:g})}},w(Av,Si);ac.extend({type:"pie",init:function(){var t=new Si;this._sectorGroup=t},render:function(t,e,i,n){if(!n||n.from!==this.uid){var r=t.getData(),o=this._data,a=this.group,s=e.get("animation"),l=!o,u=t.get("animationType"),h=t.get("animationTypeUpdate"),c=T(Cv,this.uid,t,s,i),d=t.get("selectedMode");if(r.diff(o).add(function(t){var e=new Av(r,t);l&&"scale"!==u&&e.eachChild(function(t){t.stopAnimation(!0)}),d&&e.on("click",c),r.setItemGraphicEl(t,e),a.add(e)}).update(function(t,e){var i=o.getItemGraphicEl(e);l||"transition"===h||i.eachChild(function(t){t.stopAnimation(!0)}),i.updateData(r,t),i.off("click"),d&&i.on("click",c),a.add(i),r.setItemGraphicEl(t,i)}).remove(function(t){var e=o.getItemGraphicEl(t);a.remove(e)}).execute(),s&&0<r.count()&&(l?"scale"!==u:"transition"!==h)){for(var f=r.getItemLayout(0),p=1;isNaN(f.startAngle)&&p<r.count();++p)f=r.getItemLayout(p);var g=Math.max(i.getWidth(),i.getHeight())/2,m=C(a.removeClipPath,a);a.setClipPath(this._createClipPath(f.cx,f.cy,g,f.startAngle,f.clockwise,m,t,l))}else a.removeClipPath();this._data=r}},dispose:function(){},_createClipPath:function(t,e,i,n,r,o,a,s){var l=new Wa({shape:{cx:t,cy:e,r0:0,r:i,startAngle:n,endAngle:n,clockwise:r}});return(s?nl:il)(l,{shape:{endAngle:n+(r?1:-1)*Math.PI*2}},a,o),l},containPoint:function(t,e){var i=e.getData().getItemLayout(0);if(i){var n=t[0]-i.cx,r=t[1]-i.cy,o=Math.sqrt(n*n+r*r);return o<=i.r&&o>=i.r0}}});var kv=Math.PI/180;function Pv(r,t,e,i,n,o,a,s,l,u){function h(t,e,i){for(var n=t;n<e&&!(r[n].y+i>l+a);n++)if(r[n].y+=i,t<n&&n+1<e&&r[n+1].y>r[n].y+r[n].height)return void c(n,i/2);c(e-1,i/2)}function c(t,e){for(var i=t;0<=i&&!(r[i].y-e<l)&&(r[i].y-=e,!(0<i&&r[i].y>r[i-1].y+r[i-1].height));i--);}function d(t,e,i,n,r,o){for(var a=e?Number.MAX_VALUE:0,s=0,l=t.length;s<l;s++)if("none"===t[s].labelAlignTo){var u=Math.abs(t[s].y-n),h=t[s].len,c=t[s].len2,d=u<r+h?Math.sqrt((r+h+c)*(r+h+c)-u*u):Math.abs(t[s].x-i);e&&a<=d&&(d=a-10),!e&&d<=a&&(d=a+10),t[s].x=i+d*o,a=d}}r.sort(function(t,e){return t.y-e.y});for(var f,p=0,g=r.length,m=[],v=[],y=0;y<g;y++){if("outer"===r[y].position&&"labelLine"===r[y].labelAlignTo){var _=r[y].x-u;r[y].linePoints[1][0]+=_,r[y].x=u}(f=r[y].y-p)<0&&h(y,g,-f),p=r[y].y+r[y].height}a-p<0&&c(g-1,p-a);for(y=0;y<g;y++)r[y].y>=e?v.push(r[y]):m.push(r[y]);d(m,!1,t,e,i,n),d(v,!0,t,e,i,n)}function Lv(t){return"center"===t.position}function Ov(k,P,L,t,O,e){var z,E,N=k.getData(),R=[],B=!1,V=(k.get("minShowLabelAngle")||0)*kv;N.each(function(t){var e=N.getItemLayout(t),i=N.getItemModel(t),n=i.getModel("label"),r=n.get("position")||i.get("emphasis.label.position"),o=n.get("distanceToLabelLine"),a=n.get("alignTo"),s=Cl(n.get("margin"),L),l=n.get("bleedMargin"),u=n.getFont(),h=i.getModel("labelLine"),c=h.get("length");c=Cl(c,L);var d=h.get("length2");if(d=Cl(d,L),!(e.angle<V)){var f,p,g,m,v=(e.startAngle+e.endAngle)/2,y=Math.cos(v),_=Math.sin(v);z=e.cx,E=e.cy;var x,w=k.getFormattedLabel(t,"normal")||N.getName(t),b=un(w,u,m,"top"),S="inside"===r||"inner"===r;if("center"===r)f=e.cx,p=e.cy,m="center";else{var M=(S?(e.r+e.r0)/2*y:e.r*y)+z,I=(S?(e.r+e.r0)/2*_:e.r*_)+E;if(f=M+3*y,p=I+3*_,!S){var C=M+y*(c+P-e.r),T=I+_*(c+P-e.r),A=C+(y<0?-1:1)*d;f="edge"===a?y<0?O+s:O+L-s:A+(y<0?-o:o),g=[[M,I],[C,T],[A,p=T]]}m=S?"center":"edge"===a?0<y?"right":"left":0<y?"left":"right"}var D=n.get("rotate");x="number"==typeof D?D*(Math.PI/180):D?y<0?-v+Math.PI:-v:0,B=!!x,e.label={x:f,y:p,position:r,height:b.height,len:c,len2:d,linePoints:g,textAlign:m,verticalAlign:"middle",rotation:x,inside:S,labelDistance:o,labelAlignTo:a,labelMargin:s,bleedMargin:l,textRect:b,text:w,font:u},S||R.push(e.label)}}),!B&&k.get("avoidLabelOverlap")&&function(t,e,i,n,r,o,a,s){for(var l=[],u=[],h=Number.MAX_VALUE,c=-Number.MAX_VALUE,d=0;d<t.length;d++)Lv(t[d])||(t[d].x<e?(h=Math.min(h,t[d].x),l.push(t[d])):(c=Math.max(c,t[d].x),u.push(t[d])));for(Pv(u,e,i,n,1,0,o,0,s,c),Pv(l,e,i,n,-1,0,o,0,s,h),d=0;d<t.length;d++){var f=t[d];if(!Lv(f)){var p=f.linePoints;if(p){var g,m="edge"===f.labelAlignTo,v=f.textRect.width;(g=m?f.x<e?p[2][0]-f.labelDistance-a-f.labelMargin:a+r-f.labelMargin-p[2][0]-f.labelDistance:f.x<e?f.x-a-f.bleedMargin:a+r-f.x-f.bleedMargin)<f.textRect.width&&(f.text=fn(f.text,g,f.font),"edge"===f.labelAlignTo&&(v=ln(f.text,f.font)));var y=p[1][0]-p[2][0];m?f.x<e?p[2][0]=a+f.labelMargin+v+f.labelDistance:p[2][0]=a+r-f.labelMargin-v-f.labelDistance:(f.x<e?p[2][0]=f.x+f.labelDistance:p[2][0]=f.x-f.labelDistance,p[1][0]=p[2][0]+y),p[1][1]=p[2][1]=f.y}}}}(R,z,E,P,L,t,O,e)}var zv=2*Math.PI,Ev=Math.PI/180;var Nv,Rv;Nv="pie",D([{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}],function(o){o.update="updateView",of(o,function(t,e){var r={};return e.eachComponent({mainType:"series",subType:Nv,query:t},function(i){i[o.method]&&i[o.method](t.name,t.dataIndex);var n=i.getData();n.each(function(t){var e=n.getName(t);r[e]=i.isSelected(e)||!1})}),{name:t.name,selected:r,seriesId:t.seriesId}})}),sf((Rv="pie",{getTargetSeries:function(t){var e={},i=Q();return t.eachSeriesByType(Rv,function(t){t.__paletteScope=e,i.set(t.uid,t)}),i},reset:function(s,t){var l=s.getRawData(),u={},h=s.getData();h.each(function(t){var e=h.getRawIndex(t);u[e]=t}),l.each(function(t){var e,i=u[t],n=null!=i&&h.getItemVisual(i,"color",!0),r=null!=i&&h.getItemVisual(i,"borderColor",!0);if(n&&r||(e=l.getItemModel(t)),!n){var o=e.get("itemStyle.color")||s.getColorFromPalette(l.getName(t)||t+"",s.__paletteScope,l.count());null!=i&&h.setItemVisual(i,"color",o)}if(!r){var a=e.get("itemStyle.borderColor");null!=i&&h.setItemVisual(i,"borderColor",a)}})}})),af(T(function(t,e,T,i){e.eachSeriesByType(t,function(t){var r=t.getData(),e=r.mapDimension("value"),o=function(t,e){return lu(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}(t,T),i=t.get("center"),n=t.get("radius");O(n)||(n=[0,n]),O(i)||(i=[i,i]);var a=Cl(o.width,T.getWidth()),s=Cl(o.height,T.getHeight()),l=Math.min(a,s),u=Cl(i[0],a)+o.x,h=Cl(i[1],s)+o.y,c=Cl(n[0],l/2),d=Cl(n[1],l/2),f=-t.get("startAngle")*Ev,p=t.get("minAngle")*Ev,g=0;r.each(e,function(t){isNaN(t)||g++});var m=r.getSum(e),v=Math.PI/(m||g)*2,y=t.get("clockwise"),_=t.get("roseType"),x=t.get("stillShowZeroSum"),w=r.getDataExtent(e);w[0]=0;var b=zv,S=0,M=f,I=y?1:-1;if(r.each(e,function(t,e){var i;if(isNaN(t))r.setItemLayout(e,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:y,cx:u,cy:h,r0:c,r:_?NaN:d,viewRect:o});else{(i="area"!==_?0===m&&x?v:t*v:zv/g)<p?b-=i=p:S+=t;var n=M+I*i;r.setItemLayout(e,{angle:i,startAngle:M,endAngle:n,clockwise:y,cx:u,cy:h,r0:c,r:_?Il(t,w,[c,d]):d,viewRect:o}),M=n}}),b<zv&&g)if(b<=.001){var C=zv/g;r.each(e,function(t,e){if(!isNaN(t)){var i=r.getItemLayout(e);i.angle=C,i.startAngle=f+I*e*C,i.endAngle=f+I*(e+1)*C}})}else v=b/S,M=f,r.each(e,function(t,e){if(!isNaN(t)){var i=r.getItemLayout(e),n=i.angle===p?p:t*v;i.startAngle=M,i.endAngle=M+I*n,M+=I*n}});Ov(t,d,o.width,o.height,o.x,o.y)})},"pie")),rf({seriesType:"pie",reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var r=t.getData();r.filterSelf(function(t){for(var e=r.getName(t),i=0;i<n.length;i++)if(!n[i].isSelected(e))return!1;return!0})}}}),Yh.extend({type:"series.scatter",dependencies:["grid","polar","geo","singleAxis","calendar"],getInitialData:function(t,e){return Qf(this.getSource(),this,{useEncodeDefaulter:!0})},brushSelector:"point",getProgressive:function(){var t=this.option.progressive;return null==t?this.option.large?5e3:this.get("progressive"):t},getProgressiveThreshold:function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?1e4:this.get("progressiveThreshold"):t},defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},clip:!0}});var Bv=ws({shape:{points:null},symbolProxy:null,softClipShape:null,buildPath:function(t,e){var i=e.points,n=e.size,r=this.symbolProxy,o=r.shape;if(!((t.getContext?t.getContext():t)&&n[0]<4))for(var a=0;a<i.length;){var s=i[a++],l=i[a++];isNaN(s)||isNaN(l)||this.softClipShape&&!this.softClipShape.contain(s,l)||(o.x=s-n[0]/2,o.y=l-n[1]/2,o.width=n[0],o.height=n[1],r.buildPath(t,o,!0))}},afterBrush:function(t){var e=this.shape,i=e.points,n=e.size;if(n[0]<4){this.setTransform(t);for(var r=0;r<i.length;){var o=i[r++],a=i[r++];isNaN(o)||isNaN(a)||this.softClipShape&&!this.softClipShape.contain(o,a)||t.fillRect(o-n[0]/2,a-n[1]/2,n[0],n[1])}this.restoreTransform(t)}},findDataIndex:function(t,e){for(var i=this.shape,n=i.points,r=i.size,o=Math.max(r[0],4),a=Math.max(r[1],4),s=n.length/2-1;0<=s;s--){var l=2*s,u=n[l]-o/2,h=n[1+l]-a/2;if(u<=t&&h<=e&&t<=u+o&&e<=h+a)return s}return-1}});function Vv(){this.group=new Si}var Fv=Vv.prototype;Fv.isPersistent=function(){return!this._incremental},Fv.updateData=function(t,e){this.group.removeAll();var i=new Bv({rectHover:!0,cursor:"default"});i.setShape({points:t.getLayout("symbolPoints")}),this._setCommon(i,t,!1,e),this.group.add(i),this._incremental=null},Fv.updateLayout=function(t){if(!this._incremental){var n=t.getLayout("symbolPoints");this.group.eachChild(function(t){if(null!=t.startIndex){var e=2*(t.endIndex-t.startIndex),i=4*t.startIndex*2;n=new Float32Array(n.buffer,i,e)}t.setShape("points",n)})}},Fv.incrementalPrepareUpdate=function(t){this.group.removeAll(),this._clearIncremental(),2e6<t.count()?(this._incremental||(this._incremental=new hs({silent:!0})),this.group.add(this._incremental)):this._incremental=null},Fv.incrementalUpdate=function(t,e,i){var n;this._incremental?(n=new Bv,this._incremental.addDisplayable(n,!0)):((n=new Bv({rectHover:!0,cursor:"default",startIndex:t.start,endIndex:t.end})).incremental=!0,this.group.add(n)),n.setShape({points:e.getLayout("symbolPoints")}),this._setCommon(n,e,!!this._incremental,i)},Fv._setCommon=function(i,t,e,n){var r=t.hostModel;n=n||{};var o=t.getVisual("symbolSize");i.setShape("size",o instanceof Array?o:[o,o]),i.softClipShape=n.clipShape||null,i.symbolProxy=ig(t.getVisual("symbol"),0,0,0,0),i.setColor=i.symbolProxy.setColor;var a=i.shape.size[0]<4;i.useStyle(r.getModel("itemStyle").getItemStyle(a?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("color");s&&i.setColor(s),e||(i.seriesIndex=r.seriesIndex,i.on("mousemove",function(t){i.dataIndex=null;var e=i.findDataIndex(t.offsetX,t.offsetY);0<=e&&(i.dataIndex=e+(i.startIndex||0))}))},Fv.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},Fv._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()},ff({type:"scatter",render:function(t,e,i){var n=t.getData();this._updateSymbolDraw(n,t).updateData(n,{clipShape:this._getClipShape(t)}),this._finished=!0},incrementalPrepareRender:function(t,e,i){var n=t.getData();this._updateSymbolDraw(n,t).incrementalPrepareUpdate(n),this._finished=!1},incrementalRender:function(t,e,i){this._symbolDraw.incrementalUpdate(t,e.getData(),{clipShape:this._getClipShape(e)}),this._finished=t.end===e.getData().count()},updateTransform:function(t,e,i){var n=t.getData();if(this.group.dirty(),!this._finished||1e4<n.count()||!this._symbolDraw.isPersistent())return{update:!0};var r=cm().reset(t);r.progress&&r.progress({start:0,end:n.count()},n),this._symbolDraw.updateLayout(n)},_getClipShape:function(t){var e=t.coordinateSystem,i=e&&e.getArea&&e.getArea();return t.get("clip",!0)?i:null},_updateSymbolDraw:function(t,e){var i=this._symbolDraw,n=e.pipelineContext.large;return i&&n===this._isLargeDraw||(i&&i.remove(),i=this._symbolDraw=n?new Vv:new Rg,this._isLargeDraw=n,this.group.removeAll()),this.group.add(i.group),i},remove:function(t,e){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},dispose:function(){}}),sf(hm("scatter","circle")),af(cm("scatter"));var Hv={path:null,compoundPath:null,group:Si,image:Yn,text:Ba};nf(function(t){var e=t.graphic;O(e)?e[0]&&e[0].elements?t.graphic=[t.graphic[0]]:t.graphic=[{elements:e}]:e&&!e.elements&&(t.graphic=[{elements:[e]}])});var Wv=hf({type:"graphic",defaultOption:{elements:[],parentId:null},_elOptionsToUpdate:null,mergeOption:function(t){var e=this.option.elements;this.option.elements=null,Wv.superApply(this,"mergeOption",arguments),this.option.elements=e},optionUpdated:function(t,e){var i=this.option,n=(e?i:t).elements,r=i.elements=e?[]:i.elements,o=[];this._flatten(n,o);var a=Nr(r,o);Rr(a);var s=this._elOptionsToUpdate=[];D(a,function(t,e){var i=t.option;i&&(s.push(i),function(t,e){var i=t.exist;if(e.id=t.keyInfo.id,!e.type&&i&&(e.type=i.type),null==e.parentId){var n=e.parentOption;n?e.parentId=n.id:i&&(e.parentId=i.parentId)}e.parentOption=null}(t,i),function(t,e,i){var n=k({},i),r=t[e],o=i.$action||"merge";"merge"===o?r?(m(r,n,!0),hu(r,n,{ignoreSize:!0}),du(i,r)):t[e]=n:"replace"===o?t[e]=n:"remove"===o&&r&&(t[e]=null)}(r,e,i),function(t,e){if(!t)return;t.hv=e.hv=[Uv(e,["left","right"]),Uv(e,["top","bottom"])],"group"===t.type&&(null==t.width&&(t.width=e.width=0),null==t.height&&(t.height=e.height=0))}(r[e],i))},this);for(var l=r.length-1;0<=l;l--)null==r[l]?r.splice(l,1):delete r[l].$action},_flatten:function(t,i,n){D(t,function(t){if(t){n&&(t.parentOption=n),i.push(t);var e=t.children;"group"===t.type&&e&&this._flatten(e,i,t),delete t.children}},this)},useElOptionsToUpdate:function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t}});function Gv(t,e,i,n){var r=i.type,o=new(Hv.hasOwnProperty(r)?Hv[r]:Ss(r))(i);e.add(o),n.set(t,o),o.__ecGraphicId=t}function Zv(t,e){var i=t&&t.parent;i&&("group"===t.type&&t.traverse(function(t){Zv(t,e)}),e.removeKey(t.__ecGraphicId),i.remove(t))}function Uv(e,t){var i;return D(t,function(t){null!=e[t]&&"auto"!==e[t]&&(i=!0)}),i}cf({type:"graphic",init:function(t,e){this._elMap=Q(),this._lastGraphicModel},render:function(t,e,i){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,i)},_updateElements:function(u){var t=u.useElOptionsToUpdate();if(t){var h=this._elMap,c=this.group;D(t,function(t){var e=t.$action,i=t.id,n=h.get(i),r=t.parentId,o=null!=r?h.get(r):c,a=t.style;"text"===t.type&&a&&(t.hv&&t.hv[1]&&(a.textVerticalAlign=a.textBaseline=null),!a.hasOwnProperty("textFill")&&a.fill&&(a.textFill=a.fill),!a.hasOwnProperty("textStroke")&&a.stroke&&(a.textStroke=a.stroke));var s=function(e){return e=k({},e),D(["id","parentId","$action","hv","bounding"].concat(ru),function(t){delete e[t]}),e}(t);e&&"merge"!==e?"replace"===e?(Zv(n,h),Gv(i,o,s,h)):"remove"===e&&Zv(n,h):n?n.attr(s):Gv(i,o,s,h);var l=h.get(i);l&&(l.__ecGraphicWidthOption=t.width,l.__ecGraphicHeightOption=t.height,function(t,e){var i=t.eventData;t.silent||t.ignore||i||(i=t.eventData={componentType:"graphic",componentIndex:e.componentIndex,name:t.name});i&&(i.info=t.info)}(l,u))})}},_relocate:function(t,e){for(var i=t.option.elements,n=this.group,r=this._elMap,o=e.getWidth(),a=e.getHeight(),s=0;s<i.length;s++){var l=i[s];if((h=r.get(l.id))&&h.isGroup){var u=(c=h.parent)===n;h.__ecGraphicWidth=Cl(h.__ecGraphicWidthOption,u?o:c.__ecGraphicWidth)||0,h.__ecGraphicHeight=Cl(h.__ecGraphicHeightOption,u?a:c.__ecGraphicHeight)||0}}for(s=i.length-1;0<=s;s--){var h,c;l=i[s];if(h=r.get(l.id))uu(h,l,(c=h.parent)===n?{width:o,height:a}:{width:c.__ecGraphicWidth,height:c.__ecGraphicHeight},null,{hv:l.hv,boundingMode:l.bounding})}},_clear:function(){var e=this._elMap;e.each(function(t){Zv(t,e)}),this._elMap=Q()},dispose:function(){this._clear()}});function Xv(t,e){var i,n=[],r=t.seriesIndex;if(null==r||!(i=e.getSeriesByIndex(r)))return{point:[]};var o=i.getData(),a=Fr(o,t);if(null==a||a<0||O(a))return{point:[]};var s=o.getItemGraphicEl(a),l=i.coordinateSystem;if(i.getTooltipPosition)n=i.getTooltipPosition(a)||[];else if(l&&l.dataToPoint)n=l.dataToPoint(o.getValues(P(l.dimensions,function(t){return o.mapDimension(t)}),a,!0))||[];else if(s){var u=s.getBoundingRect().clone();u.applyTransform(s.transform),n=[u.x+u.width/2,u.y+u.height/2]}return{point:n,el:s}}var Yv=D,jv=T,qv=Hr();function $v(t,e,i,n,r){var o=t.axis;if(!o.scale.isBlank()&&o.containData(e))if(t.involveSeries){var a=function(l,t){var u=t.axis,h=u.dim,c=l,d=[],f=Number.MAX_VALUE,p=-1;return Yv(t.seriesModels,function(e,t){var i,n,r=e.getData().mapDimension(h,!0);if(e.getAxisTooltipData){var o=e.getAxisTooltipData(r,l,u);n=o.dataIndices,i=o.nestestValue}else{if(!(n=e.getData().indicesOfNearest(r[0],l,"category"===u.type?.5:null)).length)return;i=e.getData().get(r[0],n[0])}if(null!=i&&isFinite(i)){var a=l-i,s=Math.abs(a);s<=f&&((s<f||0<=a&&p<0)&&(f=s,p=a,c=i,d.length=0),Yv(n,function(t){d.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:d,snapToValue:c}}(e,t),s=a.payloadBatch,l=a.snapToValue;s[0]&&null==r.seriesIndex&&k(r,s[0]),!n&&t.snap&&o.containData(l)&&null!=l&&(e=l),i.showPointer(t,e,s,r),i.showTooltip(t,a,l)}else i.showPointer(t,e)}function Kv(t,e,i,n){t[e.key]={value:i,payloadBatch:n}}function Qv(t,e,i,n){var r=i.payloadBatch,o=e.axis,a=o.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,u=qm(l),h=t.map[u];h||(h=t.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(h)),h.dataByAxis.push({axisDim:o.dim,axisIndex:a.componentIndex,axisType:a.type,axisId:a.id,value:n,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function Jv(t){var e=t.axis.model,i={},n=i.axisDim=t.axis.dim;return i.axisIndex=i[n+"AxisIndex"]=e.componentIndex,i.axisName=i[n+"AxisName"]=e.name,i.axisId=i[n+"AxisId"]=e.id,i}function ty(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}hf({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}});var ey=Hr(),iy=D;function ny(t,e,i){if(!v.node){var n=e.getZr();ey(n).records||(ey(n).records={}),function(r,o){if(ey(r).initialized)return;function t(t,n){r.on(t,function(e){var i=function(i){var n={showTip:[],hideTip:[]},r=function(t){var e=n[t.type];e?e.push(t):(t.dispatchAction=r,i.dispatchAction(t))};return{dispatchAction:r,pendings:n}}(o);iy(ey(r).records,function(t){t&&n(t,e,i.dispatchAction)}),function(t,e){var i,n=t.showTip.length,r=t.hideTip.length;n?i=t.showTip[n-1]:r&&(i=t.hideTip[r-1]);i&&(i.dispatchAction=null,e.dispatchAction(i))}(i.pendings,o)})}ey(r).initialized=!0,t("click",T(oy,"click")),t("mousemove",T(oy,"mousemove")),t("globalout",ry)}(n,e),(ey(n).records[t]||(ey(n).records[t]={})).handler=i}}function ry(t,e,i){t.handler("leave",null,i)}function oy(t,e,i,n){e.handler(t,i,n)}function ay(t,e){if(!v.node){var i=e.getZr();(ey(i).records||{})[t]&&(ey(i).records[t]=null)}}var sy=cf({type:"axisPointer",render:function(t,e,i){var n=e.getComponent("tooltip"),r=t.get("triggerOn")||n&&n.get("triggerOn")||"mousemove|click";ny("axisPointer",i,function(t,e,i){"none"!==r&&("leave"===t||0<=r.indexOf(t))&&i({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){ay(e.getZr(),"axisPointer"),sy.superApply(this._model,"remove",arguments)},dispose:function(t,e){ay("axisPointer",e),sy.superApply(this._model,"dispose",arguments)}}),ly=Hr(),uy=b,hy=C;function cy(){}function dy(t,e,i,n){!function i(n,t){{if(N(n)&&N(t)){var r=!0;return D(t,function(t,e){r=r&&i(n[e],t)}),!!r}return n===t}}(ly(i).lastProp,n)&&(ly(i).lastProp=n,e?il(i,n,t):(i.stopAnimation(),i.attr(n)))}function fy(t,e){t[e.get("label.show")?"show":"hide"]()}function py(t){return{position:t.position.slice(),rotation:t.rotation||0}}function gy(t,e,i){var n=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=r&&(t.zlevel=r),t.silent=i)})}function my(t,e,i,n,r){var o=vy(i.get("value"),e.axis,e.ecModel,i.get("seriesDataIndices"),{precision:i.get("label.precision"),formatter:i.get("label.formatter")}),a=i.getModel("label"),s=Gl(a.get("padding")||0),l=a.getFont(),u=un(o,l),h=r.position,c=u.width+s[1]+s[3],d=u.height+s[0]+s[2],f=r.align;"right"===f&&(h[0]-=c),"center"===f&&(h[0]-=c/2);var p=r.verticalAlign;"bottom"===p&&(h[1]-=d),"middle"===p&&(h[1]-=d/2),function(t,e,i,n){var r=n.getWidth(),o=n.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+i,o)-i,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}(h,c,d,n);var g=a.get("backgroundColor");g&&"auto"!==g||(g=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:c,height:d,r:a.get("borderRadius")},position:h.slice(),style:{text:o,textFont:l,textFill:a.getTextColor(),textPosition:"inside",textPadding:s,fill:g,stroke:a.get("borderColor")||"transparent",lineWidth:a.get("borderWidth")||0,shadowBlur:a.get("shadowBlur"),shadowColor:a.get("shadowColor"),shadowOffsetX:a.get("shadowOffsetX"),shadowOffsetY:a.get("shadowOffsetY")},z2:10}}function vy(t,e,r,i,n){t=e.scale.parse(t);var o=e.scale.getLabel(t,{precision:n.precision}),a=n.formatter;if(a){var s={value:Zp(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};D(i,function(t){var e=r.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,n=e&&e.getDataParams(i);n&&s.seriesData.push(n)}),E(a)?o=a.replace("{value}",o):z(a)&&(o=a(s))}return o}function yy(t,e,i){var n=ee();return ae(n,n,i.rotation),oe(n,n,i.position),ol([t.dataToCoord(e),(i.labelOffset||0)+(i.labelDirection||1)*(i.labelMargin||0)],n)}$r((cy.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,i,n){var r=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,n||this._lastValue!==r||this._lastStatus!==o){this._lastValue=r,this._lastStatus=o;var a=this._group,s=this._handle;if(!o||"hide"===o)return a&&a.hide(),void(s&&s.hide());a&&a.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,i);var u=l.graphicKey;u!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=u;var h=this._moveAnimation=this.determineAnimation(t,e);if(a){var c=T(dy,e,h);this.updatePointerEl(a,l,c,e),this.updateLabelEl(a,l,c,e)}else a=this._group=new Si,this.createPointerEl(a,l,t,e),this.createLabelEl(a,l,t,e),i.getZr().add(a);gy(a,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var i=e.get("animation"),n=t.axis,r="category"===n.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"!==i&&null!=i)return!0===i;var a=this.animationThreshold;if(r&&n.getBandWidth()>a)return!0;if(o){var s=Ym(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1},makeElOption:function(t,e,i,n,r){},createPointerEl:function(t,e,i,n){var r=e.pointer;if(r){var o=ly(t).pointerEl=new dl[r.type](uy(e.pointer));t.add(o)}},createLabelEl:function(t,e,i,n){if(e.label){var r=ly(t).labelEl=new Ja(uy(e.label));t.add(r),fy(r,n)}},updatePointerEl:function(t,e,i){var n=ly(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,i,n){var r=ly(t).labelEl;r&&(r.setStyle(e.label.style),i(r,{shape:e.label.shape,position:e.label.position}),fy(r,n))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e,i=this._axisPointerModel,n=this._api.getZr(),r=this._handle,o=i.getModel("handle"),a=i.get("status");if(!o.get("show")||!a||"hide"===a)return r&&n.remove(r),void(this._handle=null);this._handle||(e=!0,r=this._handle=ul(o.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){Wt(t.event)},onmousedown:hy(this._onHandleDragMove,this,0,0),drift:hy(this._onHandleDragMove,this),ondragend:hy(this._onHandleDragEnd,this)}),n.add(r)),gy(r,i,!1);r.setStyle(o.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var s=o.get("size");O(s)||(s=[s,s]),r.attr("scale",[s[0]/2,s[1]/2]),vc(this,"_doDispatchAxisPointer",o.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,e)}},_moveHandleToValue:function(t,e){dy(this._axisPointerModel,!e&&this._moveAnimation,this._handle,py(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(py(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(py(n)),ly(i).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){if(this._handle){var t=this._payloadInfo,e=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]})}},_onHandleDragEnd:function(t){if(this._dragging=!1,this._handle){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,i){return{x:t[i=i||0],y:t[1-i],width:e[i],height:e[1-i]}}}).constructor=cy);var _y=cy.extend({makeElOption:function(t,e,i,n,r){var o=i.axis,a=o.grid,s=n.get("type"),l=xy(a,o).getOtherAxis(o).getGlobalExtent(),u=o.toGlobalCoord(o.dataToCoord(e,!0));if(s&&"none"!==s){var h=function(t){var e,i=t.get("type"),n=t.getModel(i+"Style");return"line"===i?(e=n.getLineStyle()).fill=null:"shadow"===i&&((e=n.getAreaStyle()).stroke=null),e}(n),c=wy[s](o,u,l);c.style=h,t.graphicKey=c.type,t.pointer=c}!function(t,e,i,n,r,o){var a=Om.innerTextLayout(i.rotation,0,i.labelDirection);i.labelMargin=r.get("label.margin"),my(e,n,r,o,{position:yy(n.axis,t,i),align:a.textAlign,verticalAlign:a.textVerticalAlign})}(e,t,tv(a.model,i),i,n,r)},getHandleTransform:function(t,e,i){var n=tv(e.axis.grid.model,e,{labelInside:!1});return n.labelMargin=i.get("handle.margin"),{position:yy(e.axis,t,n),rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,i,n){var r=i.axis,o=r.grid,a=r.getGlobalExtent(!0),s=xy(o,r).getOtherAxis(r).getGlobalExtent(),l="x"===r.dim?0:1,u=t.position;u[l]+=e[l],u[l]=Math.min(a[1],u[l]),u[l]=Math.max(a[0],u[l]);var h=(s[1]+s[0])/2,c=[h,h];c[l]=u[l];return{position:u,rotation:t.rotation,cursorPoint:c,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][l]}}});function xy(t,e){var i={};return i[e.dim+"AxisIndex"]=e.index,t.getCartesian(i)}var wy={line:function(t,e,i){return{type:"Line",subPixelOptimize:!0,shape:function(t,e,i){return{x1:t[i=i||0],y1:t[1-i],x2:e[i],y2:e[1-i]}}([e,i[0]],[e,i[1]],by(t))}},shadow:function(t,e,i){var n=Math.max(1,t.getBandWidth()),r=i[1]-i[0];return{type:"Rect",shape:function(t,e,i){return{x:t[i=i||0],y:t[1-i],width:e[i],height:e[1-i]}}([e-n/2,i[0]],[n,r],by(t))}}};function by(t){return"x"===t.dim?0:1}$m.registerAxisPointerClass("CartesianAxisPointer",_y),nf(function(t){if(t){t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={});var e=t.axisPointer.link;e&&!O(e)&&(t.axisPointer.link=[e])}}),rf(gd.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=Um(t,e)}),of({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},function(t,e,i){var n=t.currTrigger,a=[t.x,t.y],r=t,o=t.dispatchAction||C(i.dispatchAction,i),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){ty(a)&&(a=Xv({seriesIndex:r.seriesIndex,dataIndex:r.dataIndex},e).point);var l=ty(a),u=r.axesInfo,h=s.axesInfo,c="leave"===n||ty(a),d={},f={},p={list:[],map:{}},g={showPointer:jv(Kv,f),showTooltip:jv(Qv,p)};Yv(s.coordSysMap,function(t,e){var o=l||t.containPoint(a);Yv(s.coordSysAxesInfo[e],function(t,e){var i=t.axis,n=function(t,e){for(var i=0;i<(t||[]).length;i++){var n=t[i];if(e.axis.dim===n.axisDim&&e.axis.model.componentIndex===n.axisIndex)return n}}(u,t);if(!c&&o&&(!u||n)){var r=n&&n.value;null!=r||l||(r=i.pointToData(a)),null!=r&&$v(t,r,g,!1,d)}})});var m={};return Yv(h,function(r,t){var o=r.linkGroup;o&&!f[t]&&Yv(o.axesInfo,function(t,e){var i=f[e];if(t!==r&&i){var n=i.value;o.mapper&&(n=r.axis.scale.parse(o.mapper(n,Jv(t),Jv(r)))),m[r.key]=n}})}),Yv(m,function(t,e){$v(h[e],t,g,!0,d)}),function(r,t,e){var o=e.axesInfo=[];Yv(t,function(t,e){var i=t.axisPointerModel.option,n=r[e];n?(t.useHandle||(i.status="show"),i.value=n.value,i.seriesDataIndices=(n.payloadBatch||[]).slice()):t.useHandle||(i.status="hide"),"show"===i.status&&o.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:i.value})})}(f,h,d),function(t,e,i,n){if(ty(e)||!t.list.length)return n({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};n({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:i.tooltipOption,position:i.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}(p,a,t,o),function(t,e,i){var n=i.getZr(),r="axisPointerLastHighlights",o=qv(n)[r]||{},a=qv(n)[r]={};Yv(t,function(t,e){var i=t.axisPointerModel.option;"show"===i.status&&Yv(i.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;a[e]=t})});var s=[],l=[];D(o,function(t,e){a[e]||l.push(t)}),D(a,function(t,e){o[e]||s.push(t)}),l.length&&i.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&i.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}(h,0,i),d}}),hf({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var Sy=D,My=Wl,Iy=["","-webkit-","-moz-","-o-"];function Cy(r){var o=[],t=r.get("transitionDuration"),e=r.get("backgroundColor"),i=r.getModel("textStyle"),n=r.get("padding");return t&&o.push(function(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return P(Iy,function(t){return t+"transition:"+i}).join(";")}(t)),e&&(v.canvasSupported?o.push("background-Color:"+e):(o.push("background-Color:#"+Ve(e)),o.push("filter:alpha(opacity=70)"))),Sy(["width","color","radius"],function(t){var e="border-"+t,i=My(e),n=r.get(i);null!=n&&o.push(e+":"+n+("color"===t?"":"px"))}),o.push(function(i){var n=[],t=i.get("fontSize"),e=i.getTextColor();e&&n.push("color:"+e),n.push("font:"+i.getFont());var r=i.get("lineHeight");null==r&&(r=Math.round(3*t/2)),t&&n.push("line-height:"+r+"px");var o=i.get("textShadowColor"),a=i.get("textShadowBlur")||0,s=i.get("textShadowOffsetX")||0,l=i.get("textShadowOffsetY")||0;return a&&n.push("text-shadow:"+s+"px "+l+"px "+a+"px "+o),Sy(["decoration","align"],function(t){var e=i.get(t);e&&n.push("text-"+t+":"+e)}),n.join(";")}(i)),null!=n&&o.push("padding:"+Gl(n).join("px ")+"px"),o.join(";")+";"}function Ty(t,e,i,n,r){var o=e&&e.painter;if(i){var a=o&&o.getViewportRoot();a&&function(t,e,i,n,r){Lt(Pt,e,n,r,!0)&&Lt(t,i,Pt[0],Pt[1])}(t,a,document.body,n,r)}else{t[0]=n,t[1]=r;var s=o&&o.getViewportRootOffset();s&&(t[0]+=s.offsetLeft,t[1]+=s.offsetTop)}t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function Ay(t,e,i){if(v.wxa)return null;var n=document.createElement("div");n.domBelongToZr=!0,this.el=n;var r=this._zr=e.getZr(),o=this._appendToBody=i&&i.appendToBody;this._styleCoord=[0,0,0,0],Ty(this._styleCoord,r,o,e.getWidth()/2,e.getHeight()/2),o?document.body.appendChild(n):t.appendChild(n),this._container=t,this._show=!1,this._hideTimeout;var a=this;n.onmouseenter=function(){a._enterable&&(clearTimeout(a._hideTimeout),a._show=!0),a._inContent=!0},n.onmousemove=function(t){if(t=t||window.event,!a._enterable){var e=r.handler;Ft(r.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t)}},n.onmouseleave=function(){a._enterable&&a._show&&a.hideLater(a._hideDelay),a._inContent=!1}}function Dy(t,e,i,n){t[0]=i,t[1]=n,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function ky(t){var e=this._zr=t.getZr();this._styleCoord=[0,0,0,0],Dy(this._styleCoord,e,t.getWidth()/2,t.getHeight()/2),this._show=!1,this._hideTimeout}Ay.prototype={constructor:Ay,_enterable:!0,update:function(t){var e=this._container,i=e.currentStyle||document.defaultView.getComputedStyle(e),n=e.style;"absolute"!==n.position&&"absolute"!==i.position&&(n.position="relative"),t.get("alwaysShowContent")&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],i=t*this._zr.getWidth(),n=e*this._zr.getHeight();this.moveTo(i,n)},show:function(t){clearTimeout(this._hideTimeout);var e=this.el,i=this._styleCoord;e.style.cssText="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+Cy(t)+";left:"+i[0]+"px;top:"+i[1]+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var i=this._styleCoord;Ty(i,this._zr,this._appendToBody,t,e);var n=this.el.style;n.left=i[0]+"px",n.top=i[1]+"px"},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(C(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){this.el.parentNode.removeChild(this.el)},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var i=document.defaultView.getComputedStyle(this.el);i&&(t+=parseInt(i.borderLeftWidth,10)+parseInt(i.borderRightWidth,10),e+=parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10))}return{width:t,height:e}}},ky.prototype={constructor:ky,_enterable:!0,update:function(t){t.get("alwaysShowContent")&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],i=t*this._zr.getWidth(),n=e*this._zr.getHeight();this.moveTo(i,n)},show:function(t){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,i){this.el&&this._zr.remove(this.el);for(var n={},r=t,o="{marker",a=r.indexOf(o);0<=a;){var s=r.indexOf("|}"),l=r.substr(a+o.length,s-a-o.length);-1<l.indexOf("sub")?n["marker"+l]={textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[l],textOffset:[3,0]}:n["marker"+l]={textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[l]},a=(r=r.substr(s+1)).indexOf("{marker")}var u=i.getModel("textStyle"),h=u.get("fontSize"),c=i.get("textLineHeight");null==c&&(c=Math.round(3*h/2)),this.el=new Ba({style:js({},u,{rich:n,text:t,textBackgroundColor:i.get("backgroundColor"),textBorderRadius:i.get("borderRadius"),textFill:i.get("textStyle.color"),textPadding:i.get("padding"),textLineHeight:c}),z:i.get("z")}),this._zr.add(this.el);var d=this;this.el.on("mouseover",function(){d._enterable&&(clearTimeout(d._hideTimeout),d._show=!0),d._inContent=!0}),this.el.on("mouseout",function(){d._enterable&&d._show&&d.hideLater(d._hideDelay),d._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){if(this.el){var i=this._styleCoord;Dy(i,this._zr,t,e),this.el.attr("position",[i[0],i[1]])}},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(C(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){clearTimeout(this._hideTimeout),this.el&&this._zr.remove(this.el)},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var Py=C,Ly=D,Oy=Cl,zy=new Ja({shape:{x:-1,y:-1,width:2,height:2}});function Ey(t){for(var e=t.pop();t.length;){var i=t.pop();i&&(_l.isInstance(i)&&(i=i.get("tooltip",!0)),"string"==typeof i&&(i={formatter:i}),e=new _l(i,e,e.ecModel))}return e}function Ny(t,e){return t.dispatchAction||C(e.dispatchAction,e)}function Ry(t){return"center"===t||"middle"===t}cf({type:"tooltip",init:function(t,e){if(!v.node){var i,n=t.getComponent("tooltip"),r=n.get("renderMode");this._renderMode=Xr(r),"html"===this._renderMode?(i=new Ay(e.getDom(),e,{appendToBody:n.get("appendToBody",!0)}),this._newLine="<br/>"):(i=new ky(e),this._newLine="\n"),this._tooltipContent=i}},render:function(t,e,i){if(!v.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var n=this._tooltipContent;n.update(t),n.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var n=this._tooltipModel.get("triggerOn");ny("itemTooltip",this._api,Py(function(t,e,i){"none"!==n&&(0<=n.indexOf(t)?this._tryShow(e,i):"leave"===t&&this._hide(i))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,i=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var n=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.isDisposed()||n.manuallyShowTip(t,e,i,{x:n._lastX,y:n._lastY})})}},manuallyShowTip:function(t,e,i,n){if(n.from!==this.uid&&!v.node){var r=Ny(n,i);this._ticket="";var o=n.dataByCoordSys;if(n.tooltip&&null!=n.x&&null!=n.y){var a=zy;a.position=[n.x,n.y],a.update(),a.tooltip=n.tooltip,this._tryShow({offsetX:n.x,offsetY:n.y,target:a},r)}else if(o)this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,dataByCoordSys:n.dataByCoordSys,tooltipOption:n.tooltipOption},r);else if(null!=n.seriesIndex){if(this._manuallyAxisShowTip(t,e,i,n))return;var s=Xv(n,e),l=s.point[0],u=s.point[1];null!=l&&null!=u&&this._tryShow({offsetX:l,offsetY:u,position:n.position,target:s.el},r)}else null!=n.x&&null!=n.y&&(i.dispatchAction({type:"updateAxisPointer",x:n.x,y:n.y}),this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,target:i.getZr().findHover(n.x,n.y).target},r))}},manuallyHideTip:function(t,e,i,n){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,n.from!==this.uid&&this._hide(Ny(n,i))},_manuallyAxisShowTip:function(t,e,i,n){var r=n.seriesIndex,o=n.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){var s=e.getSeriesByIndex(r);if(s)if("axis"===(t=Ey([s.getData().getItemModel(o),s,(s.coordinateSystem||{}).model,t])).get("trigger"))return i.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:n.position}),!0}},_tryShow:function(t,e){var i=t.target;if(this._tooltipModel){this._lastX=t.offsetX,this._lastY=t.offsetY;var n=t.dataByCoordSys;n&&n.length?this._showAxisTooltip(n,t):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,i,e)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,i,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var i=t.get("showDelay");e=C(e,this),clearTimeout(this._showTimout),0<i?this._showTimout=setTimeout(e,i):e()},_showAxisTooltip:function(t,e){var d=this._ecModel,i=this._tooltipModel,n=[e.offsetX,e.offsetY],r=[],f=[],o=Ey([e.tooltipOption,i]),p=this._renderMode,a=this._newLine,g={};Ly(t,function(t){Ly(t.dataByAxis,function(s){var l=d.getComponent(s.axisDim+"Axis",s.axisIndex),u=s.value,h=[];if(l&&null!=u){var c=vy(u,l.axis,d,s.seriesDataIndices,s.valueLabelOpt);D(s.seriesDataIndices,function(t){var e=d.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,n=e&&e.getDataParams(i);if(n.axisDim=s.axisDim,n.axisIndex=s.axisIndex,n.axisType=s.axisType,n.axisId=s.axisId,n.axisValue=Zp(l.axis,u),n.axisValueLabel=c,n){f.push(n);var r,o=e.formatTooltip(i,!0,null,p);if(N(o)){r=o.html;var a=o.markers;m(g,a)}else r=o;h.push(r)}});var t=c;"html"!==p?r.push(h.join(a)):r.push((t?Xl(t)+a:"")+h.join(a))}})},this),r.reverse(),r=r.join(this._newLine+this._newLine);var s=e.position;this._showOrMove(o,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(o,s,n[0],n[1],this._tooltipContent,f):this._showTooltipContent(o,r,f,Math.random(),n[0],n[1],s,void 0,g)})},_showSeriesItemTooltip:function(t,e,i){var n=this._ecModel,r=e.seriesIndex,o=n.getSeriesByIndex(r),a=e.dataModel||o,s=e.dataIndex,l=e.dataType,u=a.getData(l),h=Ey([u.getItemModel(s),a,o&&(o.coordinateSystem||{}).model,this._tooltipModel]),c=h.get("trigger");if(null==c||"item"===c){var d,f,p=a.getDataParams(s,l),g=a.formatTooltip(s,!1,l,this._renderMode);f=N(g)?(d=g.html,g.markers):(d=g,null);var m="item_"+a.name+"_"+s;this._showOrMove(h,function(){this._showTooltipContent(h,d,p,m,t.offsetX,t.offsetY,t.position,t.target,f)}),i({type:"showTip",dataIndexInside:s,dataIndex:u.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,i){var n=e.tooltip;if("string"==typeof n){n={content:n,formatter:n}}var r=new _l(n,this._tooltipModel,this._ecModel),o=r.get("content"),a=Math.random();this._showOrMove(r,function(){this._showTooltipContent(r,o,r.get("formatterParams")||{},a,t.offsetX,t.offsetY,t.position,e)}),i({type:"showTip",from:this.uid})},_showTooltipContent:function(i,t,n,e,r,o,a,s,l){if(this._ticket="",i.get("showContent")&&i.get("show")){var u=this._tooltipContent,h=i.get("formatter");a=a||i.get("position");var c=t;if(h&&"string"==typeof h)c=ql(h,n,!0);else if("function"==typeof h){var d=Py(function(t,e){t===this._ticket&&(u.setContent(e,l,i),this._updatePosition(i,a,r,o,u,n,s))},this);this._ticket=e,c=h(n,e,d)}u.setContent(c,l,i),u.show(i),this._updatePosition(i,a,r,o,u,n,s)}},_updatePosition:function(t,e,i,n,r,o,a){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var u=r.getSize(),h=t.get("align"),c=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();if(a&&d.applyTransform(a.transform),"function"==typeof e&&(e=e([i,n],o,r.el,d,{viewSize:[s,l],contentSize:u.slice()})),O(e))i=Oy(e[0],s),n=Oy(e[1],l);else if(N(e)){e.width=u[0],e.height=u[1];var f=lu(e,{width:s,height:l});i=f.x,n=f.y,c=h=null}else if("string"==typeof e&&a){i=(p=function(t,e,i){var n=i[0],r=i[1],o=0,a=0,s=e.width,l=e.height;switch(t){case"inside":o=e.x+s/2-n/2,a=e.y+l/2-r/2;break;case"top":o=e.x+s/2-n/2,a=e.y-r-5;break;case"bottom":o=e.x+s/2-n/2,a=e.y+l+5;break;case"left":o=e.x-n-5,a=e.y+l/2-r/2;break;case"right":o=e.x+s+5,a=e.y+l/2-r/2}return[o,a]}(e,d,u))[0],n=p[1]}else{var p;i=(p=function(t,e,i,n,r,o,a){var s=i.getOuterSize(),l=s.width,u=s.height;null!=o&&(n<t+l+o?t-=l+o:t+=o);null!=a&&(r<e+u+a?e-=u+a:e+=a);return[t,e]}(i,n,r,s,l,h?null:20,c?null:20))[0],n=p[1]}h&&(i-=Ry(h)?u[0]/2:"right"===h?u[0]:0),c&&(n-=Ry(c)?u[1]/2:"bottom"===c?u[1]:0),t.get("confine")&&(i=(p=function(t,e,i,n,r){var o=i.getOuterSize(),a=o.width,s=o.height;return t=Math.min(t+a,n)-a,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}(i,n,r,s,l))[0],n=p[1]);r.moveTo(i,n)},_updateContentNotChangedOnAxis:function(n){var t=this._lastDataByCoordSys,a=!!t&&t.length===n.length;return a&&Ly(t,function(t,e){var i=t.dataByAxis||{},o=(n[e]||{}).dataByAxis||[];(a&=i.length===o.length)&&Ly(i,function(t,e){var i=o[e]||{},n=t.seriesDataIndices||[],r=i.seriesDataIndices||[];(a&=t.value===i.value&&t.axisType===i.axisType&&t.axisId===i.axisId&&n.length===r.length)&&Ly(n,function(t,e){var i=r[e];a&=t.seriesIndex===i.seriesIndex&&t.dataIndex===i.dataIndex})})}),this._lastDataByCoordSys=n,!!a},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){v.node||(this._tooltipContent.dispose(),ay("itemTooltip",e))}}),of({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),of({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var By=xc.legend.selector,Vy={all:{type:"all",title:b(By.all)},inverse:{type:"inverse",title:b(By.inverse)}},Fy=hf({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{},this._updateSelector(t)},mergeOption:function(t){Fy.superCall(this,"mergeOption",t),this._updateSelector(t)},_updateSelector:function(t){var i=t.selector;!0===i&&(i=t.selector=["all","inverse"]),O(i)&&D(i,function(t,e){E(t)&&(t={type:t}),i[e]=m(t,Vy[t.type])})},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,i=0;i<t.length;i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),e=!0;break}}e||this.select(t[0].get("name"))}},_updateData:function(r){var o=[],a=[];r.eachRawSeries(function(t){var e,i=t.name;if(a.push(i),t.legendVisualProvider){var n=t.legendVisualProvider.getAllNames();r.isSeriesFiltered(t)||(a=a.concat(n)),n.length?o=o.concat(n):e=!0}else e=!0;e&&Br(t)&&o.push(t.name)}),this._availableNames=a;var t=P(this.get("data")||o,function(t){return"string"!=typeof t&&"number"!=typeof t||(t={name:t}),new _l(t,this,this.ecModel)},this);this._data=t},getData:function(){return this._data},select:function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&D(this._data,function(t){e[t.get("name")]=!1});e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},allSelect:function(){var t=this._data,e=this.option.selected;D(t,function(t){e[t.get("name",!0)]=!0})},inverseSelect:function(){var t=this._data,i=this.option.selected;D(t,function(t){var e=t.get("name",!0);i.hasOwnProperty(e)||(i[e]=!0),i[e]=!i[e]})},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&0<=x(this._availableNames,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",itemStyle:{borderWidth:0},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:" sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}}});function Hy(t,e,i){var r,o={},a="toggleSelected"===t;return i.eachComponent("legend",function(n){a&&null!=r?n[r?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?n[t]():(n[t](e.name),r=n.isSelected(e.name)),D(n.getData(),function(t){var e=t.get("name");if("\n"!==e&&""!==e){var i=n.isSelected(e);o.hasOwnProperty(e)?o[e]=o[e]&&i:o[e]=i}})}),"allSelect"===t||"inverseSelect"===t?{selected:o}:{name:e.name,selected:o}}function Wy(t,e){var i=Gl(e.get("padding")),n=e.getItemStyle(["color","opacity"]);return n.fill=e.get("backgroundColor"),t=new Ja({shape:{x:t.x-i[3],y:t.y-i[0],width:t.width+i[1]+i[3],height:t.height+i[0]+i[2],r:e.get("borderRadius")},style:n,silent:!0,z2:-1})}of("legendToggleSelect","legendselectchanged",T(Hy,"toggleSelected")),of("legendAllSelect","legendselectall",T(Hy,"allSelect")),of("legendInverseSelect","legendinverseselect",T(Hy,"inverseSelect")),of("legendSelect","legendselected",T(Hy,"select")),of("legendUnSelect","legendunselected",T(Hy,"unSelect"));var Gy=T,Zy=D,Uy=Si,Xy=cf({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new Uy),this._backgroundEl,this.group.add(this._selectorGroup=new Uy),this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},getSelectorGroup:function(){return this._selectorGroup},render:function(t,e,i){var n=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),t.get("show",!0)){var r=t.get("align"),o=t.get("orient");r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===o?"right":"left");var a=t.get("selector",!0),s=t.get("selectorPosition",!0);!a||s&&"auto"!==s||(s="horizontal"===o?"end":"start"),this.renderInner(r,t,e,i,a,o,s);var l=t.getBoxLayoutParams(),u={width:i.getWidth(),height:i.getHeight()},h=t.get("padding"),c=lu(l,u,h),d=this.layoutInner(t,r,c,n,a,s),f=lu(A({width:d.width,height:d.height},l),u,h);this.group.attr("position",[f.x-d.x,f.y-d.y]),this.group.add(this._backgroundEl=Wy(d,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},renderInner:function(u,h,c,d,t,e,i){var f=this.getContentGroup(),p=Q(),g=h.get("selectedMode"),m=[];c.eachRawSeries(function(t){t.get("legendHoverLink")||m.push(t.id)}),Zy(h.getData(),function(o,a){var s=o.get("name");if(this.newlineDisabled||""!==s&&"\n"!==s){var t=c.getSeriesByName(s)[0];if(!p.get(s))if(t){var e=t.getData(),i=e.getVisual("color"),n=e.getVisual("borderColor");"function"==typeof i&&(i=i(t.getDataParams(0))),"function"==typeof n&&(n=n(t.getDataParams(0)));var r=e.getVisual("legendSymbol")||"roundRect",l=e.getVisual("symbol");this._createItem(s,a,o,h,r,l,u,i,n,g).on("click",Gy(jy,s,null,d,m)).on("mouseover",Gy(qy,t.name,null,d,m)).on("mouseout",Gy($y,t.name,null,d,m)),p.set(s,!0)}else c.eachRawSeries(function(t){if(!p.get(s)&&t.legendVisualProvider){var e=t.legendVisualProvider;if(!e.containName(s))return;var i=e.indexOfName(s),n=e.getItemVisual(i,"color"),r=e.getItemVisual(i,"borderColor");this._createItem(s,a,o,h,"roundRect",null,u,n,r,g).on("click",Gy(jy,null,s,d,m)).on("mouseover",Gy(qy,null,s,d,m)).on("mouseout",Gy($y,null,s,d,m)),p.set(s,!0)}},this)}else f.add(new Uy({newline:!0}))},this),t&&this._createSelector(t,h,d,e,i)},_createSelector:function(t,o,a,e,i){var s=this.getSelectorGroup();Zy(t,function(t){!function(t){var e=t.type,i=new Ba({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){a.dispatchAction({type:"all"===e?"legendAllSelect":"legendInverseSelect"})}});s.add(i);var n=o.getModel("selectorLabel"),r=o.getModel("emphasis.selectorLabel");Ys(i.style,i.hoverStyle={},n,r,{defaultText:t.title,isRectText:!1}),Gs(i)}(t)})},_createItem:function(t,e,i,n,r,o,a,s,l,u){var h=n.get("itemWidth"),c=n.get("itemHeight"),d=n.get("inactiveColor"),f=n.get("inactiveBorderColor"),p=n.get("symbolKeepAspect"),g=n.getModel("itemStyle"),m=n.isSelected(t),v=new Uy,y=i.getModel("textStyle"),_=i.get("icon"),x=i.getModel("tooltip"),w=x.parentModel,b=ig(r=_||r,0,0,h,c,m?s:d,null==p||p);if(v.add(Yy(b,r,g,l,f,m)),!_&&o&&(o!==r||"none"===o)){var S=.8*c;"none"===o&&(o="circle");var M=ig(o,(h-S)/2,(c-S)/2,S,S,m?s:d,null==p||p);v.add(Yy(M,o,g,l,f,m))}var I="left"===a?h+5:-5,C=a,T=n.get("formatter"),A=t;"string"==typeof T&&T?A=T.replace("{name}",null!=t?t:""):"function"==typeof T&&(A=T(t)),v.add(new Ba({style:js({},y,{text:A,x:I,y:c/2,textFill:m?y.getTextColor():d,textAlign:C,textVerticalAlign:"middle"})}));var D=new Ja({shape:v.getBoundingRect(),invisible:!0,tooltip:x.get("show")?k({content:t,formatter:w.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:n.componentIndex,name:t,$vars:["name"]}},x.option):null});return v.add(D),v.eachChild(function(t){t.silent=!0}),D.silent=!u,this.getContentGroup().add(v),Gs(v),v.__legendDataIndex=e,v},layoutInner:function(t,e,i,n,r,o){var a=this.getContentGroup(),s=this.getSelectorGroup();su(t.get("orient"),a,t.get("itemGap"),i.width,i.height);var l=a.getBoundingRect(),u=[-l.x,-l.y];if(r){su("horizontal",s,t.get("selectorItemGap",!0));var h=s.getBoundingRect(),c=[-h.x,-h.y],d=t.get("selectorButtonGap",!0),f=t.getOrient().index,p=0===f?"width":"height",g=0===f?"height":"width",m=0===f?"y":"x";"end"===o?c[f]+=l[p]+d:u[f]+=h[p]+d,c[1-f]+=l[g]/2-h[g]/2,s.attr("position",c),a.attr("position",u);var v={x:0,y:0};return v[p]=l[p]+d+h[p],v[g]=Math.max(l[g],h[g]),v[m]=Math.min(0,h[m]+c[1-f]),v}return a.attr("position",u),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}});function Yy(t,e,i,n,r,o){var a;return"line"!==e&&e.indexOf("empty")<0?(a=i.getItemStyle(),t.style.stroke=n,o||(a.stroke=r)):a=i.getItemStyle(["borderWidth","borderColor"]),t.setStyle(a)}function jy(t,e,i,n){$y(t,e,i,n),i.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),qy(t,e,i,n)}function qy(t,e,i,n){var r=i.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||i.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:n})}function $y(t,e,i,n){var r=i.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||i.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:n})}rf(gd.PROCESSOR.SERIES_FILTER,function(t){var i=t.findComponents({mainType:"legend"});i&&i.length&&t.filterSeries(function(t){for(var e=0;e<i.length;e++)if(!i[e].isSelected(t.name))return!1;return!0})}),vu.registerSubTypeDefaulter("legend",function(){return"plain"});var Ky=Fy.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,i,n){var r=cu(t);Ky.superCall(this,"init",t,e,i,n),Qy(this,t,r)},mergeOption:function(t,e){Ky.superCall(this,"mergeOption",t,e),Qy(this,this.option,t)}});function Qy(t,e,i){var n=[1,1];n[t.getOrient().index]=0,hu(e,i,{type:"box",ignoreSize:n})}var Jy=Si,t_=["width","height"],e_=["x","y"],i_=Xy.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){i_.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new Jy),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Jy),this._showController},resetInner:function(){i_.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,r,e,o,i,n,a){var s=this;i_.superCall(this,"renderInner",t,r,e,o,i,n,a);var l=this._controllerGroup,u=r.get("pageIconSize",!0);O(u)||(u=[u,u]),c("pagePrev",0);var h=r.getModel("pageTextStyle");function c(t,e){var i=t+"DataIndex",n=ul(r.get("pageIcons",!0)[r.getOrient().name][e],{onclick:C(s._pageGo,s,i,r,o)},{x:-u[0]/2,y:-u[1]/2,width:u[0],height:u[1]});n.name=t,l.add(n)}l.add(new Ba({name:"pageText",style:{textFill:h.getTextColor(),font:h.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),c("pageNext",1)},layoutInner:function(t,e,i,n,r,o){var a=this.getSelectorGroup(),s=t.getOrient().index,l=t_[s],u=e_[s],h=t_[1-s],c=e_[1-s];r&&su("horizontal",a,t.get("selectorItemGap",!0));var d=t.get("selectorButtonGap",!0),f=a.getBoundingRect(),p=[-f.x,-f.y],g=b(i);r&&(g[l]=i[l]-f[l]-d);var m=this._layoutContentAndController(t,n,g,s,l,h,c);if(r){if("end"===o)p[s]+=m[l]+d;else{var v=f[l]+d;p[s]-=v,m[u]-=v}m[l]+=f[l]+d,p[1-s]+=m[c]+m[h]/2-f[h]/2,m[h]=Math.max(m[h],f[h]),m[c]=Math.min(m[c],f[c]+p[1-s]),a.attr("position",p)}return m},_layoutContentAndController:function(t,e,i,n,r,o,a){var s=this.getContentGroup(),l=this._containerGroup,u=this._controllerGroup;su(t.get("orient"),s,t.get("itemGap"),n?i.width:null,n?null:i.height),su("horizontal",u,t.get("pageButtonItemGap",!0));var h=s.getBoundingRect(),c=u.getBoundingRect(),d=this._showController=h[r]>i[r],f=[-h.x,-h.y];e||(f[n]=s.position[n]);var p=[0,0],g=[-c.x,-c.y],m=W(t.get("pageButtonGap",!0),t.get("itemGap",!0));d&&("end"===t.get("pageButtonPosition",!0)?g[n]+=i[r]-c[r]:p[n]+=c[r]+m);g[1-n]+=h[o]/2-c[o]/2,s.attr("position",f),l.attr("position",p),u.attr("position",g);var v={x:0,y:0};if(v[r]=d?i[r]:h[r],v[o]=Math.max(h[o],c[o]),v[a]=Math.min(0,c[a]+g[1-n]),l.__rectSize=i[r],d){var y={x:0,y:0};y[r]=Math.max(i[r]-c[r]-m,0),y[o]=v[o],l.setClipPath(new Ja({shape:y})),l.__rectSize=y[r]}else u.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var _=this._getPageInfo(t);return null!=_.pageIndex&&il(s,{position:_.contentPosition},d&&t),this._updatePageInfoView(t,_),v},_pageGo:function(t,e,i){var n=this._getPageInfo(e)[t];null!=n&&i.dispatchAction({type:"legendScroll",scrollDataIndex:n,legendId:e.id})},_updatePageInfoView:function(n,r){var o=this._controllerGroup;D(["pagePrev","pageNext"],function(t){var e=null!=r[t+"DataIndex"],i=o.childOfName(t);i&&(i.setStyle("fill",e?n.get("pageIconColor",!0):n.get("pageIconInactiveColor",!0)),i.cursor=e?"pointer":"default")});var t=o.childOfName("pageText"),e=n.get("pageFormatter"),i=r.pageIndex,a=null!=i?i+1:0,s=r.pageCount;t&&e&&t.setStyle("text",E(e)?e.replace("{current}",a).replace("{total}",s):e({current:a,total:s}))},_getPageInfo:function(t){var e=t.get("scrollDataIndex",!0),i=this.getContentGroup(),n=this._containerGroup.__rectSize,r=t.getOrient().index,o=t_[r],a=e_[r],s=this._findTargetItemIndex(e),l=i.children(),u=l[s],h=l.length,c=h?1:0,d={contentPosition:i.position.slice(),pageCount:c,pageIndex:c-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!u)return d;var f=y(u);d.contentPosition[r]=-f.s;for(var p=s+1,g=f,m=f,v=null;p<=h;++p)(!(v=y(l[p]))&&m.e>g.s+n||v&&!_(v,g.s))&&(g=m.i>g.i?m:v)&&(null==d.pageNextDataIndex&&(d.pageNextDataIndex=g.i),++d.pageCount),m=v;for(p=s-1,g=f,m=f,v=null;-1<=p;--p)(v=y(l[p]))&&_(m,v.s)||!(g.i<m.i)||(m=g,null==d.pagePrevDataIndex&&(d.pagePrevDataIndex=g.i),++d.pageCount,++d.pageIndex),g=v;return d;function y(t){if(t){var e=t.getBoundingRect(),i=e[a]+t.position[r];return{s:i,e:i+e[o],i:t.__legendDataIndex}}}function _(t,e){return t.e>=e&&t.s<=e+n}},_findTargetItemIndex:function(n){return this._showController?(this.getContentGroup().eachChild(function(t,e){var i=t.__legendDataIndex;null==o&&null!=i&&(o=e),i===n&&(r=e)}),null!=r?r:o):0;var r,o}});of("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;null!=i&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(i)})}),hf({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),cf({type:"title",render:function(t,e,i){if(this.group.removeAll(),t.get("show")){var n=this.group,r=t.getModel("textStyle"),o=t.getModel("subtextStyle"),a=t.get("textAlign"),s=W(t.get("textBaseline"),t.get("textVerticalAlign")),l=new Ba({style:js({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),u=l.getBoundingRect(),h=t.get("subtext"),c=new Ba({style:js({},o,{text:h,textFill:o.getTextColor(),y:u.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink"),p=t.get("triggerEvent",!0);l.silent=!d&&!p,c.silent=!f&&!p,d&&l.on("click",function(){eu(d,"_"+t.get("target"))}),f&&c.on("click",function(){eu(f,"_"+t.get("subtarget"))}),l.eventData=c.eventData=p?{componentType:"title",componentIndex:t.componentIndex}:null,n.add(l),h&&n.add(c);var g=n.getBoundingRect(),m=t.getBoxLayoutParams();m.width=g.width,m.height=g.height;var v=lu(m,{width:i.getWidth(),height:i.getHeight()},t.get("padding"));a||("middle"===(a=t.get("left")||t.get("right"))&&(a="center"),"right"===a?v.x+=v.width:"center"===a&&(v.x+=v.width/2)),s||("center"===(s=t.get("top")||t.get("bottom"))&&(s="middle"),"bottom"===s?v.y+=v.height:"middle"===s&&(v.y+=v.height/2),s=s||"top"),n.attr("position",[v.x,v.y]);var y={textAlign:a,textVerticalAlign:s};l.setStyle(y),c.setStyle(y),g=n.getBoundingRect();var _=v.margin,x=t.getItemStyle(["color","opacity"]);x.fill=t.get("backgroundColor");var w=new Ja({shape:{x:g.x-_[3],y:g.y-_[0],width:g.width+_[1]+_[3],height:g.height+_[0]+_[2],r:t.get("borderRadius")},style:x,subPixelOptimize:!0,silent:!0});n.add(w)}}});var n_=Hl,r_=Xl;function o_(t){Or(t,"label",["show"])}var a_=hf({type:"marker",dependencies:["series","grid","polar","geo"],init:function(t,e,i){this.mergeDefaultAndTheme(t,i),this._mergeOption(t,i,!1,!0)},isAnimationEnabled:function(){if(v.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},mergeOption:function(t,e){this._mergeOption(t,e,!1,!1)},_mergeOption:function(t,n,e,r){var o=this.constructor,a=this.mainType+"Model";e||n.eachSeries(function(t){var e=t.get(this.mainType,!0),i=t[a];e&&e.data?(i?i._mergeOption(e,n,!0):(r&&o_(e),D(e.data,function(t){t instanceof Array?(o_(t[0]),o_(t[1])):o_(t)}),k(i=new o(e,this,n),{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),i.__hostSeries=t),t[a]=i):t[a]=null},this)},formatTooltip:function(t,e,i,n){var r=this.getData(),o=this.getRawValue(t),a=O(o)?P(o,n_).join(", "):n_(o),s=r.getName(t),l=r_(this.name);return null==o&&!s||(l+="html"===n?"<br/>":"\n"),s&&(l+=r_(s),null!=o&&(l+=" : ")),null!=o&&(l+=r_(a)),l},getData:function(){return this._data},setData:function(t){this._data=t}});S(a_,Lh),a_.extend({type:"markPoint",defaultOption:{zlevel:0,z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}}});var s_=x;function l_(t,e,i,n,r,o){var a=[],s=$f(e,n)?e.getCalculationInfo("stackResultDimension"):n,l=g_(e,s,t),u=e.indicesOfNearest(s,l)[0];a[r]=e.get(i,u),a[o]=e.get(s,u);var h=e.get(n,u),c=Dl(e.get(n,u));return 0<=(c=Math.min(c,20))&&(a[o]=+a[o].toFixed(c)),[a,h]}var u_=T,h_={min:u_(l_,"min"),max:u_(l_,"max"),average:u_(l_,"average")};function c_(t,e){var i=t.getData(),n=t.coordinateSystem;if(e&&!function(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}(e)&&!O(e.coord)&&n){var r=n.dimensions,o=d_(e,i,n,t);if((e=b(e)).type&&h_[e.type]&&o.baseAxis&&o.valueAxis){var a=s_(r,o.baseAxis.dim),s=s_(r,o.valueAxis.dim),l=h_[e.type](i,o.baseDataDim,o.valueDataDim,a,s);e.coord=l[0],e.value=l[1]}else{for(var u=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis],h=0;h<2;h++)h_[u[h]]&&(u[h]=g_(i,i.mapDimension(r[h]),u[h]));e.coord=u}}return e}function d_(t,e,i,n){var r={};return null!=t.valueIndex||null!=t.valueDim?(r.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=i.getAxis(function(t,e){var i=t.getData(),n=i.dimensions;e=i.getDimension(e);for(var r=0;r<n.length;r++){var o=i.getDimensionInfo(n[r]);if(o.name===e)return o.coordDim}}(n,r.valueDataDim)),r.baseAxis=i.getOtherAxis(r.valueAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim)):(r.baseAxis=n.getBaseAxis(),r.valueAxis=i.getOtherAxis(r.baseAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim),r.valueDataDim=e.mapDimension(r.valueAxis.dim)),r}function f_(t,e){return!(t&&t.containData&&e.coord&&!function(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}(e))||t.containData(e.coord)}function p_(t,e,i,n){return n<2?t.coord&&t.coord[n]:t.value}function g_(t,e,i){if("average"!==i)return"median"===i?t.getMedian(e):t.getDataExtent(e,!0)["max"===i?1:0];var n=0,r=0;return t.each(e,function(t,e){isNaN(t)||(n+=t,r++)}),n/r}var m_=cf({type:"marker",init:function(){this.markerGroupMap=Q()},render:function(t,i,n){var e=this.markerGroupMap;e.each(function(t){t.__keep=!1});var r=this.type+"Model";i.eachSeries(function(t){var e=t[r];e&&this.renderSeries(t,e,i,n)},this),e.each(function(t){t.__keep||this.group.remove(t.group)},this)},renderSeries:function(){}});function v_(s,l,u){var h=l.coordinateSystem;s.each(function(t){var e,i=s.getItemModel(t),n=Cl(i.get("x"),u.getWidth()),r=Cl(i.get("y"),u.getHeight());if(isNaN(n)||isNaN(r)){if(l.getMarkerPosition)e=l.getMarkerPosition(s.getValues(s.dimensions,t));else if(h){var o=s.get(h.dimensions[0],t),a=s.get(h.dimensions[1],t);e=h.dataToPoint([o,a])}}else e=[n,r];isNaN(n)||(e[0]=n),isNaN(r)||(e[1]=r),s.setItemLayout(t,e)})}m_.extend({type:"markPoint",updateTransform:function(t,e,i){e.eachSeries(function(t){var e=t.markPointModel;e&&(v_(e.getData(),t,i),this.markerGroupMap.get(t.id).updateLayout(e))},this)},renderSeries:function(t,h,e,i){var n=t.coordinateSystem,r=t.id,c=t.getData(),o=this.markerGroupMap,a=o.get(r)||o.set(r,new Rg),d=function(t,e,i){var n;n=t?P(t&&t.dimensions,function(t){return A({name:t},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{})}):[{name:"value",type:"float"}];var r=new kf(n,i),o=P(i.get("data"),T(c_,e));t&&(o=I(o,T(f_,t)));return r.initData(o,null,t?p_:function(t){return t.value}),r}(n,t,h);h.setData(d),v_(h.getData(),t,i),d.each(function(t){var e=d.getItemModel(t),i=e.getShallow("symbol"),n=e.getShallow("symbolSize"),r=e.getShallow("symbolRotate"),o=z(i),a=z(n),s=z(r);if(o||a||s){var l=h.getRawValue(t),u=h.getDataParams(t);o&&(i=i(l,u)),a&&(n=n(l,u)),s&&(r=r(l,u))}d.setItemVisual(t,{symbol:i,symbolSize:n,symbolRotate:r,color:e.get("itemStyle.color")||c.getVisual("color")})}),a.updateData(d),this.group.add(a.group),d.eachItemGraphicEl(function(t){t.traverse(function(t){t.dataModel=h})}),a.__keep=!0,a.group.silent=h.get("silent")||t.get("silent")}}),nf(function(t){t.markPoint=t.markPoint||{}}),a_.extend({type:"markLine",defaultOption:{zlevel:0,z:5,symbol:["circle","arrow"],symbolSize:[8,16],precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"}});var y_=es.prototype,__=os.prototype;function x_(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}var w_=ws({type:"ec-line",style:{stroke:"#000",fill:null},shape:{x1:0,y1:0,x2:0,y2:0,percent:1,cpx1:null,cpy1:null},buildPath:function(t,e){this[x_(e)?"_buildPathLine":"_buildPathCurve"](t,e)},_buildPathLine:y_.buildPath,_buildPathCurve:__.buildPath,pointAt:function(t){return this[x_(this.shape)?"_pointAtLine":"_pointAtCurve"](t)},_pointAtLine:y_.pointAt,_pointAtCurve:__.pointAt,tangentAt:function(t){var e=this.shape,i=x_(e)?[e.x2-e.x1,e.y2-e.y1]:this._tangentAtCurve(t);return ft(i,i)},_tangentAtCurve:__.tangentAt}),b_=["fromSymbol","toSymbol"];function S_(t){return"_"+t+"Type"}function M_(t,e,i){var n=e.getItemVisual(i,t);if(n&&"none"!==n){var r=e.getItemVisual(i,"color"),o=e.getItemVisual(i,t+"Size"),a=e.getItemVisual(i,t+"Rotate");O(o)||(o=[o,o]);var s=ig(n,-o[0]/2,-o[1]/2,o[0],o[1],r);return s.__specifiedRotation=null==a||isNaN(a)?void 0:+a*Math.PI/180||0,s.name=t,s}}function I_(t,e){t.x1=e[0][0],t.y1=e[0][1],t.x2=e[1][0],t.y2=e[1][1],t.percent=1;var i=e[2];i?(t.cpx1=i[0],t.cpy1=i[1]):(t.cpx1=NaN,t.cpy1=NaN)}function C_(t,e,i){Si.call(this),this._createLine(t,e,i)}var T_=C_.prototype;function A_(t){this._ctor=t||C_,this.group=new Si}T_.beforeUpdate=function(){var t=this.childOfName("fromSymbol"),e=this.childOfName("toSymbol"),i=this.childOfName("label");if(t||e||!i.ignore){for(var n=1,r=this.parent;r;)r.scale&&(n/=r.scale[0]),r=r.parent;var o=this.childOfName("line");if(this.__dirty||o.__dirty){var a=o.shape.percent,s=o.pointAt(0),l=o.pointAt(a),u=st([],l,s);if(ft(u,u),t){if(t.attr("position",s),null==(c=t.__specifiedRotation)){var h=o.tangentAt(0);t.attr("rotation",Math.PI/2-Math.atan2(h[1],h[0]))}else t.attr("rotation",c);t.attr("scale",[n*a,n*a])}if(e){var c;if(e.attr("position",l),null==(c=e.__specifiedRotation)){h=o.tangentAt(1);e.attr("rotation",-Math.PI/2-Math.atan2(h[1],h[0]))}else e.attr("rotation",c);e.attr("scale",[n*a,n*a])}if(!i.ignore){var d,f,p,g;i.attr("position",l);var m=i.__labelDistance,v=m[0]*n,y=m[1]*n,_=a/2,x=[(h=o.tangentAt(_))[1],-h[0]],w=o.pointAt(_);0<x[1]&&(x[0]=-x[0],x[1]=-x[1]);var b,S=h[0]<0?-1:1;if("start"!==i.__position&&"end"!==i.__position){var M=-Math.atan2(h[1],h[0]);l[0]<s[0]&&(M=Math.PI+M),i.attr("rotation",M)}switch(i.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":b=-y,p="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":b=y,p="top";break;default:b=0,p="middle"}switch(i.__position){case"end":d=[u[0]*v+l[0],u[1]*y+l[1]],f=.8<u[0]?"left":u[0]<-.8?"right":"center",p=.8<u[1]?"top":u[1]<-.8?"bottom":"middle";break;case"start":d=[-u[0]*v+s[0],-u[1]*y+s[1]],f=.8<u[0]?"right":u[0]<-.8?"left":"center",p=.8<u[1]?"bottom":u[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":d=[v*S+s[0],s[1]+b],f=h[0]<0?"right":"left",g=[-v*S,-b];break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":d=[w[0],w[1]+b],f="center",g=[0,-b];break;case"insideEndTop":case"insideEnd":case"insideEndBottom":d=[-v*S+l[0],l[1]+b],f=0<=h[0]?"right":"left",g=[v*S,-b]}i.attr({style:{textVerticalAlign:i.__verticalAlign||p,textAlign:i.__textAlign||f},position:d,scale:[n,n],origin:g})}}}},T_._createLine=function(i,n,t){var e=i.hostModel,r=function(t){var e=new w_({name:"line",subPixelOptimize:!0});return I_(e.shape,t),e}(i.getItemLayout(n));r.shape.percent=0,nl(r,{shape:{percent:1}},e,n),this.add(r);var o=new Ba({name:"label",lineLabelOriginalOpacity:1});this.add(o),D(b_,function(t){var e=M_(t,i,n);this.add(e),this[S_(t)]=i.getItemVisual(n,t)},this),this._updateCommonStl(i,n,t)},T_.updateData=function(r,o,t){var e=r.hostModel,i=this.childOfName("line"),n=r.getItemLayout(o),a={shape:{}};I_(a.shape,n),il(i,a,e,o),D(b_,function(t){var e=r.getItemVisual(o,t),i=S_(t);if(this[i]!==e){this.remove(this.childOfName(t));var n=M_(t,r,o);this.add(n)}this[i]=e},this),this._updateCommonStl(r,o,t)},T_._updateCommonStl=function(t,e,i){var n=t.hostModel,r=this.childOfName("line"),o=i&&i.lineStyle,a=i&&i.hoverLineStyle,s=i&&i.labelModel,l=i&&i.hoverLabelModel;if(!i||t.hasItemOption){var u=t.getItemModel(e);o=u.getModel("lineStyle").getLineStyle(),a=u.getModel("emphasis.lineStyle").getLineStyle(),s=u.getModel("label"),l=u.getModel("emphasis.label")}var h=t.getItemVisual(e,"color"),c=G(t.getItemVisual(e,"opacity"),o.opacity,1);r.useStyle(A({strokeNoScale:!0,fill:"none",stroke:h,opacity:c},o)),r.hoverStyle=a,D(b_,function(t){var e=this.childOfName(t);e&&(e.setColor(h),e.setStyle({opacity:c}))},this);var d,f,p=s.getShallow("show"),g=l.getShallow("show"),m=this.childOfName("label");if((p||g)&&(d=h||"#000",null==(f=n.getFormattedLabel(e,"normal",t.dataType)))){var v=n.getRawValue(e);f=null==v?t.getName(e):isFinite(v)?Tl(v):v}var y=p?f:null,_=g?W(n.getFormattedLabel(e,"emphasis",t.dataType),f):null,x=m.style;if(null!=y||null!=_){js(m.style,s,{text:y},{autoColor:d}),m.__textAlign=x.textAlign,m.__verticalAlign=x.textVerticalAlign,m.__position=s.get("position")||"middle";var w=s.get("distance");O(w)||(w=[w,w]),m.__labelDistance=w}m.hoverStyle=null!=_?{text:_,textFill:l.getTextColor(!0),fontStyle:l.getShallow("fontStyle"),fontWeight:l.getShallow("fontWeight"),fontSize:l.getShallow("fontSize"),fontFamily:l.getShallow("fontFamily")}:{text:null},m.ignore=!p&&!g,Gs(this)},T_.highlight=function(){this.trigger("emphasis")},T_.downplay=function(){this.trigger("normal")},T_.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},T_.setLinePoints=function(t){var e=this.childOfName("line");I_(e.shape,t),e.dirty()},w(C_,Si);var D_=A_.prototype;function k_(t){var e=t.hostModel;return{lineStyle:e.getModel("lineStyle").getLineStyle(),hoverLineStyle:e.getModel("emphasis.lineStyle").getLineStyle(),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label")}}function P_(t){return isNaN(t[0])||isNaN(t[1])}function L_(t){return!P_(t[0])&&!P_(t[1])}D_.isPersistent=function(){return!0},D_.updateData=function(i){var n=this,e=n.group,r=n._lineData;n._lineData=i,r||e.removeAll();var o=k_(i);i.diff(r).add(function(t){!function(t,e,i,n){if(!L_(e.getItemLayout(i)))return;var r=new t._ctor(e,i,n);e.setItemGraphicEl(i,r),t.group.add(r)}(n,i,t,o)}).update(function(t,e){!function(t,e,i,n,r,o){var a=e.getItemGraphicEl(n);if(!L_(i.getItemLayout(r)))return t.group.remove(a);a?a.updateData(i,r,o):a=new t._ctor(i,r,o);i.setItemGraphicEl(r,a),t.group.add(a)}(n,r,i,e,t,o)}).remove(function(t){e.remove(r.getItemGraphicEl(t))}).execute()},D_.updateLayout=function(){var i=this._lineData;i&&i.eachItemGraphicEl(function(t,e){t.updateLayout(i,e)},this)},D_.incrementalPrepareUpdate=function(t){this._seriesScope=k_(t),this._lineData=null,this.group.removeAll()},D_.incrementalUpdate=function(t,e){function i(t){t.isGroup||function(t){return t.animators&&0<t.animators.length}(t)||(t.incremental=t.useHoverLayer=!0)}for(var n=t.start;n<t.end;n++){if(L_(e.getItemLayout(n))){var r=new this._ctor(e,n,this._seriesScope);r.traverse(i),this.group.add(r),e.setItemGraphicEl(n,r)}}},D_.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},D_._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()};function O_(t,e,i,n){var r=t.getData(),o=n.type;if(!O(n)&&("min"===o||"max"===o||"average"===o||"median"===o||null!=n.xAxis||null!=n.yAxis)){var a,s;if(null!=n.yAxis||null!=n.xAxis)a=e.getAxis(null!=n.yAxis?"y":"x"),s=H(n.yAxis,n.xAxis);else{var l=d_(n,r,e,t);a=l.valueAxis,s=g_(r,Kf(r,l.valueDataDim),o)}var u="x"===a.dim?0:1,h=1-u,c=b(n),d={};c.type=null,c.coord=[],d.coord=[],c.coord[h]=-1/0,d.coord[h]=1/0;var f=i.get("precision");0<=f&&"number"==typeof s&&(s=+s.toFixed(Math.min(f,20))),c.coord[u]=d.coord[u]=s,n=[c,d,{type:o,valueIndex:n.valueIndex,value:s}]}return(n=[c_(t,n[0]),c_(t,n[1]),k({},n[2])])[2].type=n[2].type||"",m(n[2],n[0]),m(n[2],n[1]),n}function z_(t){return!isNaN(t)&&!isFinite(t)}function E_(t,e,i,n){var r=1-t,o=n.dimensions[t];return z_(e[r])&&z_(i[r])&&e[t]===i[t]&&n.getAxis(o).containData(e[t])}function N_(t,e){if("cartesian2d"===t.type){var i=e[0].coord,n=e[1].coord;if(i&&n&&(E_(1,i,n,t)||E_(0,i,n,t)))return!0}return f_(t,e[0])&&f_(t,e[1])}function R_(t,e,i,n,r){var o,a=n.coordinateSystem,s=t.getItemModel(e),l=Cl(s.get("x"),r.getWidth()),u=Cl(s.get("y"),r.getHeight());if(isNaN(l)||isNaN(u)){if(n.getMarkerPosition)o=n.getMarkerPosition(t.getValues(t.dimensions,e));else{var h=a.dimensions,c=t.get(h[0],e),d=t.get(h[1],e);o=a.dataToPoint([c,d])}if("cartesian2d"===a.type){var f=a.getAxis("x"),p=a.getAxis("y");h=a.dimensions;z_(t.get(h[0],e))?o[0]=f.toGlobalCoord(f.getExtent()[i?0:1]):z_(t.get(h[1],e))&&(o[1]=p.toGlobalCoord(p.getExtent()[i?0:1]))}isNaN(l)||(o[0]=l),isNaN(u)||(o[1]=u)}else o=[l,u];t.setItemLayout(e,o)}m_.extend({type:"markLine",updateTransform:function(t,e,o){e.eachSeries(function(e){var t=e.markLineModel;if(t){var i=t.getData(),n=t.__from,r=t.__to;n.each(function(t){R_(n,t,!0,e,o),R_(r,t,!1,e,o)}),i.each(function(t){i.setItemLayout(t,[n.getItemLayout(t),r.getItemLayout(t)])}),this.markerGroupMap.get(e.id).updateLayout()}},this)},renderSeries:function(r,i,t,o){var e=r.coordinateSystem,n=r.id,a=r.getData(),s=this.markerGroupMap,l=s.get(n)||s.set(n,new A_);this.group.add(l.group);var u=function(t,e,i){var n;n=t?P(t&&t.dimensions,function(t){return A({name:t},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{})}):[{name:"value",type:"float"}];var r=new kf(n,i),o=new kf(n,i),a=new kf([],i),s=P(i.get("data"),T(O_,e,t,i));t&&(s=I(s,T(N_,t)));var l=t?p_:function(t){return t.value};return r.initData(P(s,function(t){return t[0]}),null,l),o.initData(P(s,function(t){return t[1]}),null,l),a.initData(P(s,function(t){return t[2]})),a.hasItemOption=!0,{from:r,to:o,line:a}}(e,r,i),h=u.from,c=u.to,d=u.line;i.__from=h,i.__to=c,i.setData(d);var f=i.get("symbol"),p=i.get("symbolSize");function g(t,e,i){var n=t.getItemModel(e);R_(t,e,i,r,o),t.setItemVisual(e,{symbolRotate:n.get("symbolRotate"),symbolSize:n.get("symbolSize")||p[i?0:1],symbol:n.get("symbol",!0)||f[i?0:1],color:n.get("itemStyle.color")||a.getVisual("color")})}O(f)||(f=[f,f]),"number"==typeof p&&(p=[p,p]),u.from.each(function(t){g(h,t,!0),g(c,t,!1)}),d.each(function(t){var e=d.getItemModel(t).get("lineStyle.color");d.setItemVisual(t,{color:e||h.getItemVisual(t,"color")}),d.setItemLayout(t,[h.getItemLayout(t),c.getItemLayout(t)]),d.setItemVisual(t,{fromSymbolRotate:h.getItemVisual(t,"symbolRotate"),fromSymbolSize:h.getItemVisual(t,"symbolSize"),fromSymbol:h.getItemVisual(t,"symbol"),toSymbolRotate:c.getItemVisual(t,"symbolRotate"),toSymbolSize:c.getItemVisual(t,"symbolSize"),toSymbol:c.getItemVisual(t,"symbol")})}),l.updateData(d),u.line.eachItemGraphicEl(function(t,e){t.traverse(function(t){t.dataModel=i})}),l.__keep=!0,l.group.silent=i.get("silent")||r.get("silent")}}),nf(function(t){t.markLine=t.markLine||{}}),a_.extend({type:"markArea",defaultOption:{zlevel:0,z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}}});function B_(t,e,i,n){var r=c_(t,n[0]),o=c_(t,n[1]),a=H,s=r.coord,l=o.coord;s[0]=a(s[0],-1/0),s[1]=a(s[1],-1/0),l[0]=a(l[0],1/0),l[1]=a(l[1],1/0);var u=p([{},r,o]);return u.coord=[r.coord,o.coord],u.x0=r.x,u.y0=r.y,u.x1=o.x,u.y1=o.y,u}function V_(t){return!isNaN(t)&&!isFinite(t)}function F_(t,e,i){var n=1-t;return V_(e[n])&&V_(i[n])}function H_(t,e){var i=e.coord[0],n=e.coord[1];return!("cartesian2d"!==t.type||!i||!n||!F_(1,i,n)&&!F_(0,i,n))||(f_(t,{coord:i,x:e.x0,y:e.y0})||f_(t,{coord:n,x:e.x1,y:e.y1}))}function W_(t,e,i,n,r){var o,a=n.coordinateSystem,s=t.getItemModel(e),l=Cl(s.get(i[0]),r.getWidth()),u=Cl(s.get(i[1]),r.getHeight());if(isNaN(l)||isNaN(u)){if(n.getMarkerPosition)o=n.getMarkerPosition(t.getValues(i,e));else{var h=[f=t.get(i[0],e),p=t.get(i[1],e)];a.clampData&&a.clampData(h,h),o=a.dataToPoint(h,!0)}if("cartesian2d"===a.type){var c=a.getAxis("x"),d=a.getAxis("y"),f=t.get(i[0],e),p=t.get(i[1],e);V_(f)?o[0]=c.toGlobalCoord(c.getExtent()["x0"===i[0]?0:1]):V_(p)&&(o[1]=d.toGlobalCoord(d.getExtent()["y0"===i[1]?0:1]))}isNaN(l)||(o[0]=l),isNaN(u)||(o[1]=u)}else o=[l,u];return o}var G_=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]];m_.extend({type:"markArea",updateTransform:function(t,e,r){e.eachSeries(function(i){var t=i.markAreaModel;if(t){var n=t.getData();n.each(function(e){var t=P(G_,function(t){return W_(n,e,t,i,r)});n.setItemLayout(e,t),n.getItemGraphicEl(e).setShape("points",t)})}},this)},renderSeries:function(e,a,t,i){var o=e.coordinateSystem,n=e.id,s=e.getData(),r=this.markerGroupMap,l=r.get(n)||r.set(n,{group:new Si});this.group.add(l.group),l.__keep=!0;var u=function(t,i,e){var n,r;r=t?(n=P(t&&t.dimensions,function(t){var e=i.getData();return A({name:t},e.getDimensionInfo(e.mapDimension(t))||{})}),new kf(P(["x0","y0","x1","y1"],function(t,e){return{name:t,type:n[e%2].type}}),e)):new kf(n=[{name:"value",type:"float"}],e);var o=P(e.get("data"),T(B_,i,t,e));t&&(o=I(o,T(H_,t)));var a=t?function(t,e,i,n){return t.coord[Math.floor(n/2)][n%2]}:function(t){return t.value};return r.initData(o,null,a),r.hasItemOption=!0,r}(o,e,a);a.setData(u),u.each(function(n){var t=P(G_,function(t){return W_(u,n,t,e,i)}),r=!0;D(G_,function(t){if(r){var e=u.get(t[0],n),i=u.get(t[1],n);(V_(e)||o.getAxis("x").containData(e))&&(V_(i)||o.getAxis("y").containData(i))&&(r=!1)}}),u.setItemLayout(n,{points:t,allClipped:r}),u.setItemVisual(n,{color:s.getVisual("color")})}),u.diff(l.__data).add(function(t){var e=u.getItemLayout(t);if(!e.allClipped){var i=new Xa({shape:{points:e.points}});u.setItemGraphicEl(t,i),l.group.add(i)}}).update(function(t,e){var i=l.__data.getItemGraphicEl(e),n=u.getItemLayout(t);n.allClipped?i&&l.group.remove(i):(i?il(i,{shape:{points:n.points}},a,t):i=new Xa({shape:{points:n.points}}),u.setItemGraphicEl(t,i),l.group.add(i))}).remove(function(t){var e=l.__data.getItemGraphicEl(t);l.group.remove(e)}).execute(),u.eachItemGraphicEl(function(t,e){var i=u.getItemModel(e),n=i.getModel("label"),r=i.getModel("emphasis.label"),o=u.getItemVisual(e,"color");t.useStyle(A(i.getModel("itemStyle").getItemStyle(),{fill:Ze(o,.4),stroke:o})),t.hoverStyle=i.getModel("emphasis.itemStyle").getItemStyle(),Ys(t.style,t.hoverStyle,n,r,{labelFetcher:a,labelDataIndex:e,defaultText:u.getName(e)||"",isRectText:!0,autoColor:o}),Gs(t,{}),t.dataModel=a}),l.__data=u,l.group.silent=a.get("silent")||e.get("silent")}}),nf(function(t){t.markArea=t.markArea||{}}),vu.registerSubTypeDefaulter("dataZoom",function(){return"slider"});var Z_=["cartesian2d","polar","singleAxis"];var U_,X_,Y_,j_,q_=(X_=["axisIndex","axis","index","id"],Y_=P(U_=(U_=["x","y","z","radius","angle","single"]).slice(),Jl),j_=P(X_=(X_||[]).slice(),Jl),function(r,o){D(U_,function(t,e){for(var i={name:t,capital:Y_[e]},n=0;n<X_.length;n++)i[X_[n]]=t+j_[n];r.call(o,i)})});function $_(r,o,a){return function(t){var e,i={nodes:[],records:{}};if(o(function(t){i.records[t.name]={}}),!t)return i;for(s(t,i);e=!1,r(n),e;);function n(t){!function(t,e){return 0<=x(e.nodes,t)}(t,i)&&function(t,i){var n=!1;return o(function(e){D(a(t,e)||[],function(t){i.records[e.name][t]&&(n=!0)})}),n}(t,i)&&(s(t,i),e=!0)}return i};function s(t,i){i.nodes.push(t),o(function(e){D(a(t,e)||[],function(t){i.records[e.name][t]=!0})})}}function K_(t,e,i,n,r,o){t=t||0;var a=i[1]-i[0];if(null!=r&&(r=J_(r,[0,a])),null!=o&&(o=Math.max(o,null!=r?r:0)),"all"===n){var s=Math.abs(e[1]-e[0]);r=o=J_(s=J_(s,[0,a]),[r,o]),n=0}e[0]=J_(e[0],i),e[1]=J_(e[1],i);var l=Q_(e,n);e[n]+=t;var u=r||0,h=i.slice();l.sign<0?h[0]+=u:h[1]-=u,e[n]=J_(e[n],h);var c=Q_(e,n);return null!=r&&(c.sign!==l.sign||c.span<r)&&(e[1-n]=e[n]+l.sign*r),c=Q_(e,n),null!=o&&c.span>o&&(e[1-n]=e[n]+c.sign*o),e}function Q_(t,e){var i=t[e]-t[1-e];return{span:Math.abs(i),sign:0<i?-1:i<0?1:e?-1:1}}function J_(t,e){return Math.min(null!=e[1]?e[1]:1/0,Math.max(null!=e[0]?e[0]:-1/0,t))}function tx(t,e,i,n){this._dimName=t,this._axisIndex=e,this._valueWindow,this._percentWindow,this._dataExtent,this._minMaxSpan,this.ecModel=n,this._dataZoomModel=i}var ex=D,ix=Al;function nx(t,e){var i=t.getAxisModel(),n=t._percentWindow,r=t._valueWindow;if(n){var o=Pl(r,[0,500]);o=Math.min(o,20);var a=e||0===n[0]&&100===n[1];i.setRange(a?null:+r[0].toFixed(o),a?null:+r[1].toFixed(o))}}tx.prototype={constructor:tx,hostedBy:function(t){return this._dataZoomModel===t},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var n=[],r=this.ecModel;return r.eachSeries(function(t){if(function(t){return 0<=x(Z_,t)}(t.get("coordinateSystem"))){var e=this._dimName,i=r.queryComponents({mainType:e+"Axis",index:t.get(e+"AxisIndex"),id:t.get(e+"AxisId")})[0];this._axisIndex===(i&&i.componentIndex)&&n.push(t)}},this),n},getAxisModel:function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var t,e,i,n=this._dimName,r=this.ecModel,o=this.getAxisModel();return t="x"===n||"y"===n?(e="gridIndex","x"===n?"y":"x"):(e="polarIndex","angle"===n?"radius":"angle"),r.eachComponent(t+"Axis",function(t){(t.get(e)||0)===(o.get(e)||0)&&(i=t)}),i},getMinMaxSpan:function(){return b(this._minMaxSpan)},calculateDataWindow:function(r){var o,a=this._dataExtent,s=this.getAxisModel().axis.scale,l=this._dataZoomModel.getRangePropMode(),u=[0,100],h=[],c=[];ex(["start","end"],function(t,e){var i=r[t],n=r[t+"Value"];"percent"===l[e]?(null==i&&(i=u[e]),n=s.parse(Il(i,u,a))):(o=!0,i=Il(n=null==n?a[e]:s.parse(n),a,u)),c[e]=n,h[e]=i}),ix(c),ix(h);var d=this._minMaxSpan;function t(t,e,i,n,r){var o=r?"Span":"ValueSpan";K_(0,t,i,"all",d["min"+o],d["max"+o]);for(var a=0;a<2;a++)e[a]=Il(t[a],i,n,!0),r&&(e[a]=s.parse(e[a]))}return o?t(c,h,a,u,!1):t(h,c,u,a,!0),{valueWindow:c,percentWindow:h}},reset:function(t){if(t===this._dataZoomModel){var e=this.getTargetSeriesModels();this._dataExtent=function(t,e,i){var n=[1/0,-1/0];ex(i,function(t){var i=t.getData();i&&ex(i.mapDimension(e,!0),function(t){var e=i.getApproximateExtent(t);e[0]<n[0]&&(n[0]=e[0]),e[1]>n[1]&&(n[1]=e[1])})}),n[1]<n[0]&&(n=[NaN,NaN]);return function(t,e){var i=t.getAxisModel(),n=i.getMin(!0),r="category"===i.get("type"),o=r&&i.getCategories().length;null!=n&&"dataMin"!==n&&"function"!=typeof n?e[0]=n:r&&(e[0]=0<o?0:NaN);var a=i.getMax(!0);null!=a&&"dataMax"!==a&&"function"!=typeof a?e[1]=a:r&&(e[1]=0<o?o-1:NaN);i.get("scale",!0)||(0<e[0]&&(e[0]=0),e[1]<0&&(e[1]=0))}(t,n),n}(this,this._dimName,e),function(n){var r=n._minMaxSpan={},o=n._dataZoomModel,a=n._dataExtent;ex(["min","max"],function(t){var e=o.get(t+"Span"),i=o.get(t+"ValueSpan");null!=i&&(i=n.getAxisModel().axis.scale.parse(i)),null!=i?e=Il(a[0]+i,a,[0,100],!0):null!=e&&(i=Il(e,[0,100],a,!0)-a[0]),r[t+"Span"]=e,r[t+"ValueSpan"]=i})}(this);var i=this.calculateDataWindow(t.settledOption);this._valueWindow=i.valueWindow,this._percentWindow=i.percentWindow,nx(this)}},restore:function(t){t===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,nx(this,!0))},filterData:function(t,e){if(t===this._dataZoomModel){var n=this._dimName,i=this.getTargetSeriesModels(),r=t.get("filterMode"),c=this._valueWindow;"none"!==r&&ex(i,function(i){var u=i.getData(),h=u.mapDimension(n,!0);h.length&&("weakFilter"===r?u.filterSelf(function(t){for(var e,i,n,r=0;r<h.length;r++){var o=u.get(h[r],t),a=!isNaN(o),s=o<c[0],l=o>c[1];if(a&&!s&&!l)return!0;a&&(n=!0),s&&(e=!0),l&&(i=!0)}return n&&e&&i}):ex(h,function(t){if("empty"===r)i.setData(u=u.map(t,function(t){return function(t){return t>=c[0]&&t<=c[1]}(t)?t:NaN}));else{var e={};e[t]=c,u.selectRange(e)}}),ex(h,function(t){u.setApproximateExtent(c,t)}))})}}};var rx=D,ox=q_,ax=hf({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis","singleAxis","series"],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null,minSpan:null,maxSpan:null,minValueSpan:null,maxValueSpan:null,rangeMode:null},init:function(t,e,i){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0,this._rangePropMode=["percent","percent"];var n=sx(t);this.settledOption=n,this.mergeDefaultAndTheme(t,i),this.doInit(n)},mergeOption:function(t){var e=sx(t);m(this.option,t,!0),m(this.settledOption,e,!0),this.doInit(e)},doInit:function(t){var i=this.option;v.canvasSupported||(i.realtime=!1),this._setDefaultThrottle(t),lx(this,t);var n=this.settledOption;rx([["start","startValue"],["end","endValue"]],function(t,e){"value"===this._rangePropMode[e]&&(i[t[0]]=n[t[0]]=null)},this),this.textStyleModel=this.getModel("textStyle"),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var a=this._axisProxies;this.eachTargetAxis(function(t,e,i,n){var r=this.dependentModels[t.axis][e],o=r.__dzAxisProxy||(r.__dzAxisProxy=new tx(t.name,e,this,n));a[t.name+"_"+e]=o},this)},_resetTarget:function(){var i=this.option,t=this._judgeAutoMode();ox(function(t){var e=t.axisIndex;i[e]=Lr(i[e])},this),"axisIndex"===t?this._autoSetAxisIndex():"orient"===t&&this._autoSetOrient()},_judgeAutoMode:function(){var e=this.option,i=!1;ox(function(t){null!=e[t.axisIndex]&&(i=!0)},this);var t=e.orient;return null==t&&i?"orient":i?void 0:(null==t&&(e.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var o=!0,e=this.get("orient",!0),a=this.option,t=this.dependentModels;if(o){var i="vertical"===e?"y":"x";t[i+"Axis"].length?(a[i+"AxisIndex"]=[0],o=!1):rx(t.singleAxis,function(t){o&&t.get("orient",!0)===e&&(a.singleAxisIndex=[t.componentIndex],o=!1)})}o&&ox(function(t){if(o){var e=[],i=this.dependentModels[t.axis];if(i.length&&!e.length)for(var n=0,r=i.length;n<r;n++)"category"===i[n].get("type")&&e.push(n);(a[t.axisIndex]=e).length&&(o=!1)}},this),o&&this.ecModel.eachSeries(function(r){this._isSeriesHasAllAxesTypeOf(r,"value")&&ox(function(t){var e=a[t.axisIndex],i=r.get(t.axisIndex),n=r.get(t.axisId);x(e,i=r.ecModel.queryComponents({mainType:t.axis,index:i,id:n})[0].componentIndex)<0&&e.push(i)})},this)},_autoSetOrient:function(){var e;this.eachTargetAxis(function(t){e=e||t.name},this),this.option.orient="y"===e?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(n,r){var o=!0;return ox(function(t){var e=n.get(t.axisIndex),i=this.dependentModels[t.axis][e];i&&i.get("type")===r||(o=!1)},this),o},_setDefaultThrottle:function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this.ecModel.option;this.option.throttle=e.animation&&0<e.animationDurationUpdate?100:20}},getFirstTargetAxisModel:function(){var i;return ox(function(t){if(null==i){var e=this.get(t.axisIndex);e.length&&(i=this.dependentModels[t.axis][e[0]])}},this),i},eachTargetAxis:function(i,n){var r=this.ecModel;ox(function(e){rx(this.get(e.axisIndex),function(t){i.call(n,e,t,this,r)},this)},this)},getAxisProxy:function(t,e){return this._axisProxies[t+"_"+e]},getAxisModel:function(t,e){var i=this.getAxisProxy(t,e);return i&&i.getAxisModel()},setRawRange:function(e){var i=this.option,n=this.settledOption;rx([["start","startValue"],["end","endValue"]],function(t){null==e[t[0]]&&null==e[t[1]]||(i[t[0]]=n[t[0]]=e[t[0]],i[t[1]]=n[t[1]]=e[t[1]])},this),lx(this,e)},setCalculatedRange:function(e){var i=this.option;rx(["start","startValue","end","endValue"],function(t){i[t]=e[t]})},getPercentRange:function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},getValueRange:function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var i=this.findRepresentativeAxisProxy();return i?i.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(t){if(t)return t.__dzAxisProxy;var e=this._axisProxies;for(var i in e)if(e.hasOwnProperty(i)&&e[i].hostedBy(this))return e[i];for(var i in e)if(e.hasOwnProperty(i)&&!e[i].hostedBy(this))return e[i]},getRangePropMode:function(){return this._rangePropMode.slice()}});function sx(e){var i={};return rx(["start","end","startValue","endValue","throttle"],function(t){e.hasOwnProperty(t)&&(i[t]=e[t])}),i}function lx(t,r){var o=t._rangePropMode,a=t.get("rangeMode");rx([["start","startValue"],["end","endValue"]],function(t,e){var i=null!=r[t[0]],n=null!=r[t[1]];i&&!n?o[e]="percent":!i&&n?o[e]="value":a?o[e]=a[e]:i&&(o[e]="percent")})}var ux=ec.extend({type:"dataZoom",render:function(t,e,i,n){this.dataZoomModel=t,this.ecModel=e,this.api=i},getTargetCoordInfo:function(){var t=this.dataZoomModel,r=this.ecModel,o={};return t.eachTargetAxis(function(t,e){var i=r.getComponent(t.axis,e);if(i){var n=i.getCoordSysModel();n&&function(t,e,i,n){for(var r,o=0;o<i.length;o++)if(i[o].model===t){r=i[o];break}r||i.push(r={model:t,axisModels:[],coordIndex:n});r.axisModels.push(e)}(n,i,o[n.mainType]||(o[n.mainType]=[]),n.componentIndex)}},this),o}}),hx=(ax.extend({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}}),Ja),cx=Il,dx=Al,fx=C,px=D,gx="horizontal",mx="vertical",vx=["line","bar","candlestick","scatter"],yx=ux.extend({type:"dataZoom.slider",init:function(t,e){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=e},render:function(t,e,i,n){yx.superApply(this,"render",arguments),vc(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=t.get("orient"),!1!==this.dataZoomModel.get("show")?(n&&"dataZoom"===n.type&&n.from===this.uid||this._buildView(),this._updateView()):this.group.removeAll()},remove:function(){yx.superApply(this,"remove",arguments),yc(this,"_dispatchZoomAction")},dispose:function(){yx.superApply(this,"dispose",arguments),yc(this,"_dispatchZoomAction")},_buildView:function(){var t=this.group;t.removeAll(),this._resetLocation(),this._resetInterval();var e=this._displayables.barGroup=new Si;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},_resetLocation:function(){var t=this.dataZoomModel,e=this.api,i=this._findCoordRect(),n={width:e.getWidth(),height:e.getHeight()},r=this._orient===gx?{right:n.width-i.x-i.width,top:n.height-30-7,width:i.width,height:30}:{right:7,top:i.y,width:30,height:i.height},o=cu(t.option);D(["right","top","width","height"],function(t){"ph"===o[t]&&(o[t]=r[t])});var a=lu(o,n,t.padding);this._location={x:a.x,y:a.y},this._size=[a.width,a.height],this._orient===mx&&this._size.reverse()},_positionGroup:function(){var t=this.group,e=this._location,i=this._orient,n=this.dataZoomModel.getFirstTargetAxisModel(),r=n&&n.get("inverse"),o=this._displayables.barGroup,a=(this._dataShadowInfo||{}).otherAxisInverse;o.attr(i!==gx||r?i===gx&&r?{scale:a?[-1,1]:[-1,-1]}:i!==mx||r?{scale:a?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:a?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:a?[1,1]:[1,-1]});var s=t.getBoundingRect([o]);t.attr("position",[e.x-s.x,e.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var t=this.dataZoomModel,e=this._size,i=this._displayables.barGroup;i.add(new hx({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40})),i.add(new hx({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:C(this._onClickPanelClick,this)}))},_renderDataShadow:function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(t){var e=this._size,i=t.series,n=i.getRawData(),r=i.getShadowDim?i.getShadowDim():t.otherDim;if(null!=r){var o=n.getDataExtent(r),a=.3*(o[1]-o[0]);o=[o[0]-a,o[1]+a];var s,l=[0,e[1]],u=[0,e[0]],h=[[e[0],0],[0,0]],c=[],d=u[1]/(n.count()-1),f=0,p=Math.round(n.count()/e[0]);n.each([r],function(t,e){if(0<p&&e%p)f+=d;else{var i=null==t||isNaN(t)||""===t,n=i?0:cx(t,o,l,!0);i&&!s&&e?(h.push([h[h.length-1][0],0]),c.push([c[c.length-1][0],0])):!i&&s&&(h.push([f,0]),c.push([f,0])),h.push([f,n]),c.push([f,n]),f+=d,s=i}});var g=this.dataZoomModel;this._displayables.barGroup.add(new Xa({shape:{points:h},style:A({fill:g.get("dataBackgroundColor")},g.getModel("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new Ya({shape:{points:c},style:g.getModel("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))}}},_prepareDataShadowInfo:function(){var t=this.dataZoomModel,s=t.get("showDataShadow");if(!1!==s){var l,u=this.ecModel;return t.eachTargetAxis(function(o,a){D(t.getAxisProxy(o.name,a).getTargetSeriesModels(),function(t){if(!(l||!0!==s&&x(vx,t.get("type"))<0)){var e,i=u.getComponent(o.axis,a).axis,n=function(t){return{x:"y",y:"x",radius:"angle",angle:"radius"}[t]}(o.name),r=t.coordinateSystem;null!=n&&r.getOtherAxis&&(e=r.getOtherAxis(i).inverse),n=t.getData().mapDimension(n),l={thisAxis:i,series:t,thisDim:o.name,otherDim:n,otherAxisInverse:e}}},this)},this),l}},_renderHandle:function(){var t=this._displayables,o=t.handles=[],a=t.handleLabels=[],s=this._displayables.barGroup,e=this._size,l=this.dataZoomModel;s.add(t.filler=new hx({draggable:!0,cursor:_x(this._orient),drift:fx(this._onDragMove,this,"all"),ondragstart:fx(this._showDataInfo,this,!0),ondragend:fx(this._onDragEnd,this),onmouseover:fx(this._showDataInfo,this,!0),onmouseout:fx(this._showDataInfo,this,!1),style:{fill:l.get("fillerColor"),textPosition:"inside"}})),s.add(new hx({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{stroke:l.get("dataBackgroundColor")||l.get("borderColor"),lineWidth:1,fill:"rgba(0,0,0,0)"}})),px([0,1],function(t){var e=ul(l.get("handleIcon"),{cursor:_x(this._orient),draggable:!0,drift:fx(this._onDragMove,this,t),ondragend:fx(this._onDragEnd,this),onmouseover:fx(this._showDataInfo,this,!0),onmouseout:fx(this._showDataInfo,this,!1)},{x:-1,y:0,width:2,height:2}),i=e.getBoundingRect();this._handleHeight=Cl(l.get("handleSize"),this._size[1]),this._handleWidth=i.width/i.height*this._handleHeight,e.setStyle(l.getModel("handleStyle").getItemStyle());var n=l.get("handleColor");null!=n&&(e.style.fill=n),s.add(o[t]=e);var r=l.textStyleModel;this.group.add(a[t]=new Ba({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",textFill:r.getTextColor(),textFont:r.getFont()},z2:10}))},this)},_resetInterval:function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[cx(t[0],[0,100],e,!0),cx(t[1],[0,100],e,!0)]},_updateInterval:function(t,e){var i=this.dataZoomModel,n=this._handleEnds,r=this._getViewExtent(),o=i.findRepresentativeAxisProxy().getMinMaxSpan(),a=[0,100];K_(e,n,r,i.get("zoomLock")?"all":t,null!=o.minSpan?cx(o.minSpan,a,r,!0):null,null!=o.maxSpan?cx(o.maxSpan,a,r,!0):null);var s=this._range,l=this._range=dx([cx(n[0],r,a,!0),cx(n[1],r,a,!0)]);return!s||s[0]!==l[0]||s[1]!==l[1]},_updateView:function(t){var n=this._displayables,r=this._handleEnds,e=dx(r.slice()),o=this._size;px([0,1],function(t){var e=n.handles[t],i=this._handleHeight;e.attr({scale:[i/2,i/2],position:[r[t],o[1]/2-i/2]})},this),n.filler.setShape({x:e[0],y:0,width:e[1]-e[0],height:o[1]}),this._updateDataInfo(t)},_updateDataInfo:function(t){var e=this.dataZoomModel,o=this._displayables,a=o.handleLabels,s=this._orient,l=["",""];if(e.get("showDetail")){var i=e.findRepresentativeAxisProxy();if(i){var n=i.getAxisModel().axis,r=this._range,u=t?i.calculateDataWindow({start:r[0],end:r[1]}).valueWindow:i.getDataValueWindow();l=[this._formatLabel(u[0],n),this._formatLabel(u[1],n)]}}var h=dx(this._handleEnds.slice());function c(t){var e=rl(o.handles[t].parent,this.group),i=al(0===t?"right":"left",e),n=this._handleWidth/2+5,r=ol([h[t]+(0===t?-n:n),this._size[1]/2],e);a[t].setStyle({x:r[0],y:r[1],textVerticalAlign:s===gx?"middle":i,textAlign:s===gx?i:"center",text:l[t]})}c.call(this,0),c.call(this,1)},_formatLabel:function(t,e){var i=this.dataZoomModel,n=i.get("labelFormatter"),r=i.get("labelPrecision");null!=r&&"auto"!==r||(r=e.getPixelPrecision());var o=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel(Math.round(t)):t.toFixed(Math.min(r,20));return z(n)?n(t,o):E(n)?n.replace("{value}",o):o},_showDataInfo:function(t){t=this._dragging||t;var e=this._displayables.handleLabels;e[0].attr("invisible",!t),e[1].attr("invisible",!t)},_onDragMove:function(t,e,i,n){this._dragging=!0,Wt(n.event);var r=ol([e,i],this._displayables.barGroup.getLocalTransform(),!0),o=this._updateInterval(t,r[0]),a=this.dataZoomModel.get("realtime");this._updateView(!a),o&&a&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1),this.dataZoomModel.get("realtime")||this._dispatchZoomAction()},_onClickPanelClick:function(t){var e=this._size,i=this._displayables.barGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(i[0]<0||i[0]>e[0]||i[1]<0||i[1]>e[1])){var n=this._handleEnds,r=(n[0]+n[1])/2,o=this._updateInterval("all",i[0]-r);this._updateView(),o&&this._dispatchZoomAction()}},_dispatchZoomAction:function(){var t=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:t[0],end:t[1]})},_findCoordRect:function(){var i;if(px(this.getTargetCoordInfo(),function(t){if(!i&&t.length){var e=t[0].model.coordinateSystem;i=e.getRect&&e.getRect()}}),!i){var t=this.api.getWidth(),e=this.api.getHeight();i={x:.2*t,y:.2*e,width:.6*t,height:.6*e}}return i}});function _x(t){return"vertical"===t?"ns-resize":"ew-resize"}rf({getTargetSeries:function(t){var n=Q();return t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,e,i){D(i.getAxisProxy(t.name,e).getTargetSeriesModels(),function(t){n.set(t.uid,t)})})}),n},modifyOutputEnd:!0,overallReset:function(t,n){t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,e,i){i.getAxisProxy(t.name,e).reset(i,n)}),t.eachTargetAxis(function(t,e,i){i.getAxisProxy(t.name,e).filterData(i,n)})}),t.eachComponent("dataZoom",function(t){var e=t.findRepresentativeAxisProxy(),i=e.getDataPercentWindow(),n=e.getDataValueWindow();t.setCalculatedRange({start:i[0],end:i[1],startValue:n[0],endValue:n[1]})})}}),of("dataZoom",function(i,t){var n=$_(C(t.eachComponent,t,"dataZoom"),q_,function(t,e){return t.get(e.axisIndex)}),r=[];t.eachComponent({mainType:"dataZoom",query:i},function(t,e){r.push.apply(r,n(t).nodes)}),D(r,function(t,e){t.setRawRange({start:i.start,end:i.end,startValue:i.startValue,endValue:i.endValue})})}),ax.extend({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}});var xx="\0_ec_interaction_mutex";function bx(t,e){return!!Sx(t)[e]}function Sx(t){return t[xx]||(t[xx]={})}function Mx(i){this.pointerChecker,this._zr=i,this._opt={};var t=C,n=t(Ix,this),r=t(Cx,this),o=t(Tx,this),a=t(Ax,this),s=t(Dx,this);It.call(this),this.setPointerChecker=function(t){this.pointerChecker=t},this.enable=function(t,e){this.disable(),this._opt=A(b(e)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),null==t&&(t=!0),!0!==t&&"move"!==t&&"pan"!==t||(i.on("mousedown",n),i.on("mousemove",r),i.on("mouseup",o)),!0!==t&&"scale"!==t&&"zoom"!==t||(i.on("mousewheel",a),i.on("pinch",s))},this.disable=function(){i.off("mousedown",n),i.off("mousemove",r),i.off("mouseup",o),i.off("mousewheel",a),i.off("pinch",s)},this.dispose=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}function Ix(t){if(!(Gt(t)||t.target&&t.target.draggable)){var e=t.offsetX,i=t.offsetY;this.pointerChecker&&this.pointerChecker(t,e,i)&&(this._x=e,this._y=i,this._dragging=!0)}}function Cx(t){if(this._dragging&&Lx("moveOnMouseMove",t,this._opt)&&"pinch"!==t.gestureEvent&&!bx(this._zr,"globalPan")){var e=t.offsetX,i=t.offsetY,n=this._x,r=this._y,o=e-n,a=i-r;this._x=e,this._y=i,this._opt.preventDefaultMouseMove&&Wt(t.event),Px(this,"pan","moveOnMouseMove",t,{dx:o,dy:a,oldX:n,oldY:r,newX:e,newY:i})}}function Tx(t){Gt(t)||(this._dragging=!1)}function Ax(t){var e=Lx("zoomOnMouseWheel",t,this._opt),i=Lx("moveOnMouseWheel",t,this._opt),n=t.wheelDelta,r=Math.abs(n),o=t.offsetX,a=t.offsetY;if(0!==n&&(e||i)){if(e){var s=3<r?1.4:1<r?1.2:1.1;kx(this,"zoom","zoomOnMouseWheel",t,{scale:0<n?s:1/s,originX:o,originY:a})}if(i){var l=Math.abs(n);kx(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:(0<n?1:-1)*(3<l?.4:1<l?.15:.05),originX:o,originY:a})}}}function Dx(t){bx(this._zr,"globalPan")||kx(this,"zoom",null,t,{scale:1<t.pinchScale?1.1:1/1.1,originX:t.pinchX,originY:t.pinchY})}function kx(t,e,i,n,r){t.pointerChecker&&t.pointerChecker(n,r.originX,r.originY)&&(Wt(n.event),Px(t,e,i,n,r))}function Px(t,e,i,n,r){r.isAvailableBehavior=C(Lx,null,i,n),t.trigger(e,r)}function Lx(t,e,i){var n=i[t];return!t||n&&(!E(n)||e.event[n+"Key"])}of({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},function(){}),S(Mx,It);var Ox="\0_ec_dataZoom_roams";function zx(t,n){var e=Nx(t),r=n.dataZoomId,o=n.coordId;D(e,function(t,e){var i=t.dataZoomInfos;i[r]&&x(n.allCoordIds,o)<0&&(delete i[r],t.count--)}),Rx(e);var i=e[o];i||((i=e[o]={coordId:o,dataZoomInfos:{},count:0}).controller=function(t,a){var e=new Mx(t.getZr());return D(["pan","zoom","scrollMove"],function(o){e.on(o,function(n){var r=[];D(a.dataZoomInfos,function(t){if(n.isAvailableBehavior(t.dataZoomModel.option)){var e=(t.getRange||{})[o],i=e&&e(a.controller,n);!t.dataZoomModel.get("disabled",!0)&&i&&r.push({dataZoomId:t.dataZoomId,start:i[0],end:i[1]})}}),r.length&&a.dispatchAction(r)})}),e}(t,i),i.dispatchAction=T(Bx,t)),i.dataZoomInfos[r]||i.count++,i.dataZoomInfos[r]=n;var a=function(t){var n,r={type_true:2,type_move:1,type_false:0,type_undefined:-1},o=!0;return D(t,function(t){var e=t.dataZoomModel,i=!e.get("disabled",!0)&&(!e.get("zoomLock",!0)||"move");r["type_"+n]<r["type_"+i]&&(n=i),o&=e.get("preventDefaultMouseMove",!0)}),{controlType:n,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!o}}}(i.dataZoomInfos);i.controller.enable(a.controlType,a.opt),i.controller.setPointerChecker(n.containsPoint),vc(i,"dispatchAction",n.dataZoomModel.get("throttle",!0),"fixRate")}function Ex(t){return t.type+"\0_"+t.id}function Nx(t){var e=t.getZr();return e[Ox]||(e[Ox]={})}function Rx(i){D(i,function(t,e){t.count||(t.controller.dispose(),delete i[e])})}function Bx(t,e){t.dispatchAction({type:"dataZoom",batch:e})}var Vx=C,Fx=ux.extend({type:"dataZoom.inside",init:function(t,e){this._range},render:function(a,t,s,e){Fx.superApply(this,"render",arguments),this._range=a.getPercentRange(),D(this.getTargetCoordInfo(),function(t,r){var o=P(t,function(t){return Ex(t.model)});D(t,function(e){var n=e.model,i={};D(["pan","zoom","scrollMove"],function(t){i[t]=Vx(Hx[t],this,e,r)},this),zx(s,{coordId:Ex(n),allCoordIds:o,containsPoint:function(t,e,i){return n.coordinateSystem.containPoint([e,i])},dataZoomId:a.id,dataZoomModel:a,getRange:i})},this)},this)},dispose:function(){!function(t,i){var e=Nx(t);D(e,function(t){t.controller.dispose();var e=t.dataZoomInfos;e[i]&&(delete e[i],t.count--)}),Rx(e)}(this.api,this.dataZoomModel.id),Fx.superApply(this,"dispose",arguments),this._range=null}}),Hx={zoom:function(t,e,i,n){var r=this._range,o=r.slice(),a=t.axisModels[0];if(a){var s=Gx[e](null,[n.originX,n.originY],a,i,t),l=(0<s.signal?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(o[1]-o[0])+o[0],u=Math.max(1/n.scale,0);o[0]=(o[0]-l)*u+l,o[1]=(o[1]-l)*u+l;var h=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return K_(0,o,[0,100],0,h.minSpan,h.maxSpan),this._range=o,r[0]!==o[0]||r[1]!==o[1]?o:void 0}},pan:Wx(function(t,e,i,n,r,o){var a=Gx[n]([o.oldX,o.oldY],[o.newX,o.newY],e,r,i);return a.signal*(t[1]-t[0])*a.pixel/a.pixelLength}),scrollMove:Wx(function(t,e,i,n,r,o){return Gx[n]([0,0],[o.scrollDelta,o.scrollDelta],e,r,i).signal*(t[1]-t[0])*o.scrollDelta})};function Wx(l){return function(t,e,i,n){var r=this._range,o=r.slice(),a=t.axisModels[0];if(a){var s=l(o,a,t,e,i,n);return K_(s,o,[0,100],"all"),this._range=o,r[0]!==o[0]||r[1]!==o[1]?o:void 0}}}var Gx={grid:function(t,e,i,n,r){var o=i.axis,a={},s=r.model.coordinateSystem.getRect();return t=t||[0,0],"x"===o.dim?(a.pixel=e[0]-t[0],a.pixelLength=s.width,a.pixelStart=s.x,a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=s.height,a.pixelStart=s.y,a.signal=o.inverse?-1:1),a},polar:function(t,e,i,n,r){var o=i.axis,a={},s=r.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===i.mainType?(a.pixel=e[0]-t[0],a.pixelLength=l[1]-l[0],a.pixelStart=l[0],a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=u[1]-u[0],a.pixelStart=u[0],a.signal=o.inverse?-1:1),a},singleAxis:function(t,e,i,n,r){var o=i.axis,a=r.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===o.orient?(s.pixel=e[0]-t[0],s.pixelLength=a.width,s.pixelStart=a.x,s.signal=o.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=a.height,s.pixelStart=a.y,s.signal=o.inverse?-1:1),s}},Zx={};function Ux(t,e){Zx[t]=e}function Xx(t){return Zx[t]}var Yx=hf({type:"toolbox",layoutMode:{type:"box",ignoreSize:!0},optionUpdated:function(){Yx.superApply(this,"optionUpdated",arguments),D(this.option.feature,function(t,e){var i=Xx(e);i&&m(t,i.defaultOption)})},defaultOption:{show:!0,z:6,zlevel:0,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1}}});cf({type:"toolbox",render:function(h,c,d,l){var f=this.group;if(f.removeAll(),h.get("show")){var p=+h.get("itemSize"),u=h.get("feature")||{},g=this._features||(this._features={}),m=[];D(u,function(t,e){m.push(e)}),new gf(this._featureNames||[],m).add(t).update(t).remove(T(t,null)).execute(),this._featureNames=m,function(t,e,i){var n=e.getBoxLayoutParams(),r=e.get("padding"),o={width:i.getWidth(),height:i.getHeight()},a=lu(n,o,r);su(e.get("orient"),t,e.get("itemGap"),a.width,a.height),uu(t,n,o,r)}(f,h,d),f.add(Wy(f.getBoundingRect(),h)),f.eachChild(function(t){var e=t.__title,i=t.hoverStyle;if(i&&e){var n=un(e,wn(i)),r=t.position[0]+f.position[0],o=!1;t.position[1]+f.position[1]+p+n.height>d.getHeight()&&(i.textPosition="top",o=!0);var a=o?-5-n.height:p+8;r+n.width/2>d.getWidth()?(i.textPosition=["100%",a],i.textAlign="right"):r-n.width/2<0&&(i.textPosition=[0,a],i.textAlign="left")}})}function t(t,e){var i,n=m[t],r=m[e],o=u[n],a=new _l(o,h,h.ecModel);if(l&&null!=l.newTitle&&l.featureName===n&&(o.title=l.newTitle),n&&!r){if(function(t){return 0===t.indexOf("my")}(n))i={model:a,onclick:a.option.onclick,featureName:n};else{var s=Xx(n);if(!s)return;i=new s(a,c,d)}g[n]=i}else{if(!(i=g[r]))return;i.model=a,i.ecModel=c,i.api=d}n||!r?a.get("show")&&!i.unusable?(function(r,o,t){var a=r.getModel("iconStyle"),s=r.getModel("emphasis.iconStyle"),e=o.getIcons?o.getIcons():r.get("icon"),l=r.get("title")||{};if("string"==typeof e){var i=e,n=l;l={},(e={})[t]=i,l[t]=n}var u=r.iconPaths={};D(e,function(t,e){var i=ul(t,{},{x:-p/2,y:-p/2,width:p,height:p});i.setStyle(a.getItemStyle()),i.hoverStyle=s.getItemStyle(),i.setStyle({text:l[e],textAlign:s.get("textAlign"),textBorderRadius:s.get("textBorderRadius"),textPadding:s.get("textPadding"),textFill:null});var n=h.getModel("tooltip");n&&n.get("show")&&i.attr("tooltip",k({content:l[e],formatter:n.get("formatter",!0)||function(){return l[e]},formatterParams:{componentType:"toolbox",name:e,title:l[e],$vars:["name","title"]},position:n.get("position",!0)||"bottom"},n.option)),Gs(i),h.get("showTitle")&&(i.__title=l[e],i.on("mouseover",function(){var t=s.getItemStyle(),e="vertical"===h.get("orient")?null==h.get("right")?"right":"left":null==h.get("bottom")?"bottom":"top";i.setStyle({textFill:s.get("textFill")||t.fill||t.stroke||"#000",textBackgroundColor:s.get("textBackgroundColor"),textPosition:s.get("textPosition")||e})}).on("mouseout",function(){i.setStyle({textFill:null,textBackgroundColor:null})})),i.trigger(r.get("iconStatus."+e)||"normal"),f.add(i),i.on("click",C(o.onclick,o,c,d,e)),u[e]=i})}(a,i,n),a.setIconStatus=function(t,e){var i=this.option,n=this.iconPaths;i.iconStatus=i.iconStatus||{},i.iconStatus[t]=e,n[t]&&n[t].trigger(e)},i.render&&i.render(a,c,d,l)):i.remove&&i.remove(c,d):i.dispose&&i.dispose(c,d)}},updateView:function(t,e,i,n){D(this._features,function(t){t.updateView&&t.updateView(t.model,e,i,n)})},remove:function(e,i){D(this._features,function(t){t.remove&&t.remove(e,i)}),this.group.removeAll()},dispose:function(e,i){D(this._features,function(t){t.dispose&&t.dispose(e,i)})}});var jx=xc.toolbox.saveAsImage;function qx(t){this.model=t}qx.defaultOption={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:jx.title,type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],pixelRatio:1,lang:jx.lang.slice()},qx.prototype.unusable=!v.canvasSupported,qx.prototype.onclick=function(t,e){var i=this.model,n=i.get("name")||t.get("title.0.text")||"echarts",r="svg"===e.getZr().painter.getType()?"svg":i.get("type",!0)||"png",o=e.getConnectedDataURL({type:r,backgroundColor:i.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:i.get("connectedBackgroundColor"),excludeComponents:i.get("excludeComponents"),pixelRatio:i.get("pixelRatio")});if("function"!=typeof MouseEvent||v.browser.ie||v.browser.edge)if(window.navigator.msSaveOrOpenBlob){for(var a=atob(o.split(",")[1]),s=a.length,l=new Uint8Array(s);s--;)l[s]=a.charCodeAt(s);var u=new Blob([l]);window.navigator.msSaveOrOpenBlob(u,n+"."+r)}else{var h=i.get("lang"),c='<body style="margin:0;"><img src="'+o+'" style="max-width:100%;" title="'+(h&&h[0]||"")+'" /></body>';window.open().document.write(c)}else{var d=document.createElement("a");d.download=n+"."+r,d.target="_blank",d.href=o;var f=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});d.dispatchEvent(f)}},Ux("saveAsImage",qx);var $x=xc.toolbox.magicType,Kx="__ec_magicType_stack__";function Qx(t){this.model=t}Qx.defaultOption={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:b($x.title),option:{},seriesIndex:{}};var Jx=Qx.prototype;Jx.getIcons=function(){var t=this.model,e=t.get("icon"),i={};return D(t.get("type"),function(t){e[t]&&(i[t]=e[t])}),i};var tw={line:function(t,e,i,n){if("bar"===t)return m({id:e,type:"line",data:i.get("data"),stack:i.get("stack"),markPoint:i.get("markPoint"),markLine:i.get("markLine")},n.get("option.line")||{},!0)},bar:function(t,e,i,n){if("line"===t)return m({id:e,type:"bar",data:i.get("data"),stack:i.get("stack"),markPoint:i.get("markPoint"),markLine:i.get("markLine")},n.get("option.bar")||{},!0)},stack:function(t,e,i,n){var r=i.get("stack")===Kx;if("line"===t||"bar"===t)return n.setIconStatus("stack",r?"normal":"emphasis"),m({id:e,stack:r?"":Kx},n.get("option.stack")||{},!0)}},ew=[["line","bar"],["stack"]];Jx.onclick=function(u,t,h){var c=this.model,e=c.get("seriesIndex."+h);if(tw[h]){var i,d={series:[]};if(D(ew,function(t){0<=x(t,h)&&D(t,function(t){c.setIconStatus(t,"normal")})}),c.setIconStatus(h,"emphasis"),u.eachComponent({mainType:"series",query:null==e?null:{seriesIndex:e}},function(t){var e=t.subType,i=t.id,n=tw[h](e,i,t,c);n&&(A(n,t.option),d.series.push(n));var r=t.coordinateSystem;if(r&&"cartesian2d"===r.type&&("line"===h||"bar"===h)){var o=r.getAxesByScale("ordinal")[0];if(o){var a=o.dim+"Axis",s=u.queryComponents({mainType:a,index:t.get(name+"Index"),id:t.get(name+"Id")})[0].componentIndex;d[a]=d[a]||[];for(var l=0;l<=s;l++)d[a][s]=d[a][s]||{};d[a][s].boundaryGap="bar"===h}}}),"stack"===h)i=d.series&&d.series[0]&&d.series[0].stack===Kx?m({stack:$x.title.tiled},$x.title):b($x.title);t.dispatchAction({type:"changeMagicType",currentType:h,newOption:d,newTitle:i,featureName:"magicType"})}},of({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(t,e){e.mergeOption(t.newOption)}),Ux("magicType",Qx);var iw=xc.toolbox.dataView,nw=new Array(60).join("-"),rw="\t";function ow(t){var e=function(t){var r={},o=[],a=[];return t.eachRawSeries(function(t){var e=t.coordinateSystem;if(!e||"cartesian2d"!==e.type&&"polar"!==e.type)o.push(t);else{var i=e.getBaseAxis();if("category"===i.type){var n=i.dim+"_"+i.index;r[n]||(r[n]={categoryAxis:i,valueAxis:e.getOtherAxis(i),series:[]},a.push({axisDim:i.dim,axisIndex:i.index})),r[n].series.push(t)}else o.push(t)}}),{seriesGroupByCategoryAxis:r,other:o,meta:a}}(t);return{value:I([function(t){var h=[];return D(t,function(t,e){var i=t.categoryAxis,n=t.valueAxis.dim,r=[" "].concat(P(t.series,function(t){return t.name})),o=[i.model.getCategories()];D(t.series,function(t){var e=t.getRawData();o.push(t.getRawData().mapArray(e.mapDimension(n),function(t){return t}))});for(var a=[r.join(rw)],s=0;s<o[0].length;s++){for(var l=[],u=0;u<o.length;u++)l.push(o[u][s]);a.push(l.join(rw))}h.push(a.join("\n"))}),h.join("\n\n"+nw+"\n\n")}(e.seriesGroupByCategoryAxis),function(t){return P(t,function(t){var r=t.getRawData(),o=[t.name],a=[];return r.each(r.dimensions,function(){for(var t=arguments.length,e=arguments[t-1],i=r.getName(e),n=0;n<t-1;n++)a[n]=arguments[n];o.push((i?i+rw:"")+a.join(rw))}),o.join("\n")}).join("\n\n"+nw+"\n\n")}(e.other)],function(t){return t.replace(/[\n\t\s]/g,"")}).join("\n\n"+nw+"\n\n"),meta:e.meta}}function aw(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}var sw=new RegExp("["+rw+"]+","g");function lw(t,o){var e=t.split(new RegExp("\n*"+nw+"\n*","g")),a={series:[]};return D(e,function(t,e){if(function(t){if(0<=t.slice(0,t.indexOf("\n")).indexOf(rw))return!0}(t)){var i=function(t){for(var e=t.split(/\n+/g),i=[],n=P(aw(e.shift()).split(sw),function(t){return{name:t,data:[]}}),r=0;r<e.length;r++){var o=aw(e[r]).split(sw);i.push(o.shift());for(var a=0;a<o.length;a++)n[a]&&(n[a].data[r]=o[a])}return{series:n,categories:i}}(t),n=o[e],r=n.axisDim+"Axis";n&&(a[r]=a[r]||[],a[r][n.axisIndex]={data:i.categories},a.series=a.series.concat(i.series))}else{i=function(t){for(var e=t.split(/\n+/g),i=aw(e.shift()),n=[],r=0;r<e.length;r++){var o=aw(e[r]);if(o){var a,s=o.split(sw),l="",u=!1;a=isNaN(s[0])?(u=!0,l=s[0],s=s.slice(1),n[r]={name:l,value:[]},n[r].value):n[r]=[];for(var h=0;h<s.length;h++)a.push(+s[h]);1===a.length&&(u?n[r].value=a[0]:n[r]=a[0])}}return{name:i,data:n}}(t);a.series.push(i)}}),a}function uw(t){this._dom=null,this.model=t}uw.defaultOption={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:b(iw.title),lang:b(iw.lang),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"},uw.prototype.onclick=function(t,e){var i=e.getDom(),n=this.model;this._dom&&i.removeChild(this._dom);var r=document.createElement("div");r.style.cssText="position:absolute;left:5px;top:5px;bottom:5px;right:5px;",r.style.backgroundColor=n.get("backgroundColor")||"#fff";var o=document.createElement("h4"),a=n.get("lang")||[];o.innerHTML=a[0]||n.get("title"),o.style.cssText="margin: 10px 20px;",o.style.color=n.get("textColor");var s=document.createElement("div"),l=document.createElement("textarea");s.style.cssText="display:block;width:100%;overflow:auto;";var u=n.get("optionToContent"),h=n.get("contentToOption"),c=ow(t);if("function"==typeof u){var d=u(e.getOption());"string"==typeof d?s.innerHTML=d:V(d)&&s.appendChild(d)}else s.appendChild(l),l.readOnly=n.get("readOnly"),l.style.cssText="width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;",l.style.color=n.get("textColor"),l.style.borderColor=n.get("textareaBorderColor"),l.style.backgroundColor=n.get("textareaColor"),l.value=c.value;var f=c.meta,p=document.createElement("div");p.style.cssText="position:absolute;bottom:0;left:0;right:0;";var g="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",m=document.createElement("div"),v=document.createElement("div");g+=";background-color:"+n.get("buttonColor"),g+=";color:"+n.get("buttonTextColor");var y=this;function _(){i.removeChild(r),y._dom=null}Ht(m,"click",_),Ht(v,"click",function(){var t;try{t="function"==typeof h?h(s,e.getOption()):lw(l.value,f)}catch(t){throw _(),new Error("Data view format error "+t)}t&&e.dispatchAction({type:"changeDataView",newOption:t}),_()}),m.innerHTML=a[1],v.innerHTML=a[2],v.style.cssText=g,m.style.cssText=g,n.get("readOnly")||p.appendChild(v),p.appendChild(m),r.appendChild(o),r.appendChild(s),r.appendChild(p),s.style.height=i.clientHeight-80+"px",i.appendChild(r),this._dom=r},uw.prototype.remove=function(t,e){this._dom&&e.getDom().removeChild(this._dom)},uw.prototype.dispose=function(t,e){this.remove(t,e)},Ux("dataView",uw),of({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(t,n){var r=[];D(t.newOption.series,function(t){var e=n.getSeriesByName(t.name)[0];if(e){var i=e.get("data");r.push({name:t.name,data:function(t,r){return P(t,function(t,e){var i=r&&r[e];if(!N(i)||O(i))return t;N(t)&&!O(t)||(t={value:t});var n=null!=i.name&&null==t.name;return t=A(t,i),n&&delete t.name,t})}(t.data,i)})}else r.push(k({type:"scatter"},t))}),n.mergeOption(A({series:r},t.newOption))});var hw=T,cw=D,dw=P,fw=Math.min,pw=Math.max,gw=Math.pow,mw=1e4,vw=6,yw=6,_w="globalPan",xw={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},ww={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},bw={brushStyle:{lineWidth:2,stroke:"rgba(0,0,0,0.3)",fill:"rgba(0,0,0,0.1)"},transformable:!0,brushMode:"single",removeOnClick:!1},Sw=0;function Mw(t){It.call(this),this._zr=t,this.group=new Si,this._brushType,this._brushOption,this._panels,this._track=[],this._dragging,this._covers=[],this._creatingCover,this._creatingPanel,this._enableGlobalPan,this._uid="brushController_"+Sw++,this._handlers={},cw($w,function(t,e){this._handlers[e]=C(t,this)},this)}function Iw(t,e){var i=Qw[e.brushType].createCover(t,e);return i.__brushOption=e,Aw(i,e),t.group.add(i),i}function Cw(t,e){var i=kw(e);return i.endCreating&&(i.endCreating(t,e),Aw(e,e.__brushOption)),e}function Tw(t,e){var i=e.__brushOption;kw(e).updateCoverShape(t,e,i.range,i)}function Aw(t,e){var i=e.z;null==i&&(i=mw),t.traverse(function(t){t.z=i,t.z2=i})}function Dw(t,e){kw(e).updateCommon(t,e),Tw(t,e)}function kw(t){return Qw[t.__brushOption.brushType]}function Pw(t,e,i){var n,r=t._panels;if(!r)return!0;var o=t._transform;return cw(r,function(t){t.isTargetByCursor(e,i,o)&&(n=t)}),n}function Lw(t,e){var i=t._panels;if(!i)return!0;var n=e.__brushOption.panelId;return null==n||i[n]}function Ow(e){var t=e._covers,i=t.length;return cw(t,function(t){e.group.remove(t)},e),t.length=0,!!i}function zw(t,e){var i=dw(t._covers,function(t){var e=t.__brushOption,i=b(e.range);return{brushType:e.brushType,panelId:e.panelId,range:i}});t.trigger("brush",i,{isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function Ew(t){var e=t.length-1;return e<0&&(e=0),[t[0],t[e]]}function Nw(e,i,t,n){var r=new Si;return r.add(new Ja({name:"main",style:Fw(t),silent:!0,draggable:!0,cursor:"move",drift:hw(e,i,r,"nswe"),ondragend:hw(zw,i,{isEnd:!0})})),cw(n,function(t){r.add(new Ja({name:t,style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:hw(e,i,r,t),ondragend:hw(zw,i,{isEnd:!0})}))}),r}function Rw(t,e,i,n){var r=n.brushStyle.lineWidth||0,o=pw(r,yw),a=i[0][0],s=i[1][0],l=a-r/2,u=s-r/2,h=i[0][1],c=i[1][1],d=h-o+r/2,f=c-o+r/2,p=h-a,g=c-s,m=p+r,v=g+r;Vw(t,e,"main",a,s,p,g),n.transformable&&(Vw(t,e,"w",l,u,o,v),Vw(t,e,"e",d,u,o,v),Vw(t,e,"n",l,u,m,o),Vw(t,e,"s",l,f,m,o),Vw(t,e,"nw",l,u,o,o),Vw(t,e,"ne",d,u,o,o),Vw(t,e,"sw",l,f,o,o),Vw(t,e,"se",d,f,o,o))}function Bw(n,r){var t=r.__brushOption,o=t.transformable,e=r.childAt(0);e.useStyle(Fw(t)),e.attr({silent:!o,cursor:o?"move":"default"}),cw(["w","e","n","s","se","sw","ne","nw"],function(t){var e=r.childOfName(t),i=function t(e,i){{if(1<i.length){i=i.split("");var n=[t(e,i[0]),t(e,i[1])];return"e"!==n[0]&&"w"!==n[0]||n.reverse(),n.join("")}var r={w:"left",e:"right",n:"top",s:"bottom"},o={left:"w",right:"e",top:"n",bottom:"s"},n=al(r[i],rl(e.group));return o[n]}}(n,t);e&&e.attr({silent:!o,invisible:!o,cursor:o?ww[i]+"-resize":null})})}function Vw(t,e,i,n,r,o,a){var s=e.childOfName(i);s&&s.setShape(function(t){var e=fw(t[0][0],t[1][0]),i=fw(t[0][1],t[1][1]),n=pw(t[0][0],t[1][0]),r=pw(t[0][1],t[1][1]);return{x:e,y:i,width:n-e,height:r-i}}(Uw(t,e,[[n,r],[n+o,r+a]])))}function Fw(t){return A({strokeNoScale:!0},t.brushStyle)}function Hw(t,e,i,n){var r=[fw(t,i),fw(e,n)],o=[pw(t,i),pw(e,n)];return[[r[0],o[0]],[r[1],o[1]]]}function Ww(t,e,i,n,r,o,a,s){var l=n.__brushOption,u=t(l.range),h=Zw(i,o,a);cw(r.split(""),function(t){var e=xw[t];u[e[0]][e[1]]+=h[e[0]]}),l.range=e(Hw(u[0][0],u[1][0],u[0][1],u[1][1])),Dw(i,n),zw(i,{isEnd:!1})}function Gw(t,e,i,n,r){var o=e.__brushOption.range,a=Zw(t,i,n);cw(o,function(t){t[0]+=a[0],t[1]+=a[1]}),Dw(t,e),zw(t,{isEnd:!1})}function Zw(t,e,i){var n=t.group,r=n.transformCoordToLocal(e,i),o=n.transformCoordToLocal(0,0);return[r[0]-o[0],r[1]-o[1]]}function Uw(t,e,i){var n=Lw(t,e);return n&&!0!==n?n.clipPath(i,t._transform):b(i)}function Xw(t){var e=t.event;e.preventDefault&&e.preventDefault()}function Yw(t,e,i){return t.childOfName("main").contain(e,i)}function jw(t,e,i,n){var r,o=t._creatingCover,a=t._creatingPanel,s=t._brushOption;if(t._track.push(i.slice()),function(t){var e=t._track;if(!e.length)return!1;var i=e[e.length-1],n=e[0],r=i[0]-n[0],o=i[1]-n[1],a=gw(r*r+o*o,.5);return vw<a}(t)||o){if(a&&!o){"single"===s.brushMode&&Ow(t);var l=b(s);l.brushType=qw(l.brushType,a),l.panelId=!0===a?null:a.panelId,o=t._creatingCover=Iw(t,l),t._covers.push(o)}if(o){var u=Qw[qw(t._brushType,a)];o.__brushOption.range=u.getCreatingRange(Uw(t,o,t._track)),n&&(Cw(t,o),u.updateCommon(t,o)),Tw(t,o),r={isEnd:n}}}else n&&"single"===s.brushMode&&s.removeOnClick&&Pw(t,e,i)&&Ow(t)&&(r={isEnd:n,removeOnClick:!0});return r}function qw(t,e){return"auto"===t?e.defaultBrushType:t}Mw.prototype={constructor:Mw,enableBrush:function(t){return this._brushType&&function(t){var e=t._zr;(function(t,e,i){var n=Sx(t);n[e]===i&&(n[e]=null)})(e,_w,t._uid),function(i,t){cw(t,function(t,e){i.off(e,t)})}(e,t._handlers),t._brushType=t._brushOption=null}(this),t.brushType&&function(t,e){var i=t._zr;t._enableGlobalPan||function(t,e,i){Sx(t)[e]=i}(i,_w,t._uid);(function(i,t){cw(t,function(t,e){i.on(e,t)})})(i,t._handlers),t._brushType=e.brushType,t._brushOption=m(b(bw),e,!0)}(this,t),this},setPanels:function(t){if(t&&t.length){var e=this._panels={};D(t,function(t){e[t.panelId]=b(t)})}else this._panels=null;return this},mount:function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var e=this.group;return this._zr.add(e),e.attr({position:t.position||[0,0],rotation:t.rotation||0,scale:t.scale||[1,1]}),this._transform=e.getLocalTransform(),this},eachCover:function(t,e){cw(this._covers,t,e)},updateCovers:function(r){r=P(r,function(t){return m(b(bw),t,!0)});var i="\0-brush-index-",o=this._covers,a=this._covers=[],s=this,l=this._creatingCover;return new gf(o,r,function(t,e){return n(t.__brushOption,e)},n).add(t).update(t).remove(function(t){o[t]!==l&&s.group.remove(o[t])}).execute(),this;function n(t,e){return(null!=t.id?t.id:i+e)+"-"+t.brushType}function t(t,e){var i=r[t];if(null!=e&&o[e]===l)a[t]=o[e];else{var n=a[t]=null!=e?(o[e].__brushOption=i,o[e]):Cw(s,Iw(s,i));Dw(s,n)}}},unmount:function(){return this.enableBrush(!1),Ow(this),this._zr.remove(this.group),this},dispose:function(){this.unmount(),this.off()}},S(Mw,It);var $w={mousedown:function(t){if(this._dragging)Kw(this,t);else if(!t.target||!t.target.draggable){Xw(t);var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);this._creatingCover=null,(this._creatingPanel=Pw(this,t,e))&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(t){var e=t.offsetX,i=t.offsetY,n=this.group.transformCoordToLocal(e,i);if(function(t,e,i){if(t._brushType&&!function(t,e,i){var n=t._zr;return e<0||e>n.getWidth()||i<0||i>n.getHeight()}(t,e)){var n=t._zr,r=t._covers,o=Pw(t,e,i);if(!t._dragging)for(var a=0;a<r.length;a++){var s=r[a].__brushOption;if(o&&(!0===o||s.panelId===o.panelId)&&Qw[s.brushType].contain(r[a],i[0],i[1]))return}o&&n.setCursorStyle("crosshair")}}(this,t,n),this._dragging){Xw(t);var r=jw(this,t,n,!1);r&&zw(this,r)}},mouseup:function(t){Kw(this,t)}};function Kw(t,e){if(t._dragging){Xw(e);var i=e.offsetX,n=e.offsetY,r=t.group.transformCoordToLocal(i,n),o=jw(t,e,r,!0);t._dragging=!1,t._track=[],t._creatingCover=null,o&&zw(t,o)}}var Qw={lineX:Jw(0),lineY:Jw(1),rect:{createCover:function(t,e){return Nw(hw(Ww,function(t){return t},function(t){return t}),t,e,["w","e","n","s","se","sw","ne","nw"])},getCreatingRange:function(t){var e=Ew(t);return Hw(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(t,e,i,n){Rw(t,e,i,n)},updateCommon:Bw,contain:Yw},polygon:{createCover:function(t,e){var i=new Si;return i.add(new Ya({name:"main",style:Fw(e),silent:!0})),i},getCreatingRange:function(t){return t},endCreating:function(t,e){e.remove(e.childAt(0)),e.add(new Xa({name:"main",draggable:!0,drift:hw(Gw,t,e),ondragend:hw(zw,t,{isEnd:!0})}))},updateCoverShape:function(t,e,i,n){e.childAt(0).setShape({points:Uw(t,e,i)})},updateCommon:Bw,contain:Yw}};function Jw(l){return{createCover:function(t,e){return Nw(hw(Ww,function(t){var e=[t,[0,100]];return l&&e.reverse(),e},function(t){return t[l]}),t,e,[["w","e"],["n","s"]][l])},getCreatingRange:function(t){var e=Ew(t);return[fw(e[0][l],e[1][l]),pw(e[0][l],e[1][l])]},updateCoverShape:function(t,e,i,n){var r,o=Lw(t,e);if(!0!==o&&o.getLinearBrushOtherExtent)r=o.getLinearBrushOtherExtent(l,t._transform);else{var a=t._zr;r=[0,[a.getWidth(),a.getHeight()][1-l]]}var s=[i,r];l&&s.reverse(),Rw(t,e,s,n)},updateCommon:Bw,contain:Yw}}var tb={axisPointer:1,tooltip:1,brush:1};function eb(n,r,o){return n=ib(n),function(t,e,i){return n.contain(e[0],e[1])&&!function(t,e,i){var n=e.getComponentByElement(t.topTarget),r=n&&n.coordinateSystem;return n&&n!==i&&!tb[n.mainType]&&r&&r.model!==i}(t,r,o)}}function ib(t){return bi.create(t)}var nb=D,rb=x,ob=T,ab=["dataToPoint","pointToData"],sb=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"];function lb(t,e,i){var n=this._targetInfoList=[],r={},o=cb(e,t);nb(db,function(t,e){i&&i.include&&!(0<=rb(i.include,e))||t(o,n,r)})}var ub=lb.prototype;function hb(t){return t[0]>t[1]&&t.reverse(),t}function cb(t,e){return Gr(t,e,{includeMainTypes:sb})}ub.setOutputRanges=function(t,e){this.matchOutputRanges(t,e,function(t,e,i){if((t.coordRanges||(t.coordRanges=[])).push(e),!t.coordRange){t.coordRange=e;var n=gb[t.brushType](0,i,e);t.__rangeOffset={offset:vb[t.brushType](n.values,t.range,[1,1]),xyMinMax:n.xyMinMax}}})},ub.matchOutputRanges=function(t,n,r){nb(t,function(i){var t=this.findTargetInfo(i,n);t&&!0!==t&&D(t.coordSyses,function(t){var e=gb[i.brushType](1,t,i.range);r(i,e.values,t,n)})},this)},ub.setInputRanges=function(t,r){nb(t,function(t){var e=this.findTargetInfo(t,r);if(t.range=t.range||[],e&&!0!==e){t.panelId=e.panelId;var i=gb[t.brushType](0,e.coordSys,t.coordRange),n=t.__rangeOffset;t.range=n?vb[t.brushType](i.values,n.offset,function(t,e){var i=_b(t),n=_b(e),r=[i[0]/n[0],i[1]/n[1]];return isNaN(r[0])&&(r[0]=1),isNaN(r[1])&&(r[1]=1),r}(i.xyMinMax,n.xyMinMax)):i.values}},this)},ub.makePanelOpts=function(i,n){return P(this._targetInfoList,function(t){var e=t.getPanelRect();return{panelId:t.panelId,defaultBrushType:n&&n(t),clipPath:function(i){return i=ib(i),function(t,e){return ll(t,i)}}(e),isTargetByCursor:eb(e,i,t.coordSysModel),getLinearBrushOtherExtent:function(r,o){return r=ib(r),function(t){var e=null!=o?o:t,i=e?r.width:r.height,n=e?r.x:r.y;return[n,n+(i||0)]}}(e)}})},ub.controlSeries=function(t,e,i){var n=this.findTargetInfo(t,i);return!0===n||n&&0<=rb(n.coordSyses,e.coordinateSystem)},ub.findTargetInfo=function(t,e){for(var i=this._targetInfoList,n=cb(e,t),r=0;r<i.length;r++){var o=i[r],a=t.panelId;if(a){if(o.panelId===a)return o}else for(r=0;r<fb.length;r++)if(fb[r](n,o))return o}return!0};var db={grid:function(t,n){var r=t.xAxisModels,o=t.yAxisModels,e=t.gridModels,i=Q(),a={},s={};(r||o||e)&&(nb(r,function(t){var e=t.axis.grid.model;i.set(e.id,e),a[e.id]=!0}),nb(o,function(t){var e=t.axis.grid.model;i.set(e.id,e),s[e.id]=!0}),nb(e,function(t){i.set(t.id,t),a[t.id]=!0,s[t.id]=!0}),i.each(function(t){var e=t.coordinateSystem,i=[];nb(e.getCartesians(),function(t,e){(0<=rb(r,t.getAxis("x").model)||0<=rb(o,t.getAxis("y").model))&&i.push(t)}),n.push({panelId:"grid--"+t.id,gridModel:t,coordSysModel:t,coordSys:i[0],coordSyses:i,getPanelRect:pb.grid,xAxisDeclared:a[t.id],yAxisDeclared:s[t.id]})}))},geo:function(t,i){nb(t.geoModels,function(t){var e=t.coordinateSystem;i.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:e,coordSyses:[e],getPanelRect:pb.geo})})}},fb=[function(t,e){var i=t.xAxisModel,n=t.yAxisModel,r=t.gridModel;return!r&&i&&(r=i.axis.grid.model),!r&&n&&(r=n.axis.grid.model),r&&r===e.gridModel},function(t,e){var i=t.geoModel;return i&&i===e.geoModel}],pb={grid:function(){return this.coordSys.grid.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(rl(t)),e}},gb={lineX:ob(mb,0),lineY:ob(mb,1),rect:function(t,e,i){var n=e[ab[t]]([i[0][0],i[1][0]]),r=e[ab[t]]([i[0][1],i[1][1]]),o=[hb([n[0],r[0]]),hb([n[1],r[1]])];return{values:o,xyMinMax:o}},polygon:function(i,n,t){var r=[[1/0,-1/0],[1/0,-1/0]];return{values:P(t,function(t){var e=n[ab[i]](t);return r[0][0]=Math.min(r[0][0],e[0]),r[1][0]=Math.min(r[1][0],e[1]),r[0][1]=Math.max(r[0][1],e[0]),r[1][1]=Math.max(r[1][1],e[1]),e}),xyMinMax:r}}};function mb(t,e,i,n){var r=i.getAxis(["x","y"][t]),o=hb(P([0,1],function(t){return e?r.coordToData(r.toLocalCoord(n[t])):r.toGlobalCoord(r.dataToCoord(n[t]))})),a=[];return a[t]=o,a[1-t]=[NaN,NaN],{values:o,xyMinMax:a}}var vb={lineX:ob(yb,0),lineY:ob(yb,1),rect:function(t,e,i){return[[t[0][0]-i[0]*e[0][0],t[0][1]-i[0]*e[0][1]],[t[1][0]-i[1]*e[1][0],t[1][1]-i[1]*e[1][1]]]},polygon:function(t,i,n){return P(t,function(t,e){return[t[0]-n[0]*i[e][0],t[1]-n[1]*i[e][1]]})}};function yb(t,e,i,n){return[e[0]-n[t]*i[0],e[1]-n[t]*i[1]]}function _b(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[NaN,NaN]}var xb=D,wb="\0_ec_hist_store";function bb(t){var e=t[wb];return e=e||(t[wb]=[{}])}ax.extend({type:"dataZoom.select"}),ux.extend({type:"dataZoom.select"});var Sb=xc.toolbox.dataZoom,Mb=D;function Ib(t,e,i){(this._brushController=new Mw(i.getZr())).on("brush",C(this._onBrush,this)).mount(),this._isZoomActive}Ib.defaultOption={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:b(Sb.title),brushStyle:{borderWidth:0,color:"rgba(0,0,0,0.2)"}};var Cb=Ib.prototype;Cb.render=function(t,e,i,n){this.model=t,this.ecModel=e,this.api=i,function(t,e,i,n,r){var o=i._isZoomActive;n&&"takeGlobalCursor"===n.type&&(o="dataZoomSelect"===n.key&&n.dataZoomSelectActive);i._isZoomActive=o,t.setIconStatus("zoom",o?"emphasis":"normal");var a=new lb(Ab(t.option),e,{include:["grid"]});i._brushController.setPanels(a.makePanelOpts(r,function(t){return t.xAxisDeclared&&!t.yAxisDeclared?"lineX":!t.xAxisDeclared&&t.yAxisDeclared?"lineY":"rect"})).enableBrush(!!o&&{brushType:"auto",brushStyle:t.getModel("brushStyle").getItemStyle()})}(t,e,this,n,i),function(t,e){t.setIconStatus("back",1<function(t){return bb(t).length}(e)?"emphasis":"normal")}(t,e)},Cb.onclick=function(t,e,i){Tb[i].call(this)},Cb.remove=function(t,e){this._brushController.unmount()},Cb.dispose=function(t,e){this._brushController.dispose()};var Tb={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(function(t){var n=bb(t),e=n[n.length-1];1<n.length&&n.pop();var r={};return xb(e,function(t,e){for(var i=n.length-1;0<=i;i--){if(t=n[i][e]){r[e]=t;break}}}),r}(this.ecModel))}};function Ab(e){var i={};return D(["xAxisIndex","yAxisIndex"],function(t){i[t]=e[t],null==i[t]&&(i[t]="all"),!1!==i[t]&&"none"!==i[t]||(i[t]=[])}),i}Cb._onBrush=function(t,e){if(e.isEnd&&t.length){var s={},l=this.ecModel;this._brushController.updateCovers([]),new lb(Ab(this.model.option),l,{include:["grid"]}).matchOutputRanges(t,l,function(t,e,i){if("cartesian2d"===i.type){var n=t.brushType;"rect"===n?(r("x",i,e[0]),r("y",i,e[1])):r({lineX:"x",lineY:"y"}[n],i,e)}}),function(o,t){var a=bb(o);xb(t,function(t,e){for(var i=a.length-1;0<=i;i--){if(a[i][e])break}if(i<0){var n=o.queryComponents({mainType:"dataZoom",subType:"select",id:e})[0];if(n){var r=n.getPercentRange();a[0][e]={dataZoomId:e,start:r[0],end:r[1]}}}}),a.push(t)}(l,s),this._dispatchZoomAction(s)}function r(t,e,i){var n=e.getAxis(t),r=n.model,o=function(e,i,t){var n;return t.eachComponent({mainType:"dataZoom",subType:"select"},function(t){t.getAxisModel(e,i.componentIndex)&&(n=t)}),n}(t,r,l),a=o.findRepresentativeAxisProxy(r).getMinMaxSpan();null==a.minValueSpan&&null==a.maxValueSpan||(i=K_(0,i.slice(),n.scale.getExtent(),0,a.minValueSpan,a.maxValueSpan)),o&&(s[o.id]={dataZoomId:o.id,startValue:i[0],endValue:i[1]})}},Cb._dispatchZoomAction=function(t){var i=[];Mb(t,function(t,e){i.push(b(t))}),i.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:i})},Ux("dataZoom",Ib),nf(function(s){if(s){var l=s.dataZoom||(s.dataZoom=[]);O(l)||(s.dataZoom=l=[l]);var t=s.toolbox;if(t&&(O(t)&&(t=t[0]),t&&t.feature)){var e=t.feature.dataZoom;i("xAxis",e),i("yAxis",e)}}function i(n,r){if(r){var o=n+"Index",a=r[o];null==a||"all"===a||O(a)||(a=!1===a||"none"===a?[]:[a]),function(t,e){var i=s[t];O(i)||(i=i?[i]:[]);Mb(i,e)}(n,function(t,e){if(null==a||"all"===a||-1!==x(a,e)){var i={type:"select",$fromToolbox:!0,filterMode:r.filterMode||"filter",id:"\0_ec_\0toolbox-dataZoom_"+n+e};i[o]=e,l.push(i)}})}}});var Db=xc.toolbox.restore;function kb(t){this.model=t}kb.defaultOption={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:Db.title},kb.prototype.onclick=function(t,e,i){!function(t){t[wb]=null}(t),e.dispatchAction({type:"restore",from:this.uid})},Ux("restore",kb),of({type:"restore",event:"restore",update:"prepareAndUpdate"},function(t,e){e.resetOption("recreate")});var Pb,Lb="urn:schemas-microsoft-com:vml",Ob="undefined"==typeof window?null:window,zb=!1,Eb=Ob&&Ob.document;function Nb(t){return Pb(t)}if(Eb&&!v.canvasSupported)try{Eb.namespaces.zrvml||Eb.namespaces.add("zrvml",Lb),Pb=function(t){return Eb.createElement("<zrvml:"+t+' class="zrvml">')}}catch(t){Pb=function(t){return Eb.createElement("<"+t+' xmlns="'+Lb+'" class="zrvml">')}}var Rb,Bb=Jo.CMD,Vb=Math.round,Fb=Math.sqrt,Hb=Math.abs,Wb=Math.cos,Gb=Math.sin,Zb=Math.max;if(!v.canvasSupported){var Ub=",",Xb="progid:DXImageTransform.Microsoft",Yb=21600,jb=Yb/2,qb=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=Yb+","+Yb,t.coordorigin="0,0"},$b=function(t,e,i){return"rgb("+[t,e,i].join(",")+")"},Kb=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},Qb=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},Jb=function(t,e,i){return 1e5*(parseFloat(t)||0)+1e3*(parseFloat(e)||0)+i},tS=Hn,eS=function(t,e,i){var n=Ne(e);i=+i,isNaN(i)&&(i=1),n&&(t.color=$b(n[0],n[1],n[2]),t.opacity=i*n[3])},iS=function(t,e,i,n){var r="fill"===e,o=t.getElementsByTagName(e)[0];null!=i[e]&&"none"!==i[e]&&(r||!r&&i.lineWidth)?(t[r?"filled":"stroked"]="true",i[e]instanceof rs&&Qb(t,o),o=o||Nb(e),r?function(t,e,i){var n,r,o=e.fill;if(null!=o)if(o instanceof rs){var a,s=0,l=[0,0],u=0,h=1,c=i.getBoundingRect(),d=c.width,f=c.height;if("linear"===o.type){a="gradient";var p=i.transform,g=[o.x*d,o.y*f],m=[o.x2*d,o.y2*f];p&&(yt(g,g,p),yt(m,m,p));var v=m[0]-g[0],y=m[1]-g[1];(s=180*Math.atan2(v,y)/Math.PI)<0&&(s+=360),s<1e-6&&(s=0)}else{a="gradientradial";g=[o.x*d,o.y*f],p=i.transform;var _=i.scale,x=d,w=f;l=[(g[0]-c.x)/x,(g[1]-c.y)/w],p&&yt(g,g,p),x/=_[0]*Yb,w/=_[1]*Yb;var b=Zb(x,w);u=0/b,h=2*o.r/b-u}var S=o.colorStops.slice();S.sort(function(t,e){return t.offset-e.offset});for(var M=S.length,I=[],C=[],T=0;T<M;T++){var A=S[T],D=(n=A.color,void 0,r=Ne(n),[$b(r[0],r[1],r[2]),r[3]]);C.push(A.offset*h+u+" "+D[0]),0!==T&&T!==M-1||I.push(D)}if(2<=M){var k=I[0][0],P=I[1][0],L=I[0][1]*e.opacity,O=I[1][1]*e.opacity;t.type=a,t.method="none",t.focus="100%",t.angle=s,t.color=k,t.color2=P,t.colors=C.join(","),t.opacity=O,t.opacity2=L}"radial"===a&&(t.focusposition=l.join(","))}else eS(t,o,e.opacity)}(o,i,n):function(t,e){e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof rs||eS(t,e.stroke,e.opacity)}(o,i),Kb(t,o)):(t[r?"filled":"stroked"]="false",Qb(t,o))},nS=[[],[],[]];xa.prototype.brushVML=function(t){var e=this.style,i=this._vmlEl;i||(i=Nb("shape"),qb(i),this._vmlEl=i),iS(i,"fill",e,this),iS(i,"stroke",e,this);var n=this.transform,r=null!=n,o=i.getElementsByTagName("stroke")[0];if(o){var a=e.lineWidth;if(r&&!e.strokeNoScale){var s=n[0]*n[3]-n[1]*n[2];a*=Fb(Hb(s))}o.weight=a+"px"}var l=this.path||(this.path=new Jo);this.__dirtyPath&&(l.beginPath(),l.subPixelOptimize=!1,this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),i.path=function(t,e){var i,n,r,o,a,s,l=Bb.M,u=Bb.C,h=Bb.L,c=Bb.A,d=Bb.Q,f=[],p=t.data,g=t.len();for(o=0;o<g;){switch(n="",i=0,r=p[o++]){case l:n=" m ",i=1,a=p[o++],s=p[o++],nS[0][0]=a,nS[0][1]=s;break;case h:n=" l ",i=1,a=p[o++],s=p[o++],nS[0][0]=a,nS[0][1]=s;break;case d:case u:n=" c ",i=3;var m,v,y=p[o++],_=p[o++],x=p[o++],w=p[o++];r===d?(x=((m=x)+2*y)/3,w=((v=w)+2*_)/3,y=(a+2*y)/3,_=(s+2*_)/3):(m=p[o++],v=p[o++]),nS[0][0]=y,nS[0][1]=_,nS[1][0]=x,nS[1][1]=w,a=nS[2][0]=m,s=nS[2][1]=v;break;case c:var b=0,S=0,M=1,I=1,C=0;e&&(b=e[4],S=e[5],M=Fb(e[0]*e[0]+e[1]*e[1]),I=Fb(e[2]*e[2]+e[3]*e[3]),C=Math.atan2(-e[1]/I,e[0]/M));var T=p[o++],A=p[o++],D=p[o++],k=p[o++],P=p[o++]+C,L=p[o++]+P+C;o++;var O=p[o++],z=T+Wb(P)*D,E=A+Gb(P)*k,N=(y=T+Wb(L)*D,_=A+Gb(L)*k,O?" wa ":" at ");Math.abs(z-y)<1e-4&&(.01<Math.abs(L-P)?O&&(z+=.0125):Math.abs(E-A)<1e-4?O&&z<T||!O&&T<z?_-=.0125:_+=.0125:O&&E<A||!O&&A<E?y+=.0125:y-=.0125),f.push(N,Vb(((T-D)*M+b)*Yb-jb),Ub,Vb(((A-k)*I+S)*Yb-jb),Ub,Vb(((T+D)*M+b)*Yb-jb),Ub,Vb(((A+k)*I+S)*Yb-jb),Ub,Vb((z*M+b)*Yb-jb),Ub,Vb((E*I+S)*Yb-jb),Ub,Vb((y*M+b)*Yb-jb),Ub,Vb((_*I+S)*Yb-jb)),a=y,s=_;break;case Bb.R:var R=nS[0],B=nS[1];R[0]=p[o++],R[1]=p[o++],B[0]=R[0]+p[o++],B[1]=R[1]+p[o++],e&&(yt(R,R,e),yt(B,B,e)),R[0]=Vb(R[0]*Yb-jb),B[0]=Vb(B[0]*Yb-jb),R[1]=Vb(R[1]*Yb-jb),B[1]=Vb(B[1]*Yb-jb),f.push(" m ",R[0],Ub,R[1]," l ",B[0],Ub,R[1]," l ",B[0],Ub,B[1]," l ",R[0],Ub,B[1]);break;case Bb.Z:f.push(" x ")}if(0<i){f.push(n);for(var V=0;V<i;V++){var F=nS[V];e&&yt(F,F,e),f.push(Vb(F[0]*Yb-jb),Ub,Vb(F[1]*Yb-jb),V<i-1?Ub:"")}}}return f.join("")}(l,this.transform),i.style.zIndex=Jb(this.zlevel,this.z,this.z2),Kb(t,i),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},xa.prototype.onRemove=function(t){Qb(t,this._vmlEl),this.removeRectText(t)},xa.prototype.onAdd=function(t){Kb(t,this._vmlEl),this.appendRectText(t)};Yn.prototype.brushVML=function(t){var e,i,n=this.style,r=n.image;if(function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()}(r)){var o=r.src;if(o===this._imageSrc)e=this._imageWidth,i=this._imageHeight;else{var a=r.runtimeStyle,s=a.width,l=a.height;a.width="auto",a.height="auto",e=r.width,i=r.height,a.width=s,a.height=l,this._imageSrc=o,this._imageWidth=e,this._imageHeight=i}r=o}else r===this._imageSrc&&(e=this._imageWidth,i=this._imageHeight);if(r){var u=n.x||0,h=n.y||0,c=n.width,d=n.height,f=n.sWidth,p=n.sHeight,g=n.sx||0,m=n.sy||0,v=f&&p,y=this._vmlEl;y||(y=Eb.createElement("div"),qb(y),this._vmlEl=y);var _,x=y.style,w=!1,b=1,S=1;if(this.transform&&(_=this.transform,b=Fb(_[0]*_[0]+_[1]*_[1]),S=Fb(_[2]*_[2]+_[3]*_[3]),w=_[1]||_[2]),w){var M=[u,h],I=[u+c,h],C=[u,h+d],T=[u+c,h+d];yt(M,M,_),yt(I,I,_),yt(C,C,_),yt(T,T,_);var A=Zb(M[0],I[0],C[0],T[0]),D=Zb(M[1],I[1],C[1],T[1]),k=[];k.push("M11=",_[0]/b,Ub,"M12=",_[2]/S,Ub,"M21=",_[1]/b,Ub,"M22=",_[3]/S,Ub,"Dx=",Vb(u*b+_[4]),Ub,"Dy=",Vb(h*S+_[5])),x.padding="0 "+Vb(A)+"px "+Vb(D)+"px 0",x.filter=Xb+".Matrix("+k.join("")+", SizingMethod=clip)"}else _&&(u=u*b+_[4],h=h*S+_[5]),x.filter="",x.left=Vb(u)+"px",x.top=Vb(h)+"px";var P=this._imageEl,L=this._cropEl;P||(P=Eb.createElement("div"),this._imageEl=P);var O=P.style;if(v){if(e&&i)O.width=Vb(b*e*c/f)+"px",O.height=Vb(S*i*d/p)+"px";else{var z=new Image,E=this;z.onload=function(){z.onload=null,e=z.width,i=z.height,O.width=Vb(b*e*c/f)+"px",O.height=Vb(S*i*d/p)+"px",E._imageWidth=e,E._imageHeight=i,E._imageSrc=r},z.src=r}L||((L=Eb.createElement("div")).style.overflow="hidden",this._cropEl=L);var N=L.style;N.width=Vb((c+g*c/f)*b),N.height=Vb((d+m*d/p)*S),N.filter=Xb+".Matrix(Dx="+-g*c/f*b+",Dy="+-m*d/p*S+")",L.parentNode||y.appendChild(L),P.parentNode!==L&&L.appendChild(P)}else O.width=Vb(b*c)+"px",O.height=Vb(S*d)+"px",y.appendChild(P),L&&L.parentNode&&(y.removeChild(L),this._cropEl=null);var R="",B=n.opacity;B<1&&(R+=".Alpha(opacity="+Vb(100*B)+") "),R+=Xb+".AlphaImageLoader(src="+r+", SizingMethod=scale)",O.filter=R,y.style.zIndex=Jb(this.zlevel,this.z,this.z2),Kb(t,y),null!=n.text&&this.drawRectText(t,this.getBoundingRect())}},Yn.prototype.onRemove=function(t){Qb(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},Yn.prototype.onAdd=function(t){Kb(t,this._vmlEl),this.appendRectText(t)};var rS,oS="normal",aS={},sS=0,lS=document.createElement("div");Rb=function(t,e){var i=Eb;rS||((rS=i.createElement("div")).style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",Eb.body.appendChild(rS));try{rS.style.font=e}catch(t){}return rS.innerHTML="",rS.appendChild(i.createTextNode(t)),{width:rS.offsetWidth}},sn["measureText"]=Rb;for(var uS=new bi,hS=function(t,e,i,n){var r=this.style;this.__dirty&&Dn(r);var o=r.text;if(null!=o&&(o+=""),o){if(r.rich){var a=_n(o,r);o=[];for(var s=0;s<a.lines.length;s++){for(var l=a.lines[s].tokens,u=[],h=0;h<l.length;h++)u.push(l[h].text);o.push(u.join(""))}o=o.join("\n")}var c,d,f=r.textAlign,p=r.textVerticalAlign,g=function(t){var e=aS[t];if(!e){100<sS&&(sS=0,aS={});var i,n=lS.style;try{n.font=t,i=n.fontFamily.split(",")[0]}catch(t){}e={style:n.fontStyle||oS,variant:n.fontVariant||oS,weight:n.fontWeight||oS,size:0|parseFloat(n.fontSize||12),family:i||"Microsoft YaHei"},aS[t]=e,sS++}return e}(r.font),m=g.style+" "+g.variant+" "+g.weight+" "+g.size+'px "'+g.family+'"';i=i||un(o,m,f,p,r.textPadding,r.textLineHeight);var v=this.transform;if(v&&!n&&(uS.copy(e),uS.applyTransform(v),e=uS),n)c=e.x,d=e.y;else{var y=r.textPosition;if(y instanceof Array)c=e.x+tS(y[0],e.width),d=e.y+tS(y[1],e.height),f=f||"left";else{var _=this.calculateTextPosition?this.calculateTextPosition({},r,e):dn({},r,e);c=_.x,d=_.y,f=f||_.textAlign,p=p||_.textVerticalAlign}}c=hn(c,i.width,f),d=cn(d,i.height,p),d+=i.height/2;var x,w,b,S=Nb,M=this._textVmlEl;M?w=(x=(b=M.firstChild).nextSibling).nextSibling:(M=S("line"),x=S("path"),w=S("textpath"),b=S("skew"),w.style["v-text-align"]="left",qb(M),x.textpathok=!0,w.on=!0,M.from="0 0",M.to="1000 0.05",Kb(M,b),Kb(M,x),Kb(M,w),this._textVmlEl=M);var I=[c,d],C=M.style;v&&n?(yt(I,I,v),b.on=!0,b.matrix=v[0].toFixed(3)+Ub+v[2].toFixed(3)+Ub+v[1].toFixed(3)+Ub+v[3].toFixed(3)+",0,0",b.offset=(Vb(I[0])||0)+","+(Vb(I[1])||0),b.origin="0 0",C.left="0px",C.top="0px"):(b.on=!1,C.left=Vb(c)+"px",C.top=Vb(d)+"px"),w.string=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")}(o);try{w.style.font=m}catch(t){}iS(M,"fill",{fill:r.textFill,opacity:r.opacity},this),iS(M,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash||null},this),M.style.zIndex=Jb(this.zlevel,this.z,this.z2),Kb(t,M)}},cS=function(t){Qb(t,this._textVmlEl),this._textVmlEl=null},dS=function(t){Kb(t,this._textVmlEl)},fS=[Zn,Xn,Yn,xa,Ba],pS=0;pS<fS.length;pS++){var gS=fS[pS].prototype;gS.drawRectText=hS,gS.removeRectText=cS,gS.appendRectText=dS}Ba.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},Ba.prototype.onRemove=function(t){this.removeRectText(t)},Ba.prototype.onAdd=function(t){this.appendRectText(t)}}function mS(t){return parseInt(t,10)}function vS(t,e){!function(){if(!zb&&Eb){zb=!0;var t=Eb.styleSheets;t.length<31?Eb.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}(),this.root=t,this.storage=e;var i=document.createElement("div"),n=document.createElement("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",n.style.cssText="position:absolute;left:0;top:0;",t.appendChild(i),this._vmlRoot=n,this._vmlViewport=i,this.resize();var r=e.delFromStorage,o=e.addToStorage;e.delFromStorage=function(t){r.call(e,t),t&&t.onRemove&&t.onRemove(n)},e.addToStorage=function(t){t.onAdd&&t.onAdd(n),o.call(e,t)},this._firstPaint=!0}vS.prototype={constructor:vS,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,i=0;i<t.length;i++){var n=t[i];n.invisible||n.ignore?(n.__alreadyNotVisible||n.onRemove(e),n.__alreadyNotVisible=!0):(n.__alreadyNotVisible&&n.onAdd(e),n.__alreadyNotVisible=!1,n.__dirty&&(n.beforeBrush&&n.beforeBrush(),(n.brushVML||n.brush).call(n,e),n.afterBrush&&n.afterBrush())),n.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!==t||this._height!==e){this._width=t,this._height=e;var i=this._vmlViewport.style;i.width=t+"px",i.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||mS(e.width))-mS(e.paddingLeft)-mS(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||mS(e.height))-mS(e.paddingTop)-mS(e.paddingBottom)|0}},D(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){vS.prototype[t]=function(t){return function(){ci('In IE8.0 VML mode painter not support method "'+t+'"')}}(t)}),Ir("vml",vS);function yS(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}var _S=Jo.CMD,xS=Array.prototype.join,wS="none",bS=Math.round,SS=Math.sin,MS=Math.cos,IS=Math.PI,CS=2*Math.PI,TS=180/IS,AS=1e-4;function DS(t){return bS(1e4*t)/1e4}function kS(t){return t<AS&&-AS<t}function PS(t,e){e&&LS(t,"transform","matrix("+xS.call(e,",")+")")}function LS(t,e,i){i&&("linear"===i.type||"radial"===i.type)||t.setAttribute(e,i)}function OS(t,e,i,n){if(function(t,e){var i=e?t.textFill:t.fill;return null!=i&&i!==wS}(e,i)){var r=i?e.textFill:e.fill;LS(t,"fill",r="transparent"===r?wS:r),LS(t,"fill-opacity",null!=e.fillOpacity?e.fillOpacity*e.opacity:e.opacity)}else LS(t,"fill",wS);if(function(t,e){var i=e?t.textStroke:t.stroke;return null!=i&&i!==wS}(e,i)){var o=i?e.textStroke:e.stroke;LS(t,"stroke",o="transparent"===o?wS:o),LS(t,"stroke-width",(i?e.textStrokeWidth:e.lineWidth)/(!i&&e.strokeNoScale?n.getLineScale():1)),LS(t,"paint-order",i?"stroke":"fill"),LS(t,"stroke-opacity",null!=e.strokeOpacity?e.strokeOpacity:e.opacity),e.lineDash?(LS(t,"stroke-dasharray",e.lineDash.join(",")),LS(t,"stroke-dashoffset",bS(e.lineDashOffset||0))):LS(t,"stroke-dasharray",""),e.lineCap&&LS(t,"stroke-linecap",e.lineCap),e.lineJoin&&LS(t,"stroke-linejoin",e.lineJoin),e.miterLimit&&LS(t,"stroke-miterlimit",e.miterLimit)}else LS(t,"stroke",wS)}var zS={};zS.brush=function(t){var e=t.style,i=t.__svgEl;i||(i=yS("path"),t.__svgEl=i),t.path||t.createPathProxy();var n=t.path;if(t.__dirtyPath){n.beginPath(),n.subPixelOptimize=!1,t.buildPath(n,t.shape),t.__dirtyPath=!1;var r=function(t){for(var e=[],i=t.data,n=t.len(),r=0;r<n;){var o="",a=0;switch(i[r++]){case _S.M:o="M",a=2;break;case _S.L:o="L",a=2;break;case _S.Q:o="Q",a=4;break;case _S.C:o="C",a=6;break;case _S.A:var s=i[r++],l=i[r++],u=i[r++],h=i[r++],c=i[r++],d=i[r++],f=i[r++],p=i[r++],g=Math.abs(d),m=kS(g-CS)||(p?CS<=d:CS<=-d),v=0<d?d%CS:d%CS+CS,y=!1;y=!!m||!kS(g)&&IS<=v==!!p;var _=DS(s+u*MS(c)),x=DS(l+h*SS(c));m&&(d=p?CS-1e-4:1e-4-CS,y=!0,9===r&&e.push("M",_,x));var w=DS(s+u*MS(c+d)),b=DS(l+h*SS(c+d));e.push("A",DS(u),DS(h),bS(f*TS),+y,+p,w,b);break;case _S.Z:o="Z";break;case _S.R:w=DS(i[r++]),b=DS(i[r++]);var S=DS(i[r++]),M=DS(i[r++]);e.push("M",w,b,"L",w+S,b,"L",w+S,b+M,"L",w,b+M,"L",w,b)}o&&e.push(o);for(var I=0;I<a;I++)e.push(DS(i[r++]))}return e.join(" ")}(n);r.indexOf("NaN")<0&&LS(i,"d",r)}OS(i,e,!1,t),PS(i,t.transform),null!=e.text?HS(t,t.getBoundingRect()):GS(t)};var ES={brush:function(t){var e=t.style,i=e.image;i instanceof HTMLImageElement&&(i=i.src);if(i){var n=e.x||0,r=e.y||0,o=e.width,a=e.height,s=t.__svgEl;s||(s=yS("image"),t.__svgEl=s),i!==t.__imageSrc&&(function(t,e,i){t.setAttributeNS("http://www.w3.org/1999/xlink",e,i)}(s,"href",i),t.__imageSrc=i),LS(s,"width",o),LS(s,"height",a),LS(s,"x",n),LS(s,"y",r),PS(s,t.transform),null!=e.text?HS(t,t.getBoundingRect()):GS(t)}}},NS={},RS=new bi,BS={},VS=[],FS={left:"start",right:"end",center:"middle",middle:"middle"},HS=function(t,e){var i=t.style,n=t.transform,r=t instanceof Ba||i.transformText;t.__dirty&&Dn(i);var o=i.text;if(null!=o&&(o+=""),Gn(o,i)){null==o&&(o=""),!r&&n&&(RS.copy(e),RS.applyTransform(n),e=RS);var a=t.__textSvgEl;a||(a=yS("text"),t.__textSvgEl=a);var s=a.style,l=i.font||an,u=a.__computedFont;l!==a.__styleFont&&(s.font=a.__styleFont=l,u=a.__computedFont=s.font);var h=i.textPadding,c=i.textLineHeight,d=t.__textCotentBlock;d&&!t.__dirtyText||(d=t.__textCotentBlock=yn(o,u,h,c,i.truncate));var f=d.outerHeight,p=d.lineHeight;Rn(BS,t,i,e);var g=BS.baseX,m=BS.baseY,v=BS.textAlign||"left",y=BS.textVerticalAlign;!function(t,e,i,n,r,o,a){ie(VS),e&&i&&ne(VS,i);var s=n.textRotation;if(r&&s){var l=n.textOrigin;"center"===l?(o=r.width/2+r.x,a=r.height/2+r.y):l&&(o=l[0]+r.x,a=l[1]+r.y),VS[4]-=o,VS[5]-=a,ae(VS,VS,s),VS[4]+=o,VS[5]+=a}PS(t,VS)}(a,r,n,i,e,g,m);var _=g,x=cn(m,f,y);h&&(_=function(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}(g,v,h),x+=h[0]),x+=p/2,OS(a,i,!0,t);var w=d.canCacheByTextString,b=t.__tspanList||(t.__tspanList=[]),S=b.length;if(w&&t.__canCacheByTextString&&t.__text===o){if(t.__dirtyText&&S)for(var M=0;M<S;++M)WS(b[M],v,_,x+M*p)}else{t.__text=o,t.__canCacheByTextString=w;var I=d.lines,C=I.length;for(M=0;M<C;M++){var T=b[M],A=I[M];T?T.__zrText!==A&&(T.innerHTML="",T.appendChild(document.createTextNode(A))):(T=b[M]=yS("tspan"),a.appendChild(T),T.appendChild(document.createTextNode(A))),WS(T,v,_,x+M*p)}if(C<S){for(;M<S;M++)a.removeChild(b[M]);b.length=C}}}};function WS(t,e,i,n){LS(t,"dominant-baseline","middle"),LS(t,"text-anchor",FS[e]),LS(t,"x",i),LS(t,"y",n)}function GS(t){t&&t.__textSvgEl&&(t.__textSvgEl.parentNode&&t.__textSvgEl.parentNode.removeChild(t.__textSvgEl),t.__textSvgEl=null,t.__tspanList=[],t.__text=null)}function ZS(){}function US(t,e){for(var i=0,n=e.length,r=0,o=0;i<n;i++){var a=e[i];if(a.removed){for(s=[],l=o;l<o+a.count;l++)s.push(l);a.indices=s,o+=a.count}else{for(var s=[],l=r;l<r+a.count;l++)s.push(l);a.indices=s,r+=a.count,a.added||(o+=a.count)}}return e}NS.drawRectText=HS,NS.brush=function(t){null!=t.style.text?HS(t,!1):GS(t)},ZS.prototype={diff:function(l,u,t){t=t||function(t,e){return t===e},this.equals=t;var h=this;l=l.slice();var c=(u=u.slice()).length,d=l.length,f=1,e=c+d,p=[{newPos:-1,components:[]}],i=this.extractCommon(p[0],u,l,0);if(p[0].newPos+1>=c&&d<=i+1){for(var n=[],r=0;r<u.length;r++)n.push(r);return[{indices:n,count:u.length}]}function o(){for(var t=-1*f;t<=f;t+=2){var e,i=p[t-1],n=p[t+1],r=(n?n.newPos:0)-t;i&&(p[t-1]=void 0);var o=i&&i.newPos+1<c,a=n&&0<=r&&r<d;if(o||a){if(!o||a&&i.newPos<n.newPos?(e={newPos:(s=n).newPos,components:s.components.slice(0)},h.pushComponent(e.components,void 0,!0)):((e=i).newPos++,h.pushComponent(e.components,!0,void 0)),r=h.extractCommon(e,u,l,t),e.newPos+1>=c&&d<=r+1)return US(h,e.components,u,l);p[t]=e}else p[t]=void 0}var s;f++}for(;f<=e;){var a=o();if(a)return a}},pushComponent:function(t,e,i){var n=t[t.length-1];n&&n.added===e&&n.removed===i?t[t.length-1]={count:n.count+1,added:e,removed:i}:t.push({count:1,added:e,removed:i})},extractCommon:function(t,e,i,n){for(var r=e.length,o=i.length,a=t.newPos,s=a-n,l=0;a+1<r&&s+1<o&&this.equals(e[a+1],i[s+1]);)a++,s++,l++;return l&&t.components.push({count:l}),t.newPos=a,s},tokenize:function(t){return t.slice()},join:function(t){return t.slice()}};var XS=new ZS;function YS(t,e,i,n,r){this._zrId=t,this._svgRoot=e,this._tagNames="string"==typeof i?[i]:i,this._markLabel=n,this._domName=r||"_dom",this.nextId=0}function jS(t,e){YS.call(this,t,e,["linearGradient","radialGradient"],"__gradient_in_use__")}function qS(t,e){YS.call(this,t,e,"clipPath","__clippath_in_use__")}function $S(t,e){YS.call(this,t,e,["filter"],"__filter_in_use__","_shadowDom")}function KS(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY||t.textShadowBlur||t.textShadowOffsetX||t.textShadowOffsetY)}function QS(t){return parseInt(t,10)}function JS(t,e){return e&&t&&e.parentNode!==t}function tM(t,e,i){if(JS(t,e)&&i){var n=i.nextSibling;n?t.insertBefore(e,n):t.appendChild(e)}}function eM(t,e){if(JS(t,e)){var i=t.firstChild;i?t.insertBefore(e,i):t.appendChild(e)}}function iM(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)}function nM(t){return t.__textSvgEl}function rM(t){return t.__svgEl}YS.prototype.createElement=yS,YS.prototype.getDefs=function(t){var e=this._svgRoot,n=this._svgRoot.getElementsByTagName("defs");return 0===n.length?t?((n=e.insertBefore(this.createElement("defs"),e.firstChild)).contains||(n.contains=function(t){var e=n.children;if(!e)return!1;for(var i=e.length-1;0<=i;--i)if(e[i]===t)return!0;return!1}),n):null:n[0]},YS.prototype.update=function(t,e){if(t){var i=this.getDefs(!1);if(t[this._domName]&&i.contains(t[this._domName]))"function"==typeof e&&e(t);else{var n=this.add(t);n&&(t[this._domName]=n)}}},YS.prototype.addDom=function(t){this.getDefs(!0).appendChild(t)},YS.prototype.removeDom=function(t){var e=this.getDefs(!1);e&&t[this._domName]&&(e.removeChild(t[this._domName]),t[this._domName]=null)},YS.prototype.getDoms=function(){var i=this.getDefs(!1);if(!i)return[];var n=[];return D(this._tagNames,function(t){var e=i.getElementsByTagName(t);n=n.concat([].slice.call(e))}),n},YS.prototype.markAllUnused=function(){var t=this.getDoms(),e=this;D(t,function(t){t[e._markLabel]="0"})},YS.prototype.markUsed=function(t){t&&(t[this._markLabel]="1")},YS.prototype.removeUnused=function(){var e=this.getDefs(!1);if(e){var t=this.getDoms(),i=this;D(t,function(t){"1"!==t[i._markLabel]&&e.removeChild(t)})}},YS.prototype.getSvgProxy=function(t){return t instanceof xa?zS:t instanceof Yn?ES:t instanceof Ba?NS:zS},YS.prototype.getTextSvgElement=function(t){return t.__textSvgEl},YS.prototype.getSvgElement=function(t){return t.__svgEl},w(jS,YS),jS.prototype.addWithoutUpdate=function(o,a){if(a&&a.style){var s=this;D(["fill","stroke"],function(t){if(a.style[t]&&("linear"===a.style[t].type||"radial"===a.style[t].type)){var e,i=a.style[t],n=s.getDefs(!0);i._dom?(e=i._dom,n.contains(i._dom)||s.addDom(e)):e=s.add(i),s.markUsed(a);var r=e.getAttribute("id");o.setAttribute(t,"url(#"+r+")")}})}},jS.prototype.add=function(t){var e;if("linear"===t.type)e=this.createElement("linearGradient");else{if("radial"!==t.type)return ci("Illegal gradient type."),null;e=this.createElement("radialGradient")}return t.id=t.id||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-gradient-"+t.id),this.updateDom(t,e),this.addDom(e),e},jS.prototype.update=function(i){var n=this;YS.prototype.update.call(this,i,function(){var t=i.type,e=i._dom.tagName;"linear"===t&&"linearGradient"===e||"radial"===t&&"radialGradient"===e?n.updateDom(i,i._dom):(n.removeDom(i),n.add(i))})},jS.prototype.updateDom=function(t,e){if("linear"===t.type)e.setAttribute("x1",t.x),e.setAttribute("y1",t.y),e.setAttribute("x2",t.x2),e.setAttribute("y2",t.y2);else{if("radial"!==t.type)return void ci("Illegal gradient type.");e.setAttribute("cx",t.x),e.setAttribute("cy",t.y),e.setAttribute("r",t.r)}t.global?e.setAttribute("gradientUnits","userSpaceOnUse"):e.setAttribute("gradientUnits","objectBoundingBox"),e.innerHTML="";for(var i=t.colorStops,n=0,r=i.length;n<r;++n){var o=this.createElement("stop");o.setAttribute("offset",100*i[n].offset+"%");var a=i[n].color;if(-1<a.indexOf("rgba")){var s=Ne(a)[3],l=Ve(a);o.setAttribute("stop-color","#"+l),o.setAttribute("stop-opacity",s)}else o.setAttribute("stop-color",i[n].color);e.appendChild(o)}t._dom=e},jS.prototype.markUsed=function(t){if(t.style){var e=t.style.fill;e&&e._dom&&YS.prototype.markUsed.call(this,e._dom),(e=t.style.stroke)&&e._dom&&YS.prototype.markUsed.call(this,e._dom)}},w(qS,YS),qS.prototype.update=function(t){var e=this.getSvgElement(t);e&&this.updateDom(e,t.__clipPaths,!1);var i=this.getTextSvgElement(t);i&&this.updateDom(i,t.__clipPaths,!0),this.markUsed(t)},qS.prototype.updateDom=function(t,e,i){if(e&&0<e.length){var n,r,o=this.getDefs(!0),a=e[0],s=i?"_textDom":"_dom";a[s]?(r=a[s].getAttribute("id"),n=a[s],o.contains(n)||o.appendChild(n)):(r="zr"+this._zrId+"-clip-"+this.nextId,++this.nextId,(n=this.createElement("clipPath")).setAttribute("id",r),o.appendChild(n),a[s]=n);var l=this.getSvgProxy(a);if(a.transform&&a.parent.invTransform&&!i){var u=Array.prototype.slice.call(a.transform);re(a.transform,a.parent.invTransform,a.transform),l.brush(a),a.transform=u}else l.brush(a);var h=this.getSvgElement(a);n.innerHTML="",n.appendChild(h.cloneNode()),t.setAttribute("clip-path","url(#"+r+")"),1<e.length&&this.updateDom(n,e.slice(1),i)}else t&&t.setAttribute("clip-path","none")},qS.prototype.markUsed=function(t){var e=this;t.__clipPaths&&D(t.__clipPaths,function(t){t._dom&&YS.prototype.markUsed.call(e,t._dom),t._textDom&&YS.prototype.markUsed.call(e,t._textDom)})},w($S,YS),$S.prototype.addWithoutUpdate=function(t,e){if(e&&KS(e.style)){var i;if(e._shadowDom)i=e._shadowDom,this.getDefs(!0).contains(e._shadowDom)||this.addDom(i);else i=this.add(e);this.markUsed(e);var n=i.getAttribute("id");t.style.filter="url(#"+n+")"}},$S.prototype.add=function(t){var e=this.createElement("filter");return t._shadowDomId=t._shadowDomId||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-shadow-"+t._shadowDomId),this.updateDom(t,e),this.addDom(e),e},$S.prototype.update=function(t,e){if(KS(e.style)){var i=this;YS.prototype.update.call(this,e,function(){i.updateDom(e,e._shadowDom)})}else this.remove(t,e)},$S.prototype.remove=function(t,e){null!=e._shadowDomId&&(this.removeDom(t),t.style.filter="")},$S.prototype.updateDom=function(t,e){var i=e.getElementsByTagName("feDropShadow");i=0===i.length?this.createElement("feDropShadow"):i[0];var n,r,o,a,s=t.style,l=t.scale&&t.scale[0]||1,u=t.scale&&t.scale[1]||1;if(s.shadowBlur||s.shadowOffsetX||s.shadowOffsetY)n=s.shadowOffsetX||0,r=s.shadowOffsetY||0,o=s.shadowBlur,a=s.shadowColor;else{if(!s.textShadowBlur)return void this.removeDom(e,s);n=s.textShadowOffsetX||0,r=s.textShadowOffsetY||0,o=s.textShadowBlur,a=s.textShadowColor}i.setAttribute("dx",n/l),i.setAttribute("dy",r/u),i.setAttribute("flood-color",a);var h=o/2/l+" "+o/2/u;i.setAttribute("stdDeviation",h),e.setAttribute("x","-100%"),e.setAttribute("y","-100%"),e.setAttribute("width",Math.ceil(o/2*200)+"%"),e.setAttribute("height",Math.ceil(o/2*200)+"%"),e.appendChild(i),t._shadowDom=e},$S.prototype.markUsed=function(t){t._shadowDom&&YS.prototype.markUsed.call(this,t._shadowDom)};function oM(t,e,i,n){this.root=t,this.storage=e,this._opts=i=k({},i||{});var r=yS("svg");r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("version","1.1"),r.setAttribute("baseProfile","full"),r.style.cssText="user-select:none;position:absolute;left:0;top:0;";var o=yS("g");r.appendChild(o);var a=yS("g");r.appendChild(a),this.gradientManager=new jS(n,a),this.clipPathManager=new qS(n,a),this.shadowManager=new $S(n,a);var s=document.createElement("div");s.style.cssText="overflow:hidden;position:relative",this._svgDom=r,this._svgRoot=a,this._backgroundRoot=o,this._viewport=s,t.appendChild(s),s.appendChild(r),this.resize(i.width,i.height),this._visibleList=[]}oM.prototype={constructor:oM,getType:function(){return"svg"},getViewportRoot:function(){return this._viewport},getSvgDom:function(){return this._svgDom},getSvgRoot:function(){return this._svgRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0);this._paintList(t)},setBackgroundColor:function(t){this._backgroundRoot&&this._backgroundNode&&this._backgroundRoot.removeChild(this._backgroundNode);var e=yS("rect");e.setAttribute("width",this.getWidth()),e.setAttribute("height",this.getHeight()),e.setAttribute("x",0),e.setAttribute("y",0),e.setAttribute("id",0),e.style.fill=t,this._backgroundRoot.appendChild(e),this._backgroundNode=e},_paintList:function(t){this.gradientManager.markAllUnused(),this.clipPathManager.markAllUnused(),this.shadowManager.markAllUnused();var e,i,n=this._svgRoot,r=this._visibleList,o=t.length,a=[];for(e=0;e<o;e++){var s=t[e],l=(i=s)instanceof xa?zS:i instanceof Yn?ES:i instanceof Ba?NS:zS,u=rM(s)||nM(s);s.invisible||(s.__dirty&&(l&&l.brush(s),this.clipPathManager.update(s),s.style&&(this.gradientManager.update(s.style.fill),this.gradientManager.update(s.style.stroke),this.shadowManager.update(u,s)),s.__dirty=!1),a.push(s))}var h,c=function(t,e,i){return XS.diff(t,e,i)}(r,a);for(e=0;e<c.length;e++){if((p=c[e]).removed)for(var d=0;d<p.count;d++){u=rM(s=r[p.indices[d]]);var f=nM(s);iM(n,u),iM(n,f)}}for(e=0;e<c.length;e++){var p;if((p=c[e]).added)for(d=0;d<p.count;d++){u=rM(s=a[p.indices[d]]),f=nM(s);h?tM(n,u,h):eM(n,u),u?tM(n,f,u):h?tM(n,f,h):eM(n,f),tM(n,f,u),h=f||u||h,this.gradientManager.addWithoutUpdate(u||f,s),this.shadowManager.addWithoutUpdate(u||f,s),this.clipPathManager.markUsed(s)}else if(!p.removed)for(d=0;d<p.count;d++){u=rM(s=a[p.indices[d]]),f=nM(s),u=rM(s),f=nM(s);this.gradientManager.markUsed(s),this.gradientManager.addWithoutUpdate(u||f,s),this.shadowManager.markUsed(s),this.shadowManager.addWithoutUpdate(u||f,s),this.clipPathManager.markUsed(s),f&&tM(n,f,u),h=u||f||h}}this.gradientManager.removeUnused(),this.clipPathManager.removeUnused(),this.shadowManager.removeUnused(),this._visibleList=a},_getDefs:function(t){var n,e=this._svgDom;return 0!==(n=e.getElementsByTagName("defs")).length?n[0]:t?((n=e.insertBefore(yS("defs"),e.firstChild)).contains||(n.contains=function(t){var e=n.children;if(!e)return!1;for(var i=e.length-1;0<=i;--i)if(e[i]===t)return!0;return!1}),n):null},resize:function(t,e){var i=this._viewport;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!==t||this._height!==e){this._width=t,this._height=e;var r=i.style;r.width=t+"px",r.height=e+"px";var o=this._svgDom;o.setAttribute("width",t),o.setAttribute("height",e)}this._backgroundNode&&(this._backgroundNode.setAttribute("width",t),this._backgroundNode.setAttribute("height",e))},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],n=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[n]||QS(s[i])||QS(a.style[i]))-(QS(s[r])||0)-(QS(s[o])||0)|0},dispose:function(){this.root.innerHTML="",this._svgRoot=this._backgroundRoot=this._svgDom=this._backgroundNode=this._viewport=this.storage=null},clear:function(){this._viewport&&this.root.removeChild(this._viewport)},toDataURL:function(){return this.refresh(),"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent(this._svgDom.outerHTML.replace(/></g,">\n\r<"))}},D(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","pathToImage"],function(t){oM.prototype[t]=function(t){return function(){ci('In SVG mode painter not support method "'+t+'"')}}(t)}),Ir("svg",oM),t.version="4.9.0",t.dependencies={zrender:"4.3.2"},t.PRIORITY=gd,t.init=function(t,e,i){var n=tf(t);if(n)return n;var r=new wd(t,e,i);return r.id="ec_"+qd++,Yd[r.id]=r,Ur(t,Kd,r.id),function(n){var r="__connectUpdateStatus";function o(t,e){for(var i=0;i<t.length;i++){t[i][r]=e}}cd(Fd,function(t,e){n._messageCenter.on(e,function(t){if(jd[n.group]&&0!==n[r]){if(t&&t.escapeConnect)return;var e=n.makeActionFromEvent(t),i=[];cd(Yd,function(t){t!==n&&t.group===n.group&&i.push(t)}),o(i,0),cd(i,function(t){1!==t[r]&&t.dispatchAction(e)}),o(i,2)}})})}(r),r},t.connect=function(e){if(O(e)){var t=e;e=null,cd(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+$d++,cd(t,function(t){t.group=e})}return jd[e]=!0,e},t.disConnect=Qd,t.disconnect=Jd,t.dispose=function(t){"string"==typeof t?t=Yd[t]:t instanceof wd||(t=tf(t)),t instanceof wd&&!t.isDisposed()&&t.dispose()},t.getInstanceByDom=tf,t.getInstanceById=function(t){return Yd[t]},t.registerTheme=ef,t.registerPreprocessor=nf,t.registerProcessor=rf,t.registerPostUpdate=function(t){Gd.push(t)},t.registerAction=of,t.registerCoordinateSystem=function(t,e){ju.register(t,e)},t.getCoordinateSystemDimensions=function(t){var e=ju.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},t.registerLayout=af,t.registerVisual=sf,t.registerLoading=uf,t.extendComponentModel=hf,t.extendComponentView=cf,t.extendSeriesModel=df,t.extendChartView=ff,t.setCanvasCreator=function(t){f("createCanvas",t)},t.registerMap=function(t,e,i){sd(t,e,i)},t.getMap=function(t){var e=ld(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}},t.dataTool={},t.zrender=Tr,t.number=Fl,t.format=iu,t.throttle=mc,t.helper=rg,t.matrix=ue,t.vector=wt,t.color=Xe,t.parseGeoJSON=hg,t.parseGeoJson=Sg,t.util=Mg,t.graphic=Ig,t.List=kf,t.Model=_l,t.Axis=xg,t.env=v});
