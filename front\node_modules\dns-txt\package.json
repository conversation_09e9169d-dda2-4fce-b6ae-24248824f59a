{"_from": "dns-txt@^2.0.2", "_id": "dns-txt@2.0.2", "_inBundle": false, "_integrity": "sha512-Ix5PrWjphuSoUXV/Zv5gaFHjnaJtb02F2+Si3Ht9dyJ87+Z/lMmy+dpNHtTGraNK958ndXq2i+GLkWsWHcKaBQ==", "_location": "/dns-txt", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dns-txt@^2.0.2", "name": "dns-txt", "escapedName": "dns-txt", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/bonjour"], "_resolved": "https://registry.npmjs.org/dns-txt/-/dns-txt-2.0.2.tgz", "_shasum": "b91d806f5d27188e4ab3e7d107d881a1cc4642b6", "_spec": "dns-txt@^2.0.2", "_where": "C:\\code\\t\\t101\\front\\node_modules\\bonjour", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son"}, "bugs": {"url": "https://github.com/watson/dns-txt/issues"}, "bundleDependencies": false, "coordinates": [55.6465696, 12.5491067], "dependencies": {"buffer-indexof": "^1.0.0"}, "deprecated": false, "description": "Encode/decode DNS-SD TXT record RDATA fields", "devDependencies": {"standard": "^5.3.1", "tape": "^4.2.2"}, "homepage": "https://github.com/watson/dns-txt", "keywords": ["rfc6763", "6763", "rfc6762", "6762", "dns", "mdns", "multicast", "txt", "rdata", "dns-sd", "encode", "decode", "parse", "encoder", "decoder", "parser", "service", "discovery"], "license": "MIT", "main": "index.js", "name": "dns-txt", "repository": {"type": "git", "url": "git+https://github.com/watson/dns-txt.git"}, "scripts": {"test": "standard && tape test.js"}, "version": "2.0.2"}