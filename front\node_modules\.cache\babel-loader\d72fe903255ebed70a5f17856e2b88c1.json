{"remainingRequest": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\code\\t\\157\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\code\\t\\157\\front\\src\\views\\center.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\code\\t\\157\\front\\src\\views\\center.vue", "mtime": 1645616830441}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8g5pWw5a2X77yM6YKu5Lu277yM5omL5py677yMdXJs77yM6Lqr5Lu96K+B5qCh6aqMCmltcG9ydCB7IGlzTnVtYmVyLCBpc0ludE51bWVyLCBpc0VtYWlsLCBpc01vYmlsZSwgaXNQaG9uZSwgaXNVUkwsIGNoZWNrSWRDYXJkIH0gZnJvbSAiQC91dGlscy92YWxpZGF0ZSI7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcnVsZUZvcm06IHt9LAogICAgICBmbGFnOiAnJywKICAgICAgdXNlcnNGbGFnOiBmYWxzZSwKICAgICAgc2V4VHlwZXNPcHRpb25zOiBbXQogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvL+iOt+WPluW9k+WJjeeZu+W9leeUqOaIt+eahOS/oeaBrwogICAgdmFyIHRhYmxlID0gdGhpcy4kc3RvcmFnZS5nZXQoInNlc3Npb25UYWJsZSIpOwogICAgdGhpcy5zZXNzaW9uVGFibGUgPSB0aGlzLiRzdG9yYWdlLmdldCgic2Vzc2lvblRhYmxlIik7CiAgICB0aGlzLnJvbGUgPSB0aGlzLiRzdG9yYWdlLmdldCgicm9sZSIpOwogICAgaWYgKHRoaXMucm9sZSAhPSAi566h55CG5ZGYIikge30KICAgIHRoaXMuZmxhZyA9IHRhYmxlOwogICAgdGhpcy4kaHR0cCh7CiAgICAgIHVybDogYCR7dGhpcy4kc3RvcmFnZS5nZXQoInNlc3Npb25UYWJsZSIpfS9zZXNzaW9uYCwKICAgICAgbWV0aG9kOiAiZ2V0IgogICAgfSkudGhlbigoewogICAgICBkYXRhCiAgICB9KSA9PiB7CiAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgewogICAgICAgIHRoaXMucnVsZUZvcm0gPSBkYXRhLmRhdGE7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihkYXRhLm1zZyk7CiAgICAgIH0KICAgIH0pOwogICAgdGhpcy4kaHR0cCh7CiAgICAgIHVybDogYGRpY3Rpb25hcnkvcGFnZT9wYWdlPTEmbGltaXQ9MTAwJnNvcnQ9Jm9yZGVyPSZkaWNDb2RlPXNleF90eXBlc2AsCiAgICAgIG1ldGhvZDogImdldCIKICAgIH0pLnRoZW4oKHsKICAgICAgZGF0YQogICAgfSkgPT4gewogICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsKICAgICAgICB0aGlzLnNleFR5cGVzT3B0aW9ucyA9IGRhdGEuZGF0YS5saXN0OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZGF0YS5tc2cpOwogICAgICB9CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHlvbmdodVBob3RvVXBsb2FkQ2hhbmdlKGZpbGVVcmxzKSB7CiAgICAgIHRoaXMucnVsZUZvcm0ueW9uZ2h1UGhvdG8gPSBmaWxlVXJsczsKICAgIH0sCiAgICBvblVwZGF0ZUhhbmRsZXIoKSB7CiAgICAgIGlmICghdGhpcy5ydWxlRm9ybS55b25naHVOYW1lICYmICd5b25naHUnID09IHRoaXMuZmxhZykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WRmOW3peWnk+WQjeS4jeiDveS4uuepuicpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAoIXRoaXMucnVsZUZvcm0ueW9uZ2h1UGhvdG8gJiYgJ3lvbmdodScgPT0gdGhpcy5mbGFnKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5aS05YOP5LiN6IO95Li656m6Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICgneW9uZ2h1JyA9PSB0aGlzLmZsYWcgJiYgdGhpcy5ydWxlRm9ybS55b25naHVQaG9uZSAmJiAhaXNNb2JpbGUodGhpcy5ydWxlRm9ybS55b25naHVQaG9uZSkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDmiYvmnLrlupTovpPlhaXmiYvmnLrmoLzlvI9gKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKCF0aGlzLnJ1bGVGb3JtLnlvbmdodUlkTnVtYmVyICYmICd5b25naHUnID09IHRoaXMuZmxhZykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WRmOW3pei6q+S7veivgeWPt+S4jeiDveS4uuepuicpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAoJ3lvbmdodScgPT0gdGhpcy5mbGFnICYmIHRoaXMucnVsZUZvcm0ueW9uZ2h1RW1haWwgJiYgIWlzRW1haWwodGhpcy5ydWxlRm9ybS55b25naHVFbWFpbCkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDpgq7nrrHlupTovpPlhaXpgq7nrrHmoLzlvI9gKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKCF0aGlzLnJ1bGVGb3JtLnNleFR5cGVzICYmIHRoaXMuZmxhZyAhPSAndXNlcnMnKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5oCn5Yir5LiN6IO95Li656m6Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICgndXNlcnMnID09IHRoaXMuZmxhZyAmJiB0aGlzLnJ1bGVGb3JtLnVzZXJuYW1lLnRyaW0oKS5sZW5ndGggPCAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg55So5oi35ZCN5LiN6IO95Li656m6YCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJGh0dHAoewogICAgICAgIHVybDogYCR7dGhpcy4kc3RvcmFnZS5nZXQoInNlc3Npb25UYWJsZSIpfS91cGRhdGVgLAogICAgICAgIG1ldGhvZDogInBvc3QiLAogICAgICAgIGRhdGE6IHRoaXMucnVsZUZvcm0KICAgICAgfSkudGhlbigoewogICAgICAgIGRhdGEKICAgICAgfSkgPT4gewogICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIG1lc3NhZ2U6ICLkv67mlLnkv6Hmga/miJDlip8iLAogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgIGR1cmF0aW9uOiAxNTAwLAogICAgICAgICAgICBvbkNsb3NlOiAoKSA9PiB7fQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZGF0YS5tc2cpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["isNumber", "isIntNumer", "isEmail", "isMobile", "isPhone", "isURL", "checkIdCard", "data", "ruleForm", "flag", "usersFlag", "sexTypesOptions", "mounted", "table", "$storage", "get", "sessionTable", "role", "$http", "url", "method", "then", "code", "$message", "error", "msg", "list", "methods", "yonghuPhotoUploadChange", "fileUrls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onUpdateHandler", "yo<PERSON><PERSON><PERSON><PERSON>", "yonghuPhone", "yonghuIdNumber", "yonghuEmail", "sexTypes", "username", "trim", "length", "message", "type", "duration", "onClose"], "sources": ["src/views/center.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      class=\"detail-form-content\"\r\n      ref=\"ruleForm\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >  \r\n     <el-row>\r\n                    <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='员工姓名' prop=\"yonghuName\">\r\n               <el-input v-model=\"ruleForm.yonghuName\"  placeholder='员工姓名' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n             <el-form-item v-if=\"flag=='yonghu'\" label='头像' prop=\"yonghuPhoto\">\r\n                 <file-upload\r\n                         tip=\"点击上传照片\"\r\n                         action=\"file/upload\"\r\n                         :limit=\"3\"\r\n                         :multiple=\"true\"\r\n                         :fileUrls=\"ruleForm.yonghuPhoto?ruleForm.yonghuPhoto:''\"\r\n                         @change=\"yonghuPhotoUploadChange\"\r\n                 ></file-upload>\r\n             </el-form-item>\r\n         </el-col>\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='员工手机号' prop=\"yonghuPhone\">\r\n               <el-input v-model=\"ruleForm.yonghuPhone\"  placeholder='员工手机号' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='员工身份证号' prop=\"yonghuIdNumber\">\r\n               <el-input v-model=\"ruleForm.yonghuIdNumber\"  placeholder='员工身份证号' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-col :span=\"12\">\r\n           <el-form-item v-if=\"flag=='yonghu'\"  label='邮箱' prop=\"yonghuEmail\">\r\n               <el-input v-model=\"ruleForm.yonghuEmail\"  placeholder='邮箱' clearable></el-input>\r\n           </el-form-item>\r\n         </el-col>\r\n\r\n         <el-form-item v-if=\"flag=='users'\" label=\"用户名\" prop=\"username\">\r\n             <el-input v-model=\"ruleForm.username\"\r\n                       placeholder=\"用户名\"></el-input>\r\n         </el-form-item>\r\n         <el-col :span=\"12\">\r\n             <el-form-item v-if=\"flag!='users'\"  label=\"性别\" prop=\"sexTypes\">\r\n                 <el-select v-model=\"ruleForm.sexTypes\" placeholder=\"请选择性别\">\r\n                     <el-option\r\n                             v-for=\"(item,index) in sexTypesOptions\"\r\n                             v-bind:key=\"item.codeIndex\"\r\n                             :label=\"item.indexName\"\r\n                             :value=\"item.codeIndex\">\r\n                     </el-option>\r\n                 </el-select>\r\n             </el-form-item>\r\n         </el-col>\r\n         <el-col :span=\"24\">\r\n             <el-form-item>\r\n                 <el-button type=\"primary\" @click=\"onUpdateHandler\">修 改</el-button>\r\n             </el-form-item>\r\n         </el-col>\r\n     </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      ruleForm: {},\r\n      flag: '',\r\n      usersFlag: false,\r\n      sexTypesOptions : [],\r\n    };\r\n  },\r\n  mounted() {\r\n    //获取当前登录用户的信息\r\n    var table = this.$storage.get(\"sessionTable\");\r\n    this.sessionTable = this.$storage.get(\"sessionTable\");\r\n    this.role = this.$storage.get(\"role\");\r\n    if (this.role != \"管理员\"){\r\n    }\r\n\r\n    this.flag = table;\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n      this.$http({\r\n          url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=sex_types`,\r\n          method: \"get\"\r\n      }).then(({ data }) => {\r\n          if (data && data.code === 0) {\r\n          this.sexTypesOptions = data.data.list;\r\n      } else {\r\n          this.$message.error(data.msg);\r\n      }\r\n  });\r\n  },\r\n  methods: {\r\n    yonghuPhotoUploadChange(fileUrls) {\r\n        this.ruleForm.yonghuPhoto = fileUrls;\r\n    },\r\n\r\n    onUpdateHandler() {\r\n                         if((!this.ruleForm.yonghuName)&& 'yonghu'==this.flag){\r\n                             this.$message.error('员工姓名不能为空');\r\n                             return\r\n                         }\r\n\r\n                         if((!this.ruleForm.yonghuPhoto)&& 'yonghu'==this.flag){\r\n                             this.$message.error('头像不能为空');\r\n                             return\r\n                         }\r\n\r\n                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuPhone&&(!isMobile(this.ruleForm.yonghuPhone))){\r\n                                 this.$message.error(`手机应输入手机格式`);\r\n                                 return\r\n                             }\r\n                         if((!this.ruleForm.yonghuIdNumber)&& 'yonghu'==this.flag){\r\n                             this.$message.error('员工身份证号不能为空');\r\n                             return\r\n                         }\r\n\r\n                             if( 'yonghu' ==this.flag && this.ruleForm.yonghuEmail&&(!isEmail(this.ruleForm.yonghuEmail))){\r\n                                 this.$message.error(`邮箱应输入邮箱格式`);\r\n                                 return\r\n                             }\r\n        if((!this.ruleForm.sexTypes)&& this.flag !='users'){\r\n            this.$message.error('性别不能为空');\r\n            return\r\n        }\r\n      if('users'==this.flag && this.ruleForm.username.trim().length<1) {\r\n        this.$message.error(`用户名不能为空`);\r\n        return\t\r\n      }\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: this.ruleForm\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"修改信息成功\",\r\n            type: \"success\",\r\n            duration: 1500,\r\n            onClose: () => {\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"], "mappings": "AAuEA;AACA,SAAAA,QAAA,EAAAC,UAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,KAAA,EAAAC,WAAA;AAEA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,SAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,IAAAC,KAAA,QAAAC,QAAA,CAAAC,GAAA;IACA,KAAAC,YAAA,QAAAF,QAAA,CAAAC,GAAA;IACA,KAAAE,IAAA,QAAAH,QAAA,CAAAC,GAAA;IACA,SAAAE,IAAA,YACA;IAEA,KAAAR,IAAA,GAAAI,KAAA;IACA,KAAAK,KAAA;MACAC,GAAA,UAAAL,QAAA,CAAAC,GAAA;MACAK,MAAA;IACA,GAAAC,IAAA;MAAAd;IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAe,IAAA;QACA,KAAAd,QAAA,GAAAD,IAAA,CAAAA,IAAA;MACA;QACA,KAAAgB,QAAA,CAAAC,KAAA,CAAAjB,IAAA,CAAAkB,GAAA;MACA;IACA;IACA,KAAAP,KAAA;MACAC,GAAA;MACAC,MAAA;IACA,GAAAC,IAAA;MAAAd;IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAe,IAAA;QACA,KAAAX,eAAA,GAAAJ,IAAA,CAAAA,IAAA,CAAAmB,IAAA;MACA;QACA,KAAAH,QAAA,CAAAC,KAAA,CAAAjB,IAAA,CAAAkB,GAAA;MACA;IACA;EACA;EACAE,OAAA;IACAC,wBAAAC,QAAA;MACA,KAAArB,QAAA,CAAAsB,WAAA,GAAAD,QAAA;IACA;IAEAE,gBAAA;MACA,UAAAvB,QAAA,CAAAwB,UAAA,qBAAAvB,IAAA;QACA,KAAAc,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,UAAAhB,QAAA,CAAAsB,WAAA,qBAAArB,IAAA;QACA,KAAAc,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,qBAAAf,IAAA,SAAAD,QAAA,CAAAyB,WAAA,KAAA9B,QAAA,MAAAK,QAAA,CAAAyB,WAAA;QACA,KAAAV,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAhB,QAAA,CAAA0B,cAAA,qBAAAzB,IAAA;QACA,KAAAc,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,qBAAAf,IAAA,SAAAD,QAAA,CAAA2B,WAAA,KAAAjC,OAAA,MAAAM,QAAA,CAAA2B,WAAA;QACA,KAAAZ,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAhB,QAAA,CAAA4B,QAAA,SAAA3B,IAAA;QACA,KAAAc,QAAA,CAAAC,KAAA;QACA;MACA;MACA,oBAAAf,IAAA,SAAAD,QAAA,CAAA6B,QAAA,CAAAC,IAAA,GAAAC,MAAA;QACA,KAAAhB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAN,KAAA;QACAC,GAAA,UAAAL,QAAA,CAAAC,GAAA;QACAK,MAAA;QACAb,IAAA,OAAAC;MACA,GAAAa,IAAA;QAAAd;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAe,IAAA;UACA,KAAAC,QAAA;YACAiB,OAAA;YACAC,IAAA;YACAC,QAAA;YACAC,OAAA,EAAAA,CAAA,MACA;UACA;QACA;UACA,KAAApB,QAAA,CAAAC,KAAA,CAAAjB,IAAA,CAAAkB,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}