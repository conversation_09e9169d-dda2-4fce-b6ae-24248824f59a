# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## v1.0.0 - 2024-03-04

### Commits

- Initial implementation, tests, readme, types [`8b94518`](https://github.com/ljharb/data-view-byte-offset/commit/8b94518cd2a87df3084cdf60b52f70d9f65b94b6)
- Initial commit [`aee2acc`](https://github.com/ljharb/data-view-byte-offset/commit/aee2accbbefcd5645693f4587ce2eabde166b1a0)
- npm init [`10a21a4`](https://github.com/ljharb/data-view-byte-offset/commit/10a21a4189c51a3add252e3f76fe31a0b5bdcfc1)
- Only apps should have lockfiles [`f6cfa3e`](https://github.com/ljharb/data-view-byte-offset/commit/f6cfa3e917d58c2e130f9383f5e04f5d5069d0e6)
