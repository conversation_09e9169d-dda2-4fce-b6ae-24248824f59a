!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.csso=e()}}(function(){return function e(t,n,r){function o(a,s){if(!n[a]){if(!t[a]){var l="function"==typeof require&&require;if(!s&&l)return l(a,!0);if(i)return i(a,!0);var c=new Error("Cannot find module '"+a+"'");throw c.code="MODULE_NOT_FOUND",c}var u=n[a]={exports:{}};t[a][0].call(u.exports,function(e){var n=t[a][1][e];return o(n||e)},u,u.exports,e,t,n,r)}return n[a].exports}for(var i="function"==typeof require&&require,a=0;a<r.length;a++)o(r[a]);return o}({1:[function(e,t,n){var r=e("css-tree").keyword;t.exports=function(e,t,n){if(e.block&&(null!==this.stylesheet&&(this.stylesheet.firstAtrulesAllowed=!1),e.block.children.isEmpty()))return void n.remove(t);switch(e.name){case"charset":if(!e.prelude||e.prelude.children.isEmpty())return void n.remove(t);if(t.prev)return void n.remove(t);break;case"import":if(null===this.stylesheet||!this.stylesheet.firstAtrulesAllowed)return void n.remove(t);n.prevUntil(t.prev,function(e){if("Atrule"!==e.type||"import"!==e.name&&"charset"!==e.name)return this.root.firstAtrulesAllowed=!1,n.remove(t),!0},this);break;default:var o=r(e.name).basename;"keyframes"!==o&&"media"!==o&&"supports"!==o||e.prelude&&!e.prelude.children.isEmpty()||n.remove(t)}}},{"css-tree":44}],2:[function(e,t,n){t.exports=function(e,t,n){n.remove(t)}},{}],3:[function(e,t,n){t.exports=function(e,t,n){e.value.children&&e.value.children.isEmpty()&&n.remove(t)}},{}],4:[function(e,t,n){t.exports=function(e,t,n){"+"!==e.value&&"-"!==e.value&&(null!==t.prev&&"WhiteSpace"===t.prev.data.type&&n.remove(t.prev),null!==t.next&&"WhiteSpace"===t.next.data.type&&n.remove(t.next))}},{}],5:[function(e,t,n){function r(e,t){return e.children.each(function(n,a,s){var l=!1;i(n,function(n){if(null===this.selector||this.selector===e)switch(n.type){case"SelectorList":null!==this.function&&"not"===this.function.name.toLowerCase()||r(n,t)&&(l=!0);break;case"ClassSelector":null===t.whitelist||null===t.whitelist.classes||o.call(t.whitelist.classes,n.name)||(l=!0),null!==t.blacklist&&null!==t.blacklist.classes&&o.call(t.blacklist.classes,n.name)&&(l=!0);break;case"IdSelector":null===t.whitelist||null===t.whitelist.ids||o.call(t.whitelist.ids,n.name)||(l=!0),null!==t.blacklist&&null!==t.blacklist.ids&&o.call(t.blacklist.ids,n.name)&&(l=!0);break;case"TypeSelector":"*"!==n.name.charAt(n.name.length-1)&&(null===t.whitelist||null===t.whitelist.tags||o.call(t.whitelist.tags,n.name.toLowerCase())||(l=!0),null!==t.blacklist&&null!==t.blacklist.tags&&o.call(t.blacklist.tags,n.name.toLowerCase())&&(l=!0))}}),l&&s.remove(a)}),e.children.isEmpty()}var o=Object.prototype.hasOwnProperty,i=e("css-tree").walk;t.exports=function(e,t,n,o){var i=o.usage;!i||null===i.whitelist&&null===i.blacklist||r(e.prelude,i),(e.prelude.children.isEmpty()||e.block.children.isEmpty())&&n.remove(t)}},{"css-tree":44}],6:[function(e,t,n){t.exports=function(e,t,n){if("*"===t.data.name){var r=t.next&&t.next.data.type;"IdSelector"!==r&&"ClassSelector"!==r&&"AttributeSelector"!==r&&"PseudoClassSelector"!==r&&"PseudoElementSelector"!==r||n.remove(t)}}},{}],7:[function(e,t,n){t.exports=function(e,t,n){return null===t.next||null===t.prev?void n.remove(t):"WhiteSpace"===t.prev.data.type?void n.remove(t):null!==this.stylesheet&&this.stylesheet.children===n||null!==this.block&&this.block.children===n?void n.remove(t):void 0}},{}],8:[function(e,t,n){var r=e("css-tree").walk,o={Atrule:e("./Atrule"),Rule:e("./Rule"),Declaration:e("./Declaration"),TypeSelector:e("./TypeSelector"),Comment:e("./Comment"),Operator:e("./Operator"),WhiteSpace:e("./WhiteSpace")};t.exports=function(e,t){r(e,{leave:function(e,n,r){o.hasOwnProperty(e.type)&&o[e.type].call(this,e,n,r,t)}})}},{"./Atrule":1,"./Comment":2,"./Declaration":3,"./Operator":4,"./Rule":5,"./TypeSelector":6,"./WhiteSpace":7,"css-tree":44}],9:[function(e,t,n){function r(e,t){var n,r=new l,o=!1;return e.nextUntil(e.head,function(e,i,a){if("Comment"===e.type)return t&&"!"===e.value.charAt(0)?!(!o&&!n)||(a.remove(i),void(n=e)):void a.remove(i);"WhiteSpace"!==e.type&&(o=!0),r.insert(a.remove(i))}),{comment:n,stylesheet:{type:"StyleSheet",loc:null,children:r}}}function o(e,t,n,r){r.logger("Compress block #"+n,null,!0);var o=1;return"StyleSheet"===e.type&&(e.firstAtrulesAllowed=t,e.id=o++),f(e,{visit:"Atrule",enter:function(e){null!==e.block&&(e.block.id=o++)}}),r.logger("init",e),h(e,r),r.logger("clean",e),p(e,r),r.logger("replace",e),r.restructuring&&d(e,r),e}function i(e){var t="comments"in e?e.comments:"exclamation";return"boolean"==typeof t?t=!!t&&"exclamation":"exclamation"!==t&&"first-exclamation"!==t&&(t=!1),t}function a(e){return"restructure"in e?e.restructure:!("restructuring"in e)||e.restructuring}function s(e){return(new l).appendData({type:"Rule",loc:null,prelude:{type:"SelectorList",loc:null,children:(new l).appendData({type:"Selector",loc:null,children:(new l).appendData({type:"TypeSelector",loc:null,name:"x"})})},block:e})}var l=e("css-tree").List,c=e("css-tree").clone,u=e("./usage"),h=e("./clean"),p=e("./replace"),d=e("./restructure"),f=e("css-tree").walk;t.exports=function(e,t){e=e||{type:"StyleSheet",loc:null,children:new l},t=t||{};var n,h,p,d={logger:"function"==typeof t.logger?t.logger:function(){},restructuring:a(t),forceMediaMerge:Boolean(t.forceMediaMerge),usage:!!t.usage&&u.buildIndex(t.usage)},f=i(t),m=!0,g=new l,b=1;t.clone&&(e=c(e)),"StyleSheet"===e.type?(n=e.children,e.children=g):n=s(e);do{if(h=r(n,Boolean(f)),o(h.stylesheet,m,b++,d),p=h.stylesheet.children,h.comment&&(g.isEmpty()||g.insert(l.createItem({type:"Raw",value:"\n"})),g.insert(l.createItem(h.comment)),p.isEmpty()||g.insert(l.createItem({type:"Raw",value:"\n"}))),m&&!p.isEmpty()){var y=p.last();("Atrule"!==y.type||"import"!==y.name&&"charset"!==y.name)&&(m=!1)}"exclamation"!==f&&(f=!1),g.appendList(p)}while(!n.isEmpty());return{ast:e}}},{"./clean":8,"./replace":21,"./restructure":33,"./usage":39,"css-tree":44}],10:[function(e,t,n){function r(e,t,n,r){return t.debug&&console.error("## "+e+" done in %d ms\n",Date.now()-n),r}function o(e){var t;return function(n,r){var o=n;if(r&&(o="["+((Date.now()-t)/1e3).toFixed(3)+"s] "+o),e>1&&r){var i=f(r);2===e&&i.length>256&&(i=i.substr(0,256)+"..."),o+="\n  "+i+"\n"}console.error(o),t=Date.now()}}function i(e){var t={};for(var n in e)t[n]=e[n];return t}function a(e){return e=i(e),"function"!=typeof e.logger&&e.debug&&(e.logger=o(e.debug)),e}function s(e,t,n){Array.isArray(n)||(n=[n]),n.forEach(function(n){n(e,t)})}function l(e,t,n){n=n||{};var o=n.filename||"<unknown>",i=r("parsing",n,Date.now(),p(t,{context:e,filename:o,positions:Boolean(n.sourceMap)}));n.beforeCompress&&r("beforeCompress",n,Date.now(),s(i,n,n.beforeCompress));var l=r("compress",n,Date.now(),d(i,a(n)));return n.afterCompress&&r("afterCompress",n,Date.now(),s(l,n,n.afterCompress)),n.sourceMap?r("generate(sourceMap: true)",n,Date.now(),function(){var e=f(l.ast,{sourceMap:!0});return e.map._file=o,e.map.setSourceContent(o,t),e}()):r("generate",n,Date.now(),{css:f(l.ast),map:null})}function c(e,t){return l("stylesheet",e,t)}function u(e,t){return l("declarationList",e,t)}var h=e("css-tree"),p=h.parse,d=e("./compress"),f=h.generate;t.exports={version:e("../package.json").version,minify:c,minifyBlock:u,compress:d,syntax:h}},{"../package.json":158,"./compress":9,"css-tree":44}],11:[function(e,t,n){var r=e("css-tree").keyword,o=e("./atrule/keyframes");t.exports=function(e){"keyframes"===r(e.name).basename&&o(e)}},{"./atrule/keyframes":19,"css-tree":44}],12:[function(e,t,n){function r(e){if(""!==e&&"-"!==e)return e=e.replace(o,"a"),!i.test(e)}var o=/\\([0-9A-Fa-f]{1,6})(\r\n|[ \t\n\f\r])?|\\./g,i=/^(-?\d|--)|[\u0000-\u002c\u002e\u002f\u003A-\u0040\u005B-\u005E\u0060\u007B-\u009f]/;t.exports=function(e){var t=e.value;if(t&&"String"===t.type){var n=t.value.replace(/^(.)(.*)\1$/,"$2");r(n)&&(e.value={type:"Identifier",loc:t.loc,name:n})}}},{}],13:[function(e,t,n){var r=e("./Number").pack,o={px:!0,mm:!0,cm:!0,in:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vh:!0,vw:!0,vmin:!0,vmax:!0,vm:!0};t.exports=function(e,t){var n=r(e.value,t);if(e.value=n,"0"===n&&null!==this.declaration&&null===this.atrulePrelude){var i=e.unit.toLowerCase();if(!o.hasOwnProperty(i))return;if("-ms-flex"===this.declaration.property||"flex"===this.declaration.property)return;if(this.function&&"calc"===this.function.name)return;t.data={type:"Number",loc:e.loc,value:n}}}},{"./Number":14}],14:[function(e,t,n){function r(e,t){var n=t&&null!==t.prev&&a.hasOwnProperty(t.prev.data.type)?i:o;return e=String(e).replace(n,"$1$2$3"),""!==e&&"-"!==e||(e="0"),e}var o=/^(?:\+|(-))?0*(\d*)(?:\.0*|(\.\d*?)0*)?$/,i=/^([\+\-])?0*(\d*)(?:\.0*|(\.\d*?)0*)?$/,a={Dimension:!0,HexColor:!0,Identifier:!0,Number:!0,Raw:!0,UnicodeRange:!0};t.exports=function(e,t){e.value=r(e.value,t)},t.exports.pack=r},{}],15:[function(e,t,n){var r=e("./Number").pack,o={margin:!0,"margin-top":!0,"margin-left":!0,"margin-bottom":!0,"margin-right":!0,padding:!0,"padding-top":!0,"padding-left":!0,"padding-bottom":!0,"padding-right":!0,top:!0,left:!0,bottom:!0,right:!0,"background-position":!0,"background-position-x":!0,"background-position-y":!0,"background-size":!0,border:!0,"border-width":!0,"border-top-width":!0,"border-left-width":!0,"border-bottom-width":!0,"border-right-width":!0,"border-image-width":!0,"border-radius":!0,"border-bottom-left-radius":!0,"border-bottom-right-radius":!0,"border-top-left-radius":!0,"border-top-right-radius":!0};t.exports=function(e,t){var n=r(e.value,t),i=null!==this.declaration?this.declaration.property:null;e.value=n,null!==i&&o.hasOwnProperty(i)&&"0"===n&&(t.data={type:"Number",loc:e.loc,value:n})}},{"./Number":14}],16:[function(e,t,n){t.exports=function(e){var t=e.value;t=t.replace(/\\(\r\n|\r|\n|\f)/g,""),e.value=t}},{}],17:[function(e,t,n){var r=new RegExp("^((\\\\[0-9a-f]{1,6}(\\r\\n|[ \\n\\r\\t\\f])?|\\\\[^\\n\\r\\f0-9a-fA-F])|[^\"'\\(\\)\\\\\\s\0\b\v-])*$","i");t.exports=function(e){var t=e.value;if("String"===t.type){var n=t.value[0],o=t.value.substr(1,t.value.length-2);o=o.replace(/\\\\/g,"/"),r.test(o)?e.value={type:"Raw",loc:e.value.loc,value:o}:e.value.value=-1===o.indexOf('"')?'"'+o+'"':n+o+n}}},{}],18:[function(e,t,n){var r=e("css-tree").property,o={font:e("./property/font"),"font-weight":e("./property/font-weight"),background:e("./property/background"),border:e("./property/border"),outline:e("./property/border")};t.exports=function(e){if(this.declaration){var t=r(this.declaration.property);o.hasOwnProperty(t.basename)&&o[t.basename](e)}}},{"./property/background":22,"./property/border":23,"./property/font":25,"./property/font-weight":24,"css-tree":44}],19:[function(e,t,n){t.exports=function(e){e.block.children.each(function(e){e.prelude.children.each(function(e){e.children.each(function(e,t){"Percentage"===e.type&&"100"===e.value?t.data={type:"TypeSelector",loc:e.loc,name:"to"}:"TypeSelector"===e.type&&"from"===e.name&&(t.data={type:"Percentage",loc:e.loc,value:"0"})})})})}},{}],20:[function(e,t,n){function r(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function o(e,t,n,o){var i,a,s;if(0===t)i=a=s=n;else{var l=n<.5?n*(1+t):n+t-n*t,c=2*n-l;i=r(c,l,e+1/3),a=r(c,l,e),s=r(c,l,e-1/3)}return[Math.round(255*i),Math.round(255*a),Math.round(255*s),o]}function i(e){return e=e.toString(16),1===e.length?"0"+e:e}function a(e,t,n){for(var r=e.head,o=[],i=!1;null!==r;){var a=r.data,s=a.type;switch(s){case"Number":case"Percentage":if(i)return;i=!0,o.push({type:s,value:Number(a.value)});break;case"Operator":if(","===a.value){if(!i)return;i=!1}else if(i||"+"!==a.value)return;break;default:return}r=r.next}if(o.length===t){if(4===o.length){if("Number"!==o[3].type)return;o[3].type="Alpha"}if(n){if(o[0].type!==o[1].type||o[0].type!==o[2].type)return}else{if("Number"!==o[0].type||"Percentage"!==o[1].type||"Percentage"!==o[2].type)return;o[0].type="Angle"}return o.map(function(e){var t=Math.max(0,e.value);switch(e.type){case"Number":t=Math.min(t,255);break;case"Percentage":if(t=Math.min(t,100)/100,!n)return t;t*=255;break;case"Angle":return(t%360+360)%360/360;case"Alpha":return Math.min(t,1)}return Math.round(t)})}}function s(e,t,n){var r,s=e.name;if("rgba"===s||"hsla"===s){if(!(r=a(e.children,4,"rgba"===s)))return;if("hsla"===s&&(r=o.apply(null,r),e.name="rgba"),0===r[3]){var l=this.function&&this.function.name;if(0===r[0]&&0===r[1]&&0===r[2]||!/^(?:to|from|color-stop)$|gradient$/i.test(l))return void(t.data={type:"Identifier",loc:e.loc,name:"transparent"})}if(1!==r[3])return void e.children.each(function(e,t,n){if("Operator"===e.type)return void(","!==e.value&&n.remove(t));t.data={type:"Number",loc:e.loc,value:h(r.shift(),null)}});s="rgb"}if("hsl"===s){if(!(r=r||a(e.children,3,!1)))return;r=o.apply(null,r),s="rgb"}if("rgb"===s){if(!(r=r||a(e.children,3,!0)))return;var u=t.next;u&&"WhiteSpace"!==u.data.type&&n.insert(n.createItem({type:"WhiteSpace",value:" "}),u),t.data={type:"HexColor",loc:e.loc,value:i(r[0])+i(r[1])+i(r[2])},c(t.data,t)}}function l(e,t){if(null!==this.declaration){var n=e.name.toLowerCase();if(p.hasOwnProperty(n)&&u.matchDeclaration(this.declaration).isType(e,"color")){var r=p[n];r.length+1<=n.length?t.data={type:"HexColor",loc:e.loc,value:r}:("grey"===n&&(n="gray"),e.name=n)}}}function c(e,t){var n=e.value.toLowerCase();6===n.length&&n[0]===n[1]&&n[2]===n[3]&&n[4]===n[5]&&(n=n[0]+n[2]+n[4]),d[n]?t.data={type:"Identifier",loc:e.loc,name:d[n]}:e.value=n}var u=e("css-tree").lexer,h=e("./Number").pack,p={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgrey:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",grey:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},d={800000:"maroon",800080:"purple",808000:"olive",808080:"gray","00ffff":"cyan",f0ffff:"azure",f5f5dc:"beige",ffe4c4:"bisque","000000":"black","0000ff":"blue",a52a2a:"brown",ff7f50:"coral",ffd700:"gold","008000":"green","4b0082":"indigo",fffff0:"ivory",f0e68c:"khaki","00ff00":"lime",faf0e6:"linen","000080":"navy",ffa500:"orange",da70d6:"orchid",cd853f:"peru",ffc0cb:"pink",dda0dd:"plum",f00:"red",ff0000:"red",fa8072:"salmon",a0522d:"sienna",c0c0c0:"silver",fffafa:"snow",d2b48c:"tan","008080":"teal",ff6347:"tomato",ee82ee:"violet",f5deb3:"wheat",ffffff:"white",ffff00:"yellow"};t.exports={compressFunction:s,compressIdent:l,compressHex:c}},{"./Number":14,"css-tree":44}],21:[function(e,t,n){var r=e("css-tree").walk,o={Atrule:e("./Atrule"),AttributeSelector:e("./AttributeSelector"),Value:e("./Value"),Dimension:e("./Dimension"),Percentage:e("./Percentage"),Number:e("./Number"),String:e("./String"),Url:e("./Url"),HexColor:e("./color").compressHex,Identifier:e("./color").compressIdent,Function:e("./color").compressFunction};t.exports=function(e){r(e,{leave:function(e,t,n){o.hasOwnProperty(e.type)&&o[e.type].call(this,e,t,n)}})}},{"./Atrule":11,"./AttributeSelector":12,"./Dimension":13,"./Number":14,"./Percentage":15,"./String":16,"./Url":17,"./Value":18,"./color":20,"css-tree":44}],22:[function(e,t,n){var r=e("css-tree").List;t.exports=function(e){function t(){if(i.length)return i[i.length-1].type}function n(){"WhiteSpace"===t()&&i.pop(),i.length||i.unshift({type:"Number",loc:null,value:"0"},{type:"WhiteSpace",value:" "},{type:"Number",loc:null,value:"0"}),o.push.apply(o,i),i=[]}var o=[],i=[];e.children.each(function(e){if("Operator"===e.type&&","===e.value)return n(),void o.push(e);("Identifier"!==e.type||"transparent"!==e.name&&"none"!==e.name&&"repeat"!==e.name&&"scroll"!==e.name)&&("WhiteSpace"!==e.type||i.length&&"WhiteSpace"!==t())&&i.push(e)}),n(),e.children=(new r).fromArray(o)}},{"css-tree":44}],23:[function(e,t,n){function r(e,t){var n=t.prev,r=t.next;null!==r?"WhiteSpace"!==r.data.type||null!==n&&"WhiteSpace"!==n.data.type||e.remove(r):null!==n&&"WhiteSpace"===n.data.type&&e.remove(n),e.remove(t)}t.exports=function(e){e.children.each(function(e,t,n){"Identifier"===e.type&&"none"===e.name.toLowerCase()&&(n.head===n.tail?t.data={type:"Number",loc:e.loc,value:"0"}:r(n,t))})}},{}],24:[function(e,t,n){t.exports=function(e){var t=e.children.head.data;if("Identifier"===t.type)switch(t.name){case"normal":e.children.head.data={type:"Number",loc:t.loc,value:"400"};break;case"bold":e.children.head.data={type:"Number",loc:t.loc,value:"700"}}}},{}],25:[function(e,t,n){t.exports=function(e){var t=e.children;t.eachRight(function(e,t){if("Identifier"===e.type)if("bold"===e.name)t.data={type:"Number",loc:e.loc,value:"700"};else if("normal"===e.name){var n=t.prev;n&&"Operator"===n.data.type&&"/"===n.data.value&&this.remove(n),this.remove(t)}else if("medium"===e.name){var r=t.next;r&&"Operator"===r.data.type||this.remove(t)}}),t.each(function(e,t){"WhiteSpace"===e.type&&(t.prev&&t.next&&"WhiteSpace"!==t.next.data.type||this.remove(t))}),t.isEmpty()&&t.insert(t.createItem({type:"Identifier",name:"normal"}))}},{}],26:[function(e,t,n){function r(e,t,n,r){var o=t.data,i=l(o.name).basename,a=o.name.toLowerCase()+"/"+(o.prelude?o.prelude.id:null);c.call(e,i)||(e[i]=Object.create(null)),r&&delete e[i][a],c.call(e[i],a)||(e[i][a]=new s),e[i][a].append(n.remove(t))}function o(e,t){var n=Object.create(null),o=null;e.children.each(function(e,i,a){if("Atrule"===e.type){var s=l(e.name).basename;switch(s){case"keyframes":return void r(n,i,a,!0);case"media":if(t.forceMediaMerge)return void r(n,i,a,!1)}null===o&&"charset"!==s&&"import"!==s&&(o=i)}else null===o&&(o=i)});for(var i in n)for(var a in n[i])e.children.insertList(n[i][a],"media"===i?null:o)}function i(e){return"Atrule"===e.type&&"media"===e.name}function a(e,t,n){if(i(e)){var r=t.prev&&t.prev.data;r&&i(r)&&e.prelude&&r.prelude&&e.prelude.id===r.prelude.id&&(r.block.children.appendList(e.block.children),n.remove(t))}}var s=e("css-tree").List,l=e("css-tree").keyword,c=Object.prototype.hasOwnProperty,u=e("css-tree").walk;t.exports=function(e,t){o(e,t),u(e,{visit:"Atrule",reverse:!0,enter:a})}},{"css-tree":44}],27:[function(e,t,n){function r(e,t,n){var r=e.prelude.children,o=e.block.children;n.prevUntil(t.prev,function(a){if("Rule"!==a.type)return i.unsafeToSkipNode.call(r,a);var s=a.prelude.children,l=a.block.children;if(e.pseudoSignature===a.pseudoSignature){if(i.isEqualSelectors(s,r))return l.appendList(o),n.remove(t),!0;if(i.isEqualDeclarations(o,l))return i.addSelectors(s,r),n.remove(t),!0}return i.hasSimilarSelectors(r,s)})}var o=e("css-tree").walk,i=e("./utils");t.exports=function(e){o(e,{visit:"Rule",enter:r})}},{"./utils":38,"css-tree":44}],28:[function(e,t,n){function r(e,t,n){for(var r=e.prelude.children;r.head!==r.tail;){var i=new o;i.insert(r.remove(r.head)),n.insert(n.createItem({type:"Rule",loc:e.loc,prelude:{type:"SelectorList",loc:e.prelude.loc,children:i},block:{type:"Block",loc:e.block.loc,children:e.block.children.copy()},pseudoSignature:e.pseudoSignature}),t)}}var o=e("css-tree").List,i=e("css-tree").walk;t.exports=function(e){i(e,{visit:"Rule",reverse:!0,enter:r})}},{"css-tree":44}],29:[function(e,t,n){function r(e){this.name=e,this.loc=null,this.iehack=void 0,this.sides={top:null,right:null,bottom:null,left:null}}function o(e,t,n,o){var i=e.block.children,a=e.prelude.children.first().id;return e.block.children.eachRight(function(e,s){var l=e.property;if(b.hasOwnProperty(l)){var h,p,d=b[l];if(o&&a!==o||d in t&&(p=u,h=t[d]),!(h&&h.add(l,e)||(p=c,h=new r(d),h.add(l,e))))return void(o=null);t[d]=h,n.push({operation:p,block:i,item:s,shorthand:h}),o=a}}),o}function i(e,t){e.forEach(function(e){var n=e.shorthand;n.isOkToMinimize()&&(e.operation===c?e.item.data=t(n.getDeclaration()):e.block.remove(e.item))})}var a=e("css-tree").List,s=e("css-tree").generate,l=e("css-tree").walk,c=1,u=2,h=0,p=1,d=2,f=3,m=["top","right","bottom","left"],g={"margin-top":"top","margin-right":"right","margin-bottom":"bottom","margin-left":"left","padding-top":"top","padding-right":"right","padding-bottom":"bottom","padding-left":"left","border-top-color":"top","border-right-color":"right","border-bottom-color":"bottom","border-left-color":"left","border-top-width":"top","border-right-width":"right","border-bottom-width":"bottom","border-left-width":"left","border-top-style":"top","border-right-style":"right","border-bottom-style":"bottom","border-left-style":"left"},b={margin:"margin","margin-top":"margin","margin-right":"margin","margin-bottom":"margin","margin-left":"margin",padding:"padding","padding-top":"padding","padding-right":"padding","padding-bottom":"padding","padding-left":"padding","border-color":"border-color","border-top-color":"border-color","border-right-color":"border-color","border-bottom-color":"border-color","border-left-color":"border-color","border-width":"border-width","border-top-width":"border-width","border-right-width":"border-width","border-bottom-width":"border-width","border-left-width":"border-width","border-style":"border-style","border-top-style":"border-style","border-right-style":"border-style","border-bottom-style":"border-style","border-left-style":"border-style"};r.prototype.getValueSequence=function(e,t){var n=[],r="";return!(e.value.children.some(function(t){var o=!1;switch(t.type){case"Identifier":switch(t.name){case"\\0":case"\\9":return void(r=t.name);case"inherit":case"initial":case"unset":case"revert":o=t.name}break;case"Dimension":switch(t.unit){case"rem":case"vw":case"vh":case"vmin":case"vmax":case"vm":o=t.unit}break;case"HexColor":case"Number":case"Percentage":break;case"Function":o=t.name;break;case"WhiteSpace":return!1;default:return!0}n.push({node:t,special:o,important:e.important})})||n.length>t)&&(("string"!=typeof this.iehack||this.iehack===r)&&(this.iehack=r,n))},r.prototype.canOverride=function(e,t){var n=this.sides[e];return!n||t.important&&!n.important},r.prototype.add=function(e,t){function n(){var n=this.sides,r=g[e];if(r){if(r in n==!1)return!1;var o=this.getValueSequence(t,1);if(!o||!o.length)return!1;for(var i in n)if(null!==n[i]&&n[i].special!==o[0].special)return!1;return!this.canOverride(r,o[0])||(n[r]=o[0],!0)}if(e===this.name){var o=this.getValueSequence(t,4);if(!o||!o.length)return!1;switch(o.length){case 1:o[p]=o[h],o[d]=o[h],o[f]=o[h];break;case 2:o[d]=o[h],o[f]=o[p];break;case 3:o[f]=o[p]}for(var a=0;a<4;a++)for(var i in n)if(null!==n[i]&&n[i].special!==o[a].special)return!1;for(var a=0;a<4;a++)this.canOverride(m[a],o[a])&&(n[m[a]]=o[a]);return!0}}return!!n.call(this)&&(this.loc||(this.loc=t.loc),!0)},r.prototype.isOkToMinimize=function(){var e=this.sides.top,t=this.sides.right,n=this.sides.bottom,r=this.sides.left;if(e&&t&&n&&r){var o=e.important+t.important+n.important+r.important;return 0===o||4===o}return!1},r.prototype.getValue=function(){var e=new a,t=this.sides,n=[t.top,t.right,t.bottom,t.left],r=[s(t.top.node),s(t.right.node),s(t.bottom.node),s(t.left.node)];r[f]===r[p]&&(n.pop(),r[d]===r[h]&&(n.pop(),r[p]===r[h]&&n.pop()));for(var o=0;o<n.length;o++)o&&e.appendData({type:"WhiteSpace",value:" "}),e.appendData(n[o].node);return this.iehack&&(e.appendData({type:"WhiteSpace",value:" "}),e.appendData({type:"Identifier",loc:null,name:this.iehack})),{type:"Value",loc:null,children:e}},r.prototype.getDeclaration=function(){return{type:"Declaration",loc:this.loc,important:this.sides.top.important,property:this.name,value:this.getValue()}},t.exports=function(e,t){var n={},r=[];l(e,{visit:"Rule",reverse:!0,enter:function(e){var t,i,a=this.block||this.stylesheet,s=(e.pseudoSignature||"")+"|"+e.prelude.children.first().id;n.hasOwnProperty(a.id)?t=n[a.id]:(t={lastShortSelector:null},n[a.id]=t),t.hasOwnProperty(s)?i=t[s]:(i={},t[s]=i),t.lastShortSelector=o.call(this,e,i,r,t.lastShortSelector)}}),i(r,t.declaration)}},{"css-tree":44}],30:[function(e,t,n){function r(e,t,n){var r=a(e).basename;if("background"===r)return e+":"+c(t.value);var o=t.id,i=n[o];if(!i){switch(t.value.type){case"Value":var l="",h="",m={},g=!1;t.value.children.each(function e(t){switch(t.type){case"Value":case"Brackets":case"Parentheses":t.children.each(e);break;case"Raw":g=!0;break;case"Identifier":var n=t.name;l||(l=s(n).vendor),/\\[09]/.test(n)&&(h=RegExp.lastMatch),"cursor"===r?-1===d.indexOf(n)&&(m[n]=!0):"position"===r?-1===f.indexOf(n)&&(m[n]=!0):p.hasOwnProperty(r)&&p[r].test(n)&&(m[n]=!0);break;case"Function":var n=t.name;if(l||(l=s(n).vendor),"rect"===n){t.children.some(function(e){return"Operator"===e.type&&","===e.value})||(n="rect-backward")}m[n+"()"]=!0,t.children.each(e);break;case"Dimension":var o=t.unit;switch(o){case"rem":case"vw":case"vh":case"vmin":case"vmax":case"vm":m[o]=!0}}}),i=g?"!"+u++:"!"+Object.keys(m).sort()+"|"+h+l;break;case"Raw":i="!"+t.value.value;break;default:i=c(t.value)}n[o]=i}return e+i}function o(e,t,n){var o=a(t.property);if(m.hasOwnProperty(o.basename))for(var i=m[o.basename],s=0;s<i.length;s++){var l=r(o.prefix+i[s],t,n),c=e.hasOwnProperty(l)?e[l]:null;if(c&&(!t.important||c.item.data.important))return c}}function i(e,t,n,i,a){var s=e.block.children;s.eachRight(function(e,t){var n=e.property,l=r(n,e,a),c=i[l];if(c&&!h.hasOwnProperty(n))e.important&&!c.item.data.important?(i[l]={block:s,item:t},c.block.remove(c.item)):s.remove(t);else{var c=o(i,e,a);c?s.remove(t):(e.fingerprint=l,i[l]={block:s,item:t})}}),s.isEmpty()&&n.remove(t)}var a=e("css-tree").property,s=e("css-tree").keyword,l=e("css-tree").walk,c=e("css-tree").generate,u=1,h={src:1},p={display:/table|ruby|flex|-(flex)?box$|grid|contents|run-in/i,"text-align":/^(start|end|match-parent|justify-all)$/i},d=["auto","crosshair","default","move","text","wait","help","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","pointer","progress","not-allowed","no-drop","vertical-text","all-scroll","col-resize","row-resize"],f=["static","relative","absolute","fixed"],m={"border-width":["border"],"border-style":["border"],"border-color":["border"],"border-top":["border"],"border-right":["border"],"border-bottom":["border"],"border-left":["border"],"border-top-width":["border-top","border-width","border"],"border-right-width":["border-right","border-width","border"],"border-bottom-width":["border-bottom","border-width","border"],"border-left-width":["border-left","border-width","border"],"border-top-style":["border-top","border-style","border"],"border-right-style":["border-right","border-style","border"],"border-bottom-style":["border-bottom","border-style","border"],"border-left-style":["border-left","border-style","border"],"border-top-color":["border-top","border-color","border"],"border-right-color":["border-right","border-color","border"],"border-bottom-color":["border-bottom","border-color","border"],"border-left-color":["border-left","border-color","border"],"margin-top":["margin"],"margin-right":["margin"],"margin-bottom":["margin"],"margin-left":["margin"],"padding-top":["padding"],"padding-right":["padding"],"padding-bottom":["padding"],"padding-left":["padding"],"font-style":["font"],"font-variant":["font"],"font-weight":["font"],"font-size":["font"],"font-family":["font"],"list-style-type":["list-style"],"list-style-position":["list-style"],"list-style-image":["list-style"]};t.exports=function(e){var t={},n=Object.create(null);l(e,{visit:"Rule",reverse:!0,enter:function(e,r,o){var a,s,l=this.block||this.stylesheet,c=(e.pseudoSignature||"")+"|"+e.prelude.children.first().id;t.hasOwnProperty(l.id)?a=t[l.id]:(a={},t[l.id]=a),a.hasOwnProperty(c)?s=a[c]:(s={},a[c]=s),i.call(this,e,r,o,s,n)}})}},{"css-tree":44}],31:[function(e,t,n){function r(e,t,n){var r=e.prelude.children,o=e.block.children,a=r.first().compareMarker,s={};n.nextUntil(t.next,function(t,l){if("Rule"!==t.type)return i.unsafeToSkipNode.call(r,t);if(e.pseudoSignature!==t.pseudoSignature)return!0;var c=t.prelude.children.head,u=t.block.children,h=c.data.compareMarker;if(h in s)return!0;if(r.head===r.tail&&r.first().id===c.data.id)return o.appendList(u),void n.remove(l);if(i.isEqualDeclarations(o,u)){var p=c.data.id;return r.some(function(e,t){var n=e.id;return p<n?(r.insert(c,t),!0):t.next?void 0:(r.insert(c),!0)}),void n.remove(l)}if(h===a)return!0;s[h]=!0})}var o=e("css-tree").walk,i=e("./utils");t.exports=function(e){o(e,{visit:"Rule",enter:r})}},{"./utils":38,"css-tree":44}],32:[function(e,t,n){function r(e){var t=0;return e.each(function(e){t+=e.id.length+1}),t-1}function o(e){for(var t=0,n=0;n<e.length;n++)t+=e[n].length;return t+e.length-1}function i(e,t,n){var i=null!==this.block&&this.block.avoidRulesMerge,s=e.prelude.children,c=e.block,u=Object.create(null),h=!0,p=!0;n.prevUntil(t.prev,function(d,f){if("Rule"!==d.type)return l.unsafeToSkipNode.call(s,d);var m=d.prelude.children,g=d.block;if(e.pseudoSignature!==d.pseudoSignature)return!0;if(!(p=!m.some(function(e){return e.compareMarker in u}))&&!h)return!0;if(h&&l.isEqualSelectors(m,s))return g.children.appendList(c.children),n.remove(t),!0;var b=l.compareDeclarations(c.children,g.children);if(b.eq.length){if(!b.ne1.length&&!b.ne2.length)return p&&(l.addSelectors(s,m),n.remove(f)),!0;if(!i)if(b.ne1.length&&!b.ne2.length){var y=r(s),v=o(b.eq);h&&y<v&&(l.addSelectors(m,s),c.children=(new a).fromArray(b.ne1))}else if(!b.ne1.length&&b.ne2.length){var y=r(m),v=o(b.eq);p&&y<v&&(l.addSelectors(s,m),g.children=(new a).fromArray(b.ne2))}else{var k={type:"SelectorList",loc:null,children:l.addSelectors(m.copy(),s)},x=r(k.children)+2,v=o(b.eq);if(p&&v>=x){
var w={type:"Rule",loc:null,prelude:k,block:{type:"Block",loc:null,children:(new a).fromArray(b.eq)},pseudoSignature:e.pseudoSignature};return c.children=(new a).fromArray(b.ne1),g.children=(new a).fromArray(b.ne2.concat(b.ne2overrided)),n.insert(n.createItem(w),f),!0}}}h&&(h=!m.some(function(e){return s.some(function(t){return t.compareMarker===e.compareMarker})})),m.each(function(e){u[e.compareMarker]=!0})})}var a=e("css-tree").List,s=e("css-tree").walk,l=e("./utils");t.exports=function(e){s(e,{visit:"Rule",reverse:!0,enter:i})}},{"./utils":38,"css-tree":44}],33:[function(e,t,n){var r=e("./prepare/index"),o=e("./1-mergeAtrule"),i=e("./2-initialMergeRuleset"),a=e("./3-disjoinRuleset"),s=e("./4-restructShorthand"),l=e("./6-restructBlock"),c=e("./7-mergeRuleset"),u=e("./8-restructRuleset");t.exports=function(e,t){var n=r(e,t);t.logger("prepare",e),o(e,t),t.logger("mergeAtrule",e),i(e),t.logger("initialMergeRuleset",e),a(e),t.logger("disjoinRuleset",e),s(e,n),t.logger("restructShorthand",e),l(e),t.logger("restructBlock",e),c(e),t.logger("mergeRuleset",e),u(e),t.logger("restructRuleset",e)}},{"./1-mergeAtrule":26,"./2-initialMergeRuleset":27,"./3-disjoinRuleset":28,"./4-restructShorthand":29,"./6-restructBlock":30,"./7-mergeRuleset":31,"./8-restructRuleset":32,"./prepare/index":35}],34:[function(e,t,n){function r(){this.seed=0,this.map=Object.create(null)}var o=e("css-tree").generate;r.prototype.resolve=function(e){var t=this.map[e];return t||(t=++this.seed,this.map[e]=t),t},t.exports=function(){var e=new r;return function(t){var n=o(t);return t.id=e.resolve(n),t.length=n.length,t.fingerprint=null,t}}},{"css-tree":44}],35:[function(e,t,n){var r=e("css-tree").keyword,o=e("css-tree").walk,i=e("css-tree").generate,a=e("./createDeclarationIndexer"),s=e("./processSelector");t.exports=function(e,t){var n=a();return o(e,{visit:"Rule",enter:function(e){e.block.children.each(n),s(e,t.usage)}}),o(e,{visit:"Atrule",enter:function(e){e.prelude&&(e.prelude.id=null,e.prelude.id=i(e.prelude)),"keyframes"===r(e.name).basename&&(e.block.avoidRulesMerge=!0,e.block.children.each(function(e){e.prelude.children.each(function(e){e.compareMarker=e.id})}))}}),{declaration:n}}},{"./createDeclarationIndexer":34,"./processSelector":36,"css-tree":44}],36:[function(e,t,n){var r=e("css-tree").generate,o=e("./specificity"),i={"first-letter":!0,"first-line":!0,after:!0,before:!0},a={link:!0,visited:!0,hover:!0,active:!0,"first-letter":!0,"first-line":!0,after:!0,before:!0};t.exports=function(e,t){var n=Object.create(null),s=!1;e.prelude.children.each(function(e){var l="*",c=0;e.children.each(function(o){switch(o.type){case"ClassSelector":if(t&&t.scopes){var u=t.scopes[o.name]||0;if(0!==c&&u!==c)throw new Error("Selector can't has classes from different scopes: "+r(e));c=u}break;case"PseudoClassSelector":var h=o.name.toLowerCase();a.hasOwnProperty(h)||(n[h]=!0,s=!0);break;case"PseudoElementSelector":var h=o.name.toLowerCase();i.hasOwnProperty(h)||(n[h]=!0,s=!0);break;case"TypeSelector":l=o.name.toLowerCase();break;case"AttributeSelector":o.flags&&(n["["+o.flags.toLowerCase()+"]"]=!0,s=!0);break;case"WhiteSpace":case"Combinator":l="*"}}),e.compareMarker=o(e).toString(),e.id=null,e.id=r(e),c&&(e.compareMarker+=":"+c),"*"!==l&&(e.compareMarker+=","+l)}),e.pseudoSignature=s&&Object.keys(n).sort().join(",")}},{"./specificity":37,"css-tree":44}],37:[function(e,t,n){t.exports=function(e){var t=0,n=0,r=0;return e.children.each(function e(o){switch(o.type){case"SelectorList":case"Selector":o.children.each(e);break;case"IdSelector":t++;break;case"ClassSelector":case"AttributeSelector":n++;break;case"PseudoClassSelector":switch(o.name.toLowerCase()){case"not":o.children.each(e);break;case"before":case"after":case"first-line":case"first-letter":r++;break;default:n++}break;case"PseudoElementSelector":r++;break;case"TypeSelector":"*"!==o.name.charAt(o.name.length-1)&&r++}}),[t,n,r]}},{}],38:[function(e,t,n){function r(e,t){for(var n=e.head,r=t.head;null!==n&&null!==r&&n.data.id===r.data.id;)n=n.next,r=r.next;return null===n&&null===r}function o(e,t){for(var n=e.head,r=t.head;null!==n&&null!==r&&n.data.id===r.data.id;)n=n.next,r=r.next;return null===n&&null===r}function i(e,t){for(var n={eq:[],ne1:[],ne2:[],ne2overrided:[]},r=Object.create(null),o=Object.create(null),i=t.head;i;i=i.next)o[i.data.id]=!0;for(var i=e.head;i;i=i.next){var a=i.data;a.fingerprint&&(r[a.fingerprint]=a.important),o[a.id]?(o[a.id]=!1,n.eq.push(a)):n.ne1.push(a)}for(var i=t.head;i;i=i.next){var a=i.data;o[a.id]&&(c.call(r,a.fingerprint)&&Number(r[a.fingerprint])>=Number(a.important)?n.ne2overrided.push(a):n.ne2.push(a))}return n}function a(e,t){return t.each(function(t){for(var n=t.id,r=e.head;r;){var o=r.data.id;if(o===n)return;if(o>n)break;r=r.next}e.insert(e.createItem(t),r)}),e}function s(e,t){for(var n=e.head;null!==n;){for(var r=t.head;null!==r;){if(n.data.compareMarker===r.data.compareMarker)return!0;r=r.next}n=n.next}return!1}function l(e){switch(e.type){case"Rule":return s(e.prelude.children,this);case"Atrule":if(e.block)return e.block.children.some(l,this);break;case"Declaration":return!1}return!0}var c=Object.prototype.hasOwnProperty;t.exports={isEqualSelectors:r,isEqualDeclarations:o,compareDeclarations:i,addSelectors:a,hasSimilarSelectors:s,unsafeToSkipNode:l}},{}],39:[function(e,t,n){function r(e,t){var n=Object.create(null);if(!Array.isArray(e))return null;for(var r=0;r<e.length;r++){var o=e[r];t&&(o=o.toLowerCase()),n[o]=!0}return n}function o(e){if(!e)return null;var t=r(e.tags,!0),n=r(e.ids),o=r(e.classes);return null===t&&null===n&&null===o?null:{tags:t,ids:n,classes:o}}function i(e){var t=!1;if(e.scopes&&Array.isArray(e.scopes)){t=Object.create(null);for(var n=0;n<e.scopes.length;n++){var r=e.scopes[n];if(!r||!Array.isArray(r))throw new Error("Wrong usage format");for(var i=0;i<r.length;i++){var s=r[i];if(a.call(t,s))throw new Error("Class can't be used for several scopes: "+s);t[s]=n+1}}}return{whitelist:o(e),blacklist:o(e.blacklist),scopes:t}}var a=Object.prototype.hasOwnProperty;t.exports={buildIndex:i}},{}],40:[function(e,t,n){t.exports={generic:!0,types:{"absolute-size":"xx-small | x-small | small | medium | large | x-large | xx-large","alpha-value":"<number> | <percentage>","angle-percentage":"<angle> | <percentage>","animateable-feature":"scroll-position | contents | <custom-ident>",attachment:"scroll | fixed | local","auto-repeat":"repeat( [ auto-fill | auto-fit ] , [ <line-names>? <fixed-size> ]+ <line-names>? )","auto-track-list":"[ <line-names>? [ <fixed-size> | <fixed-repeat> ] ]* <line-names>? <auto-repeat> [ <line-names>? [ <fixed-size> | <fixed-repeat> ] ]* <line-names>?","baseline-position":"[ first | last ]? baseline","basic-shape":"<inset()> | <circle()> | <ellipse()> | <polygon()>","bg-image":"none | <image>","bg-layer":"<bg-image> || <bg-position> [ / <bg-size> ]? || <repeat-style> || <attachment> || <box> || <box>","bg-position":"[ [ left | center | right | top | bottom | <length-percentage> ] | [ left | center | right | <length-percentage> ] [ top | center | bottom | <length-percentage> ] | [ center | [ left | right ] <length-percentage>? ] && [ center | [ top | bottom ] <length-percentage>? ] ]","bg-size":"[ <length-percentage> | auto ]{1,2} | cover | contain","blur()":"blur( <length> )","blend-mode":"normal | multiply | screen | overlay | darken | lighten | color-dodge | color-burn | hard-light | soft-light | difference | exclusion | hue | saturation | color | luminosity",box:"border-box | padding-box | content-box","br-style":"none | hidden | dotted | dashed | solid | double | groove | ridge | inset | outset","br-width":"<length> | thin | medium | thick","brightness()":"brightness( <number-percentage> )","calc()":"calc( <calc-sum> )","calc-sum":"<calc-product> [ [ '+' | '-' ] <calc-product> ]*","calc-product":"<calc-value> [ '*' <calc-value> | '/' <number> ]*","calc-value":"<number> | <dimension> | <percentage> | ( <calc-sum> )","cf-final-image":"<image> | <color>","cf-mixing-image":"<percentage>? && <image>","circle()":"circle( [ <shape-radius> ]? [ at <position> ]? )","clip-source":"<url>",color:"<rgb()> | <rgba()> | <hsl()> | <hsla()> | <hex-color> | <named-color> | currentcolor | <deprecated-system-color>","color-stop":"<color> <length-percentage>?","color-stop-list":"<color-stop>#{2,}","common-lig-values":"[ common-ligatures | no-common-ligatures ]","composite-style":"clear | copy | source-over | source-in | source-out | source-atop | destination-over | destination-in | destination-out | destination-atop | xor","compositing-operator":"add | subtract | intersect | exclude","contextual-alt-values":"[ contextual | no-contextual ]","content-distribution":"space-between | space-around | space-evenly | stretch","content-list":"[ <string> | contents | <url> | <quote> | <attr()> | counter( <ident> , <'list-style-type'>? ) ]+","content-position":"center | start | end | flex-start | flex-end","content-replacement":"<image>","contrast()":"contrast( [ <number-percentage> ] )","counter-style":"<counter-style-name> | symbols( )","counter-style-name":"<custom-ident>","cross-fade()":"cross-fade( <cf-mixing-image> , <cf-final-image>? )","cubic-bezier-timing-function":"ease | ease-in | ease-out | ease-in-out | cubic-bezier( <number> , <number> , <number> , <number> )","deprecated-system-color":"ActiveBorder | ActiveCaption | AppWorkspace | Background | ButtonFace | ButtonHighlight | ButtonShadow | ButtonText | CaptionText | GrayText | Highlight | HighlightText | InactiveBorder | InactiveCaption | InactiveCaptionText | InfoBackground | InfoText | Menu | MenuText | Scrollbar | ThreeDDarkShadow | ThreeDFace | ThreeDHighlight | ThreeDLightShadow | ThreeDShadow | Window | WindowFrame | WindowText","discretionary-lig-values":"[ discretionary-ligatures | no-discretionary-ligatures ]","display-box":"contents | none","display-inside":"flow | flow-root | table | flex | grid | subgrid | ruby","display-internal":"table-row-group | table-header-group | table-footer-group | table-row | table-cell | table-column-group | table-column | table-caption | ruby-base | ruby-text | ruby-base-container | ruby-text-container","display-legacy":"inline-block | inline-list-item | inline-table | inline-flex | inline-grid","display-listitem":"<display-outside>? && [ flow | flow-root ]? && list-item","display-outside":"block | inline | run-in","drop-shadow()":"drop-shadow( <length>{2,3} <color>? )","east-asian-variant-values":"[ jis78 | jis83 | jis90 | jis04 | simplified | traditional ]","east-asian-width-values":"[ full-width | proportional-width ]","element()":"element( <id-selector> )","ellipse()":"ellipse( [ <shape-radius>{2} ]? [ at <position> ]? )","ending-shape":"circle | ellipse","explicit-track-list":"[ <line-names>? <track-size> ]+ <line-names>?","family-name":"<string> | <custom-ident>+","feature-tag-value":"<string> [ <integer> | on | off ]?","feature-value-name":"<custom-ident>","fill-rule":"nonzero | evenodd","filter-function":"<blur()> | <brightness()> | <contrast()> | <drop-shadow()> | <grayscale()> | <hue-rotate()> | <invert()> | <opacity()> | <saturate()> | <sepia()>","filter-function-list":"[ <filter-function> | <url> ]+","final-bg-layer":"<'background-color'> || <bg-image> || <bg-position> [ / <bg-size> ]? || <repeat-style> || <attachment> || <box> || <box>","fit-content()":"fit-content( [ <length> | <percentage> ] )","fixed-breadth":"<length-percentage>","fixed-repeat":"repeat( [ <positive-integer> ] , [ <line-names>? <fixed-size> ]+ <line-names>? )","fixed-size":"<fixed-breadth> | minmax( <fixed-breadth> , <track-breadth> ) | minmax( <inflexible-breadth> , <fixed-breadth> )","font-variant-css21":"[ normal | small-caps ]","frames-timing-function":"frames( <integer> )","frequency-percentage":"<frequency> | <percentage>","generic-family":"serif | sans-serif | cursive | fantasy | monospace | -apple-system","generic-name":"serif | sans-serif | cursive | fantasy | monospace","geometry-box":"<shape-box> | fill-box | stroke-box | view-box",gradient:"<-legacy-gradient> | <linear-gradient()> | <repeating-linear-gradient()> | <radial-gradient()> | <repeating-radial-gradient()>","grayscale()":"grayscale( <number-percentage> )","grid-line":"auto | <custom-ident> | [ <integer> && <custom-ident>? ] | [ span && [ <integer> || <custom-ident> ] ]","historical-lig-values":"[ historical-ligatures | no-historical-ligatures ]","hsl()":"hsl( <hue> <percentage> <percentage> [ / <alpha-value> ]? ) | hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )","hsla()":"hsla( <hue> <percentage> <percentage> [ / <alpha-value> ]? ) | hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )",hue:"<number> | <angle>","hue-rotate()":"hue-rotate( <angle> )",image:"<url> | <image()> | <image-set()> | <element()> | <cross-fade()> | <gradient>","image()":"image( [ [ <image> | <string> ]? , <color>? ]! )","image-set()":"image-set( <image-set-option># )","image-set-option":"[ <image> | <string> ] <resolution>","inflexible-breadth":"<length> | <percentage> | min-content | max-content | auto","inset()":"inset( <length-percentage>{1,4} [ round <'border-radius'> ]? )","invert()":"invert( <number-percentage> )","keyframes-name":"<custom-ident> | <string>","keyframe-selector":"from | to | <percentage>","leader()":"leader( <leader-type> )","leader-type":"dotted | solid | space | <string>","length-percentage":"<length> | <percentage>","line-names":"'[' <custom-ident>* ']'","line-name-list":"[ <line-names> | <name-repeat> ]+","linear-gradient()":"linear-gradient( [ <angle> | to <side-or-corner> ]? , <color-stop-list> )","mask-layer":"<mask-reference> || <position> [ / <bg-size> ]? || <repeat-style> || <geometry-box> || [ <geometry-box> | no-clip ] || <compositing-operator> || <masking-mode>","mask-position":"[ <length-percentage> | left | center | right ] [ <length-percentage> | top | center | bottom ]?","mask-reference":"none | <image> | <mask-source>","mask-source":"<url>","masking-mode":"alpha | luminance | match-source","matrix()":"matrix( <number> [, <number> ]{5} )","matrix3d()":"matrix3d( <number> [, <number> ]{15} )","media-type":"<ident>","mf-boolean":"<mf-name>","mf-name":"<ident>","minmax()":"minmax( [ <length> | <percentage> | <flex> | min-content | max-content | auto ] , [ <length> | <percentage> | <flex> | min-content | max-content | auto ] )","named-color":"transparent | aliceblue | antiquewhite | aqua | aquamarine | azure | beige | bisque | black | blanchedalmond | blue | blueviolet | brown | burlywood | cadetblue | chartreuse | chocolate | coral | cornflowerblue | cornsilk | crimson | cyan | darkblue | darkcyan | darkgoldenrod | darkgray | darkgreen | darkgrey | darkkhaki | darkmagenta | darkolivegreen | darkorange | darkorchid | darkred | darksalmon | darkseagreen | darkslateblue | darkslategray | darkslategrey | darkturquoise | darkviolet | deeppink | deepskyblue | dimgray | dimgrey | dodgerblue | firebrick | floralwhite | forestgreen | fuchsia | gainsboro | ghostwhite | gold | goldenrod | gray | green | greenyellow | grey | honeydew | hotpink | indianred | indigo | ivory | khaki | lavender | lavenderblush | lawngreen | lemonchiffon | lightblue | lightcoral | lightcyan | lightgoldenrodyellow | lightgray | lightgreen | lightgrey | lightpink | lightsalmon | lightseagreen | lightskyblue | lightslategray | lightslategrey | lightsteelblue | lightyellow | lime | limegreen | linen | magenta | maroon | mediumaquamarine | mediumblue | mediumorchid | mediumpurple | mediumseagreen | mediumslateblue | mediumspringgreen | mediumturquoise | mediumvioletred | midnightblue | mintcream | mistyrose | moccasin | navajowhite | navy | oldlace | olive | olivedrab | orange | orangered | orchid | palegoldenrod | palegreen | paleturquoise | palevioletred | papayawhip | peachpuff | peru | pink | plum | powderblue | purple | rebeccapurple | red | rosybrown | royalblue | saddlebrown | salmon | sandybrown | seagreen | seashell | sienna | silver | skyblue | slateblue | slategray | slategrey | snow | springgreen | steelblue | tan | teal | thistle | tomato | turquoise | violet | wheat | white | whitesmoke | yellow | yellowgreen | <-non-standard-color>","namespace-prefix":"<ident>","number-percentage":"<number> | <percentage>","numeric-figure-values":"[ lining-nums | oldstyle-nums ]","numeric-fraction-values":"[ diagonal-fractions | stacked-fractions ]","numeric-spacing-values":"[ proportional-nums | tabular-nums ]","opacity()":"opacity( [ <number-percentage> ] )","overflow-position":"unsafe | safe","outline-radius":"<border-radius>","perspective()":"perspective( <length> )","polygon()":"polygon( <fill-rule>? , [ <length-percentage> <length-percentage> ]# )",position:"[ [ left | center | right ] || [ top | center | bottom ] | [ left | center | right | <length-percentage> ] [ top | center | bottom | <length-percentage> ]? | [ [ left | right ] <length-percentage> ] && [ [ top | bottom ] <length-percentage> ] ]",quote:"open-quote | close-quote | no-open-quote | no-close-quote","radial-gradient()":"radial-gradient( [ <ending-shape> || <size> ]? [ at <position> ]? , <color-stop-list> )","relative-size":"larger | smaller","repeat-style":"repeat-x | repeat-y | [ repeat | space | round | no-repeat ]{1,2}","repeating-linear-gradient()":"repeating-linear-gradient( [ <angle> | to <side-or-corner> ]? , <color-stop-list> )","repeating-radial-gradient()":"repeating-radial-gradient( [ <ending-shape> || <size> ]? [ at <position> ]? , <color-stop-list> )","rgb()":"rgb( <percentage>{3} [ / <alpha-value> ]? ) | rgb( <number>{3} [ / <alpha-value> ]? ) | rgb( <percentage>#{3} , <alpha-value>? ) | rgb( <number>#{3} , <alpha-value>? )","rgba()":"rgba( <percentage>{3} [ / <alpha-value> ]? ) | rgba( <number>{3} [ / <alpha-value> ]? ) | rgba( <percentage>#{3} , <alpha-value>? ) | rgba( <number>#{3} , <alpha-value>? )","rotate()":"rotate( <angle> )","rotate3d()":"rotate3d( <number> , <number> , <number> , <angle> )","rotateX()":"rotateX( <angle> )","rotateY()":"rotateY( <angle> )","rotateZ()":"rotateZ( <angle> )","saturate()":"saturate( <number-percentage> )","scale()":"scale( <number> [, <number> ]? )","scale3d()":"scale3d( <number> , <number> , <number> )","scaleX()":"scaleX( <number> )","scaleY()":"scaleY( <number> )","scaleZ()":"scaleZ( <number> )","self-position":"center | start | end | self-start | self-end | flex-start | flex-end","shape-radius":"<length-percentage> | closest-side | farthest-side","skew()":"skew( <angle> [, <angle> ]? )","skewX()":"skewX( <angle> )","skewY()":"skewY( <angle> )","sepia()":"sepia( <number-percentage> )",shadow:"inset? && <length>{2,4} && <color>?","shadow-t":"[ <length>{2,3} && <color>? ]",shape:"rect( [ [ <top> , <right> , <bottom> , <left> ] | [ <top> <right> <bottom> <left> ] ] )","shape-box":"<box> | margin-box","side-or-corner":"[ left | right ] || [ top | bottom ]","single-animation":"<time> || <single-timing-function> || <time> || <single-animation-iteration-count> || <single-animation-direction> || <single-animation-fill-mode> || <single-animation-play-state> || [ none | <keyframes-name> ]","single-animation-direction":"normal | reverse | alternate | alternate-reverse","single-animation-fill-mode":"none | forwards | backwards | both","single-animation-iteration-count":"infinite | <number>","single-animation-play-state":"running | paused","single-timing-function":"linear | <cubic-bezier-timing-function> | <step-timing-function> | <frames-timing-function>","single-transition":"<single-transition-timing-function> || [ none | <single-transition-property> ] || <time> || <time>","single-transition-timing-function":"<single-timing-function>","single-transition-property":"all | <custom-ident>",size:"closest-side | farthest-side | closest-corner | farthest-corner | <length> | <length-percentage>{2}","step-timing-function":"step-start | step-end | steps( <integer> [, [ start | end ] ]? )",symbol:"<string> | <image> | <custom-ident>",target:"<target-counter()> | <target-counters()> | <target-text()>","target-counter()":"target-counter( [ <string> | <url> ] , <custom-ident> , <counter-style>? )","target-counters()":"target-counters( [ <string> | <url> ] , <custom-ident> , <string> , <counter-style>? )","target-text()":"target-text( [ <string> | <url> ] , [ content | before | after | first-letter ]? )","time-percentage":"<time> | <percentage>","track-breadth":"<length-percentage> | <flex> | min-content | max-content | auto","track-list":"[ <line-names>? [ <track-size> | <track-repeat> ] ]+ <line-names>?","track-repeat":"repeat( [ <positive-integer> ] , [ <line-names>? <track-size> ]+ <line-names>? )","track-size":"<track-breadth> | minmax( <inflexible-breadth> , <track-breadth> ) | fit-content( [ <length> | <percentage> ] )","transform-function":"<matrix()> | <translate()> | <translateX()> | <translateY()> | <scale()> | <scaleX()> | <scaleY()> | <rotate()> | <skew()> | <skewX()> | <skewY()> | <matrix3d()> | <translate3d()> | <translateZ()> | <scale3d()> | <scaleZ()> | <rotate3d()> | <rotateX()> | <rotateY()> | <rotateZ()> | <perspective()>","transform-list":"<transform-function>+","translate()":"translate( <length-percentage> [, <length-percentage> ]? )","translate3d()":"translate3d( <length-percentage> , <length-percentage> , <length> )","translateX()":"translateX( <length-percentage> )","translateY()":"translateY( <length-percentage> )","translateZ()":"translateZ( <length> )","type-or-unit":"string | integer | color | url | integer | number | length | angle | time | frequency | em | ex | px | rem | vw | vh | vmin | vmax | mm | q | cm | in | pt | pc | deg | grad | rad | ms | s | Hz | kHz | %","viewport-length":"auto | <length-percentage>","-legacy-gradient":"<-webkit-gradient()> | <-legacy-linear-gradient> | <-legacy-repeating-linear-gradient> | <-legacy-radial-gradient> | <-legacy-repeating-radial-gradient>","-legacy-linear-gradient":"-moz-linear-gradient( <-legacy-linear-gradient-arguments> ) | -webkit-linear-gradient( <-legacy-linear-gradient-arguments> ) | -o-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-repeating-linear-gradient":"-moz-repeating-linear-gradient( <-legacy-linear-gradient-arguments> ) | -webkit-repeating-linear-gradient( <-legacy-linear-gradient-arguments> ) | -o-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-linear-gradient-arguments":"[ <angle> | <side-or-corner> ]? , <color-stop-list>","-legacy-radial-gradient":"-moz-radial-gradient( <-legacy-radial-gradient-arguments> ) | -webkit-radial-gradient( <-legacy-radial-gradient-arguments> ) | -o-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-repeating-radial-gradient":"-moz-repeating-radial-gradient( <-legacy-radial-gradient-arguments> ) | -webkit-repeating-radial-gradient( <-legacy-radial-gradient-arguments> ) | -o-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-radial-gradient-arguments":"[ <position> , ]? [ [ [ <-legacy-radial-gradient-shape> || <-legacy-radial-gradient-size> ] | [ <length> | <percentage> ]{2} ] , ]? <color-stop-list>","-legacy-radial-gradient-size":"closest-side | closest-corner | farthest-side | farthest-corner | contain | cover","-legacy-radial-gradient-shape":"circle | ellipse","-non-standard-font":"-apple-system-body | -apple-system-headline | -apple-system-subheadline | -apple-system-caption1 | -apple-system-caption2 | -apple-system-footnote | -apple-system-short-body | -apple-system-short-headline | -apple-system-short-subheadline | -apple-system-short-caption1 | -apple-system-short-footnote | -apple-system-tall-body","-non-standard-color":"-moz-ButtonDefault | -moz-ButtonHoverFace | -moz-ButtonHoverText | -moz-CellHighlight | -moz-CellHighlightText | -moz-Combobox | -moz-ComboboxText | -moz-Dialog | -moz-DialogText | -moz-dragtargetzone | -moz-EvenTreeRow | -moz-Field | -moz-FieldText | -moz-html-CellHighlight | -moz-html-CellHighlightText | -moz-mac-accentdarkestshadow | -moz-mac-accentdarkshadow | -moz-mac-accentface | -moz-mac-accentlightesthighlight | -moz-mac-accentlightshadow | -moz-mac-accentregularhighlight | -moz-mac-accentregularshadow | -moz-mac-chrome-active | -moz-mac-chrome-inactive | -moz-mac-focusring | -moz-mac-menuselect | -moz-mac-menushadow | -moz-mac-menutextselect | -moz-MenuHover | -moz-MenuHoverText | -moz-MenuBarText | -moz-MenuBarHoverText | -moz-nativehyperlinktext | -moz-OddTreeRow | -moz-win-communicationstext | -moz-win-mediatext | -moz-activehyperlinktext | -moz-default-background-color | -moz-default-color | -moz-hyperlinktext | -moz-visitedhyperlinktext | -webkit-activelink | -webkit-focus-ring-color | -webkit-link | -webkit-text","-non-standard-image-rendering":"optimize-contrast | -moz-crisp-edges | -o-crisp-edges | -webkit-optimize-contrast","-non-standard-overflow":"-moz-scrollbars-none | -moz-scrollbars-horizontal | -moz-scrollbars-vertical | -moz-hidden-unscrollable","-non-standard-width":"min-intrinsic | intrinsic | -moz-min-content | -moz-max-content | -webkit-min-content | -webkit-max-content","-non-standard-word-break":"break-word","-webkit-gradient()":"-webkit-gradient( <-webkit-gradient-type> , <-webkit-gradient-point> [, <-webkit-gradient-point> | , <-webkit-gradient-radius> , <-webkit-gradient-point> ] [, <-webkit-gradient-radius> ]? [, <-webkit-gradient-color-stop> ]* )","-webkit-gradient-color-stop":"from( <color> ) | color-stop( [ <number-zero-one> | <percentage> ] , <color> ) | to( <color> )","-webkit-gradient-point":"[ left | center | right | <length-percentage> ] [ top | center | bottom | <length-percentage> ]","-webkit-gradient-radius":"<length> | <percentage>","-webkit-gradient-type":"linear | radial","-webkit-mask-box-repeat":"repeat | stretch | round","-webkit-mask-clip-style":"border | border-box | padding | padding-box | content | content-box | text","-ms-filter":"[ <progid> | FlipH | FlipV ]+",age:"child | young | old","border-radius":"<length-percentage>{1,2}",bottom:"<length> | auto","generic-voice":"[ <age>? <gender> <integer>? ]",gender:"male | female | neutral",left:"<length> | auto","mask-image":"<mask-reference>#","name-repeat":"repeat( [ <positive-integer> | auto-fill ] , <line-names>+ )",paint:"none | currentColor | <color> | <url> [ none | currentColor | <color> ]?","path()":"path( <string> )",right:"<length> | auto","svg-length":"<percentage> | <length> | <number>","svg-writing-mode":"lr-tb | rl-tb | tb-rl | lr | rl | tb",top:"<length> | auto",x:"<number>",y:"<number>"},properties:{"-ms-accelerator":"false | true","-ms-block-progression":"tb | rl | bt | lr","-ms-content-zoom-chaining":"none | chained","-ms-content-zooming":"none | zoom","-ms-content-zoom-limit":"<'-ms-content-zoom-limit-min'> <'-ms-content-zoom-limit-max'>","-ms-content-zoom-limit-max":"<percentage>","-ms-content-zoom-limit-min":"<percentage>","-ms-content-zoom-snap":"<'-ms-content-zoom-snap-type'> || <'-ms-content-zoom-snap-points'>","-ms-content-zoom-snap-points":"snapInterval( <percentage> , <percentage> ) | snapList( <percentage># )","-ms-content-zoom-snap-type":"none | proximity | mandatory","-ms-filter":"<string>","-ms-flow-from":"[ none | <custom-ident> ]#","-ms-flow-into":"[ none | <custom-ident> ]#","-ms-high-contrast-adjust":"auto | none","-ms-hyphenate-limit-chars":"auto | <integer>{1,3}","-ms-hyphenate-limit-lines":"no-limit | <integer>","-ms-hyphenate-limit-zone":"<percentage> | <length>","-ms-ime-align":"auto | after","-ms-overflow-style":"auto | none | scrollbar | -ms-autohiding-scrollbar","-ms-scrollbar-3dlight-color":"<color>","-ms-scrollbar-arrow-color":"<color>","-ms-scrollbar-base-color":"<color>","-ms-scrollbar-darkshadow-color":"<color>","-ms-scrollbar-face-color":"<color>","-ms-scrollbar-highlight-color":"<color>","-ms-scrollbar-shadow-color":"<color>","-ms-scrollbar-track-color":"<color>","-ms-scroll-chaining":"chained | none","-ms-scroll-limit":"<'-ms-scroll-limit-x-min'> <'-ms-scroll-limit-y-min'> <'-ms-scroll-limit-x-max'> <'-ms-scroll-limit-y-max'>","-ms-scroll-limit-x-max":"auto | <length>","-ms-scroll-limit-x-min":"<length>","-ms-scroll-limit-y-max":"auto | <length>","-ms-scroll-limit-y-min":"<length>","-ms-scroll-rails":"none | railed","-ms-scroll-snap-points-x":"snapInterval( <length-percentage> , <length-percentage> ) | snapList( <length-percentage># )","-ms-scroll-snap-points-y":"snapInterval( <length-percentage> , <length-percentage> ) | snapList( <length-percentage># )","-ms-scroll-snap-type":"none | proximity | mandatory","-ms-scroll-snap-x":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-x'>","-ms-scroll-snap-y":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-y'>","-ms-scroll-translation":"none | vertical-to-horizontal","-ms-text-autospace":"none | ideograph-alpha | ideograph-numeric | ideograph-parenthesis | ideograph-space","-ms-touch-select":"grippers | none","-ms-user-select":"none | element | text","-ms-wrap-flow":"auto | both | start | end | maximum | clear","-ms-wrap-margin":"<length>","-ms-wrap-through":"wrap | none","-moz-appearance":"none | button | button-arrow-down | button-arrow-next | button-arrow-previous | button-arrow-up | button-bevel | button-focus | caret | checkbox | checkbox-container | checkbox-label | checkmenuitem | dualbutton | groupbox | listbox | listitem | menuarrow | menubar | menucheckbox | menuimage | menuitem | menuitemtext | menulist | menulist-button | menulist-text | menulist-textfield | menupopup | menuradio | menuseparator | meterbar | meterchunk | progressbar | progressbar-vertical | progresschunk | progresschunk-vertical | radio | radio-container | radio-label | radiomenuitem | range | range-thumb | resizer | resizerpanel | scale-horizontal | scalethumbend | scalethumb-horizontal | scalethumbstart | scalethumbtick | scalethumb-vertical | scale-vertical | scrollbarbutton-down | scrollbarbutton-left | scrollbarbutton-right | scrollbarbutton-up | scrollbarthumb-horizontal | scrollbarthumb-vertical | scrollbartrack-horizontal | scrollbartrack-vertical | searchfield | separator | sheet | spinner | spinner-downbutton | spinner-textfield | spinner-upbutton | splitter | statusbar | statusbarpanel | tab | tabpanel | tabpanels | tab-scroll-arrow-back | tab-scroll-arrow-forward | textfield | textfield-multiline | toolbar | toolbarbutton | toolbarbutton-dropdown | toolbargripper | toolbox | tooltip | treeheader | treeheadercell | treeheadersortarrow | treeitem | treeline | treetwisty | treetwistyopen | treeview | -moz-mac-unified-toolbar | -moz-win-borderless-glass | -moz-win-browsertabbar-toolbox | -moz-win-communicationstext | -moz-win-communications-toolbox | -moz-win-exclude-glass | -moz-win-glass | -moz-win-mediatext | -moz-win-media-toolbox | -moz-window-button-box | -moz-window-button-box-maximized | -moz-window-button-close | -moz-window-button-maximize | -moz-window-button-minimize | -moz-window-button-restore | -moz-window-frame-bottom | -moz-window-frame-left | -moz-window-frame-right | -moz-window-titlebar | -moz-window-titlebar-maximized","-moz-binding":"<url> | none","-moz-border-bottom-colors":"<color>+ | none","-moz-border-left-colors":"<color>+ | none","-moz-border-right-colors":"<color>+ | none","-moz-border-top-colors":"<color>+ | none","-moz-context-properties":"none | [ fill | fill-opacity | stroke | stroke-opacity ]#","-moz-float-edge":"border-box | content-box | margin-box | padding-box","-moz-force-broken-image-icon":"<integer>","-moz-image-region":"<shape> | auto","-moz-orient":"inline | block | horizontal | vertical","-moz-outline-radius":"<outline-radius>{1,4} [ / <outline-radius>{1,4} ]?","-moz-outline-radius-bottomleft":"<outline-radius>","-moz-outline-radius-bottomright":"<outline-radius>","-moz-outline-radius-topleft":"<outline-radius>","-moz-outline-radius-topright":"<outline-radius>","-moz-stack-sizing":"ignore | stretch-to-fit","-moz-text-blink":"none | blink","-moz-user-focus":"ignore | normal | select-after | select-before | select-menu | select-same | select-all | none","-moz-user-input":"auto | none | enabled | disabled",
"-moz-user-modify":"read-only | read-write | write-only","-moz-window-dragging":"drag | no-drag","-moz-window-shadow":"default | menu | tooltip | sheet | none","-webkit-appearance":"none | button | button-bevel | caps-lock-indicator | caret | checkbox | default-button | listbox | listitem | media-fullscreen-button | media-mute-button | media-play-button | media-seek-back-button | media-seek-forward-button | media-slider | media-sliderthumb | menulist | menulist-button | menulist-text | menulist-textfield | push-button | radio | scrollbarbutton-down | scrollbarbutton-left | scrollbarbutton-right | scrollbarbutton-up | scrollbargripper-horizontal | scrollbargripper-vertical | scrollbarthumb-horizontal | scrollbarthumb-vertical | scrollbartrack-horizontal | scrollbartrack-vertical | searchfield | searchfield-cancel-button | searchfield-decoration | searchfield-results-button | searchfield-results-decoration | slider-horizontal | slider-vertical | sliderthumb-horizontal | sliderthumb-vertical | square-button | textarea | textfield","-webkit-border-before":"<'border-width'> || <'border-style'> || <'color'>","-webkit-border-before-color":"<'color'>","-webkit-border-before-style":"<'border-style'>","-webkit-border-before-width":"<'border-width'>","-webkit-box-reflect":"[ above | below | right | left ]? <length>? <image>?","-webkit-mask":"[ <mask-reference> || <position> [ / <bg-size> ]? || <repeat-style> || [ <box> | border | padding | content | text ] || [ <box> | border | padding | content ] ]#","-webkit-mask-attachment":"<attachment>#","-webkit-mask-clip":"<-webkit-mask-clip-style> [, <-webkit-mask-clip-style> ]*","-webkit-mask-composite":"<composite-style>#","-webkit-mask-image":"<mask-reference>#","-webkit-mask-origin":"[ <box> | border | padding | content ]#","-webkit-mask-position":"<position>#","-webkit-mask-position-x":"[ <length-percentage> | left | center | right ]#","-webkit-mask-position-y":"[ <length-percentage> | top | center | bottom ]#","-webkit-mask-repeat":"<repeat-style>#","-webkit-mask-repeat-x":"repeat | no-repeat | space | round","-webkit-mask-repeat-y":"repeat | no-repeat | space | round","-webkit-mask-size":"<bg-size>#","-webkit-overflow-scrolling":"auto | touch","-webkit-tap-highlight-color":"<color>","-webkit-text-fill-color":"<color>","-webkit-text-stroke":"<length> || <color>","-webkit-text-stroke-color":"<color>","-webkit-text-stroke-width":"<length>","-webkit-touch-callout":"default | none","-webkit-user-modify":"read-only | read-write | read-write-plaintext-only","align-content":"normal | <baseline-position> | <content-distribution> | <overflow-position>? <content-position>","align-items":"normal | stretch | <baseline-position> | [ <overflow-position>? <self-position> ]","align-self":"auto | normal | stretch | <baseline-position> | <overflow-position>? <self-position>",all:"initial | inherit | unset | revert",animation:"<single-animation>#","animation-delay":"<time>#","animation-direction":"<single-animation-direction>#","animation-duration":"<time>#","animation-fill-mode":"<single-animation-fill-mode>#","animation-iteration-count":"<single-animation-iteration-count>#","animation-name":"[ none | <keyframes-name> ]#","animation-play-state":"<single-animation-play-state>#","animation-timing-function":"<single-timing-function>#",appearance:"auto | none",azimuth:"<angle> | [ [ left-side | far-left | left | center-left | center | center-right | right | far-right | right-side ] || behind ] | leftwards | rightwards","backdrop-filter":"none | <filter-function-list>","backface-visibility":"visible | hidden",background:"[ <bg-layer> , ]* <final-bg-layer>","background-attachment":"<attachment>#","background-blend-mode":"<blend-mode>#","background-clip":"<box>#","background-color":"<color>","background-image":"<bg-image>#","background-origin":"<box>#","background-position":"<bg-position>#","background-position-x":"[ center | [ left | right | x-start | x-end ]? <length-percentage>? ]#","background-position-y":"[ center | [ top | bottom | y-start | y-end ]? <length-percentage>? ]#","background-repeat":"<repeat-style>#","background-size":"<bg-size>#","block-overflow":"clip | ellipsis | <string>","block-size":"<'width'>",border:"<br-width> || <br-style> || <color>","border-block-end":"<'border-width'> || <'border-style'> || <'color'>","border-block-end-color":"<'color'>","border-block-end-style":"<'border-style'>","border-block-end-width":"<'border-width'>","border-block-start":"<'border-width'> || <'border-style'> || <'color'>","border-block-start-color":"<'color'>","border-block-start-style":"<'border-style'>","border-block-start-width":"<'border-width'>","border-bottom":"<br-width> || <br-style> || <color>","border-bottom-color":"<color>","border-bottom-left-radius":"<length-percentage>{1,2}","border-bottom-right-radius":"<length-percentage>{1,2}","border-bottom-style":"<br-style>","border-bottom-width":"<br-width>","border-collapse":"collapse | separate","border-color":"<color>{1,4}","border-image":"<'border-image-source'> || <'border-image-slice'> [ / <'border-image-width'> | / <'border-image-width'>? / <'border-image-outset'> ]? || <'border-image-repeat'>","border-image-outset":"[ <length> | <number> ]{1,4}","border-image-repeat":"[ stretch | repeat | round | space ]{1,2}","border-image-slice":"<number-percentage>{1,4} && fill?","border-image-source":"none | <image>","border-image-width":"[ <length-percentage> | <number> | auto ]{1,4}","border-inline-end":"<'border-width'> || <'border-style'> || <'color'>","border-inline-end-color":"<'color'>","border-inline-end-style":"<'border-style'>","border-inline-end-width":"<'border-width'>","border-inline-start":"<'border-width'> || <'border-style'> || <'color'>","border-inline-start-color":"<'color'>","border-inline-start-style":"<'border-style'>","border-inline-start-width":"<'border-width'>","border-left":"<br-width> || <br-style> || <color>","border-left-color":"<color>","border-left-style":"<br-style>","border-left-width":"<br-width>","border-radius":"<length-percentage>{1,4} [ / <length-percentage>{1,4} ]?","border-right":"<br-width> || <br-style> || <color>","border-right-color":"<color>","border-right-style":"<br-style>","border-right-width":"<br-width>","border-spacing":"<length> <length>?","border-style":"<br-style>{1,4}","border-top":"<br-width> || <br-style> || <color>","border-top-color":"<color>","border-top-left-radius":"<length-percentage>{1,2}","border-top-right-radius":"<length-percentage>{1,2}","border-top-style":"<br-style>","border-top-width":"<br-width>","border-width":"<br-width>{1,4}",bottom:"<length> | <percentage> | auto","box-align":"start | center | end | baseline | stretch","box-decoration-break":"slice | clone","box-direction":"normal | reverse | inherit","box-flex":"<number>","box-flex-group":"<integer>","box-lines":"single | multiple","box-ordinal-group":"<integer>","box-orient":"horizontal | vertical | inline-axis | block-axis | inherit","box-pack":"start | center | end | justify","box-shadow":"none | <shadow>#","box-sizing":"content-box | border-box","break-after":"auto | avoid | avoid-page | page | left | right | recto | verso | avoid-column | column | avoid-region | region","break-before":"auto | avoid | avoid-page | page | left | right | recto | verso | avoid-column | column | avoid-region | region","break-inside":"auto | avoid | avoid-page | avoid-column | avoid-region","caption-side":"top | bottom | block-start | block-end | inline-start | inline-end","caret-color":"auto | <color>",clear:"none | left | right | both | inline-start | inline-end",clip:"<shape> | auto","clip-path":"<clip-source> | [ <basic-shape> || <geometry-box> ] | none",color:"<color>","color-adjust":"economy | exact","column-count":"<integer> | auto","column-fill":"auto | balance | balance-all","column-gap":"normal | <length-percentage>","column-rule":"<'column-rule-width'> || <'column-rule-style'> || <'column-rule-color'>","column-rule-color":"<color>","column-rule-style":"<'border-style'>","column-rule-width":"<'border-width'>","column-span":"none | all","column-width":"<length> | auto",columns:"<'column-width'> || <'column-count'>",contain:"none | strict | content | [ size || layout || style || paint ]",content:"normal | none | [ <content-replacement> | <content-list> ] [ / <string> ]?","counter-increment":"[ <custom-ident> <integer>? ]+ | none","counter-reset":"[ <custom-ident> <integer>? ]+ | none",cursor:"[ [ <url> [ <x> <y> ]? , ]* [ auto | default | none | context-menu | help | pointer | progress | wait | cell | crosshair | text | vertical-text | alias | copy | move | no-drop | not-allowed | e-resize | n-resize | ne-resize | nw-resize | s-resize | se-resize | sw-resize | w-resize | ew-resize | ns-resize | nesw-resize | nwse-resize | col-resize | row-resize | all-scroll | zoom-in | zoom-out | grab | grabbing | hand | -webkit-grab | -webkit-grabbing | -webkit-zoom-in | -webkit-zoom-out | -moz-grab | -moz-grabbing | -moz-zoom-in | -moz-zoom-out ] ]",direction:"ltr | rtl",display:"none | inline | block | list-item | inline-list-item | inline-block | inline-table | table | table-cell | table-column | table-column-group | table-footer-group | table-header-group | table-row | table-row-group | flex | inline-flex | grid | inline-grid | run-in | ruby | ruby-base | ruby-text | ruby-base-container | ruby-text-container | contents | -ms-flexbox | -ms-inline-flexbox | -ms-grid | -ms-inline-grid | -webkit-flex | -webkit-inline-flex | -webkit-box | -webkit-inline-box | -moz-inline-stack | -moz-box | -moz-inline-box","empty-cells":"show | hide",filter:"none | <filter-function-list> | <-ms-filter>",flex:"none | [ <'flex-grow'> <'flex-shrink'>? || <'flex-basis'> ]","flex-basis":"content | <'width'>","flex-direction":"row | row-reverse | column | column-reverse","flex-flow":"<'flex-direction'> || <'flex-wrap'>","flex-grow":"<number>","flex-shrink":"<number>","flex-wrap":"nowrap | wrap | wrap-reverse",float:"left | right | none | inline-start | inline-end",font:"[ [ <'font-style'> || <font-variant-css21> || <'font-weight'> || <'font-stretch'> ]? <'font-size'> [ / <'line-height'> ]? <'font-family'> ] | caption | icon | menu | message-box | small-caption | status-bar | <-non-standard-font>","font-family":"[ <family-name> | <generic-family> ]#","font-feature-settings":"normal | <feature-tag-value>#","font-kerning":"auto | normal | none","font-language-override":"normal | <string>","font-optical-sizing":"auto | none","font-variation-settings":"normal | [ <string> <number> ]#","font-size":"<absolute-size> | <relative-size> | <length-percentage>","font-size-adjust":"none | <number>","font-stretch":"normal | ultra-condensed | extra-condensed | condensed | semi-condensed | semi-expanded | expanded | extra-expanded | ultra-expanded","font-style":"normal | italic | oblique","font-synthesis":"none | [ weight || style ]","font-variant":"normal | none | [ <common-lig-values> || <discretionary-lig-values> || <historical-lig-values> || <contextual-alt-values> || stylistic( <feature-value-name> ) || historical-forms || styleset( <feature-value-name># ) || character-variant( <feature-value-name># ) || swash( <feature-value-name> ) || ornaments( <feature-value-name> ) || annotation( <feature-value-name> ) || [ small-caps | all-small-caps | petite-caps | all-petite-caps | unicase | titling-caps ] || <numeric-figure-values> || <numeric-spacing-values> || <numeric-fraction-values> || ordinal || slashed-zero || <east-asian-variant-values> || <east-asian-width-values> || ruby ]","font-variant-alternates":"normal | [ stylistic( <feature-value-name> ) || historical-forms || styleset( <feature-value-name># ) || character-variant( <feature-value-name># ) || swash( <feature-value-name> ) || ornaments( <feature-value-name> ) || annotation( <feature-value-name> ) ]","font-variant-caps":"normal | small-caps | all-small-caps | petite-caps | all-petite-caps | unicase | titling-caps","font-variant-east-asian":"normal | [ <east-asian-variant-values> || <east-asian-width-values> || ruby ]","font-variant-ligatures":"normal | none | [ <common-lig-values> || <discretionary-lig-values> || <historical-lig-values> || <contextual-alt-values> ]","font-variant-numeric":"normal | [ <numeric-figure-values> || <numeric-spacing-values> || <numeric-fraction-values> || ordinal || slashed-zero ]","font-variant-position":"normal | sub | super","font-weight":"normal | bold | bolder | lighter | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900",gap:"<'row-gap'> <'column-gap'>?",grid:"<'grid-template'> | <'grid-template-rows'> / [ auto-flow && dense? ] <'grid-auto-columns'>? | [ auto-flow && dense? ] <'grid-auto-rows'>? / <'grid-template-columns'>","grid-area":"<grid-line> [ / <grid-line> ]{0,3}","grid-auto-columns":"<track-size>+","grid-auto-flow":"[ row | column ] || dense","grid-auto-rows":"<track-size>+","grid-column":"<grid-line> [ / <grid-line> ]?","grid-column-end":"<grid-line>","grid-column-gap":"<length-percentage>","grid-column-start":"<grid-line>","grid-gap":"<'grid-row-gap'> <'grid-column-gap'>?","grid-row":"<grid-line> [ / <grid-line> ]?","grid-row-end":"<grid-line>","grid-row-gap":"<length-percentage>","grid-row-start":"<grid-line>","grid-template":"none | [ <'grid-template-rows'> / <'grid-template-columns'> ] | [ <line-names>? <string> <track-size>? <line-names>? ]+ [ / <explicit-track-list> ]?","grid-template-areas":"none | <string>+","grid-template-columns":"none | <track-list> | <auto-track-list>","grid-template-rows":"none | <track-list> | <auto-track-list>","hanging-punctuation":"none | [ first || [ force-end | allow-end ] || last ]",height:"[ <length> | <percentage> ] && [ border-box | content-box ]? | available | min-content | max-content | fit-content | auto",hyphens:"none | manual | auto","image-orientation":"from-image | <angle> | [ <angle>? flip ]","image-rendering":"auto | crisp-edges | pixelated | optimizeSpeed | optimizeQuality | <-non-standard-image-rendering>","image-resolution":"[ from-image || <resolution> ] && snap?","ime-mode":"auto | normal | active | inactive | disabled","initial-letter":"normal | [ <number> <integer>? ]","initial-letter-align":"[ auto | alphabetic | hanging | ideographic ]","inline-size":"<'width'>",isolation:"auto | isolate","justify-content":"normal | <content-distribution> | <overflow-position>? [ <content-position> | left | right ]","justify-items":"normal | stretch | <baseline-position> | <overflow-position>? [ <self-position> | left | right ] | legacy | legacy && [ left | right | center ]","justify-self":"auto | normal | stretch | <baseline-position> | <overflow-position>? [ <self-position> | left | right ]",left:"<length> | <percentage> | auto","letter-spacing":"normal | <length-percentage>","line-break":"auto | loose | normal | strict","line-clamp":"none | <integer>","line-height":"normal | <number> | <length> | <percentage>","line-height-step":"none | <length>","list-style":"<'list-style-type'> || <'list-style-position'> || <'list-style-image'>","list-style-image":"<url> | none","list-style-position":"inside | outside","list-style-type":"<counter-style> | <string> | none",margin:"[ <length> | <percentage> | auto ]{1,4}","margin-block-end":"<'margin-left'>","margin-block-start":"<'margin-left'>","margin-bottom":"<length> | <percentage> | auto","margin-inline-end":"<'margin-left'>","margin-inline-start":"<'margin-left'>","margin-left":"<length> | <percentage> | auto","margin-right":"<length> | <percentage> | auto","margin-top":"<length> | <percentage> | auto",mask:"<mask-layer>#","mask-border":"<'mask-border-source'> || <'mask-border-slice'> [ / <'mask-border-width'>? [ / <'mask-border-outset'> ]? ]? || <'mask-border-repeat'> || <'mask-border-mode'>","mask-border-mode":"luminance | alpha","mask-border-outset":"[ <length> | <number> ]{1,4}","mask-border-repeat":"[ stretch | repeat | round | space ]{1,2}","mask-border-slice":"<number-percentage>{1,4} fill?","mask-border-source":"none | <image>","mask-border-width":"[ <length-percentage> | <number> | auto ]{1,4}","mask-clip":"[ <geometry-box> | no-clip ]#","mask-composite":"<compositing-operator>#","mask-image":"<mask-reference>#","mask-mode":"<masking-mode>#","mask-origin":"<geometry-box>#","mask-position":"<position>#","mask-repeat":"<repeat-style>#","mask-size":"<bg-size>#","mask-type":"luminance | alpha","max-block-size":"<'max-width'>","max-height":"<length> | <percentage> | none | max-content | min-content | fit-content | fill-available","max-inline-size":"<'max-width'>","max-lines":"none | <integer>","max-width":"<length> | <percentage> | none | max-content | min-content | fit-content | fill-available | <-non-standard-width>","min-block-size":"<'min-width'>","min-height":"<length> | <percentage> | auto | max-content | min-content | fit-content | fill-available","min-inline-size":"<'min-width'>","min-width":"<length> | <percentage> | auto | max-content | min-content | fit-content | fill-available | <-non-standard-width>","mix-blend-mode":"<blend-mode>","object-fit":"fill | contain | cover | none | scale-down","object-position":"<position>",offset:"[ <'offset-position'>? [ <'offset-path'> [ <'offset-distance'> || <'offset-rotate'> ]? ]? ]! [ / <'offset-anchor'> ]?","offset-anchor":"auto | <position>","offset-block-end":"<'left'>","offset-block-start":"<'left'>","offset-inline-end":"<'left'>","offset-inline-start":"<'left'>","offset-distance":"<length-percentage>","offset-path":"none | ray( [ <angle> && <size>? && contain? ] ) | <path()> | <url> | [ <basic-shape> || <geometry-box> ]","offset-position":"auto | <position>","offset-rotate":"[ auto | reverse ] || <angle>",opacity:"<number-zero-one>",order:"<integer>",orphans:"<integer>",outline:"[ <'outline-color'> || <'outline-style'> || <'outline-width'> ]","outline-color":"<color> | invert","outline-offset":"<length>","outline-style":"auto | <br-style>","outline-width":"<br-width>",overflow:"visible | hidden | scroll | auto | <-non-standard-overflow>","overflow-anchor":"auto | none","overflow-block":"<'overflow'>","overflow-clip-box":"padding-box | content-box","overflow-inline":"<'overflow'>","overflow-wrap":"normal | break-word","overflow-x":"visible | hidden | clip | scroll | auto","overflow-y":"visible | hidden | clip | scroll | auto","overscroll-behavior":"[ contain | none | auto ]{1,2}","overscroll-behavior-x":"contain | none | auto","overscroll-behavior-y":"contain | none | auto",padding:"[ <length> | <percentage> ]{1,4}","padding-block-end":"<'padding-left'>","padding-block-start":"<'padding-left'>","padding-bottom":"<length> | <percentage>","padding-inline-end":"<'padding-left'>","padding-inline-start":"<'padding-left'>","padding-left":"<length> | <percentage>","padding-right":"<length> | <percentage>","padding-top":"<length> | <percentage>","page-break-after":"auto | always | avoid | left | right | recto | verso","page-break-before":"auto | always | avoid | left | right | recto | verso","page-break-inside":"auto | avoid","paint-order":"normal | [ fill || stroke || markers ]",perspective:"none | <length>","perspective-origin":"<position>","place-content":"<'align-content'> <'justify-content'>?","pointer-events":"auto | none | visiblePainted | visibleFill | visibleStroke | visible | painted | fill | stroke | all | inherit",position:"static | relative | absolute | sticky | fixed | -webkit-sticky",quotes:"none | [ <string> <string> ]+",resize:"none | both | horizontal | vertical",right:"<length> | <percentage> | auto",rotate:"none | [ x | y | z | <number>{3} ]? && <angle>","row-gap":"normal | <length-percentage>","ruby-align":"start | center | space-between | space-around","ruby-merge":"separate | collapse | auto","ruby-position":"over | under | inter-character",scale:"none | <number>{1,3}","scroll-behavior":"auto | smooth","scroll-snap-coordinate":"none | <position>#","scroll-snap-destination":"<position>","scroll-snap-points-x":"none | repeat( <length-percentage> )","scroll-snap-points-y":"none | repeat( <length-percentage> )","scroll-snap-type":"none | mandatory | proximity","scroll-snap-type-x":"none | mandatory | proximity","scroll-snap-type-y":"none | mandatory | proximity","shape-image-threshold":"<number>","shape-margin":"<length-percentage>","shape-outside":"none | <shape-box> || <basic-shape> | <image>","tab-size":"<integer> | <length>","table-layout":"auto | fixed","text-align":"start | end | left | right | center | justify | match-parent","text-align-last":"auto | start | end | left | right | center | justify","text-combine-upright":"none | all | [ digits <integer>? ]","text-decoration":"<'text-decoration-line'> || <'text-decoration-style'> || <'text-decoration-color'>","text-decoration-color":"<color>","text-decoration-line":"none | [ underline || overline || line-through || blink ]","text-decoration-skip":"none | [ objects || [ spaces | [ leading-spaces || trailing-spaces ] ] || edges || box-decoration ]","text-decoration-skip-ink":"auto | none","text-decoration-style":"solid | double | dotted | dashed | wavy","text-emphasis":"<'text-emphasis-style'> || <'text-emphasis-color'>","text-emphasis-color":"<color>","text-emphasis-position":"[ over | under ] && [ right | left ]","text-emphasis-style":"none | [ [ filled | open ] || [ dot | circle | double-circle | triangle | sesame ] ] | <string>","text-indent":"<length-percentage> && hanging? && each-line?","text-justify":"auto | inter-character | inter-word | none","text-orientation":"mixed | upright | sideways","text-overflow":"[ clip | ellipsis | <string> ]{1,2}","text-rendering":"auto | optimizeSpeed | optimizeLegibility | geometricPrecision","text-shadow":"none | <shadow-t>#","text-size-adjust":"none | auto | <percentage>","text-transform":"none | capitalize | uppercase | lowercase | full-width","text-underline-position":"auto | [ under || [ left | right ] ]",top:"<length> | <percentage> | auto","touch-action":"auto | none | [ [ pan-x | pan-left | pan-right ] || [ pan-y | pan-up | pan-down ] || pinch-zoom ] | manipulation",transform:"none | <transform-list>","transform-box":"border-box | fill-box | view-box","transform-origin":"[ [ <length-percentage> | left | center | right ] && [ <length-percentage> | top | center | bottom ] ] <length>? | [ <length-percentage> | left | center | right | top | bottom ]","transform-style":"flat | preserve-3d",transition:"<single-transition>#","transition-delay":"<time>#","transition-duration":"<time>#","transition-property":"none | <single-transition-property>#","transition-timing-function":"<single-transition-timing-function>#",translate:"none | <length-percentage> [ <length-percentage> <length>? ]?","unicode-bidi":"normal | embed | isolate | bidi-override | isolate-override | plaintext | -moz-isolate | -moz-isolate-override | -moz-plaintext | -webkit-isolate","user-select":"auto | text | none | contain | all","vertical-align":"baseline | sub | super | text-top | text-bottom | middle | top | bottom | <percentage> | <length>",visibility:"visible | hidden | collapse","white-space":"normal | pre | nowrap | pre-wrap | pre-line",widows:"<integer>",width:"[ <length> | <percentage> ] && [ border-box | content-box ]? | available | min-content | max-content | fit-content | auto","will-change":"auto | <animateable-feature>#","word-break":"normal | break-all | keep-all | <-non-standard-word-break>","word-spacing":"normal | <length-percentage>","word-wrap":"normal | break-word","writing-mode":"horizontal-tb | vertical-rl | vertical-lr | sideways-rl | sideways-lr | <svg-writing-mode>","z-index":"auto | <integer>",zoom:"normal | reset | <number> | <percentage>","-moz-background-clip":"padding | border","-moz-border-radius-bottomleft":"<'border-bottom-left-radius'>","-moz-border-radius-bottomright":"<'border-bottom-right-radius'>","-moz-border-radius-topleft":"<'border-top-left-radius'>","-moz-border-radius-topright":"<'border-bottom-right-radius'>","-moz-osx-font-smoothing":"auto | grayscale","-moz-user-select":"none | text | all | -moz-none","-ms-flex-align":"start | end | center | baseline | stretch","-ms-flex-item-align":"auto | start | end | center | baseline | stretch","-ms-flex-line-pack":"start | end | center | justify | distribute | stretch","-ms-flex-negative":"<'flex-shrink'>","-ms-flex-pack":"start | end | center | justify | distribute","-ms-flex-order":"<integer>","-ms-flex-positive":"<'flex-grow'>","-ms-flex-preferred-size":"<'flex-basis'>","-ms-interpolation-mode":"nearest-neighbor | bicubic","-ms-grid-column-align":"start | end | center | stretch","-ms-grid-row-align":"start | end | center | stretch","-webkit-background-clip":"[ <box> | border | padding | content | text ]#","-webkit-column-break-after":"always | auto | avoid","-webkit-column-break-before":"always | auto | avoid","-webkit-column-break-inside":"always | auto | avoid","-webkit-font-smoothing":"auto | none | antialiased | subpixel-antialiased","-webkit-line-clamp":"<positive-integer>","-webkit-mask-box-image":"[ <url> | <gradient> | none ] [ <length-percentage>{4} <-webkit-mask-box-repeat>{2} ]?","-webkit-print-color-adjust":"economy | exact","-webkit-text-security":"none | circle | disc | square","-webkit-user-drag":"none | element | auto","-webkit-user-select":"auto | none | text | all","alignment-baseline":"auto | baseline | before-edge | text-before-edge | middle | central | after-edge | text-after-edge | ideographic | alphabetic | hanging | mathematical","baseline-shift":"baseline | sub | super | <svg-length>",behavior:"<url>+","clip-rule":"nonzero | evenodd",cue:"<'cue-before'> <'cue-after'>?","cue-after":"<url> <decibel>? | none","cue-before":"<url> <decibel>? | none","dominant-baseline":"auto | use-script | no-change | reset-size | ideographic | alphabetic | hanging | mathematical | central | middle | text-after-edge | text-before-edge",fill:"<paint>","fill-opacity":"<number-zero-one>","fill-rule":"nonzero | evenodd","glyph-orientation-horizontal":"<angle>","glyph-orientation-vertical":"<angle>",kerning:"auto | <svg-length>",marker:"none | <url>","marker-end":"none | <url>","marker-mid":"none | <url>","marker-start":"none | <url>",pause:"<'pause-before'> <'pause-after'>?","pause-after":"<time> | none | x-weak | weak | medium | strong | x-strong","pause-before":"<time> | none | x-weak | weak | medium | strong | x-strong",rest:"<'rest-before'> <'rest-after'>?","rest-after":"<time> | none | x-weak | weak | medium | strong | x-strong","rest-before":"<time> | none | x-weak | weak | medium | strong | x-strong","shape-rendering":"auto | optimizeSpeed | crispEdges | geometricPrecision",src:"[ <url> [ format( <string># ) ]? | local( <family-name> ) ]#",speak:"auto | none | normal","speak-as":"normal | spell-out || digits || [ literal-punctuation | no-punctuation ]",stroke:"<paint>","stroke-dasharray":"none | [ <svg-length>+ ]#","stroke-dashoffset":"<svg-length>","stroke-linecap":"butt | round | square","stroke-linejoin":"miter | round | bevel","stroke-miterlimit":"<number-one-or-greater>","stroke-opacity":"<number-zero-one>","stroke-width":"<svg-length>","text-anchor":"start | middle | end","unicode-range":"<unicode-range>#","voice-balance":"<number> | left | center | right | leftwards | rightwards","voice-duration":"auto | <time>","voice-family":"[ [ <family-name> | <generic-voice> ] , ]* [ <family-name> | <generic-voice> ] | preserve","voice-pitch":"<frequency> && absolute | [ [ x-low | low | medium | high | x-high ] || [ <frequency> | <semitones> | <percentage> ] ]","voice-range":"<frequency> && absolute | [ [ x-low | low | medium | high | x-high ] || [ <frequency> | <semitones> | <percentage> ] ]","voice-rate":"[ normal | x-slow | slow | medium | fast | x-fast ] || <percentage>","voice-stress":"normal | strong | moderate | none | reduced","voice-volume":"silent | [ [ x-soft | soft | medium | loud | x-loud ] || <decibel> ]"}}},{}],41:[function(e,t,n){var r=e("../utils/list");t.exports=function(e){return{fromPlainObject:function(t){return e(t,{enter:function(e){e.children&&e.children instanceof r==!1&&(e.children=(new r).fromArray(e.children))}}),t},toPlainObject:function(t){return e(t,{leave:function(e){e.children&&e.children instanceof r&&(e.children=e.children.toArray())}}),t}}}},{"../utils/list":144}],42:[function(e,t,n){"use strict";function r(e,t){var n=e.children,r=null;"function"!=typeof t?n.forEach(this.node,this):n.forEach(function(e){null!==r&&t.call(this,r),this.node(e),r=e},this)}var o=e("./sourceMap"),i=Object.prototype.hasOwnProperty;t.exports=function(e){function t(e){if(!i.call(n,e.type))throw new Error("Unknown node type: "+e.type);n[e.type].call(this,e)}var n={};if(e.node)for(var a in e.node)n[a]=e.node[a].generate;return function(e,n){var i="",a={children:r,node:t,chunk:function(e){i+=e},result:function(){return i}};return n&&("function"==typeof n.decorator&&(a=n.decorator(a)),n.sourceMap&&(a=o(a))),a.node(e),a.result()}}},{"./sourceMap":43}],43:[function(e,t,n){"use strict";var r=e("source-map").SourceMapGenerator,o={Atrule:!0,Selector:!0,Declaration:!0};t.exports=function(e){var t=new r,n=1,i=0,a={line:1,column:0},s={line:0,column:0},l=!1,c={line:1,column:0},u={generated:c},h=e.node;e.node=function(e){if(e.loc&&e.loc.start&&o.hasOwnProperty(e.type)){var r=e.loc.start.line,p=e.loc.start.column-1;s.line===r&&s.column===p||(s.line=r,s.column=p,a.line=n,a.column=i,l&&(l=!1,a.line===c.line&&a.column===c.column||t.addMapping(u)),l=!0,t.addMapping({source:e.loc.source,original:s,generated:a}))}h.call(this,e),l&&o.hasOwnProperty(e.type)&&(c.line=n,c.column=i)};var p=e.chunk;e.chunk=function(e){for(var t=0;t<e.length;t++)10===e.charCodeAt(t)?(n++,i=0):i++;p(e)};var d=e.result;return e.result=function(){return l&&t.addMapping(u),{css:d(),map:t}},e}},{"source-map":157}],44:[function(e,t,n){"use strict";t.exports=e("./syntax")},{"./syntax":76}],45:[function(e,t,n){"use strict";function r(e,t){var n={};for(var r in e)e[r].syntax&&(n[r]=t?e[r].syntax:p(e[r].syntax));return n}function o(e){var t=!1;return this.syntax.walk(e,function(e){"Function"===e.type&&"var"===e.name.toLowerCase()&&(t=!0)}),t}function i(e,t,n){return{matched:e,iterations:n,error:t,getTrace:b.getTrace,isType:b.isType,isProperty:b.isProperty,isKeyword:b.isKeyword}}function a(e,t,n,r){if(!n)return i(null,new Error("Node is undefined"));if(o.call(e,n))return i(null,new Error("Matching for a tree with var() is not supported"));var a,s=e.syntax.generate(n,f);return r&&(a=g(s,e.valueCommonSyntax,e)),r&&a.match||(a=g(s,t.match,e),a.match)?i(a.match,null,a.iterations):i(null,new l(a.reason,e,t.syntax,n,a),a.iterations)}var s=e("./error").SyntaxReferenceError,l=e("./error").MatchError,c=e("../utils/names"),u=e("./generic"),h=e("./grammar/parse"),p=e("./grammar/generate"),d=e("./grammar/walk"),f=e("./ast-to-tokens"),m=e("./match-graph").buildMatchGraph,g=e("./match").matchAsTree,b=e("./trace"),y=e("./search"),v=e("./structure").getStructureFromConfig,k=m(h("inherit | initial | unset")),x=m(h("inherit | initial | unset | <expression>")),w=function(e,t,n){if(this.valueCommonSyntax=k,this.syntax=t,this.generic=!1,this.properties={},this.types={},this.structure=n||v(e),e){if(e.generic){this.generic=!0;for(var r in u)this.addType_(r,u[r])}if(e.types)for(var r in e.types)this.addType_(r,e.types[r]);if(e.properties)for(var r in e.properties)this.addProperty_(r,e.properties[r])}};w.prototype={structure:{},checkStructure:function(e){function t(e,t){r.push({node:e,message:t})}var n=this.structure,r=[];return this.syntax.walk(e,function(e){n.hasOwnProperty(e.type)?n[e.type].check(e,t):t(e,"Unknown node type `"+e.type+"`")}),!!r.length&&r},createDescriptor:function(e,t,n){var r={type:t,name:n},o={type:t,name:n,syntax:null,match:null};return"function"==typeof e?o.match=m(e,r):("string"==typeof e?Object.defineProperty(o,"syntax",{get:function(){return Object.defineProperty(o,"syntax",{value:h(e)}),o.syntax}}):o.syntax=e,Object.defineProperty(o,"match",{get:function(){return Object.defineProperty(o,"match",{value:m(o.syntax,r)}),o.match}})),o},addProperty_:function(e,t){
this.properties[e]=this.createDescriptor(t,"Property",e)},addType_:function(e,t){this.types[e]=this.createDescriptor(t,"Type",e),t===u.expression&&(this.valueCommonSyntax=x)},matchDeclaration:function(e){return"Declaration"!==e.type?i(null,new Error("Not a Declaration node")):this.matchProperty(e.property,e.value)},matchProperty:function(e,t){var n=c.property(e);if(n.custom)return i(null,new Error("Lexer matching doesn't applicable for custom properties"));var r=n.vendor?this.getProperty(n.name)||this.getProperty(n.basename):this.getProperty(n.name);return r?a(this,r,t,!0):i(null,new s("Unknown property",e))},matchType:function(e,t){var n=this.getType(e);return n?a(this,n,t,!1):i(null,new s("Unknown type",e))},match:function(e,t){return e&&e.type?(e.match||(e=this.createDescriptor(e)),a(this,e,t,!1)):i(null,new s("Bad syntax"))},findValueFragments:function(e,t,n,r){return y.matchFragments(this,t,this.matchProperty(e,t),n,r)},findDeclarationValueFragments:function(e,t,n){return y.matchFragments(this,e.value,this.matchDeclaration(e),t,n)},findAllFragments:function(e,t,n){var r=[];return this.syntax.walk(e,{visit:"Declaration",enter:function(e){r.push.apply(r,this.findDeclarationValueFragments(e,t,n))}.bind(this)}),r},getProperty:function(e){return this.properties.hasOwnProperty(e)?this.properties[e]:null},getType:function(e){return this.types.hasOwnProperty(e)?this.types[e]:null},validate:function(){function e(r,o,i,a){if(i.hasOwnProperty(o))return i[o];i[o]=!1,null!==a.syntax&&d(a.syntax,function(a){if("Type"===a.type||"Property"===a.type){var s="Type"===a.type?r.types:r.properties,l="Type"===a.type?t:n;s.hasOwnProperty(a.name)&&!e(r,a.name,l,s[a.name])||(i[o]=!0)}},this)}var t={},n={};for(var r in this.types)e(this,r,t,this.types[r]);for(var r in this.properties)e(this,r,n,this.properties[r]);return t=Object.keys(t).filter(function(e){return t[e]}),n=Object.keys(n).filter(function(e){return n[e]}),t.length||n.length?{types:t,properties:n}:null},dump:function(e){return{generic:this.generic,types:r(this.types,e),properties:r(this.properties,e)}},toString:function(){return JSON.stringify(this.dump())}},t.exports=w},{"../utils/names":145,"./ast-to-tokens":46,"./error":47,"./generic":48,"./grammar/generate":50,"./grammar/parse":52,"./grammar/walk":54,"./match":56,"./match-graph":55,"./search":57,"./structure":58,"./trace":59}],46:[function(e,t,n){t.exports={decorator:function(e){var t=null,n=null,r=[];return{children:e.children,node:function(n){var r=t;t=n,e.node.call(this,n),t=r},chunk:function(e){if(r.length>0)switch(t.type){case"Dimension":case"HexColor":case"IdSelector":case"Percentage":if(n.node===t)return void(n.value+=e);break;case"Function":case"PseudoClassSelector":case"PseudoElementSelector":case"Url":if("("===e)return void(n.value+=e);break;case"Atrule":if(n.node===t&&"@"===n.value)return void(n.value+=e)}r.push(n={value:e,node:t})},result:function(){return r}}}}},{}],47:[function(e,t,n){"use strict";function r(e){for(var t=e.tokens,n=e.longestMatch,r=n<t.length?t[n].node:null,o=0,i=0,a="",s=0;s<t.length;s++)s===n&&(o=a.length),null!==r&&t[s].node===r&&(s<=n?i++:i=0),a+=t[s].value;return null===r&&(o=a.length),{node:r,css:a,mismatchOffset:o,last:null===r||i>1}}function o(e,t){var n=e&&e.loc&&e.loc[t];return n?{offset:n.offset,line:n.line,column:n.column}:null}var i=e("../utils/createCustomError"),a=e("./grammar/generate"),s=function(e,t){var n=i("SyntaxReferenceError",e+(t?" `"+t+"`":""));return n.reference=t,n},l=function(e,t,n,s,l){var c=i("SyntaxMatchError",e),u=r(l),h=u.mismatchOffset||0,p=u.node||s,d=o(p,"end"),f=u.last?d:o(p,"start"),m=u.css;return c.rawMessage=e,c.syntax=n?a(n):"<generic>",c.css=m,c.mismatchOffset=h,c.loc={source:p&&p.loc&&p.loc.source||"<unknown>",start:f,end:d},c.line=f?f.line:void 0,c.column=f?f.column:void 0,c.offset=f?f.offset:void 0,c.message=e+"\n  syntax: "+c.syntax+"\n   value: "+(c.css||"<empty string>")+"\n  --------"+new Array(c.mismatchOffset+1).join("-")+"^",c};t.exports={SyntaxReferenceError:s,MatchError:l}},{"../utils/createCustomError":143,"./grammar/generate":50}],48:[function(e,t,n){function r(e,t,n){var r,o=1;do{r=n(o++)}while(null!==r&&r.node!==e.node);if(null===r)return!1;for(;;)if(t()===r)break;return!0}function o(e,t,n){if(null===e)return!1;var o=e.value.toLowerCase();return("calc("===o||"-moz-calc("===o||"-webkit-calc("===o)&&r(e,t,n)}function i(e,t,n){return null!==e&&"attr("===e.value.toLowerCase()&&r(e,t,n)}function a(e,t,n){return null!==e&&"expression("===e.value.toLowerCase()&&r(e,t,n)}function s(e,t,n){return null!==e&&"url("===e.value.toLowerCase()&&r(e,t,n)}function l(e,t){return null!==e&&(e.value.charCodeAt(0)===_&&(h(e.value,1)===e.value.length&&(t(),!0)))}function c(e){return/^[-+]?(\d+|\d*\.\d+)([eE][-+]?\d+)?$/.test(e)}function u(e,t){var n=e.charCodeAt(0);return C(e,n===E||n===O?1:0,t)}function h(e,t){var n=e.charCodeAt(t);return n<128&&L[n]!==P&&n!==O?t:S(e,t+1)}function p(e){return function(t,n){return null!==t&&t.node.type===e&&(n(),!0)}}function d(e){return function(t,n,r){if(o(t,n,r))return!0;if(null===t)return!1;var i=u(t.value,!0);if(0===i)return!1;if(e){if(!e.hasOwnProperty(t.value.substr(i).toLowerCase()))return!1}else{var a=h(t.value,i);if(a===i||a!==t.value.length)return!1}return n(),!0}}function f(e){var t=d(e);return function(e,n,r){return!!t(e,n,r)||null!==e&&0===Number(e.value)&&(n(),!0)}}function m(e,t,n){return!!o(e,t,n)||null!==e&&(u(e.value,!0)===e.value.length&&(t(),!0))}function g(e,t,n){if(o(e,t,n))return!0;if(null===e||!c(e.value))return!1;var r=Number(e.value);return!(r<0||r>1)&&(t(),!0)}function b(e,t,n){return!!o(e,t,n)||!(null===e||!c(e.value))&&(!(Number(e.value)<1)&&(t(),!0))}function y(e,t,n){return!!o(e,t,n)||null!==e&&(u(e.value,!1)===e.value.length&&(t(),!0))}function v(e,t,n){return!!o(e,t,n)||null!==e&&(A(e.value,0)===e.value.length&&e.value.charCodeAt(0)!==O&&(t(),!0))}function k(e,t){if(null===e||e.value.charCodeAt(0)!==_)return!1;var n=e.value.length-1;if(3!==n&&4!==n&&6!==n&&8!==n)return!1;for(var r=1;r<n;r++)if(!z(e.value.charCodeAt(r)))return!1;return t(),!0}function x(e,t){if(null===e)return!1;if(h(e.value,0)!==e.value.length)return!1;var n=e.value.toLowerCase();return"unset"!==n&&"initial"!==n&&"inherit"!==n&&("default"!==n&&(t(),!0))}var w=e("../tokenizer/utils"),S=w.findIdentifierEnd,C=w.findNumberEnd,A=w.findDecimalNumberEnd,z=w.isHex,T=e("../tokenizer/const"),L=T.SYMBOL_TYPE,P=T.TYPE.Identifier,E=T.TYPE.PlusSign,O=T.TYPE.HyphenMinus,_=T.TYPE.NumberSign,M={"%":!0},R={px:!0,mm:!0,cm:!0,in:!0,pt:!0,pc:!0,q:!0,em:!0,ex:!0,ch:!0,rem:!0,vh:!0,vw:!0,vmin:!0,vmax:!0,vm:!0},N={deg:!0,grad:!0,rad:!0,turn:!0},D={s:!0,ms:!0},B={hz:!0,khz:!0},j={dpi:!0,dpcm:!0,dppx:!0,x:!0},I={fr:!0},F={db:!0},W={st:!0};t.exports={angle:f(N),"attr()":i,"custom-ident":x,decibel:d(F),dimension:d(),frequency:d(B),flex:d(I),"hex-color":k,"id-selector":l,ident:p("Identifier"),integer:y,length:f(R),number:m,"number-zero-one":g,"number-one-or-greater":b,percentage:d(M),"positive-integer":v,resolution:d(j),semitones:d(W),string:p("String"),time:d(D),"unicode-range":p("UnicodeRange"),url:s,progid:p("Raw"),expression:a}},{"../tokenizer/const":138,"../tokenizer/utils":141}],49:[function(e,t,n){var r=e("../../utils/createCustomError"),o=function(e,t,n){var o=r("SyntaxParseError",e);return o.input=t,o.offset=n,o.rawMessage=e,o.message=o.rawMessage+"\n  "+o.input+"\n--"+new Array((o.offset||o.input.length)+1).join("-")+"^",o};t.exports={SyntaxParseError:o}},{"../../utils/createCustomError":143}],50:[function(e,t,n){function r(e){return e}function o(e){return 0===e.min&&0===e.max?"*":0===e.min&&1===e.max?"?":1===e.min&&0===e.max?e.comma?"#":"+":1===e.min&&1===e.max?"":(e.comma?"#":"")+(e.min===e.max?"{"+e.min+"}":"{"+e.min+","+(0!==e.max?e.max:"")+"}")}function i(e,t,n){var r=e.terms.map(function(e){return a(e,t,n)}).join(" "===e.combinator?" ":" "+e.combinator+" ");return(e.explicit||t)&&(r=(","!==r[0]?"[ ":"[")+r+" ]"),r}function a(e,t,n){var r;switch(e.type){case"Group":r=i(e,t,n)+(e.disallowEmpty?"!":"");break;case"Multiplier":return a(e.term,t,n)+n(o(e),e);case"Type":r="<"+e.name+">";break;case"Property":r="<'"+e.name+"'>";break;case"Keyword":r=e.name;break;case"AtKeyword":r="@"+e.name;break;case"Function":r=e.name+"(";break;case"String":case"Token":r=e.value;break;case"Comma":r=",";break;default:throw new Error("Unknown node type `"+e.type+"`")}return n(r,e)}t.exports=function(e,t){var n=r,o=!1;return"function"==typeof t?n=t:t&&(o=Boolean(t.forceBraces),"function"==typeof t.decorate&&(n=t.decorate)),a(e,o,n)}},{}],51:[function(e,t,n){t.exports={SyntaxParseError:e("./error").SyntaxParseError,parse:e("./parse"),generate:e("./generate"),walk:e("./walk")}},{"./error":49,"./generate":50,"./parse":52,"./walk":54}],52:[function(e,t,n){function r(e){return e.substringToPos(e.findWsEnd(e.pos+1))}function o(e){for(var t=e.pos;t<e.str.length;t++){var n=e.str.charCodeAt(t);if(n>=128||0===U[n])break}return e.pos===t&&e.error("Expect a keyword"),e.substringToPos(t)}function i(e){for(var t=e.pos;t<e.str.length;t++){var n=e.str.charCodeAt(t);if(n<48||n>57)break}return e.pos===t&&e.error("Expect a number"),e.substringToPos(t)}function a(e){var t=e.str.indexOf("'",e.pos+1);return-1===t&&(e.pos=e.str.length,e.error("Expect an apostrophe")),e.substringToPos(t+1)}function s(e){var t=null,n=null;return e.eat(F),t=i(e),e.charCode()===M?(e.pos++,e.charCode()!==q&&(n=i(e))):n=t,e.eat(q),{min:Number(t),max:n?Number(n):0}}function l(e){var t=null,n=!1;switch(e.charCode()){case O:e.pos++,t={min:0,max:0};break;case _:e.pos++,t={min:1,max:0};break;case D:e.pos++,t={min:0,max:1};break;case z:e.pos++,n=!0,t=e.charCode()===F?s(e):{min:1,max:0};break;case F:t=s(e);break;default:return null}return{type:"Multiplier",comma:n,min:t.min,max:t.max,term:null}}function c(e,t){var n=l(e);return null!==n?(n.term=t,n):t}function u(e){var t=e.peek();return""===t?null:{type:"Token",value:t}}function h(e){var t;return e.eat(R),e.eat(L),t=o(e),e.eat(L),e.eat(N),c(e,{type:"Property",name:t})}function p(e){var t;return e.eat(R),t=o(e),e.charCode()===P&&e.nextCharCode()===E&&(e.pos+=2,t+="()"),e.eat(N),c(e,{type:"Type",name:t})}function d(e){var t;return t=o(e),e.charCode()===P?(e.pos++,{type:"Function",name:t}):c(e,{type:"Keyword",name:t})}function f(e,t){function n(e,t){return{type:"Group",terms:e,combinator:t,disallowEmpty:!1,explicit:!1}}for(t=Object.keys(t).sort(function(e,t){return Y[e]-Y[t]});t.length>0;){for(var r=t.shift(),o=0,i=0;o<e.length;o++){var a=e[o];"Combinator"===a.type&&(a.value===r?(-1===i&&(i=o-1),e.splice(o,1),o--):(-1!==i&&o-i>1&&(e.splice(i,o-i,n(e.slice(i,o),r)),o=i+1),i=-1))}-1!==i&&t.length&&e.splice(i,o-i,n(e.slice(i,o),r))}return r}function m(e){for(var t,n=[],r={},o=null,i=e.pos;t=b(e);)"Spaces"!==t.type&&("Combinator"===t.type?(null!==o&&"Combinator"!==o.type||(e.pos=i,e.error("Unexpected combinator")),r[t.value]=!0):null!==o&&"Combinator"!==o.type&&(r[" "]=!0,n.push({type:"Combinator",value:" "})),n.push(t),o=t,i=e.pos);return null!==o&&"Combinator"===o.type&&(e.pos-=i,e.error("Unexpected combinator")),{type:"Group",terms:n,combinator:f(n,r)||" ",disallowEmpty:!1,explicit:!1}}function g(e){var t;return e.eat(j),t=m(e),e.eat(I),t.explicit=!0,e.charCode()===A&&(e.pos++,t.disallowEmpty=!0),t}function b(e){var t=e.charCode();if(t<128&&1===U[t])return d(e);switch(t){case I:break;case j:return c(e,g(e));case R:return e.nextCharCode()===L?h(e):p(e);case W:return{type:"Combinator",value:e.substringToPos(e.nextCharCode()===W?e.pos+2:e.pos+1)};case T:return e.pos++,e.eat(T),{type:"Combinator",value:"&&"};case M:return e.pos++,{type:"Comma"};case L:return c(e,{type:"String",value:a(e)});case C:case k:case x:case S:case w:return{type:"Spaces",value:r(e)};case B:return t=e.nextCharCode(),t<128&&1===U[t]?(e.pos++,{type:"AtKeyword",name:o(e)}):u(e);case O:case _:case D:case z:case A:break;case F:if((t=e.nextCharCode())<48||t>57)return u(e);break;default:return u(e)}}function y(e){var t=new v(e),n=m(t);return t.pos!==e.length&&t.error("Unexpected input"),1===n.terms.length&&"Group"===n.terms[0].type&&(n=n.terms[0]),n}var v=e("./tokenizer"),k=9,x=10,w=12,S=13,C=32,A=33,z=35,T=38,L=39,P=40,E=41,O=42,_=43,M=44,R=60,N=62,D=63,B=64,j=91,I=93,F=123,W=124,q=125,U=function(e){for(var t="function"==typeof Uint32Array?new Uint32Array(128):new Array(128),n=0;n<128;n++)t[n]=e(String.fromCharCode(n))?1:0;return t}(function(e){return/[a-zA-Z0-9\-]/.test(e)}),Y={" ":1,"&&":2,"||":3,"|":4};y("[a&&<b>#|<'c'>*||e() f{2} /,(% g#{1,2} h{2,})]!"),t.exports=y},{"./tokenizer":53}],53:[function(e,t,n){var r=e("./error").SyntaxParseError,o=function(e){this.str=e,this.pos=0};o.prototype={charCodeAt:function(e){return e<this.str.length?this.str.charCodeAt(e):0},charCode:function(){return this.charCodeAt(this.pos)},nextCharCode:function(){return this.charCodeAt(this.pos+1)},nextNonWsCode:function(e){return this.charCodeAt(this.findWsEnd(e))},findWsEnd:function(e){for(;e<this.str.length;e++){var t=this.str.charCodeAt(e);if(13!==t&&10!==t&&12!==t&&32!==t&&9!==t)break}return e},substringToPos:function(e){return this.str.substring(this.pos,this.pos=e)},eat:function(e){this.charCode()!==e&&this.error("Expect `"+String.fromCharCode(e)+"`"),this.pos++},peek:function(){return this.pos<this.str.length?this.str.charAt(this.pos++):""},error:function(e){throw new r(e,this.str,this.pos)}},t.exports=o},{"./error":49}],54:[function(e,t,n){"use strict";function r(e){return"function"==typeof e?e:o}var o=function(){};t.exports=function(e,t,n){function i(e){switch(a.call(n,e),e.type){case"Group":e.terms.forEach(i);break;case"Multiplier":i(e.term);break;case"Type":case"Property":case"Keyword":case"AtKeyword":case"Function":case"String":case"Token":case"Comma":break;default:throw new Error("Unknown type: "+e.type)}s.call(n,e)}var a=o,s=o;if("function"==typeof t?a=t:t&&(a=r(t.enter),s=r(t.leave)),a===o&&s===o)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");i(e,n)}},{}],55:[function(e,t,n){function r(e,t,n){return t===u&&n===h?e:e===u&&t===u&&n===u?e:("If"===e.type&&e.else===h&&t===u&&(t=e.then,e=e.match),{type:"If",match:e,then:t,else:n})}function o(e){return e.length>2&&e.charCodeAt(e.length-2)===d&&e.charCodeAt(e.length-1)===f}function i(e){return"Keyword"===e.type||"AtKeyword"===e.type||"Function"===e.type||"Type"===e.type&&o(e.name)}function a(e,t,n){switch(e){case" ":for(var s=u,l=t.length-1;l>=0;l--){var c=t[l];s=r(c,s,h)}return s;case"|":for(var s=h,p=null,l=t.length-1;l>=0;l--){var c=t[l];if(i(c)&&(null===p&&l>0&&i(t[l-1])&&(p=Object.create(null),s=r({type:"Enum",map:p},u,s)),null!==p)){var d=(o(c.name)?c.name.slice(0,-1):c.name).toLowerCase();if(d in p==!1){p[d]=c;continue}}p=null,s=r(c,u,s)}return s;case"&&":if(t.length>5)return{type:"MatchOnce",terms:t,all:!0};for(var s=h,l=t.length-1;l>=0;l--){var f,c=t[l];f=t.length>1?a(e,t.filter(function(e){return e!==c}),!1):u,s=r(c,f,s)}return s;case"||":if(t.length>5)return{type:"MatchOnce",terms:t,all:!1};for(var s=n?u:h,l=t.length-1;l>=0;l--){var f,c=t[l];f=t.length>1?a(e,t.filter(function(e){return e!==c}),!0):u,s=r(c,f,s)}return s}}function s(e){var t=u,n=l(e.term);if(0===e.max)n=r(n,p,h),t=r(n,null,h),t.then=r(u,u,t),e.comma&&(t.then.else=r({type:"Comma",syntax:e},t,h));else for(var o=e.min||1;o<=e.max;o++)e.comma&&t!==u&&(t=r({type:"Comma",syntax:e},t,h)),t=r(n,r(u,u,t),h);if(0===e.min)t=r(u,u,t);else for(var o=0;o<e.min-1;o++)e.comma&&t!==u&&(t=r({type:"Comma",syntax:e},t,h)),t=r(n,t,h);return t}function l(e){if("function"==typeof e)return{type:"Generic",fn:e};switch(e.type){case"Group":var t=a(e.combinator,e.terms.map(l),!1);return e.disallowEmpty&&(t=r(t,p,h)),t;case"Multiplier":return s(e);case"Type":case"Property":return{type:e.type,name:e.name,syntax:e};case"Keyword":return{type:e.type,name:e.name.toLowerCase(),syntax:e};case"AtKeyword":return{type:e.type,name:"@"+e.name.toLowerCase(),syntax:e};case"Function":return{type:e.type,name:e.name.toLowerCase()+"(",syntax:e};case"String":return 3===e.value.length?{type:"Token",value:e.value.charAt(1),syntax:e}:{type:e.type,value:e.value,syntax:e};case"Token":return{type:e.type,value:e.value,syntax:e};case"Comma":return{type:e.type,syntax:e};default:throw new Error("Unknown node type:",e.type)}}var c=e("./grammar/parse"),u={type:"Match"},h={type:"Mismatch"},p={type:"DisallowEmpty"},d=40,f=41;t.exports={MATCH:u,MISMATCH:h,DISALLOW_EMPTY:p,buildMatchGraph:function(e,t){return"string"==typeof e&&(e=c(e)),{type:"MatchGraph",match:l(e),syntax:t||null,source:e}}}},{"./grammar/parse":52}],56:[function(e,t,n){"use strict";function r(e,t){for(var n=[];e;)n.unshift(t(e)),e=e.prev;return n}function o(e){return null===e||(","===(e=e.value.charAt(e.value.length-1))||"("===e||"["===e||"/"===e)}function i(e){return null===e||(")"===(e=e.value.charAt(0))||"]"===e||"/"===e)}function a(e,t,n){function r(){do{E++,O=E<e.length?e[E]:null}while(null!==O&&!/\S/.test(O.value))}function a(t){var n=E+t;return n<e.length?e[n]:null}function s(e){C={nextSyntax:e,matchStack:L,syntaxStack:S,prev:C}}function l(e){A={nextSyntax:e,matchStack:L,syntaxStack:S,thenStack:C,tokenCursor:E,token:O,prev:A}}function u(){return L={type:f,syntax:t.syntax,token:O,prev:L},r(),E>P&&(P=E),L.token}function w(){L=L.type===m?L.prev:{type:g,syntax:S.syntax,token:L.token,prev:L},S=S.prev}var S=null,C=null,A=null,z=0,T=b,L={type:"Stub",syntax:null,token:null,tokenCursor:-1,prev:null},P=0,E=-1,O=null;for(r();;){if(++z===k){console.warn("[csstree-match] BREAK after "+k+" iterations"),T=v;break}if(t!==h)if(t!==p)switch(t.type){case"MatchGraph":t=t.match;break;case"If":t.else!==p&&l(t.else),t.then!==h&&s(t.then),t=t.match;break;case"MatchOnce":t={type:"MatchOnceBuffer",terms:t.terms,all:t.all,matchStack:L,index:0,mask:0};break;case"MatchOnceBuffer":if(t.index===t.terms.length){if(t.matchStack===L){if(0===t.mask||t.all){t=p;break}t=h;break}t.index=0,t.matchStack=L}for(;t.index<t.terms.length;t.index++)if(0==(t.mask&1<<t.index)){l(t),s({type:"AddMatchOnce",buffer:t}),t=t.terms[t.index++];break}break;case"AddMatchOnce":t=t.buffer;var _=t.mask|1<<t.index-1;if(_===(1<<t.terms.length)-1){t=h;continue}t={type:"MatchOnceBuffer",terms:t.terms,all:t.all,matchStack:t.matchStack,index:t.index,mask:_};break;case"Enum":var M=null!==O?O.value.toLowerCase():"";-1!==M.indexOf("\\")&&(M=M.replace(/\\[09].*$/,"")),t=c.call(t.map,M)?t.map[M]:p;break;case"Generic":t=t.fn(O,u,a)?h:p;break;case"Type":case"Property":!function(){S={syntax:t,prev:S},L={type:m,syntax:t.syntax,token:L.token,prev:L}}();var R="Type"===t.type?"types":"properties";if(!(t=c.call(n,R)&&n[R][t.name]?n[R][t.name].match:void 0))throw new Error("Bad syntax reference: "+("Type"===S.syntax.type?"<"+S.syntax.name+">":"<'"+S.syntax.name+"'>"));break;case"Keyword":var M=t.name;if(null!==O){var N=O.value;if(-1!==N.indexOf("\\")&&(N=N.replace(/\\[09].*$/,"")),N.toLowerCase()===M){u(),t=h;break}}t=p;break;case"AtKeyword":case"Function":if(null!==O&&O.value.toLowerCase()===t.name){u(),t=h;break}t=p;break;case"Token":if(null!==O&&O.value===t.value){u(),t=h;break}t=p;break;case"Comma":null!==O&&","===O.value?o(L.token)?t=p:(u(),t=i(O)?p:h):t=o(L.token)||i(O)?h:p;break;default:throw new Error("Unknown node type: "+t.type)}else{if(null===A){T=y;break}t=A.nextSyntax,C=A.thenStack,S=A.syntaxStack,L=A.matchStack,E=A.tokenCursor,O=A.token,A=A.prev}else{if(null===C){if(null!==O&&(E!==e.length-1||"\\0"!==O.value&&"\\9"!==O.value)){t=p;continue}T=b;break}if((t=C.nextSyntax)===d){if(C.matchStack.token===L.token){t=p;continue}t=h}for(;null!==S&&C.syntaxStack!==S;)w();C=C.prev}}if(x+=z,T===b)for(;null!==S;)w();else L=null;return{tokens:e,reason:T,iterations:z,match:L,longestMatch:P}}function s(e,t,n){var o=a(e,t,n||{});return null!==o.match&&(o.match=r(o.match,function(e){return e.type===m||e.type===g?{type:e.type,syntax:e.syntax}:{syntax:e.syntax,token:e.token&&e.token.value,node:e.token&&e.token.node}}).slice(1)),o}function l(e,t,n){var r=a(e,t,n||{});if(null===r.match)return r;for(var o=r.match,i=r.match={syntax:t.syntax||null,match:[]},s=[i],l=null,c=null;null!==o;)c=o.prev,o.prev=l,l=o,o=c;for(o=l.prev;null!==o&&null!==o.syntax;){var u=o;switch(u.type){case m:i.match.push(i={syntax:u.syntax,match:[]}),s.push(i);break;case g:s.pop(),i=s[s.length-1];break;default:i.match.push({syntax:u.syntax||null,token:u.token.value,node:u.token.node})}o=o.prev}return r}var c=Object.prototype.hasOwnProperty,u=e("./match-graph"),h=u.MATCH,p=u.MISMATCH,d=u.DISALLOW_EMPTY,f=1,m=2,g=3,b="Match",y="Mismatch",v="Maximum iteration number exceeded (please fill an issue on https://github.com/csstree/csstree/issues)",k=1e4,x=0;t.exports={matchAsList:s,matchAsTree:l,getTotalIterationCount:function(){return x}}},{"./match-graph":55}],57:[function(e,t,n){function r(e){return"node"in e?e.node:r(e.match[0])}function o(e){return"node"in e?e.node:o(e.match[e.match.length-1])}function i(e,t,n,i,s){function l(n){if(null!==n.syntax&&n.syntax.type===i&&n.syntax.name===s){var u=r(n),h=o(n);e.syntax.walk(t,function(e,t,n){if(e===u){var r=new a;do{if(r.appendData(t.data),t.data===h)break;t=t.next}while(null!==t);c.push({parent:n,nodes:r})}})}Array.isArray(n.match)&&n.match.forEach(l)}var c=[];return null!==n.matched&&l(n.matched),c}var a=e("../utils/list");t.exports={matchFragments:i}},{"../utils/list":144}],58:[function(e,t,n){function r(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&e>=0}function o(e){return Boolean(e)&&r(e.offset)&&r(e.line)&&r(e.column)}function i(e,t){return function(n,r){if(!n||n.constructor!==Object)return r(n,"Type of node should be an Object");for(var i in n){var a=!0;if(!1!==l.call(n,i)){if("type"===i)n.type!==e&&r(n,"Wrong node type `"+n.type+"`, expected `"+e+"`");else if("loc"===i){if(null===n.loc)continue;if(n.loc&&n.loc.constructor===Object)if("string"!=typeof n.loc.source)i+=".source";else if(o(n.loc.start)){if(o(n.loc.end))continue;i+=".end"}else i+=".start";a=!1}else if(t.hasOwnProperty(i))for(var c=0,a=!1;!a&&c<t[i].length;c++){var u=t[i][c];switch(u){case String:a="string"==typeof n[i];break;case Boolean:a="boolean"==typeof n[i];break;case null:a=null===n[i];break;default:"string"==typeof u?a=n[i]&&n[i].type===u:Array.isArray(u)&&(a=n[i]instanceof s)}}else r(n,"Unknown field `"+i+"` for "+e+" node type");a||r(n,"Bad value for `"+e+"."+i+"`")}}for(var i in t)l.call(t,i)&&!1===l.call(n,i)&&r(n,"Field `"+e+"."+i+"` is missed")}}function a(e,t){var n=t.structure,r={type:String,loc:!0},o={type:'"'+e+'"'};for(var a in n)if(!1!==l.call(n,a)){for(var s=[],c=r[a]=Array.isArray(n[a])?n[a].slice():[n[a]],u=0;u<c.length;u++){var h=c[u];if(h===String||h===Boolean)s.push(h.name);else if(null===h)s.push("null");else if("string"==typeof h)s.push("<"+h+">");else{if(!Array.isArray(h))throw new Error("Wrong value `"+h+"` in `"+e+"."+a+"` structure definition");s.push("List")}}o[a]=s.join(" | ")}return{docs:o,check:i(e,r)}}var s=e("../utils/list"),l=Object.prototype.hasOwnProperty;t.exports={getStructureFromConfig:function(e){var t={};if(e.node)for(var n in e.node)if(l.call(e.node,n)){var r=e.node[n];if(!r.structure)throw new Error("Missed `structure` field in `"+n+"` node type definition");t[n]=a(n,r)}return t}}},{"../utils/list":144}],59:[function(e,t,n){function r(e){function t(e){return null!==e&&("Type"===e.type||"Property"===e.type||"Keyword"===e.type)}function n(o){if(Array.isArray(o.match)){for(var i=0;i<o.match.length;i++)if(n(o.match[i]))return t(o.syntax)&&r.unshift(o.syntax),!0}else if(o.node===e)return r=t(o.syntax)?[o.syntax]:[],!0;return!1}var r=null;return null!==this.matched&&n(this.matched),r}function o(e,t,n){var o=r.call(e,t);return null!==o&&o.some(n)}function i(e,t){return o(this,e,function(e){return"Type"===e.type&&e.name===t})}function a(e,t){return o(this,e,function(e){return"Property"===e.type&&e.name===t})}function s(e){return o(this,e,function(e){return"Keyword"===e.type})}t.exports={getTrace:r,isType:i,isProperty:a,isKeyword:s}},{}],60:[function(e,t,n){"use strict";function r(e){return function(){return this[e]()}}function o(e){var t={context:{},scope:{},atrule:{},pseudo:{}};if(e.parseContext)for(var n in e.parseContext)switch(typeof e.parseContext[n]){case"function":t.context[n]=e.parseContext[n];break;case"string":t.context[n]=r(e.parseContext[n])}if(e.scope)for(var n in e.scope)t.scope[n]=e.scope[n];if(e.atrule)for(var n in e.atrule){var o=e.atrule[n];o.parse&&(t.atrule[n]=o.parse)}if(e.pseudo)for(var n in e.pseudo){var i=e.pseudo[n];i.parse&&(t.pseudo[n]=i.parse)}if(e.node)for(var n in e.node)t[n]=e.node[n].parse;return t}var i=e("../tokenizer"),a=e("../utils/list"),s=e("./sequence"),l=function(){};t.exports=function(e){var t={scanner:new i,filename:"<unknown>",needPositions:!1,onParseError:l,onParseErrorThrow:!1,parseAtrulePrelude:!0,parseRulePrelude:!0,parseValue:!0,parseCustomProperty:!1,readSequence:s,createList:function(){return new a},createSingleNodeList:function(e){return(new a).appendData(e)},getFirstListNode:function(e){return e&&e.first()},getLastListNode:function(e){return e.last()},parseWithFallback:function(e,t){var n=this.scanner.currentToken;try{return e.call(this)}catch(e){if(this.onParseErrorThrow)throw e;var r=t.call(this,n);return this.onParseErrorThrow=!0,this.onParseError(e,r),this.onParseErrorThrow=!1,r}},getLocation:function(e,t){return this.needPositions?this.scanner.getLocationRange(e,t,this.filename):null},getLocationFromList:function(e){if(this.needPositions){var t=this.getFirstListNode(e),n=this.getLastListNode(e);return this.scanner.getLocationRange(null!==t?t.loc.start.offset-this.scanner.startOffset:this.scanner.tokenStart,null!==n?n.loc.end.offset-this.scanner.startOffset:this.scanner.tokenStart,this.filename)}return null}};e=o(e||{});for(var n in e)t[n]=e[n];return function(e,n){n=n||{};var r,o=n.context||"default";if(t.scanner.setSource(e,n.offset,n.line,n.column),t.filename=n.filename||"<unknown>",t.needPositions=Boolean(n.positions),t.onParseError="function"==typeof n.onParseError?n.onParseError:l,t.onParseErrorThrow=!1,t.parseAtrulePrelude=!("parseAtrulePrelude"in n)||Boolean(n.parseAtrulePrelude),t.parseRulePrelude=!("parseRulePrelude"in n)||Boolean(n.parseRulePrelude),t.parseValue=!("parseValue"in n)||Boolean(n.parseValue),t.parseCustomProperty="parseCustomProperty"in n&&Boolean(n.parseCustomProperty),!t.context.hasOwnProperty(o))throw new Error("Unknown context `"+o+"`");return r=t.context[o].call(t,n),t.scanner.eof||t.scanner.error(),r}}},{"../tokenizer":140,"../utils/list":144,"./sequence":61}],61:[function(e,t,n){var r=e("../tokenizer").TYPE,o=r.WhiteSpace,i=r.Comment;t.exports=function(e){var t=this.createList(),n=null,r={recognizer:e,space:null,ignoreWS:!1,ignoreWSAfter:!1};for(this.scanner.skipSC();!this.scanner.eof;){switch(this.scanner.tokenType){case i:this.scanner.next();continue;case o:r.ignoreWS?this.scanner.next():r.space=this.WhiteSpace();continue}if(void 0===(n=e.getNode.call(this,r)))break;null!==r.space&&(t.push(r.space),r.space=null),t.push(n),r.ignoreWSAfter?(r.ignoreWSAfter=!1,r.ignoreWS=!0):r.ignoreWS=!1}return t}},{"../tokenizer":140}],62:[function(e,t,n){t.exports={parse:{prelude:null,block:function(){return this.Block(!0)}}}},{}],63:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.String,i=r.Identifier,a=r.Url,s=r.LeftParenthesis;t.exports={parse:{prelude:function(){var e=this.createList();switch(this.scanner.skipSC(),this.scanner.tokenType){case o:e.push(this.String());break;case a:e.push(this.Url());break;default:this.scanner.error("String or url() is expected")}return this.scanner.lookupNonWSType(0)!==i&&this.scanner.lookupNonWSType(0)!==s||(e.push(this.WhiteSpace()),e.push(this.MediaQueryList())),e},block:null}}},{"../../tokenizer":140}],64:[function(e,t,n){t.exports={"font-face":e("./font-face"),import:e("./import"),media:e("./media"),page:e("./page"),supports:e("./supports")}},{"./font-face":62,"./import":63,"./media":65,"./page":66,"./supports":67}],65:[function(e,t,n){t.exports={parse:{prelude:function(){return this.createSingleNodeList(this.MediaQueryList())},block:function(){return this.Block(!1)}}}},{}],66:[function(e,t,n){t.exports={parse:{prelude:function(){return this.createSingleNodeList(this.SelectorList())},block:function(){return this.Block(!0)}}}},{}],67:[function(e,t,n){function r(){return this.createSingleNodeList(this.Raw(this.scanner.currentToken,0,0,!1,!1))}function o(){var e=0;return this.scanner.skipSC(),this.scanner.tokenType===c?e=1:this.scanner.tokenType===p&&this.scanner.lookupType(1)===c&&(e=2),0!==e&&this.scanner.lookupNonWSType(e)===d?this.createSingleNodeList(this.Declaration()):i.call(this)}function i(){var e,t=this.createList(),n=null;this.scanner.skipSC();e:for(;!this.scanner.eof;){switch(this.scanner.tokenType){case s:n=this.WhiteSpace();continue;case l:this.scanner.next();continue;case u:e=this.Function(r,this.scope.AtrulePrelude);break;case c:e=this.Identifier();break;case h:e=this.Parentheses(o,this.scope.AtrulePrelude);break;default:break e}null!==n&&(t.push(n),n=null),t.push(e)}return t}var a=e("../../tokenizer").TYPE,s=a.WhiteSpace,l=a.Comment,c=a.Identifier,u=a.Function,h=a.LeftParenthesis,p=a.HyphenMinus,d=a.Colon;t.exports={parse:{prelude:function(){var e=i.call(this);return null===this.getFirstListNode(e)&&this.scanner.error("Condition is expected"),e},block:function(){return this.Block(!1)}}}},{"../../tokenizer":140}],68:[function(e,t,n){var r=e("../../../data");t.exports={generic:!0,types:r.types,properties:r.properties,node:e("../node")}},{"../../../data":40,"../node":117}],69:[function(e,t,n){function r(e){return e&&e.constructor===Object}function o(e){if(r(e)){var t={};for(var n in e)s.call(e,n)&&(t[n]=e[n]);return t}return e}function i(e,t){for(var n in t)s.call(t,n)&&(r(e[n])?i(e[n],o(t[n])):e[n]=o(t[n]))}function a(e,t,n){for(var l in n)if(!1!==s.call(n,l))if(!0===n[l])l in t&&s.call(t,l)&&(e[l]=o(t[l]));else if(n[l])if(r(n[l])){var c={};i(c,e[l]),i(c,t[l]),e[l]=c}else if(Array.isArray(n[l])){var c={},u=n[l].reduce(function(e,t){return e[t]=!0,e},{});for(var h in e[l])s.call(e[l],h)&&(c[h]={},e[l]&&e[l][h]&&a(c[h],e[l][h],u));for(var h in t[l])s.call(t[l],h)&&(c[h]||(c[h]={}),t[l]&&t[l][h]&&a(c[h],t[l][h],u));e[l]=c}return e}var s=Object.prototype.hasOwnProperty,l={generic:!0,types:{},properties:{},parseContext:{},scope:{},atrule:["parse"],pseudo:["parse"],node:["name","structure","parse","generate","walkContext"]};t.exports=function(e,t){return a(e,t,l)}},{}],70:[function(e,t,n){t.exports={parseContext:{default:"StyleSheet",stylesheet:"StyleSheet",atrule:"Atrule",atrulePrelude:function(e){return this.AtrulePrelude(e.atrule?String(e.atrule):null)},mediaQueryList:"MediaQueryList",mediaQuery:"MediaQuery",rule:"Rule",selectorList:"SelectorList",selector:"Selector",block:function(){return this.Block(!0)},declarationList:"DeclarationList",declaration:"Declaration",value:"Value"},scope:e("../scope"),atrule:e("../atrule"),pseudo:e("../pseudo"),node:e("../node")}},{"../atrule":64,"../node":117,"../pseudo":123,"../scope":134}],71:[function(e,t,n){t.exports={node:e("../node")}},{"../node":117}],72:[function(e,t,n){function r(e,t){for(var n in t)e[n]=t[n];return e}function o(e){var t=c(e),n=p(e),g=u(e),b=h(n),y={List:i,Tokenizer:a,Lexer:s,vendorPrefix:f.vendorPrefix,keyword:f.keyword,property:f.property,isCustomProperty:f.isCustomProperty,grammar:l,lexer:null,createLexer:function(e){return new s(e,y,y.lexer.structure)},parse:t,walk:n,generate:g,clone:d,fromPlainObject:b.fromPlainObject,toPlainObject:b.toPlainObject,createSyntax:function(e){return o(m({},e))},fork:function(t){var n=m({},e);return o("function"==typeof t?t(n,r):m(n,t))}};return y.lexer=new s({generic:!0,types:e.types,properties:e.properties,node:e.node},y),y}var i=e("../utils/list"),a=e("../tokenizer"),s=e("../lexer/Lexer"),l=e("../lexer/grammar"),c=e("../parser/create"),u=e("../generator/create"),h=e("../convertor/create"),p=e("../walker/create"),d=e("../utils/clone"),f=e("../utils/names"),m=e("./config/mix");n.create=function(e){return o(m({},e))}},{
"../convertor/create":41,"../generator/create":42,"../lexer/Lexer":45,"../lexer/grammar":51,"../parser/create":60,"../tokenizer":140,"../utils/clone":142,"../utils/list":144,"../utils/names":145,"../walker/create":146,"./config/mix":69}],73:[function(e,t,n){t.exports=function(){this.scanner.skipSC();var e=this.createSingleNodeList(this.IdSelector());return this.scanner.skipSC(),e}},{}],74:[function(e,t,n){t.exports=function(){return this.createSingleNodeList(this.Raw(this.scanner.currentToken,0,0,!1,!1))}},{}],75:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Identifier,i=r.Comma,a=r.Semicolon,s=r.HyphenMinus,l=r.ExclamationMark;t.exports=function(){var e=this.createList();this.scanner.skipSC();var t=this.scanner.tokenStart;return this.scanner.eat(s),this.scanner.source.charCodeAt(this.scanner.tokenStart)!==s&&this.scanner.error("HyphenMinus is expected"),this.scanner.eat(o),e.push({type:"Identifier",loc:this.getLocation(t,this.scanner.tokenStart),name:this.scanner.substrToCursor(t)}),this.scanner.skipSC(),this.scanner.tokenType===i&&(e.push(this.Operator()),e.push(this.parseCustomProperty?this.Value(null):this.Raw(this.scanner.currentToken,l,a,!1,!1))),e}},{"../../tokenizer":140}],76:[function(e,t,n){t.exports=e("./create").create(function(){for(var e={},t=0;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}(e("./config/lexer"),e("./config/parser"),e("./config/walker")))},{"./config/lexer":68,"./config/parser":70,"./config/walker":71,"./create":72}],77:[function(e,t,n){function r(e,t){var n=e.tokenStart;for(e.source.charCodeAt(n)!==c&&e.source.charCodeAt(n)!==u||(t&&e.error(),n++);n<e.tokenEnd;n++)i(e.source.charCodeAt(n))||e.error("Unexpected input",n)}var o=e("../../tokenizer").cmpChar,i=e("../../tokenizer").isNumber,a=e("../../tokenizer").TYPE,s=a.Identifier,l=a.Number,c=a.PlusSign,u=a.HyphenMinus;t.exports={name:"AnPlusB",structure:{a:[String,null],b:[String,null]},parse:function(){var e=this.scanner.tokenStart,t=e,n="",a=null,h=null;if(this.scanner.tokenType!==l&&this.scanner.tokenType!==c||(r(this.scanner,!1),n=this.scanner.getTokenValue(),this.scanner.next(),t=this.scanner.tokenStart),this.scanner.tokenType===s){var p=this.scanner.tokenStart;o(this.scanner.source,p,u)&&(""===n?(n="-",p++):this.scanner.error("Unexpected hyphen minus")),o(this.scanner.source,p,110)||this.scanner.error(),a=""===n?"1":"+"===n?"+1":"-"===n?"-1":n;var d=this.scanner.tokenEnd-p;d>1?(this.scanner.source.charCodeAt(p+1)!==u&&this.scanner.error("Unexpected input",p+1),d>2?this.scanner.tokenStart=p+2:(this.scanner.next(),this.scanner.skipSC()),r(this.scanner,!0),h="-"+this.scanner.getTokenValue(),this.scanner.next(),t=this.scanner.tokenStart):(n="",this.scanner.next(),t=this.scanner.tokenStart,this.scanner.skipSC(),this.scanner.tokenType!==u&&this.scanner.tokenType!==c||(n=this.scanner.getTokenValue(),this.scanner.next(),this.scanner.skipSC()),this.scanner.tokenType===l?(r(this.scanner,""!==n),i(this.scanner.source.charCodeAt(this.scanner.tokenStart))||(n=this.scanner.source.charAt(this.scanner.tokenStart),this.scanner.tokenStart++),""===n?this.scanner.error():"+"===n&&(n=""),h=n+this.scanner.getTokenValue(),this.scanner.next(),t=this.scanner.tokenStart):n&&this.scanner.eat(l))}else""!==n&&"+"!==n||this.scanner.error("Number or identifier is expected",this.scanner.tokenStart+(this.scanner.tokenType===c||this.scanner.tokenType===u)),h=n;return{type:"AnPlusB",loc:this.getLocation(e,t),a:a,b:h}},generate:function(e){var t=null!==e.a&&void 0!==e.a,n=null!==e.b&&void 0!==e.b;t?(this.chunk("+1"===e.a?"+n":"1"===e.a?"n":"-1"===e.a?"-n":e.a+"n"),n&&(n=String(e.b),"-"===n.charAt(0)||"+"===n.charAt(0)?(this.chunk(n.charAt(0)),this.chunk(n.substr(1))):(this.chunk("+"),this.chunk(n)))):this.chunk(String(e.b))}}},{"../../tokenizer":140}],78:[function(e,t,n){function r(e){return this.Raw(e,s,l,!1,!0)}function o(){for(var e,t=1;e=this.scanner.lookupType(t);t++){if(e===c)return!0;if(e===l||e===a)return!1}return!1}var i=e("../../tokenizer").TYPE,a=i.AtKeyword,s=i.Semicolon,l=i.LeftCurlyBracket,c=i.RightCurlyBracket;t.exports={name:"Atrule",structure:{name:String,prelude:["AtrulePrelude","Raw",null],block:["Block",null]},parse:function(){var e,t,n=this.scanner.tokenStart,i=null,c=null;switch(this.scanner.eat(a),e=this.scanner.substrToCursor(n+1),t=e.toLowerCase(),this.scanner.skipSC(),!1===this.scanner.eof&&this.scanner.tokenType!==l&&this.scanner.tokenType!==s&&(this.parseAtrulePrelude?(i=this.parseWithFallback(this.AtrulePrelude.bind(this,e),r),"AtrulePrelude"===i.type&&null===i.children.head&&(i=null)):i=r.call(this,this.scanner.currentToken),this.scanner.skipSC()),this.scanner.tokenType){case s:this.scanner.next();break;case l:c=this.atrule.hasOwnProperty(t)&&"function"==typeof this.atrule[t].block?this.atrule[t].block.call(this):this.Block(o.call(this))}return{type:"Atrule",loc:this.getLocation(n,this.scanner.tokenStart),name:e,prelude:i,block:c}},generate:function(e){this.chunk("@"),this.chunk(e.name),null!==e.prelude&&(this.chunk(" "),this.node(e.prelude)),e.block?this.node(e.block):this.chunk(";")},walkContext:"atrule"}},{"../../tokenizer":140}],79:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Semicolon,i=r.LeftCurlyBracket;t.exports={name:"AtrulePrelude",structure:{children:[[]]},parse:function(e){var t=null;return null!==e&&(e=e.toLowerCase()),this.scanner.skipSC(),t=this.atrule.hasOwnProperty(e)&&"function"==typeof this.atrule[e].prelude?this.atrule[e].prelude.call(this):this.readSequence(this.scope.AtrulePrelude),this.scanner.skipSC(),!0!==this.scanner.eof&&this.scanner.tokenType!==i&&this.scanner.tokenType!==o&&this.scanner.error("Semicolon or block is expected"),null===t&&(t=this.createList()),{type:"AtrulePrelude",loc:this.getLocationFromList(t),children:t}},generate:function(e){this.children(e)},walkContext:"atrulePrelude"}},{"../../tokenizer":140}],80:[function(e,t,n){function r(){this.scanner.eof&&this.scanner.error("Unexpected end of input");var e=this.scanner.tokenStart,t=!1,n=!0;return this.scanner.tokenType===c?(t=!0,n=!1,this.scanner.next()):this.scanner.tokenType!==m&&this.scanner.eat(a),this.scanner.tokenType===m?this.scanner.lookupType(1)!==h?(this.scanner.next(),this.scanner.eat(a)):t&&this.scanner.error("Identifier is expected",this.scanner.tokenEnd):t&&this.scanner.error("Vertical line is expected"),n&&this.scanner.tokenType===u&&(this.scanner.next(),this.scanner.eat(a)),{type:"Identifier",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}}function o(){var e=this.scanner.tokenStart,t=this.scanner.tokenType;return t!==h&&t!==g&&t!==f&&t!==l&&t!==c&&t!==m&&this.scanner.error("Attribute selector (=, ~=, ^=, $=, *=, |=) is expected"),t===h?this.scanner.next():(this.scanner.next(),this.scanner.eat(h)),this.scanner.substrToCursor(e)}var i=e("../../tokenizer").TYPE,a=i.Identifier,s=i.String,l=i.DollarSign,c=i.Asterisk,u=i.Colon,h=i.EqualsSign,p=i.LeftSquareBracket,d=i.RightSquareBracket,f=i.CircumflexAccent,m=i.VerticalLine,g=i.Tilde;t.exports={name:"AttributeSelector",structure:{name:"Identifier",matcher:[String,null],value:["String","Identifier",null],flags:[String,null]},parse:function(){var e,t=this.scanner.tokenStart,n=null,i=null,l=null;return this.scanner.eat(p),this.scanner.skipSC(),e=r.call(this),this.scanner.skipSC(),this.scanner.tokenType!==d&&(this.scanner.tokenType!==a&&(n=o.call(this),this.scanner.skipSC(),i=this.scanner.tokenType===s?this.String():this.Identifier(),this.scanner.skipSC()),this.scanner.tokenType===a&&(l=this.scanner.getTokenValue(),this.scanner.next(),this.scanner.skipSC())),this.scanner.eat(d),{type:"AttributeSelector",loc:this.getLocation(t,this.scanner.tokenStart),name:e,matcher:n,value:i,flags:l}},generate:function(e){var t=" ";this.chunk("["),this.node(e.name),null!==e.matcher&&(this.chunk(e.matcher),null!==e.value&&(this.node(e.value),"String"===e.value.type&&(t=""))),null!==e.flags&&(this.chunk(t),this.chunk(e.flags)),this.chunk("]")}}},{"../../tokenizer":140}],81:[function(e,t,n){function r(e){return this.Raw(e,0,0,!1,!0)}function o(){return this.parseWithFallback(this.Rule,r)}function i(e){return this.Raw(e,0,u,!0,!0)}function a(){if(this.scanner.tokenType===u)return i.call(this,this.scanner.currentToken);var e=this.parseWithFallback(this.Declaration,i);return this.scanner.tokenType===u&&this.scanner.next(),e}var s=e("../../tokenizer").TYPE,l=s.WhiteSpace,c=s.Comment,u=s.Semicolon,h=s.AtKeyword,p=s.LeftCurlyBracket,d=s.RightCurlyBracket;t.exports={name:"Block",structure:{children:[["Atrule","Rule","Declaration"]]},parse:function(e){var t=e?a:o,n=this.scanner.tokenStart,i=this.createList();this.scanner.eat(p);e:for(;!this.scanner.eof;)switch(this.scanner.tokenType){case d:break e;case l:case c:this.scanner.next();break;case h:i.push(this.parseWithFallback(this.Atrule,r));break;default:i.push(t.call(this))}return this.scanner.eof||this.scanner.eat(d),{type:"Block",loc:this.getLocation(n,this.scanner.tokenStart),children:i}},generate:function(e){this.chunk("{"),this.children(e,function(e){"Declaration"===e.type&&this.chunk(";")}),this.chunk("}")},walkContext:"block"}},{"../../tokenizer":140}],82:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.LeftSquareBracket,i=r.RightSquareBracket;t.exports={name:"Brackets",structure:{children:[[]]},parse:function(e,t){var n=this.scanner.tokenStart,r=null;return this.scanner.eat(o),r=e.call(this,t),this.scanner.eof||this.scanner.eat(i),{type:"Brackets",loc:this.getLocation(n,this.scanner.tokenStart),children:r}},generate:function(e){this.chunk("["),this.children(e),this.chunk("]")}}},{"../../tokenizer":140}],83:[function(e,t,n){var r=e("../../tokenizer").TYPE.CDC;t.exports={name:"CDC",structure:[],parse:function(){var e=this.scanner.tokenStart;return this.scanner.eat(r),{type:"CDC",loc:this.getLocation(e,this.scanner.tokenStart)}},generate:function(){this.chunk("--\x3e")}}},{"../../tokenizer":140}],84:[function(e,t,n){var r=e("../../tokenizer").TYPE.CDO;t.exports={name:"CDO",structure:[],parse:function(){var e=this.scanner.tokenStart;return this.scanner.eat(r),{type:"CDO",loc:this.getLocation(e,this.scanner.tokenStart)}},generate:function(){this.chunk("\x3c!--")}}},{"../../tokenizer":140}],85:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Identifier,i=r.FullStop;t.exports={name:"ClassSelector",structure:{name:String},parse:function(){return this.scanner.eat(i),{type:"ClassSelector",loc:this.getLocation(this.scanner.tokenStart-1,this.scanner.tokenEnd),name:this.scanner.consume(o)}},generate:function(e){this.chunk("."),this.chunk(e.name)}}},{"../../tokenizer":140}],86:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.PlusSign,i=r.Solidus,a=r.GreaterThanSign,s=r.Tilde;t.exports={name:"Combinator",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;switch(this.scanner.tokenType){case a:case o:case s:this.scanner.next();break;case i:this.scanner.next(),this.scanner.expectIdentifier("deep"),this.scanner.eat(i);break;default:this.scanner.error("Combinator is expected")}return{type:"Combinator",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.name)}}},{"../../tokenizer":140}],87:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Asterisk,i=r.Solidus;t.exports={name:"Comment",structure:{value:String},parse:function(){var e=this.scanner.tokenStart,t=this.scanner.tokenEnd;return t-e+2>=2&&this.scanner.source.charCodeAt(t-2)===o&&this.scanner.source.charCodeAt(t-1)===i&&(t-=2),this.scanner.next(),{type:"Comment",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.source.substring(e+2,t)}},generate:function(e){this.chunk("/*"),this.chunk(e.value),this.chunk("*/")}}},{"../../tokenizer":140}],88:[function(e,t,n){function r(e){return this.Raw(e,p,b,!1,!0)}function o(e){return this.Raw(e,p,b,!1,!1)}function i(){var e=this.scanner.currentToken,t=this.Value();return"Raw"!==t.type&&!1===this.scanner.eof&&this.scanner.tokenType!==b&&this.scanner.tokenType!==p&&!1===this.scanner.isBalanceEdge(e)&&this.scanner.error(),t}function a(){var e=this.scanner.tokenStart,t=0;switch(this.scanner.tokenType){case f:case m:case y:case v:t=1;break;case d:t=this.scanner.lookupType(1)===d?2:1}return this.scanner.lookupType(t)===g&&t++,t&&this.scanner.skip(t),this.scanner.eat(u),this.scanner.substrToCursor(e)}function s(e){e.eat(p),e.skipSC();var t=e.consume(u);return"important"===t||t}var l=e("../../utils/names").isCustomProperty,c=e("../../tokenizer").TYPE,u=c.Identifier,h=c.Colon,p=c.ExclamationMark,d=c.Solidus,f=c.Asterisk,m=c.DollarSign,g=c.HyphenMinus,b=c.Semicolon,y=c.PlusSign,v=c.NumberSign;t.exports={name:"Declaration",structure:{important:[Boolean,String],property:String,value:["Value","Raw"]},parse:function(){var e,t=this.scanner.tokenStart,n=this.scanner.currentToken,c=a.call(this),u=l(c),d=u?this.parseCustomProperty:this.parseValue,f=u?o:r,m=!1;return this.scanner.skipSC(),this.scanner.eat(h),u||this.scanner.skipSC(),e=d?this.parseWithFallback(i,f):f.call(this,this.scanner.currentToken),this.scanner.tokenType===p&&(m=s(this.scanner),this.scanner.skipSC()),!1===this.scanner.eof&&this.scanner.tokenType!==b&&!1===this.scanner.isBalanceEdge(n)&&this.scanner.error(),{type:"Declaration",loc:this.getLocation(t,this.scanner.tokenStart),important:m,property:c,value:e}},generate:function(e){this.chunk(e.property),this.chunk(":"),this.node(e.value),e.important&&this.chunk(!0===e.important?"!important":"!"+e.important)},walkContext:"declaration"}},{"../../tokenizer":140,"../../utils/names":145}],89:[function(e,t,n){function r(e){return this.Raw(e,0,s,!0,!0)}var o=e("../../tokenizer").TYPE,i=o.WhiteSpace,a=o.Comment,s=o.Semicolon;t.exports={name:"DeclarationList",structure:{children:[["Declaration"]]},parse:function(){for(var e=this.createList();!this.scanner.eof;)switch(this.scanner.tokenType){case i:case a:case s:this.scanner.next();break;default:e.push(this.parseWithFallback(this.Declaration,r))}return{type:"DeclarationList",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e,function(e){"Declaration"===e.type&&this.chunk(";")})}}},{"../../tokenizer":140}],90:[function(e,t,n){function r(e){var t=e.getTokenValue(),n=t.indexOf("\\");return n>0?(e.tokenStart+=n,t.substring(0,n)):(e.next(),t)}var o=e("../../tokenizer").TYPE.Number;t.exports={name:"Dimension",structure:{value:String,unit:String},parse:function(){var e=this.scanner.tokenStart,t=this.scanner.consume(o),n=r(this.scanner);return{type:"Dimension",loc:this.getLocation(e,this.scanner.tokenStart),value:t,unit:n}},generate:function(e){this.chunk(e.value),this.chunk(e.unit)}}},{"../../tokenizer":140}],91:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.RightParenthesis;t.exports={name:"Function",structure:{name:String,children:[[]]},parse:function(e,t){var n,r=this.scanner.tokenStart,i=this.scanner.consumeFunctionName(),a=i.toLowerCase();return n=t.hasOwnProperty(a)?t[a].call(this,t):e.call(this,t),this.scanner.eof||this.scanner.eat(o),{type:"Function",loc:this.getLocation(r,this.scanner.tokenStart),name:i,children:n}},generate:function(e){this.chunk(e.name),this.chunk("("),this.children(e),this.chunk(")")},walkContext:"function"}},{"../../tokenizer":140}],92:[function(e,t,n){function r(e,t){if(!o(e.source.charCodeAt(e.tokenStart))){if(!t)return;e.error("Unexpected input",e.tokenStart)}for(var n=e.tokenStart+1;n<e.tokenEnd;n++){var r=e.source.charCodeAt(n);if(!o(r))return void(e.tokenStart=n)}e.next()}var o=e("../../tokenizer").isHex,i=e("../../tokenizer").TYPE,a=i.Identifier,s=i.Number,l=i.NumberSign;t.exports={name:"HexColor",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;switch(this.scanner.eat(l),this.scanner.tokenType){case s:r(this.scanner,!0),this.scanner.tokenType===a&&r(this.scanner,!1);break;case a:r(this.scanner,!0);break;default:this.scanner.error("Number or identifier is expected")}return{type:"HexColor",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e+1)}},generate:function(e){this.chunk("#"),this.chunk(e.value)}}},{"../../tokenizer":140}],93:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Identifier,i=r.NumberSign;t.exports={name:"IdSelector",structure:{name:String},parse:function(){return this.scanner.eat(i),{type:"IdSelector",loc:this.getLocation(this.scanner.tokenStart-1,this.scanner.tokenEnd),name:this.scanner.consume(o)}},generate:function(e){this.chunk("#"),this.chunk(e.name)}}},{"../../tokenizer":140}],94:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Identifier;t.exports={name:"Identifier",structure:{name:String},parse:function(){return{type:"Identifier",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),name:this.scanner.consume(o)}},generate:function(e){this.chunk(e.name)}}},{"../../tokenizer":140}],95:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Identifier,i=r.Number,a=r.LeftParenthesis,s=r.RightParenthesis,l=r.Colon,c=r.Solidus;t.exports={name:"MediaFeature",structure:{name:String,value:["Identifier","Number","Dimension","Ratio",null]},parse:function(){var e,t=this.scanner.tokenStart,n=null;if(this.scanner.eat(a),this.scanner.skipSC(),e=this.scanner.consume(o),this.scanner.skipSC(),this.scanner.tokenType!==s){switch(this.scanner.eat(l),this.scanner.skipSC(),this.scanner.tokenType){case i:n=this.scanner.lookupType(1)===o?this.Dimension():this.scanner.lookupNonWSType(1)===c?this.Ratio():this.Number();break;case o:n=this.Identifier();break;default:this.scanner.error("Number, dimension, ratio or identifier is expected")}this.scanner.skipSC()}return this.scanner.eat(s),{type:"MediaFeature",loc:this.getLocation(t,this.scanner.tokenStart),name:e,value:n}},generate:function(e){this.chunk("("),this.chunk(e.name),null!==e.value&&(this.chunk(":"),this.node(e.value)),this.chunk(")")}}},{"../../tokenizer":140}],96:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.WhiteSpace,i=r.Comment,a=r.Identifier,s=r.LeftParenthesis;t.exports={name:"MediaQuery",structure:{children:[["Identifier","MediaFeature","WhiteSpace"]]},parse:function(){this.scanner.skipSC();var e=this.createList(),t=null,n=null;e:for(;!this.scanner.eof;){switch(this.scanner.tokenType){case i:this.scanner.next();continue;case o:n=this.WhiteSpace();continue;case a:t=this.Identifier();break;case s:t=this.MediaFeature();break;default:break e}null!==n&&(e.push(n),n=null),e.push(t)}return null===t&&this.scanner.error("Identifier or parenthesis is expected"),{type:"MediaQuery",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e)}}},{"../../tokenizer":140}],97:[function(e,t,n){var r=e("../../tokenizer").TYPE.Comma;t.exports={name:"MediaQueryList",structure:{children:[["MediaQuery"]]},parse:function(e){var t=this.createList();for(this.scanner.skipSC();!this.scanner.eof&&(t.push(this.MediaQuery(e)),this.scanner.tokenType===r);)this.scanner.next();return{type:"MediaQueryList",loc:this.getLocationFromList(t),children:t}},generate:function(e){this.children(e,function(){this.chunk(",")})}}},{"../../tokenizer":140}],98:[function(e,t,n){t.exports={name:"Nth",structure:{nth:["AnPlusB","Identifier"],selector:["SelectorList",null]},parse:function(e){this.scanner.skipSC();var t,n=this.scanner.tokenStart,r=n,o=null;return t=this.scanner.lookupValue(0,"odd")||this.scanner.lookupValue(0,"even")?this.Identifier():this.AnPlusB(),this.scanner.skipSC(),e&&this.scanner.lookupValue(0,"of")?(this.scanner.next(),o=this.SelectorList(),this.needPositions&&(r=this.getLastListNode(o.children).loc.end.offset)):this.needPositions&&(r=t.loc.end.offset),{type:"Nth",loc:this.getLocation(n,r),nth:t,selector:o}},generate:function(e){this.node(e.nth),null!==e.selector&&(this.chunk(" of "),this.node(e.selector))}}},{}],99:[function(e,t,n){var r=e("../../tokenizer").TYPE.Number;t.exports={name:"Number",structure:{value:String},parse:function(){return{type:"Number",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),value:this.scanner.consume(r)}},generate:function(e){this.chunk(e.value)}}},{"../../tokenizer":140}],100:[function(e,t,n){t.exports={name:"Operator",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.next(),{type:"Operator",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.value)}}},{}],101:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.LeftParenthesis,i=r.RightParenthesis;t.exports={name:"Parentheses",structure:{children:[[]]},parse:function(e,t){var n=this.scanner.tokenStart,r=null;return this.scanner.eat(o),r=e.call(this,t),this.scanner.eof||this.scanner.eat(i),{type:"Parentheses",loc:this.getLocation(n,this.scanner.tokenStart),children:r}},generate:function(e){this.chunk("("),this.children(e),this.chunk(")")}}},{"../../tokenizer":140}],102:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Number,i=r.PercentSign;t.exports={name:"Percentage",structure:{value:String},parse:function(){var e=this.scanner.tokenStart,t=this.scanner.consume(o);return this.scanner.eat(i),{type:"Percentage",loc:this.getLocation(e,this.scanner.tokenStart),value:t}},generate:function(e){this.chunk(e.value),this.chunk("%")}}},{"../../tokenizer":140}],103:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Identifier,i=r.Function,a=r.Colon,s=r.RightParenthesis;t.exports={name:"PseudoClassSelector",structure:{name:String,children:[["Raw"],null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null;return this.scanner.eat(a),this.scanner.tokenType===i?(e=this.scanner.consumeFunctionName(),t=e.toLowerCase(),this.pseudo.hasOwnProperty(t)?(this.scanner.skipSC(),r=this.pseudo[t].call(this),this.scanner.skipSC()):(r=this.createList(),r.push(this.Raw(this.scanner.currentToken,0,0,!1,!1))),this.scanner.eat(s)):e=this.scanner.consume(o),{type:"PseudoClassSelector",loc:this.getLocation(n,this.scanner.tokenStart),name:e,children:r}},generate:function(e){this.chunk(":"),this.chunk(e.name),null!==e.children&&(this.chunk("("),this.children(e),this.chunk(")"))},walkContext:"function"}},{"../../tokenizer":140}],104:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Identifier,i=r.Function,a=r.Colon,s=r.RightParenthesis;t.exports={name:"PseudoElementSelector",structure:{name:String,children:[["Raw"],null]},parse:function(){var e,t,n=this.scanner.tokenStart,r=null;return this.scanner.eat(a),this.scanner.eat(a),this.scanner.tokenType===i?(e=this.scanner.consumeFunctionName(),t=e.toLowerCase(),this.pseudo.hasOwnProperty(t)?(this.scanner.skipSC(),r=this.pseudo[t].call(this),this.scanner.skipSC()):(r=this.createList(),r.push(this.Raw(this.scanner.currentToken,0,0,!1,!1))),this.scanner.eat(s)):e=this.scanner.consume(o),{type:"PseudoElementSelector",loc:this.getLocation(n,this.scanner.tokenStart),name:e,children:r}},generate:function(e){this.chunk("::"),this.chunk(e.name),null!==e.children&&(this.chunk("("),this.children(e),this.chunk(")"))},walkContext:"function"}},{"../../tokenizer":140}],105:[function(e,t,n){function r(e){for(var t=e.consumeNonWS(a),n=0;n<t.length;n++){var r=t.charCodeAt(n);o(r)||r===l||e.error("Unsigned number is expected",e.tokenStart-t.length+n)}return 0===Number(t)&&e.error("Zero number is not allowed",e.tokenStart-t.length),t}var o=e("../../tokenizer").isNumber,i=e("../../tokenizer").TYPE,a=i.Number,s=i.Solidus,l=i.FullStop;t.exports={name:"Ratio",structure:{left:String,right:String},parse:function(){var e,t=this.scanner.tokenStart,n=r(this.scanner);return this.scanner.eatNonWS(s),e=r(this.scanner),{type:"Ratio",loc:this.getLocation(t,this.scanner.tokenStart),left:n,right:e}},generate:function(e){this.chunk(e.left),this.chunk("/"),this.chunk(e.right)}}},{"../../tokenizer":140}],106:[function(e,t,n){t.exports={name:"Raw",structure:{value:String},parse:function(e,t,n,r,o){var i,a=this.scanner.getTokenStart(e);return this.scanner.skip(this.scanner.getRawLength(e,t,n,r)),i=o&&this.scanner.tokenStart>a?this.scanner.getOffsetExcludeWS():this.scanner.tokenStart,{type:"Raw",loc:this.getLocation(a,i),value:this.scanner.source.substring(a,i)}},generate:function(e){this.chunk(e.value)}}},{}],107:[function(e,t,n){function r(e){return this.Raw(e,a,0,!1,!0)}function o(){var e=this.SelectorList();return"Raw"!==e.type&&!1===this.scanner.eof&&this.scanner.tokenType!==a&&this.scanner.error(),e}var i=e("../../tokenizer").TYPE,a=i.LeftCurlyBracket;t.exports={name:"Rule",structure:{prelude:["SelectorList","Raw"],block:["Block"]},parse:function(){var e,t,n=this.scanner.currentToken,i=this.scanner.tokenStart;return e=this.parseRulePrelude?this.parseWithFallback(o,r):r.call(this,n),t=this.Block(!0),{type:"Rule",loc:this.getLocation(i,this.scanner.tokenStart),prelude:e,block:t}},generate:function(e){this.node(e.prelude),this.node(e.block)},walkContext:"rule"}},{"../../tokenizer":140}],108:[function(e,t,n){t.exports={name:"Selector",structure:{children:[["TypeSelector","IdSelector","ClassSelector","AttributeSelector","PseudoClassSelector","PseudoElementSelector","Combinator","WhiteSpace"]]},parse:function(){var e=this.readSequence(this.scope.Selector);return null===this.getFirstListNode(e)&&this.scanner.error("Selector is expected"),{type:"Selector",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e)}}},{}],109:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.Comma;t.exports={name:"SelectorList",structure:{children:[["Selector","Raw"]]},parse:function(){for(var e=this.createList();!this.scanner.eof;){e.push(this.Selector());{if(this.scanner.tokenType!==o)break;this.scanner.next()}}return{type:"SelectorList",loc:this.getLocationFromList(e),children:e}},generate:function(e){this.children(e,function(){this.chunk(",")})},walkContext:"selector"}},{"../../tokenizer":140}],110:[function(e,t,n){var r=e("../../tokenizer").TYPE.String;t.exports={name:"String",structure:{value:String},parse:function(){return{type:"String",loc:this.getLocation(this.scanner.tokenStart,this.scanner.tokenEnd),value:this.scanner.consume(r)}},generate:function(e){this.chunk(e.value)}}},{"../../tokenizer":140}],111:[function(e,t,n){function r(e){return this.Raw(e,0,0,!1,!1)}var o=e("../../tokenizer").TYPE,i=o.WhiteSpace,a=o.Comment,s=o.ExclamationMark,l=o.AtKeyword,c=o.CDO,u=o.CDC;t.exports={name:"StyleSheet",structure:{children:[["Comment","CDO","CDC","Atrule","Rule","Raw"]]},parse:function(){for(var e,t=this.scanner.tokenStart,n=this.createList();!this.scanner.eof;){switch(this.scanner.tokenType){case i:this.scanner.next();continue;case a:if(this.scanner.source.charCodeAt(this.scanner.tokenStart+2)!==s){this.scanner.next();continue}e=this.Comment();break;case c:e=this.CDO();break;case u:e=this.CDC();break;case l:e=this.parseWithFallback(this.Atrule,r);break;default:e=this.parseWithFallback(this.Rule,r)}n.push(e)}return{type:"StyleSheet",loc:this.getLocation(t,this.scanner.tokenStart),children:n}},generate:function(e){this.children(e)},walkContext:"stylesheet"}},{"../../tokenizer":140}],112:[function(e,t,n){function r(){this.scanner.tokenType!==i&&this.scanner.tokenType!==a&&this.scanner.error("Identifier or asterisk is expected"),this.scanner.next()}var o=e("../../tokenizer").TYPE,i=o.Identifier,a=o.Asterisk,s=o.VerticalLine;t.exports={name:"TypeSelector",structure:{name:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.tokenType===s?(this.scanner.next(),r.call(this)):(r.call(this),this.scanner.tokenType===s&&(this.scanner.next(),r.call(this))),{type:"TypeSelector",loc:this.getLocation(e,this.scanner.tokenStart),name:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.name)}}},{"../../tokenizer":140}],113:[function(e,t,n){function r(e){for(var t=e.tokenStart+1;t<e.tokenEnd;t++){var n=e.source.charCodeAt(t);if(n===h||n===c)return e.tokenStart=t,!1}return!0}function o(e){var t=e.tokenStart+1,n=0;e:{if(e.tokenType===l){if(e.source.charCodeAt(e.tokenStart)!==h&&r(e))e.next();else if(e.source.charCodeAt(e.tokenStart)!==u)break e}else e.next();e.tokenType===u&&e.next(),e.tokenType===l&&e.next(),e.tokenType===s&&e.next(),e.tokenStart===t&&e.error("Unexpected input",t)}for(var o=t,a=!1;o<e.tokenStart;o++){var c=e.source.charCodeAt(o);!1!==i(c)||c===u&&!a||e.error("Unexpected input",o),c===u?(0===n&&e.error("Unexpected input",o),a=!0,n=0):++n>6&&e.error("Too long hex sequence",o)}if(0===n&&e.error("Unexpected input",o-1),!a)for(;n<6&&!e.eof&&e.tokenType===p;e.next())n++}var i=e("../../tokenizer").isHex,a=e("../../tokenizer").TYPE,s=a.Identifier,l=a.Number,c=a.PlusSign,u=a.HyphenMinus,h=a.FullStop,p=a.QuestionMark;t.exports={name:"UnicodeRange",structure:{value:String},parse:function(){var e=this.scanner.tokenStart;return this.scanner.next(),o(this.scanner),{type:"UnicodeRange",loc:this.getLocation(e,this.scanner.tokenStart),value:this.scanner.substrToCursor(e)}},generate:function(e){this.chunk(e.value)}}},{"../../tokenizer":140}],114:[function(e,t,n){var r=e("../../tokenizer").TYPE,o=r.String,i=r.Url,a=r.Raw,s=r.RightParenthesis;t.exports={name:"Url",structure:{value:["String","Raw"]},parse:function(){var e,t=this.scanner.tokenStart;switch(this.scanner.eat(i),this.scanner.skipSC(),this.scanner.tokenType){case o:e=this.String();break;case a:e=this.Raw(this.scanner.currentToken,0,a,!0,!1);break;default:this.scanner.error("String or Raw is expected")}return this.scanner.skipSC(),this.scanner.eat(s),{type:"Url",loc:this.getLocation(t,this.scanner.tokenStart),value:e}},generate:function(e){this.chunk("url"),this.chunk("("),this.node(e.value),this.chunk(")")}}},{"../../tokenizer":140}],115:[function(e,t,n){t.exports={name:"Value",structure:{children:[[]]},parse:function(){var e=this.scanner.tokenStart,t=this.readSequence(this.scope.Value);return{type:"Value",loc:this.getLocation(e,this.scanner.tokenStart),children:t}},generate:function(e){this.children(e)}}},{}],116:[function(e,t,n){var r=e("../../tokenizer").TYPE.WhiteSpace,o=Object.freeze({type:"WhiteSpace",loc:null,value:" "});t.exports={name:"WhiteSpace",structure:{value:String},parse:function(){return this.scanner.eat(r),o},generate:function(e){this.chunk(e.value)}}},{"../../tokenizer":140}],117:[function(e,t,n){t.exports={AnPlusB:e("./AnPlusB"),Atrule:e("./Atrule"),AtrulePrelude:e("./AtrulePrelude"),AttributeSelector:e("./AttributeSelector"),Block:e("./Block"),Brackets:e("./Brackets"),CDC:e("./CDC"),CDO:e("./CDO"),ClassSelector:e("./ClassSelector"),Combinator:e("./Combinator"),Comment:e("./Comment"),Declaration:e("./Declaration"),DeclarationList:e("./DeclarationList"),Dimension:e("./Dimension"),Function:e("./Function"),HexColor:e("./HexColor"),Identifier:e("./Identifier"),IdSelector:e("./IdSelector"),MediaFeature:e("./MediaFeature"),MediaQuery:e("./MediaQuery"),MediaQueryList:e("./MediaQueryList"),Nth:e("./Nth"),Number:e("./Number"),Operator:e("./Operator"),Parentheses:e("./Parentheses"),Percentage:e("./Percentage"),PseudoClassSelector:e("./PseudoClassSelector"),PseudoElementSelector:e("./PseudoElementSelector"),Ratio:e("./Ratio"),Raw:e("./Raw"),Rule:e("./Rule"),Selector:e("./Selector"),SelectorList:e("./SelectorList"),String:e("./String"),StyleSheet:e("./StyleSheet"),TypeSelector:e("./TypeSelector"),UnicodeRange:e("./UnicodeRange"),Url:e("./Url"),Value:e("./Value"),WhiteSpace:e("./WhiteSpace")}},{"./AnPlusB":77,"./Atrule":78,"./AtrulePrelude":79,"./AttributeSelector":80,"./Block":81,"./Brackets":82,"./CDC":83,"./CDO":84,"./ClassSelector":85,"./Combinator":86,"./Comment":87,"./Declaration":88,"./DeclarationList":89,"./Dimension":90,"./Function":91,"./HexColor":92,"./IdSelector":93,"./Identifier":94,"./MediaFeature":95,"./MediaQuery":96,"./MediaQueryList":97,"./Nth":98,"./Number":99,"./Operator":100,"./Parentheses":101,"./Percentage":102,"./PseudoClassSelector":103,"./PseudoElementSelector":104,"./Ratio":105,"./Raw":106,"./Rule":107,"./Selector":108,"./SelectorList":109,"./String":110,"./StyleSheet":111,"./TypeSelector":112,"./UnicodeRange":113,"./Url":114,"./Value":115,"./WhiteSpace":116}],118:[function(e,t,n){t.exports={parse:function(){
return this.createSingleNodeList(this.Nth(!1))}}},{}],119:[function(e,t,n){t.exports={parse:function(){return this.createSingleNodeList(this.Nth(!0))}}},{}],120:[function(e,t,n){t.exports={parse:function(){return this.createSingleNodeList(this.SelectorList())}}},{}],121:[function(e,t,n){t.exports={parse:function(){return this.createSingleNodeList(this.Identifier())}}},{}],122:[function(e,t,n){t.exports={parse:function(){return this.createSingleNodeList(this.SelectorList())}}},{}],123:[function(e,t,n){t.exports={dir:e("./dir"),has:e("./has"),lang:e("./lang"),matches:e("./matches"),not:e("./not"),"nth-child":e("./nth-child"),"nth-last-child":e("./nth-last-child"),"nth-last-of-type":e("./nth-last-of-type"),"nth-of-type":e("./nth-of-type"),slotted:e("./slotted")}},{"./dir":121,"./has":122,"./lang":124,"./matches":125,"./not":126,"./nth-child":127,"./nth-last-child":128,"./nth-last-of-type":129,"./nth-of-type":130,"./slotted":131}],124:[function(e,t,n){arguments[4][121][0].apply(n,arguments)},{dup:121}],125:[function(e,t,n){t.exports=e("./common/selectorList")},{"./common/selectorList":120}],126:[function(e,t,n){arguments[4][125][0].apply(n,arguments)},{"./common/selectorList":120,dup:125}],127:[function(e,t,n){t.exports=e("./common/nthWithOfClause")},{"./common/nthWithOfClause":119}],128:[function(e,t,n){arguments[4][127][0].apply(n,arguments)},{"./common/nthWithOfClause":119,dup:127}],129:[function(e,t,n){t.exports=e("./common/nth")},{"./common/nth":118}],130:[function(e,t,n){arguments[4][129][0].apply(n,arguments)},{"./common/nth":118,dup:129}],131:[function(e,t,n){t.exports={parse:function(){return this.createSingleNodeList(this.Selector())}}},{}],132:[function(e,t,n){t.exports={getNode:e("./default")}},{"./default":133}],133:[function(e,t,n){var r=e("../../tokenizer").cmpChar,o=e("../../tokenizer").TYPE,i=o.Identifier,a=o.String,s=o.Number,l=o.Function,c=o.Url,u=o.NumberSign,h=o.LeftParenthesis,p=o.LeftSquareBracket,d=o.PlusSign,f=o.HyphenMinus,m=o.Comma,g=o.Solidus,b=o.Asterisk,y=o.PercentSign,v=o.Backslash;t.exports=function(e){switch(this.scanner.tokenType){case u:return this.HexColor();case m:return e.space=null,e.ignoreWSAfter=!0,this.Operator();case g:case b:case d:case f:return this.Operator();case h:return this.Parentheses(this.readSequence,e.recognizer);case p:return this.Brackets(this.readSequence,e.recognizer);case a:return this.String();case s:switch(this.scanner.lookupType(1)){case y:return this.Percentage();case i:return r(this.scanner.source,this.scanner.tokenEnd,v)?this.Number():this.Dimension();default:return this.Number()}case l:return this.Function(this.readSequence,e.recognizer);case c:return this.Url();case i:return r(this.scanner.source,this.scanner.tokenStart,117)&&r(this.scanner.source,this.scanner.tokenStart+1,d)?this.UnicodeRange():this.Identifier()}}},{"../../tokenizer":140}],134:[function(e,t,n){t.exports={AtrulePrelude:e("./atrulePrelude"),Selector:e("./selector"),Value:e("./value")}},{"./atrulePrelude":132,"./selector":135,"./value":136}],135:[function(e,t,n){function r(e){switch(this.scanner.tokenType){case c:case f:case g:return e.space=null,e.ignoreWSAfter=!0,this.Combinator();case u:return this.Combinator();case p:return this.ClassSelector();case l:return this.AttributeSelector();case s:return this.IdSelector();case d:return this.scanner.lookupType(1)===d?this.PseudoElementSelector():this.PseudoClassSelector();case i:case h:case m:return this.TypeSelector();case a:return this.Percentage()}}var o=e("../../tokenizer").TYPE,i=o.Identifier,a=o.Number,s=o.NumberSign,l=o.LeftSquareBracket,c=o.PlusSign,u=o.Solidus,h=o.Asterisk,p=o.FullStop,d=o.Colon,f=o.GreaterThanSign,m=o.VerticalLine,g=o.Tilde;t.exports={getNode:r}},{"../../tokenizer":140}],136:[function(e,t,n){t.exports={getNode:e("./default"),"-moz-element":e("../function/element"),element:e("../function/element"),expression:e("../function/expression"),var:e("../function/var")}},{"../function/element":73,"../function/expression":74,"../function/var":75,"./default":133}],137:[function(e,t,n){"use strict";function r(e,t){var n=t.length,r=h(t),o=e.lines,i=e.startLine,a=e.columns,s=e.startColumn;(null===o||o.length<n+1)&&(o=new ne(Math.max(n+1024,J)),a=new ne(o.length));for(var l=r;l<n;l++){var c=t.charCodeAt(l);o[l]=i,a[l]=s++,c!==M&&c!==N&&c!==R||(c===N&&l+1<n&&t.charCodeAt(l+1)===M&&(l++,o[l]=i,a[l]=s),i++,s=1)}o[l]=i,a[l]=s,e.linesAnsColumnsComputed=!0,e.lines=o,e.columns=a}function o(e,t,n){var r=t.length,o=e.offsetAndType,i=e.balance,a=0,s=0,l=n,u=0,h=0,f=0,M=0;for((null===o||o.length<r+1)&&(o=new ne(r+1024),i=new ne(r+1024));l<r;){var R=t.charCodeAt(l),N=R<128?c[R]:w;switch(i[a]=r,N){case x:l=m(t,l+1);break;case z:switch(R){case h:for(M=f&ee,f=i[M],h=f>>te,i[a]=M,i[M++]=a;M<a;M++)i[M]===r&&(i[M]=a);break;case X:i[a]=f,h=Z,f=h<<te|a;break;case $:i[a]=f,h=K,f=h<<te|a;break;case H:i[a]=f,h=Q,f=h<<te|a}if(R===D&&s===B){N=A,l=g(t,l+1),a--;break}if(R===j&&(s===I||s===F)&&l+1<r&&d(t.charCodeAt(l+1))){N=S,l=y(t,l+2,!1),a--;break}if(R===U&&s===q&&l+2<r&&t.charCodeAt(l+1)===F&&t.charCodeAt(l+2)===F){N=T,l+=3,a--;break}if(R===F&&s===F&&l+1<r&&t.charCodeAt(l+1)===W){N=L,l+=2,a--;break}if(R===H&&s===w){l+=1,a--,i[a]=i[a+1],f--,l-u==4&&p(t,u,l,"url(")?(u=m(t,l),R=t.charCodeAt(u),R!==H&&R!==Q&&R!==G&&R!==V?(o[a++]=O<<te|l,i[a]=r,u!==l&&(o[a++]=x<<te|u,i[a]=r),N=_,l=k(t,u)):N=O):N=E;break}N=R,l+=1;break;case S:l=y(t,l+1,s!==j),s!==j&&s!==F&&s!==I||a--;break;case C:l=b(t,l+1,R);break;default:u=l,l=v(t,l),s===F&&(a--,s=0===a?0:o[a-1]>>te),s===Y&&(a--,N=P)}o[a++]=N<<te|l,s=N}for(o[a]=l,i[a]=r,i[r]=r;0!==f;)M=f&ee,f=i[M],i[M]=r;e.offsetAndType=o,e.tokenCount=a,e.balance=i}var i=e("./error"),a=e("./const"),s=a.TYPE,l=a.NAME,c=a.SYMBOL_TYPE,u=e("./utils"),h=u.firstCharOffset,p=u.cmpStr,d=u.isNumber,f=u.findWhiteSpaceStart,m=u.findWhiteSpaceEnd,g=u.findCommentEnd,b=u.findStringEnd,y=u.findNumberEnd,v=u.findIdentifierEnd,k=u.findUrlRawEnd,x=s.WhiteSpace,w=s.Identifier,S=s.Number,C=s.String,A=s.Comment,z=s.Punctuator,T=s.CDO,L=s.CDC,P=s.AtKeyword,E=s.Function,O=s.Url,_=s.Raw,M=10,R=12,N=13,D=s.Asterisk,B=s.Solidus,j=s.FullStop,I=s.PlusSign,F=s.HyphenMinus,W=s.GreaterThanSign,q=s.LessThanSign,U=s.ExclamationMark,Y=s.CommercialAt,G=s.QuotationMark,V=s.Apostrophe,H=s.LeftParenthesis,Q=s.RightParenthesis,$=s.LeftCurlyBracket,K=s.RightCurlyBracket,X=s.LeftSquareBracket,Z=s.RightSquareBracket,J=16384,ee=16777215,te=24,ne="undefined"!=typeof Uint32Array?Uint32Array:Array,re=function(e,t,n,r){this.offsetAndType=null,this.balance=null,this.lines=null,this.columns=null,this.setSource(e,t,n,r)};re.prototype={setSource:function(e,t,n,r){var i=String(e||""),a=h(i);this.source=i,this.firstCharOffset=a,this.startOffset=void 0===t?0:t,this.startLine=void 0===n?1:n,this.startColumn=void 0===r?1:r,this.linesAnsColumnsComputed=!1,this.eof=!1,this.currentToken=-1,this.tokenType=0,this.tokenStart=a,this.tokenEnd=a,o(this,i,a),this.next()},lookupType:function(e){return e+=this.currentToken,e<this.tokenCount?this.offsetAndType[e]>>te:0},lookupNonWSType:function(e){e+=this.currentToken;for(var t;e<this.tokenCount;e++)if((t=this.offsetAndType[e]>>te)!==x)return t;return 0},lookupValue:function(e,t){return(e+=this.currentToken)<this.tokenCount&&p(this.source,this.offsetAndType[e-1]&ee,this.offsetAndType[e]&ee,t)},getTokenStart:function(e){return e===this.currentToken?this.tokenStart:e>0?e<this.tokenCount?this.offsetAndType[e-1]&ee:this.offsetAndType[this.tokenCount]&ee:this.firstCharOffset},getOffsetExcludeWS:function(){return this.currentToken>0&&this.offsetAndType[this.currentToken-1]>>te===x?this.currentToken>1?this.offsetAndType[this.currentToken-2]&ee:this.firstCharOffset:this.tokenStart},getRawLength:function(e,t,n,r){var o,i=e;e:for(;i<this.tokenCount&&!((o=this.balance[i])<e);i++)switch(this.offsetAndType[i]>>te){case t:break e;case n:r&&i++;break e;default:this.balance[o]===i&&(i=o)}return i-this.currentToken},isBalanceEdge:function(e){return this.balance[this.currentToken]<e},getTokenValue:function(){return this.source.substring(this.tokenStart,this.tokenEnd)},substrToCursor:function(e){return this.source.substring(e,this.tokenStart)},skipWS:function(){for(var e=this.currentToken,t=0;e<this.tokenCount&&this.offsetAndType[e]>>te===x;e++,t++);t>0&&this.skip(t)},skipSC:function(){for(;this.tokenType===x||this.tokenType===A;)this.next()},skip:function(e){var t=this.currentToken+e;t<this.tokenCount?(this.currentToken=t,this.tokenStart=this.offsetAndType[t-1]&ee,t=this.offsetAndType[t],this.tokenType=t>>te,this.tokenEnd=t&ee):(this.currentToken=this.tokenCount,this.next())},next:function(){var e=this.currentToken+1;e<this.tokenCount?(this.currentToken=e,this.tokenStart=this.tokenEnd,e=this.offsetAndType[e],this.tokenType=e>>te,this.tokenEnd=e&ee):(this.currentToken=this.tokenCount,this.eof=!0,this.tokenType=0,this.tokenStart=this.tokenEnd=this.source.length)},eat:function(e){if(this.tokenType!==e){var t=this.tokenStart,n=l[e]+" is expected";e===w?this.tokenType!==E&&this.tokenType!==O||(t=this.tokenEnd-1,n+=" but function found"):this.source.charCodeAt(this.tokenStart)===e&&(t+=1),this.error(n,t)}this.next()},eatNonWS:function(e){this.skipWS(),this.eat(e)},consume:function(e){var t=this.getTokenValue();return this.eat(e),t},consumeFunctionName:function(){var e=this.source.substring(this.tokenStart,this.tokenEnd-1);return this.eat(E),e},consumeNonWS:function(e){return this.skipWS(),this.consume(e)},expectIdentifier:function(e){this.tokenType===w&&!1!==p(this.source,this.tokenStart,this.tokenEnd,e)||this.error("Identifier `"+e+"` is expected"),this.next()},getLocation:function(e,t){return this.linesAnsColumnsComputed||r(this,this.source),{source:t,offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]}},getLocationRange:function(e,t,n){return this.linesAnsColumnsComputed||r(this,this.source),{source:n,start:{offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]},end:{offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]}}},error:function(e,t){var n=void 0!==t&&t<this.source.length?this.getLocation(t):this.eof?this.getLocation(f(this.source,this.source.length-1)):this.getLocation(this.tokenStart);throw new i(e||"Unexpected input",this.source,n.offset,n.line,n.column)},dump:function(){var e=0;return Array.prototype.slice.call(this.offsetAndType,0,this.tokenCount).map(function(t,n){var r=e,o=t&ee;return e=o,{idx:n,type:l[t>>te],chunk:this.source.substring(r,o),balance:this.balance[n]}},this)}},re.CssSyntaxError=i,Object.keys(a).forEach(function(e){re[e]=a[e]}),Object.keys(u).forEach(function(e){re[e]=u[e]}),new re("\n\r\r\n\f\x3c!----\x3e//\"\"''/*\r\n\f*/1a;.\\31\t+2{url(a);func();+1.2e3 -.4e-5 .6e+7}").getLocation(),t.exports=re},{"./const":138,"./error":139,"./utils":141}],138:[function(e,t,n){"use strict";for(var r={WhiteSpace:1,Identifier:2,Number:3,String:4,Comment:5,Punctuator:6,CDO:7,CDC:8,AtKeyword:14,Function:15,Url:16,Raw:17,ExclamationMark:33,QuotationMark:34,NumberSign:35,DollarSign:36,PercentSign:37,Ampersand:38,Apostrophe:39,LeftParenthesis:40,RightParenthesis:41,Asterisk:42,PlusSign:43,Comma:44,HyphenMinus:45,FullStop:46,Solidus:47,Colon:58,Semicolon:59,LessThanSign:60,EqualsSign:61,GreaterThanSign:62,QuestionMark:63,CommercialAt:64,LeftSquareBracket:91,Backslash:92,RightSquareBracket:93,CircumflexAccent:94,LowLine:95,GraveAccent:96,LeftCurlyBracket:123,VerticalLine:124,RightCurlyBracket:125,Tilde:126},o=Object.keys(r).reduce(function(e,t){return e[r[t]]=t,e},{}),i="undefined"!=typeof Uint32Array?Uint32Array:Array,a=new i(128),s=new i(128),l=new i(128),c=0;c<a.length;c++)a[c]=2;[r.ExclamationMark,r.QuotationMark,r.NumberSign,r.DollarSign,r.PercentSign,r.Ampersand,r.Apostrophe,r.LeftParenthesis,r.RightParenthesis,r.Asterisk,r.PlusSign,r.Comma,r.HyphenMinus,r.FullStop,r.Solidus,r.Colon,r.Semicolon,r.LessThanSign,r.EqualsSign,r.GreaterThanSign,r.QuestionMark,r.CommercialAt,r.LeftSquareBracket,r.RightSquareBracket,r.CircumflexAccent,r.GraveAccent,r.LeftCurlyBracket,r.VerticalLine,r.RightCurlyBracket,r.Tilde].forEach(function(e){a[Number(e)]=6,s[Number(e)]=6});for(var c=48;c<=57;c++)a[c]=3;a[32]=1,a[9]=1,a[10]=1,a[13]=1,a[12]=1,a[r.Apostrophe]=4,a[r.QuotationMark]=4,l[32]=1,l[9]=1,l[10]=1,l[13]=1,l[12]=1,l[r.Apostrophe]=1,l[r.QuotationMark]=1,l[r.LeftParenthesis]=1,l[r.RightParenthesis]=1,s[32]=6,s[9]=6,s[10]=6,s[13]=6,s[12]=6,s[r.HyphenMinus]=0,t.exports={TYPE:r,NAME:o,SYMBOL_TYPE:a,PUNCTUATION:s,STOP_URL_RAW:l}},{}],139:[function(e,t,n){"use strict";function r(e,t){function n(e,t){return r.slice(e,t).map(function(t,n){for(var r=String(e+n+1);r.length<h;)r=" "+r;return r+" |"+t}).join("\n")}var r=e.source.split(/\r\n?|\n|\f/),o=e.line,l=e.column,c=Math.max(1,o-t)-1,u=Math.min(o+t,r.length+1),h=Math.max(4,String(u).length)+1,p=0;(l+=(s.length-1)*(r[o-1].substr(0,l-1).match(/\t/g)||[]).length)>i&&(p=l-a+3,l=a-2);for(var d=c;d<=u;d++)d>=0&&d<r.length&&(r[d]=r[d].replace(/\t/g,s),r[d]=(p>0&&r[d].length>p?"…":"")+r[d].substr(p,i-2)+(r[d].length>p+i-1?"…":""));return[n(c,o),new Array(l+h+2).join("-")+"^",n(o,u)].filter(Boolean).join("\n")}var o=e("../utils/createCustomError"),i=100,a=60,s="    ",l=function(e,t,n,i,a){var s=o("CssSyntaxError",e);return s.source=t,s.offset=n,s.line=i,s.column=a,s.sourceFragment=function(e){return r(s,isNaN(e)?0:e)},Object.defineProperty(s,"formattedMessage",{get:function(){return"Parse error: "+s.message+"\n"+r(s,2)}}),s.parseError={offset:n,line:i,column:a},s};t.exports=l},{"../utils/createCustomError":143}],140:[function(e,t,n){t.exports=e("./Tokenizer")},{"./Tokenizer":137}],141:[function(e,t,n){"use strict";function r(e){return 65279===e.charCodeAt(0)||65534===e.charCodeAt(0)?1:0}function o(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function i(e){return e>=48&&e<=57}function a(e){return e===_||e===L||s(e)}function s(e){return e===O||e===P||e===E}function l(e,t,n){return s(n)?n===O&&t+1<e.length&&e.charCodeAt(t+1)===P?2:1:0}function c(e,t,n){var r=e.charCodeAt(t);return r>=65&&r<=90&&(r|=32),r===n}function u(e,t,n,r){if(n-t!==r.length)return!1;if(t<0||n>e.length)return!1;for(var o=t;o<n;o++){var i=e.charCodeAt(o),a=r.charCodeAt(o-t);if(i>=65&&i<=90&&(i|=32),i!==a)return!1}return!0}function h(e,t){for(;t>=0&&a(e.charCodeAt(t));)t--;return t+1}function p(e,t){for(;t<e.length&&a(e.charCodeAt(t));)t++;return t}function d(e,t){var n=e.indexOf("*/",t);return-1===n?e.length:n+2}function f(e,t,n){for(;t<e.length;t++){var r=e.charCodeAt(t);if(r===M)t++;else if(r===n){t++;break}}return t}function m(e,t){for(;t<e.length&&i(e.charCodeAt(t));)t++;return t}function g(e,t,n){var r;return t=m(e,t),n&&t+1<e.length&&e.charCodeAt(t)===C&&(r=e.charCodeAt(t+1),i(r)&&(t=m(e,t+1))),t+1<e.length&&(32|e.charCodeAt(t))===R&&(r=e.charCodeAt(t+1),r!==A&&r!==z||t+2<e.length&&(r=e.charCodeAt(t+2)),i(r)&&(t=m(e,t+2))),t}function b(e,t){for(var n=0;n<7&&t+n<e.length;n++){var r=e.charCodeAt(t+n);if(6===n||!o(r)){n>0&&(t+=n-1+l(e,t+n,r),r!==_&&r!==L||t++);break}}return t}function y(e,t){for(;t<e.length;t++){var n=e.charCodeAt(t);if(n===M)t=b(e,t+1);else if(n<128&&x[n]===T)break}return t}function v(e,t){for(;t<e.length;t++){var n=e.charCodeAt(t);if(n===M)t=b(e,t+1);else if(n<128&&1===w[n])break}return t}var k=e("./const"),x=k.PUNCTUATION,w=k.STOP_URL_RAW,S=k.TYPE,C=S.FullStop,A=S.PlusSign,z=S.HyphenMinus,T=S.Punctuator,L=9,P=10,E=12,O=13,_=32,M=92,R=101;t.exports={firstCharOffset:r,isHex:o,isNumber:i,isWhiteSpace:a,isNewline:s,getNewlineLength:l,cmpChar:c,cmpStr:u,findWhiteSpaceStart:h,findWhiteSpaceEnd:p,findCommentEnd:d,findStringEnd:f,findDecimalNumberEnd:m,findNumberEnd:g,findEscapeEnd:b,findIdentifierEnd:y,findUrlRawEnd:v}},{"./const":138}],142:[function(e,t,n){"use strict";var r=e("./list");t.exports=function e(t){var n={};for(var o in t){var i=t[o];i&&(Array.isArray(i)||i instanceof r?i=i.map(e):i.constructor===Object&&(i=e(i))),n[o]=i}return n}},{"./list":144}],143:[function(e,t,n){t.exports=function(e,t){var n=Object.create(SyntaxError.prototype),r=new Error;return n.name=e,n.message=t,Object.defineProperty(n,"stack",{get:function(){return(r.stack||"").replace(/^(.+\n){1,3}/,e+": "+t+"\n")}}),n}},{}],144:[function(e,t,n){"use strict";function r(e){return{prev:null,next:null,data:e}}function o(e,t,n){var r;return null!==a?(r=a,a=a.cursor,r.prev=t,r.next=n,r.cursor=e.cursor):r={prev:t,next:n,cursor:e.cursor},e.cursor=r,r}function i(e){var t=e.cursor;e.cursor=t.cursor,t.prev=null,t.next=null,t.cursor=a,a=t}var a=null,s=function(){this.cursor=null,this.head=null,this.tail=null};s.createItem=r,s.prototype.createItem=r,s.prototype.updateCursors=function(e,t,n,r){for(var o=this.cursor;null!==o;)o.prev===e&&(o.prev=t),o.next===n&&(o.next=r),o=o.cursor},s.prototype.getSize=function(){for(var e=0,t=this.head;t;)e++,t=t.next;return e},s.prototype.fromArray=function(e){var t=null;this.head=null;for(var n=0;n<e.length;n++){var o=r(e[n]);null!==t?t.next=o:this.head=o,o.prev=t,t=o}return this.tail=t,this},s.prototype.toArray=function(){for(var e=this.head,t=[];e;)t.push(e.data),e=e.next;return t},s.prototype.toJSON=s.prototype.toArray,s.prototype.isEmpty=function(){return null===this.head},s.prototype.first=function(){return this.head&&this.head.data},s.prototype.last=function(){return this.tail&&this.tail.data},s.prototype.each=function(e,t){var n;void 0===t&&(t=this);for(var r=o(this,null,this.head);null!==r.next;)n=r.next,r.next=n.next,e.call(t,n.data,n,this);i(this)},s.prototype.forEach=s.prototype.each,s.prototype.eachRight=function(e,t){var n;void 0===t&&(t=this);for(var r=o(this,this.tail,null);null!==r.prev;)n=r.prev,r.prev=n.prev,e.call(t,n.data,n,this);i(this)},s.prototype.forEachRight=s.prototype.eachRight,s.prototype.nextUntil=function(e,t,n){if(null!==e){var r;void 0===n&&(n=this);for(var a=o(this,null,e);null!==a.next&&(r=a.next,a.next=r.next,!t.call(n,r.data,r,this)););i(this)}},s.prototype.prevUntil=function(e,t,n){if(null!==e){var r;void 0===n&&(n=this);for(var a=o(this,e,null);null!==a.prev&&(r=a.prev,a.prev=r.prev,!t.call(n,r.data,r,this)););i(this)}},s.prototype.some=function(e,t){var n=this.head;for(void 0===t&&(t=this);null!==n;){if(e.call(t,n.data,n,this))return!0;n=n.next}return!1},s.prototype.map=function(e,t){var n=new s,r=this.head;for(void 0===t&&(t=this);null!==r;)n.appendData(e.call(t,r.data,r,this)),r=r.next;return n},s.prototype.filter=function(e,t){var n=new s,r=this.head;for(void 0===t&&(t=this);null!==r;)e.call(t,r.data,r,this)&&n.appendData(r.data),r=r.next;return n},s.prototype.clear=function(){this.head=null,this.tail=null},s.prototype.copy=function(){for(var e=new s,t=this.head;null!==t;)e.insert(r(t.data)),t=t.next;return e},s.prototype.prepend=function(e){return this.updateCursors(null,e,this.head,e),null!==this.head?(this.head.prev=e,e.next=this.head):this.tail=e,this.head=e,this},s.prototype.prependData=function(e){return this.prepend(r(e))},s.prototype.append=function(e){return this.insert(e)},s.prototype.appendData=function(e){return this.insert(r(e))},s.prototype.insert=function(e,t){if(void 0!==t&&null!==t)if(this.updateCursors(t.prev,e,t,e),null===t.prev){if(this.head!==t)throw new Error("before doesn't belong to list");this.head=e,t.prev=e,e.next=t,this.updateCursors(null,e)}else t.prev.next=e,e.prev=t.prev,t.prev=e,e.next=t;else this.updateCursors(this.tail,e,null,e),null!==this.tail?(this.tail.next=e,e.prev=this.tail):this.head=e,this.tail=e;return this},s.prototype.insertData=function(e,t){return this.insert(r(e),t)},s.prototype.remove=function(e){if(this.updateCursors(e,e.prev,e,e.next),null!==e.prev)e.prev.next=e.next;else{if(this.head!==e)throw new Error("item doesn't belong to list");this.head=e.next}if(null!==e.next)e.next.prev=e.prev;else{if(this.tail!==e)throw new Error("item doesn't belong to list");this.tail=e.prev}return e.prev=null,e.next=null,e},s.prototype.push=function(e){this.insert(r(e))},s.prototype.pop=function(){if(null!==this.tail)return this.remove(this.tail)},s.prototype.unshift=function(e){this.prepend(r(e))},s.prototype.shift=function(){if(null!==this.head)return this.remove(this.head)},s.prototype.prependList=function(e){return this.insertList(e,this.head)},s.prototype.appendList=function(e){return this.insertList(e)},s.prototype.insertList=function(e,t){return null===e.head?this:(void 0!==t&&null!==t?(this.updateCursors(t.prev,e.tail,t,e.head),null!==t.prev?(t.prev.next=e.head,e.head.prev=t.prev):this.head=e.head,t.prev=e.tail,e.tail.next=t):(this.updateCursors(this.tail,e.tail,null,e.head),null!==this.tail?(this.tail.next=e.head,e.head.prev=this.tail):this.head=e.head,this.tail=e.tail),e.head=null,e.tail=null,this)},s.prototype.replace=function(e,t){"head"in t?this.insertList(t,e):this.insert(t,e),this.remove(e)},t.exports=s},{}],145:[function(e,t,n){"use strict";function r(e,t){return t=t||0,e.length-t>=2&&e.charCodeAt(t)===u&&e.charCodeAt(t+1)===u}function o(e,t){if(t=t||0,e.length-t>=3&&e.charCodeAt(t)===u&&e.charCodeAt(t+1)!==u){var n=e.indexOf("-",t+2);if(-1!==n)return e.substring(t,n+1)}return""}function i(e){if(s.call(l,e))return l[e];var t=e.toLowerCase();if(s.call(l,t))return l[e]=l[t];var n=r(t,0),i=n?"":o(t,0);return l[e]=Object.freeze({basename:t.substr(i.length),name:t,vendor:i,prefix:i,custom:n})}function a(e){if(s.call(c,e))return c[e];var t=e,n=e[0];"/"===n?n="/"===e[1]?"//":"/":"_"!==n&&"*"!==n&&"$"!==n&&"#"!==n&&"+"!==n&&(n="");var i=r(t,n.length);if(!i&&(t=t.toLowerCase(),s.call(c,t)))return c[e]=c[t];var a=i?"":o(t,n.length),l=t.substr(0,n.length+a.length);return c[e]=Object.freeze({basename:t.substr(l.length),name:t.substr(n.length),hack:n,vendor:a,prefix:l,custom:i})}var s=Object.prototype.hasOwnProperty,l=Object.create(null),c=Object.create(null),u=45;t.exports={keyword:i,property:a,isCustomProperty:r,vendorPrefix:o}},{}],146:[function(e,t,n){"use strict";function r(e){return"function"==typeof e?e:u}function o(e,t){return function(n,r,o){n.type===t&&e.call(this,n,r,o)}}function i(e,t){var n=t.structure,r=[];for(var o in n)if(!1!==c.call(n,o)){var i=n[o],a={name:o,type:!1,nullable:!1};Array.isArray(n[o])||(i=[n[o]]);for(var s=0;s<i.length;s++){var l=i[s];null===l?a.nullable=!0:"string"==typeof l?a.type="node":Array.isArray(l)&&(a.type="list")}a.type&&r.push(a)}return r.length?{context:t.walkContext,fields:r}:null}function a(e){var t={};for(var n in e.node)if(c.call(e.node,n)){var r=e.node[n];if(!r.structure)throw new Error("Missed `structure` field in `"+n+"` node type definition");t[n]=i(n,r)}return t}function s(e,t){var n=t?e.fields.slice().reverse():e.fields,r=n.map(function(e){var n,r="node."+e.name;return n="list"===e.type?t?r+".forEachRight(walk);":r+".forEach(walk);":"walk("+r+");",e.nullable&&(n="if ("+r+") {\n    "+n+"}"),n});return e.context&&(r=[].concat("var old = context."+e.context+";","context."+e.context+" = node;",r,"context."+e.context+" = old;")),new Function("node","context","walk",r.join("\n"))}function l(e){return{Atrule:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block},Rule:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block},Declaration:{StyleSheet:e.StyleSheet,Atrule:e.Atrule,Rule:e.Rule,Block:e.Block}}}var c=Object.prototype.hasOwnProperty,u=function(){};t.exports=function(e){var t=a(e),n={},i={};for(var h in t)c.call(t,h)&&null!==t[h]&&(n[h]=s(t[h],!1),i[h]=s(t[h],!0));var p=l(n),d=l(i);return function(e,a){function s(e,t,n){l.call(f,e,t,n),h.hasOwnProperty(e.type)&&h[e.type](e,f,s),c.call(f,e,t,n)}var l=u,c=u,h=n,f={root:e,stylesheet:null,atrule:null,atrulePrelude:null,rule:null,selector:null,block:null,declaration:null,function:null};if("function"==typeof a)l=a;else if(a&&(l=r(a.enter),c=r(a.leave),a.reverse&&(h=i),a.visit)){if(p.hasOwnProperty(a.visit))h=a.reverse?d[a.visit]:p[a.visit];else if(!t.hasOwnProperty(a.visit))throw new Error("Bad value `"+a.visit+"` for `visit` option (should be: "+Object.keys(t).join(", ")+")");l=o(l,a.visit),c=o(c,a.visit)}if(l===u&&c===u)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");if(a.reverse){var m=l;l=c,c=m}s(e)}}},{}],147:[function(e,t,n){function r(){this._array=[],this._set=a?new Map:Object.create(null)}var o=e("./util"),i=Object.prototype.hasOwnProperty,a="undefined"!=typeof Map;r.fromArray=function(e,t){for(var n=new r,o=0,i=e.length;o<i;o++)n.add(e[o],t);return n},r.prototype.size=function(){return a?this._set.size:Object.getOwnPropertyNames(this._set).length},r.prototype.add=function(e,t){var n=a?e:o.toSetString(e),r=a?this.has(e):i.call(this._set,n),s=this._array.length;r&&!t||this._array.push(e),r||(a?this._set.set(e,s):this._set[n]=s)},r.prototype.has=function(e){if(a)return this._set.has(e);var t=o.toSetString(e);return i.call(this._set,t)},r.prototype.indexOf=function(e){if(a){var t=this._set.get(e);if(t>=0)return t}else{var n=o.toSetString(e);if(i.call(this._set,n))return this._set[n]}throw new Error('"'+e+'" is not in the set.')},r.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},r.prototype.toArray=function(){return this._array.slice()},n.ArraySet=r},{"./util":156}],148:[function(e,t,n){function r(e){return e<0?1+(-e<<1):0+(e<<1)}function o(e){var t=1==(1&e),n=e>>1;return t?-n:n}var i=e("./base64");n.encode=function(e){var t,n="",o=r(e);do{t=31&o,o>>>=5,o>0&&(t|=32),n+=i.encode(t)}while(o>0);return n},n.decode=function(e,t,n){var r,a,s=e.length,l=0,c=0;do{if(t>=s)throw new Error("Expected more digits in base 64 VLQ value.");if(-1===(a=i.decode(e.charCodeAt(t++))))throw new Error("Invalid base64 digit: "+e.charAt(t-1));r=!!(32&a),a&=31,l+=a<<c,c+=5}while(r);n.value=o(l),n.rest=t}},{"./base64":149}],149:[function(e,t,n){var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");n.encode=function(e){if(0<=e&&e<r.length)return r[e];throw new TypeError("Must be between 0 and 63: "+e)},n.decode=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1}},{}],150:[function(e,t,n){function r(e,t,o,i,a,s){var l=Math.floor((t-e)/2)+e,c=a(o,i[l],!0);return 0===c?l:c>0?t-l>1?r(l,t,o,i,a,s):s==n.LEAST_UPPER_BOUND?t<i.length?t:-1:l:l-e>1?r(e,l,o,i,a,s):s==n.LEAST_UPPER_BOUND?l:e<0?-1:e}n.GREATEST_LOWER_BOUND=1,n.LEAST_UPPER_BOUND=2,n.search=function(e,t,o,i){if(0===t.length)return-1;var a=r(-1,t.length,e,t,o,i||n.GREATEST_LOWER_BOUND);if(a<0)return-1;for(;a-1>=0&&0===o(t[a],t[a-1],!0);)--a;return a}},{}],151:[function(e,t,n){function r(e,t){var n=e.generatedLine,r=t.generatedLine,o=e.generatedColumn,a=t.generatedColumn;return r>n||r==n&&a>=o||i.compareByGeneratedPositionsInflated(e,t)<=0}function o(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}var i=e("./util");o.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},o.prototype.add=function(e){r(this._last,e)?(this._last=e,this._array.push(e)):(this._sorted=!1,this._array.push(e))},o.prototype.toArray=function(){return this._sorted||(this._array.sort(i.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},n.MappingList=o},{"./util":156}],152:[function(e,t,n){function r(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function o(e,t){return Math.round(e+Math.random()*(t-e))}function i(e,t,n,a){if(n<a){var s=o(n,a),l=n-1;r(e,s,a);for(var c=e[a],u=n;u<a;u++)t(e[u],c)<=0&&(l+=1,r(e,l,u));r(e,l+1,u);var h=l+1;i(e,t,n,h-1),i(e,t,h+1,a)}}n.quickSort=function(e,t){i(e,t,0,e.length-1)}},{}],153:[function(e,t,n){function r(e){var t=e;return"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,""))),null!=t.sections?new a(t):new o(t)}function o(e){var t=e;"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,"")));var n=s.getArg(t,"version"),r=s.getArg(t,"sources"),o=s.getArg(t,"names",[]),i=s.getArg(t,"sourceRoot",null),a=s.getArg(t,"sourcesContent",null),l=s.getArg(t,"mappings"),u=s.getArg(t,"file",null);if(n!=this._version)throw new Error("Unsupported version: "+n);r=r.map(String).map(s.normalize).map(function(e){return i&&s.isAbsolute(i)&&s.isAbsolute(e)?s.relative(i,e):e}),this._names=c.fromArray(o.map(String),!0),this._sources=c.fromArray(r,!0),this.sourceRoot=i,this.sourcesContent=a,this._mappings=l,this.file=u}function i(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function a(e){var t=e;"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,"")));var n=s.getArg(t,"version"),o=s.getArg(t,"sections");if(n!=this._version)throw new Error("Unsupported version: "+n);this._sources=new c,this._names=new c;var i={line:-1,column:0};this._sections=o.map(function(e){if(e.url)throw new Error("Support for url field in sections not implemented.");var t=s.getArg(e,"offset"),n=s.getArg(t,"line"),o=s.getArg(t,"column");if(n<i.line||n===i.line&&o<i.column)throw new Error("Section offsets must be ordered and non-overlapping.");return i=t,{generatedOffset:{generatedLine:n+1,generatedColumn:o+1},consumer:new r(s.getArg(e,"map"))}})}var s=e("./util"),l=e("./binary-search"),c=e("./array-set").ArraySet,u=e("./base64-vlq"),h=e("./quick-sort").quickSort;r.fromSourceMap=function(e){return o.fromSourceMap(e)},r.prototype._version=3,r.prototype.__generatedMappings=null,Object.defineProperty(r.prototype,"_generatedMappings",{get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),r.prototype.__originalMappings=null,Object.defineProperty(r.prototype,"_originalMappings",{get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),r.prototype._charIsMappingSeparator=function(e,t){var n=e.charAt(t);return";"===n||","===n},r.prototype._parseMappings=function(e,t){throw new Error("Subclasses must implement _parseMappings")},r.GENERATED_ORDER=1,r.ORIGINAL_ORDER=2,r.GREATEST_LOWER_BOUND=1,r.LEAST_UPPER_BOUND=2,r.prototype.eachMapping=function(e,t,n){var o,i=t||null,a=n||r.GENERATED_ORDER;switch(a){case r.GENERATED_ORDER:o=this._generatedMappings;break;case r.ORIGINAL_ORDER:o=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var l=this.sourceRoot;o.map(function(e){var t=null===e.source?null:this._sources.at(e.source);return null!=t&&null!=l&&(t=s.join(l,t)),{source:t,generatedLine:e.generatedLine,generatedColumn:e.generatedColumn,originalLine:e.originalLine,originalColumn:e.originalColumn,name:null===e.name?null:this._names.at(e.name)}},this).forEach(e,i)},r.prototype.allGeneratedPositionsFor=function(e){var t=s.getArg(e,"line"),n={source:s.getArg(e,"source"),originalLine:t,originalColumn:s.getArg(e,"column",0)};if(null!=this.sourceRoot&&(n.source=s.relative(this.sourceRoot,n.source)),!this._sources.has(n.source))return[];n.source=this._sources.indexOf(n.source);var r=[],o=this._findMapping(n,this._originalMappings,"originalLine","originalColumn",s.compareByOriginalPositions,l.LEAST_UPPER_BOUND);if(o>=0){var i=this._originalMappings[o];if(void 0===e.column)for(var a=i.originalLine;i&&i.originalLine===a;)r.push({line:s.getArg(i,"generatedLine",null),column:s.getArg(i,"generatedColumn",null),lastColumn:s.getArg(i,"lastGeneratedColumn",null)}),i=this._originalMappings[++o];else for(var c=i.originalColumn;i&&i.originalLine===t&&i.originalColumn==c;)r.push({line:s.getArg(i,"generatedLine",null),column:s.getArg(i,"generatedColumn",null),lastColumn:s.getArg(i,"lastGeneratedColumn",null)}),i=this._originalMappings[++o]}return r},n.SourceMapConsumer=r,o.prototype=Object.create(r.prototype),o.prototype.consumer=r,o.fromSourceMap=function(e){var t=Object.create(o.prototype),n=t._names=c.fromArray(e._names.toArray(),!0),r=t._sources=c.fromArray(e._sources.toArray(),!0);t.sourceRoot=e._sourceRoot,t.sourcesContent=e._generateSourcesContent(t._sources.toArray(),t.sourceRoot),t.file=e._file;for(var a=e._mappings.toArray().slice(),l=t.__generatedMappings=[],u=t.__originalMappings=[],p=0,d=a.length;p<d;p++){var f=a[p],m=new i;m.generatedLine=f.generatedLine,m.generatedColumn=f.generatedColumn,
f.source&&(m.source=r.indexOf(f.source),m.originalLine=f.originalLine,m.originalColumn=f.originalColumn,f.name&&(m.name=n.indexOf(f.name)),u.push(m)),l.push(m)}return h(t.__originalMappings,s.compareByOriginalPositions),t},o.prototype._version=3,Object.defineProperty(o.prototype,"sources",{get:function(){return this._sources.toArray().map(function(e){return null!=this.sourceRoot?s.join(this.sourceRoot,e):e},this)}}),o.prototype._parseMappings=function(e,t){for(var n,r,o,a,l,c=1,p=0,d=0,f=0,m=0,g=0,b=e.length,y=0,v={},k={},x=[],w=[];y<b;)if(";"===e.charAt(y))c++,y++,p=0;else if(","===e.charAt(y))y++;else{for(n=new i,n.generatedLine=c,a=y;a<b&&!this._charIsMappingSeparator(e,a);a++);if(r=e.slice(y,a),o=v[r])y+=r.length;else{for(o=[];y<a;)u.decode(e,y,k),l=k.value,y=k.rest,o.push(l);if(2===o.length)throw new Error("Found a source, but no line and column");if(3===o.length)throw new Error("Found a source and line, but no column");v[r]=o}n.generatedColumn=p+o[0],p=n.generatedColumn,o.length>1&&(n.source=m+o[1],m+=o[1],n.originalLine=d+o[2],d=n.originalLine,n.originalLine+=1,n.originalColumn=f+o[3],f=n.originalColumn,o.length>4&&(n.name=g+o[4],g+=o[4])),w.push(n),"number"==typeof n.originalLine&&x.push(n)}h(w,s.compareByGeneratedPositionsDeflated),this.__generatedMappings=w,h(x,s.compareByOriginalPositions),this.__originalMappings=x},o.prototype._findMapping=function(e,t,n,r,o,i){if(e[n]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+e[n]);if(e[r]<0)throw new TypeError("Column must be greater than or equal to 0, got "+e[r]);return l.search(e,t,o,i)},o.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var t=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var n=this._generatedMappings[e+1];if(t.generatedLine===n.generatedLine){t.lastGeneratedColumn=n.generatedColumn-1;continue}}t.lastGeneratedColumn=1/0}},o.prototype.originalPositionFor=function(e){var t={generatedLine:s.getArg(e,"line"),generatedColumn:s.getArg(e,"column")},n=this._findMapping(t,this._generatedMappings,"generatedLine","generatedColumn",s.compareByGeneratedPositionsDeflated,s.getArg(e,"bias",r.GREATEST_LOWER_BOUND));if(n>=0){var o=this._generatedMappings[n];if(o.generatedLine===t.generatedLine){var i=s.getArg(o,"source",null);null!==i&&(i=this._sources.at(i),null!=this.sourceRoot&&(i=s.join(this.sourceRoot,i)));var a=s.getArg(o,"name",null);return null!==a&&(a=this._names.at(a)),{source:i,line:s.getArg(o,"originalLine",null),column:s.getArg(o,"originalColumn",null),name:a}}}return{source:null,line:null,column:null,name:null}},o.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&(this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(e){return null==e}))},o.prototype.sourceContentFor=function(e,t){if(!this.sourcesContent)return null;if(null!=this.sourceRoot&&(e=s.relative(this.sourceRoot,e)),this._sources.has(e))return this.sourcesContent[this._sources.indexOf(e)];var n;if(null!=this.sourceRoot&&(n=s.urlParse(this.sourceRoot))){var r=e.replace(/^file:\/\//,"");if("file"==n.scheme&&this._sources.has(r))return this.sourcesContent[this._sources.indexOf(r)];if((!n.path||"/"==n.path)&&this._sources.has("/"+e))return this.sourcesContent[this._sources.indexOf("/"+e)]}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')},o.prototype.generatedPositionFor=function(e){var t=s.getArg(e,"source");if(null!=this.sourceRoot&&(t=s.relative(this.sourceRoot,t)),!this._sources.has(t))return{line:null,column:null,lastColumn:null};t=this._sources.indexOf(t);var n={source:t,originalLine:s.getArg(e,"line"),originalColumn:s.getArg(e,"column")},o=this._findMapping(n,this._originalMappings,"originalLine","originalColumn",s.compareByOriginalPositions,s.getArg(e,"bias",r.GREATEST_LOWER_BOUND));if(o>=0){var i=this._originalMappings[o];if(i.source===n.source)return{line:s.getArg(i,"generatedLine",null),column:s.getArg(i,"generatedColumn",null),lastColumn:s.getArg(i,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},n.BasicSourceMapConsumer=o,a.prototype=Object.create(r.prototype),a.prototype.constructor=r,a.prototype._version=3,Object.defineProperty(a.prototype,"sources",{get:function(){for(var e=[],t=0;t<this._sections.length;t++)for(var n=0;n<this._sections[t].consumer.sources.length;n++)e.push(this._sections[t].consumer.sources[n]);return e}}),a.prototype.originalPositionFor=function(e){var t={generatedLine:s.getArg(e,"line"),generatedColumn:s.getArg(e,"column")},n=l.search(t,this._sections,function(e,t){var n=e.generatedLine-t.generatedOffset.generatedLine;return n||e.generatedColumn-t.generatedOffset.generatedColumn}),r=this._sections[n];return r?r.consumer.originalPositionFor({line:t.generatedLine-(r.generatedOffset.generatedLine-1),column:t.generatedColumn-(r.generatedOffset.generatedLine===t.generatedLine?r.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}},a.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(e){return e.consumer.hasContentsOfAllSources()})},a.prototype.sourceContentFor=function(e,t){for(var n=0;n<this._sections.length;n++){var r=this._sections[n],o=r.consumer.sourceContentFor(e,!0);if(o)return o}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')},a.prototype.generatedPositionFor=function(e){for(var t=0;t<this._sections.length;t++){var n=this._sections[t];if(-1!==n.consumer.sources.indexOf(s.getArg(e,"source"))){var r=n.consumer.generatedPositionFor(e);if(r){return{line:r.line+(n.generatedOffset.generatedLine-1),column:r.column+(n.generatedOffset.generatedLine===r.line?n.generatedOffset.generatedColumn-1:0)}}}}return{line:null,column:null}},a.prototype._parseMappings=function(e,t){this.__generatedMappings=[],this.__originalMappings=[];for(var n=0;n<this._sections.length;n++)for(var r=this._sections[n],o=r.consumer._generatedMappings,i=0;i<o.length;i++){var a=o[i],l=r.consumer._sources.at(a.source);null!==r.consumer.sourceRoot&&(l=s.join(r.consumer.sourceRoot,l)),this._sources.add(l),l=this._sources.indexOf(l);var c=r.consumer._names.at(a.name);this._names.add(c),c=this._names.indexOf(c);var u={source:l,generatedLine:a.generatedLine+(r.generatedOffset.generatedLine-1),generatedColumn:a.generatedColumn+(r.generatedOffset.generatedLine===a.generatedLine?r.generatedOffset.generatedColumn-1:0),originalLine:a.originalLine,originalColumn:a.originalColumn,name:c};this.__generatedMappings.push(u),"number"==typeof u.originalLine&&this.__originalMappings.push(u)}h(this.__generatedMappings,s.compareByGeneratedPositionsDeflated),h(this.__originalMappings,s.compareByOriginalPositions)},n.IndexedSourceMapConsumer=a},{"./array-set":147,"./base64-vlq":148,"./binary-search":150,"./quick-sort":152,"./util":156}],154:[function(e,t,n){function r(e){e||(e={}),this._file=i.getArg(e,"file",null),this._sourceRoot=i.getArg(e,"sourceRoot",null),this._skipValidation=i.getArg(e,"skipValidation",!1),this._sources=new a,this._names=new a,this._mappings=new s,this._sourcesContents=null}var o=e("./base64-vlq"),i=e("./util"),a=e("./array-set").ArraySet,s=e("./mapping-list").MappingList;r.prototype._version=3,r.fromSourceMap=function(e){var t=e.sourceRoot,n=new r({file:e.file,sourceRoot:t});return e.eachMapping(function(e){var r={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(r.source=e.source,null!=t&&(r.source=i.relative(t,r.source)),r.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(r.name=e.name)),n.addMapping(r)}),e.sources.forEach(function(t){var r=e.sourceContentFor(t);null!=r&&n.setSourceContent(t,r)}),n},r.prototype.addMapping=function(e){var t=i.getArg(e,"generated"),n=i.getArg(e,"original",null),r=i.getArg(e,"source",null),o=i.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,n,r,o),null!=r&&(r=String(r),this._sources.has(r)||this._sources.add(r)),null!=o&&(o=String(o),this._names.has(o)||this._names.add(o)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=n&&n.line,originalColumn:null!=n&&n.column,source:r,name:o})},r.prototype.setSourceContent=function(e,t){var n=e;null!=this._sourceRoot&&(n=i.relative(this._sourceRoot,n)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[i.toSetString(n)]=t):this._sourcesContents&&(delete this._sourcesContents[i.toSetString(n)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},r.prototype.applySourceMap=function(e,t,n){var r=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');r=e.file}var o=this._sourceRoot;null!=o&&(r=i.relative(o,r));var s=new a,l=new a;this._mappings.unsortedForEach(function(t){if(t.source===r&&null!=t.originalLine){var a=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=a.source&&(t.source=a.source,null!=n&&(t.source=i.join(n,t.source)),null!=o&&(t.source=i.relative(o,t.source)),t.originalLine=a.line,t.originalColumn=a.column,null!=a.name&&(t.name=a.name))}var c=t.source;null==c||s.has(c)||s.add(c);var u=t.name;null==u||l.has(u)||l.add(u)},this),this._sources=s,this._names=l,e.sources.forEach(function(t){var r=e.sourceContentFor(t);null!=r&&(null!=n&&(t=i.join(n,t)),null!=o&&(t=i.relative(o,t)),this.setSourceContent(t,r))},this)},r.prototype._validateMapping=function(e,t,n,r){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||n||r)&&!(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n))throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:r}))},r.prototype._serializeMappings=function(){for(var e,t,n,r,a=0,s=1,l=0,c=0,u=0,h=0,p="",d=this._mappings.toArray(),f=0,m=d.length;f<m;f++){if(t=d[f],e="",t.generatedLine!==s)for(a=0;t.generatedLine!==s;)e+=";",s++;else if(f>0){if(!i.compareByGeneratedPositionsInflated(t,d[f-1]))continue;e+=","}e+=o.encode(t.generatedColumn-a),a=t.generatedColumn,null!=t.source&&(r=this._sources.indexOf(t.source),e+=o.encode(r-h),h=r,e+=o.encode(t.originalLine-1-c),c=t.originalLine-1,e+=o.encode(t.originalColumn-l),l=t.originalColumn,null!=t.name&&(n=this._names.indexOf(t.name),e+=o.encode(n-u),u=n)),p+=e}return p},r.prototype._generateSourcesContent=function(e,t){return e.map(function(e){if(!this._sourcesContents)return null;null!=t&&(e=i.relative(t,e));var n=i.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,n)?this._sourcesContents[n]:null},this)},r.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},r.prototype.toString=function(){return JSON.stringify(this.toJSON())},n.SourceMapGenerator=r},{"./array-set":147,"./base64-vlq":148,"./mapping-list":151,"./util":156}],155:[function(e,t,n){function r(e,t,n,r,o){this.children=[],this.sourceContents={},this.line=null==e?null:e,this.column=null==t?null:t,this.source=null==n?null:n,this.name=null==o?null:o,this[s]=!0,null!=r&&this.add(r)}var o=e("./source-map-generator").SourceMapGenerator,i=e("./util"),a=/(\r?\n)/,s="$$$isSourceNode$$$";r.fromStringWithSourceMap=function(e,t,n){function o(e,t){if(null===e||void 0===e.source)s.add(t);else{var o=n?i.join(n,e.source):e.source;s.add(new r(e.originalLine,e.originalColumn,o,t,e.name))}}var s=new r,l=e.split(a),c=0,u=function(){function e(){return c<l.length?l[c++]:void 0}return e()+(e()||"")},h=1,p=0,d=null;return t.eachMapping(function(e){if(null!==d){if(!(h<e.generatedLine)){var t=l[c],n=t.substr(0,e.generatedColumn-p);return l[c]=t.substr(e.generatedColumn-p),p=e.generatedColumn,o(d,n),void(d=e)}o(d,u()),h++,p=0}for(;h<e.generatedLine;)s.add(u()),h++;if(p<e.generatedColumn){var t=l[c];s.add(t.substr(0,e.generatedColumn)),l[c]=t.substr(e.generatedColumn),p=e.generatedColumn}d=e},this),c<l.length&&(d&&o(d,u()),s.add(l.splice(c).join(""))),t.sources.forEach(function(e){var r=t.sourceContentFor(e);null!=r&&(null!=n&&(e=i.join(n,e)),s.setSourceContent(e,r))}),s},r.prototype.add=function(e){if(Array.isArray(e))e.forEach(function(e){this.add(e)},this);else{if(!e[s]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);e&&this.children.push(e)}return this},r.prototype.prepend=function(e){if(Array.isArray(e))for(var t=e.length-1;t>=0;t--)this.prepend(e[t]);else{if(!e[s]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);this.children.unshift(e)}return this},r.prototype.walk=function(e){for(var t,n=0,r=this.children.length;n<r;n++)t=this.children[n],t[s]?t.walk(e):""!==t&&e(t,{source:this.source,line:this.line,column:this.column,name:this.name})},r.prototype.join=function(e){var t,n,r=this.children.length;if(r>0){for(t=[],n=0;n<r-1;n++)t.push(this.children[n]),t.push(e);t.push(this.children[n]),this.children=t}return this},r.prototype.replaceRight=function(e,t){var n=this.children[this.children.length-1];return n[s]?n.replaceRight(e,t):"string"==typeof n?this.children[this.children.length-1]=n.replace(e,t):this.children.push("".replace(e,t)),this},r.prototype.setSourceContent=function(e,t){this.sourceContents[i.toSetString(e)]=t},r.prototype.walkSourceContents=function(e){for(var t=0,n=this.children.length;t<n;t++)this.children[t][s]&&this.children[t].walkSourceContents(e);for(var r=Object.keys(this.sourceContents),t=0,n=r.length;t<n;t++)e(i.fromSetString(r[t]),this.sourceContents[r[t]])},r.prototype.toString=function(){var e="";return this.walk(function(t){e+=t}),e},r.prototype.toStringWithSourceMap=function(e){var t={code:"",line:1,column:0},n=new o(e),r=!1,i=null,a=null,s=null,l=null;return this.walk(function(e,o){t.code+=e,null!==o.source&&null!==o.line&&null!==o.column?(i===o.source&&a===o.line&&s===o.column&&l===o.name||n.addMapping({source:o.source,original:{line:o.line,column:o.column},generated:{line:t.line,column:t.column},name:o.name}),i=o.source,a=o.line,s=o.column,l=o.name,r=!0):r&&(n.addMapping({generated:{line:t.line,column:t.column}}),i=null,r=!1);for(var c=0,u=e.length;c<u;c++)10===e.charCodeAt(c)?(t.line++,t.column=0,c+1===u?(i=null,r=!1):r&&n.addMapping({source:o.source,original:{line:o.line,column:o.column},generated:{line:t.line,column:t.column},name:o.name})):t.column++}),this.walkSourceContents(function(e,t){n.setSourceContent(e,t)}),{code:t.code,map:n}},n.SourceNode=r},{"./source-map-generator":154,"./util":156}],156:[function(e,t,n){function r(e,t,n){if(t in e)return e[t];if(3===arguments.length)return n;throw new Error('"'+t+'" is a required argument.')}function o(e){var t=e.match(b);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}function i(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}function a(e){var t=e,r=o(e);if(r){if(!r.path)return e;t=r.path}for(var a,s=n.isAbsolute(t),l=t.split(/\/+/),c=0,u=l.length-1;u>=0;u--)a=l[u],"."===a?l.splice(u,1):".."===a?c++:c>0&&(""===a?(l.splice(u+1,c),c=0):(l.splice(u,2),c--));return t=l.join("/"),""===t&&(t=s?"/":"."),r?(r.path=t,i(r)):t}function s(e,t){""===e&&(e="."),""===t&&(t=".");var n=o(t),r=o(e);if(r&&(e=r.path||"/"),n&&!n.scheme)return r&&(n.scheme=r.scheme),i(n);if(n||t.match(y))return t;if(r&&!r.host&&!r.path)return r.host=t,i(r);var s="/"===t.charAt(0)?t:a(e.replace(/\/+$/,"")+"/"+t);return r?(r.path=s,i(r)):s}function l(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var n=0;0!==t.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0)return t;if(e=e.slice(0,r),e.match(/^([^\/]+:\/)?\/*$/))return t;++n}return Array(n+1).join("../")+t.substr(e.length+1)}function c(e){return e}function u(e){return p(e)?"$"+e:e}function h(e){return p(e)?e.slice(1):e}function p(e){if(!e)return!1;var t=e.length;if(t<9)return!1;if(95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var n=t-10;n>=0;n--)if(36!==e.charCodeAt(n))return!1;return!0}function d(e,t,n){var r=e.source-t.source;return 0!==r?r:0!==(r=e.originalLine-t.originalLine)?r:0!==(r=e.originalColumn-t.originalColumn)||n?r:0!==(r=e.generatedColumn-t.generatedColumn)?r:(r=e.generatedLine-t.generatedLine,0!==r?r:e.name-t.name)}function f(e,t,n){var r=e.generatedLine-t.generatedLine;return 0!==r?r:0!==(r=e.generatedColumn-t.generatedColumn)||n?r:0!==(r=e.source-t.source)?r:0!==(r=e.originalLine-t.originalLine)?r:(r=e.originalColumn-t.originalColumn,0!==r?r:e.name-t.name)}function m(e,t){return e===t?0:e>t?1:-1}function g(e,t){var n=e.generatedLine-t.generatedLine;return 0!==n?n:0!==(n=e.generatedColumn-t.generatedColumn)?n:0!==(n=m(e.source,t.source))?n:0!==(n=e.originalLine-t.originalLine)?n:(n=e.originalColumn-t.originalColumn,0!==n?n:m(e.name,t.name))}n.getArg=r;var b=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.]*)(?::(\d+))?(\S*)$/,y=/^data:.+\,.+$/;n.urlParse=o,n.urlGenerate=i,n.normalize=a,n.join=s,n.isAbsolute=function(e){return"/"===e.charAt(0)||!!e.match(b)},n.relative=l;var v=function(){return!("__proto__"in Object.create(null))}();n.toSetString=v?c:u,n.fromSetString=v?c:h,n.compareByOriginalPositions=d,n.compareByGeneratedPositionsDeflated=f,n.compareByGeneratedPositionsInflated=g},{}],157:[function(e,t,n){n.SourceMapGenerator=e("./lib/source-map-generator").SourceMapGenerator,n.SourceMapConsumer=e("./lib/source-map-consumer").SourceMapConsumer,n.SourceNode=e("./lib/source-node").SourceNode},{"./lib/source-map-consumer":153,"./lib/source-map-generator":154,"./lib/source-node":155}],158:[function(e,t,n){t.exports={version:"3.5.1"}},{}]},{},[10])(10)});