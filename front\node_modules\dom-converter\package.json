{"_from": "dom-converter@^0.2.0", "_id": "dom-converter@0.2.0", "_inBundle": false, "_integrity": "sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==", "_location": "/dom-converter", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dom-converter@^0.2.0", "name": "dom-converter", "escapedName": "dom-converter", "rawSpec": "^0.2.0", "saveSpec": null, "fetchSpec": "^0.2.0"}, "_requiredBy": ["/renderkid"], "_resolved": "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz", "_shasum": "6721a9daee2e293682955b6afe416771627bb768", "_spec": "dom-converter@^0.2.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\renderkid", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/AriaMinaei/dom-converter/issues"}, "bundleDependencies": false, "dependencies": {"utila": "~0.4"}, "deprecated": false, "description": "converts bare objects to DOM objects or xml representations", "devDependencies": {"chai": "^1.10.0", "chai-changes": "^1.3.4", "chai-fuzzy": "^1.4.0", "coffee-script": "^1.8.0", "jitter": "^1.3.0", "mocha": "^2.0.1", "mocha-pretty-spec-reporter": "0.1.0-beta.1", "sinon": "^1.12.2", "sinon-chai": "^2.6.0"}, "homepage": "https://github.com/AriaMinaei/dom-converter#readme", "license": "MIT", "main": "lib/domConverter.js", "name": "dom-converter", "repository": {"type": "git", "url": "git+https://github.com/AriaMinaei/dom-converter.git"}, "scripts": {"compile": "coffee --bare --compile --output ./lib ./src", "compile:watch": "jitter src lib -b", "prepublish": "npm run compile", "test": "mocha \"test/**/*.coffee\"", "test:watch": "mocha \"test/**/*.coffee\" --watch", "watch": "npm run compile:watch & npm run test:watch", "winwatch": "start/b npm run compile:watch & npm run test:watch"}, "version": "0.2.0"}