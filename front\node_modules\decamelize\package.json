{"_from": "decamelize@^1.2.0", "_id": "decamelize@1.2.0", "_inBundle": false, "_integrity": "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==", "_location": "/decamelize", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "decamelize@^1.2.0", "name": "decamelize", "escapedName": "decamelize", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/webpack-dev-server/yargs-parser"], "_resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "_shasum": "f6534d15148269b20352e7bee26f501f9a191290", "_spec": "decamelize@^1.2.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\webpack-dev-server\\node_modules\\yargs-parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/decamelize/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Convert a camelized string into a lowercased one with a custom separator: unicorn<PERSON>ain<PERSON> → unicorn_rainbow", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/decamelize#readme", "keywords": ["decamelize", "decamelcase", "camelcase", "lowercase", "case", "dash", "hyphen", "string", "str", "text", "convert"], "license": "MIT", "name": "decamelize", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decamelize.git"}, "scripts": {"test": "xo && ava"}, "version": "1.2.0"}