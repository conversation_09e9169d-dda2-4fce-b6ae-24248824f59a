{"_from": "cyclist@^1.0.1", "_id": "cyclist@1.0.2", "_inBundle": false, "_integrity": "sha512-0sVXIohTfLqVIW3kb/0n6IiWF3Ifj5nm2XaSrLq2DI6fKIGa2fYAZdk917rUneaeLVpYfFcyXE2ft0fe3remsA==", "_location": "/cyclist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cyclist@^1.0.1", "name": "cyclist", "escapedName": "cyclist", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/parallel-transform"], "_resolved": "https://registry.npmjs.org/cyclist/-/cyclist-1.0.2.tgz", "_shasum": "673b5f233bf34d8e602b949429f8171d9121bea3", "_spec": "cyclist@^1.0.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\parallel-transform", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mafintosh/cyclist/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Cyclist is an efficient cyclic list implemention.", "devDependencies": {"brittle": "^3.3.0", "standard": "^17.0.0"}, "homepage": "https://github.com/mafintosh/cyclist", "keywords": ["circular", "buffer", "ring", "cyclic", "data"], "license": "MIT", "main": "index.js", "name": "cyclist", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/cyclist.git"}, "scripts": {"test": "standard && brittle test.js"}, "version": "1.0.2"}