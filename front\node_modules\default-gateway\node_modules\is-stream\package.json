{"_from": "is-stream@^2.0.0", "_id": "is-stream@2.0.1", "_inBundle": false, "_integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==", "_location": "/default-gateway/is-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-stream@^2.0.0", "name": "is-stream", "escapedName": "is-stream", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/default-gateway/execa"], "_resolved": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "_shasum": "fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077", "_spec": "is-stream@^2.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\default-gateway\\node_modules\\execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if something is a Node.js stream", "devDependencies": {"@types/node": "^11.13.6", "ava": "^1.4.1", "tempy": "^0.3.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/is-stream#readme", "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "license": "MIT", "name": "is-stream", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.0.1"}