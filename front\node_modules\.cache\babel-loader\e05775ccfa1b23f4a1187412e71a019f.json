{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\front\\src\\views\\update-password.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\src\\views\\update-password.vue", "mtime": 1649064848753}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "dialogVisible", "ruleForm", "user", "rules", "password", "required", "message", "trigger", "newpassword", "repassword", "mounted", "$http", "url", "$storage", "get", "method", "then", "code", "$message", "error", "msg", "methods", "onLogout", "remove", "$router", "replace", "name", "onUpdateHandler", "$refs", "validate", "valid", "mima", "type", "duration", "onClose"], "sources": ["src/views/update-password.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      class=\"detail-form-content\"\r\n      ref=\"ruleForm\"\r\n      :rules=\"rules\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >\r\n      <el-form-item label=\"原密码\" prop=\"password\">\r\n        <el-input v-model=\"ruleForm.password\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"新密码\" prop=\"newpassword\">\r\n        <el-input type=\"password\" v-model=\"ruleForm.newpassword\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"确认密码\" prop=\"repassword\">\r\n        <el-input type=\"password\" v-model=\"ruleForm.repassword\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"onUpdateHandler\">确 定</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      ruleForm: {},\r\n      user: {},\r\n      rules: {\r\n        password: [\r\n          {\r\n            required: true,\r\n            message: \"密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        newpassword: [\r\n          {\r\n            required: true,\r\n            message: \"新密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        repassword: [\r\n          {\r\n            required: true,\r\n            message: \"确认密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.user = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n  },\r\n  methods: {\r\n    onLogout() {\r\n      this.$storage.remove(\"Token\");\r\n      this.$router.replace({ name: \"login\" });\r\n    },\r\n    // 修改密码\r\n    onUpdateHandler() {\r\n      this.$refs[\"ruleForm\"].validate(valid => {\r\n        if (valid) {\r\n          var password = \"\";\r\n          if (this.user.mima) {\r\n            password = this.user.mima;\r\n          } else if (this.user.password) {\r\n            password = this.user.password;\r\n          }\r\n          if (this.ruleForm.password != password) {\r\n            this.$message.error(\"原密码错误\");\r\n            return;\r\n          }\r\n          if (this.ruleForm.newpassword != this.ruleForm.repassword) {\r\n            this.$message.error(\"两次密码输入不一致\");\r\n            return;\r\n          }\r\n          this.user.password = this.ruleForm.newpassword;\r\n          this.user.mima = this.ruleForm.newpassword;\r\n          this.$http({\r\n            url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n            method: \"post\",\r\n            data: this.user\r\n          }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n              this.$message({\r\n                message: \"修改密码成功,下次登录系统生效\",\r\n                type: \"success\",\r\n                duration: 1500,\r\n                onClose: () => {\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error(data.msg);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"], "mappings": "AAyBA;EACAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;QACAC,QAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,WAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAE,UAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAG,QAAA;IACA,KAAAC,KAAA;MACAC,GAAA,UAAAC,QAAA,CAAAC,GAAA;MACAC,MAAA;IACA,GAAAC,IAAA;MAAAjB;IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAkB,IAAA;QACA,KAAAf,IAAA,GAAAH,IAAA,CAAAA,IAAA;MACA;QACA,KAAAmB,QAAA,CAAAC,KAAA,CAAApB,IAAA,CAAAqB,GAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,SAAA;MACA,KAAAT,QAAA,CAAAU,MAAA;MACA,KAAAC,OAAA,CAAAC,OAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAC,gBAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,CAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA1B,QAAA;UACA,SAAAF,IAAA,CAAA6B,IAAA;YACA3B,QAAA,QAAAF,IAAA,CAAA6B,IAAA;UACA,gBAAA7B,IAAA,CAAAE,QAAA;YACAA,QAAA,QAAAF,IAAA,CAAAE,QAAA;UACA;UACA,SAAAH,QAAA,CAAAG,QAAA,IAAAA,QAAA;YACA,KAAAc,QAAA,CAAAC,KAAA;YACA;UACA;UACA,SAAAlB,QAAA,CAAAO,WAAA,SAAAP,QAAA,CAAAQ,UAAA;YACA,KAAAS,QAAA,CAAAC,KAAA;YACA;UACA;UACA,KAAAjB,IAAA,CAAAE,QAAA,QAAAH,QAAA,CAAAO,WAAA;UACA,KAAAN,IAAA,CAAA6B,IAAA,QAAA9B,QAAA,CAAAO,WAAA;UACA,KAAAG,KAAA;YACAC,GAAA,UAAAC,QAAA,CAAAC,GAAA;YACAC,MAAA;YACAhB,IAAA,OAAAG;UACA,GAAAc,IAAA;YAAAjB;UAAA;YACA,IAAAA,IAAA,IAAAA,IAAA,CAAAkB,IAAA;cACA,KAAAC,QAAA;gBACAZ,OAAA;gBACA0B,IAAA;gBACAC,QAAA;gBACAC,OAAA,EAAAA,CAAA,MACA;cACA;YACA;cACA,KAAAhB,QAAA,CAAAC,KAAA,CAAApB,IAAA,CAAAqB,GAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}