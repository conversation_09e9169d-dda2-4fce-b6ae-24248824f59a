/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.0 (2020-02-13)
 */
!function(d){"use strict";function t(){}function m(n){return function(){return n}}function n(){return f}var e,u=function(n){function e(){return r}var r=n;return{get:e,set:function(n){r=n},clone:function(){return u(e())}}},r=tinymce.util.Tools.resolve("tinymce.PluginManager"),o=function(n){return{isFullscreen:function(){return null!==n.get()}}},c=m(!1),i=m(!0),f=(e={fold:function(n,e){return n()},is:c,isSome:c,isNone:i,getOr:l,getOrThunk:a,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:m(null),getOrUndefined:m(undefined),or:l,orThunk:a,map:n,each:t,bind:n,exists:c,forall:i,filter:n,equals:s,equals_:s,toArray:function(){return[]},toString:m("none()")},Object.freeze&&Object.freeze(e),e);function s(n){return n.isNone()}function a(n){return n()}function l(n){return n}function h(){return function(n){function e(){r.get().each(n)}var r=u(Y.none());return{clear:function(){e(),r.set(Y.none())},isSet:function(){return r.get().isSome()},set:function(n){e(),r.set(Y.some(n))}}}(function(n){n.unbind()})}function v(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e}(n)===e}}function g(n,e){for(var r=n.length,t=new Array(r),o=0;o<r;o++){var i=n[o];t[o]=e(i,o)}return t}function p(n,e){for(var r=0,t=n.length;r<t;r++){e(n[r],r)}}function w(n,e){for(var r=[],t=0,o=n.length;t<o;t++){var i=n[t];e(i,t)&&r.push(i)}return r}function y(n,e){return function(n){for(var e=[],r=0,t=n.length;r<t;++r){if(!$(n[r]))throw new Error("Arr.flatten item "+r+" was not an array, input: "+n);nn.apply(e,n[r])}return e}(g(n,e))}function S(n,e){return-1!==n.indexOf(e)}function O(n){return n.style!==undefined&&J(n.style.getPropertyValue)}function E(n,e,r){!function(n,e,r){if(!(G(r)||K(r)||Q(r)))throw d.console.error("Invalid call to Attr.set. Key ",e,":: Value ",r,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,r+"")}(n.dom(),e,r)}function N(n,e){var r=n.dom().getAttribute(e);return null===r?undefined:r}function T(n,e){n.dom().removeAttribute(e)}function x(n,e){var r=n.dom();!function(n,e){for(var r=en(n),t=0,o=r.length;t<o;t++){var i=r[t];e(n[i],i)}}(e,function(n,e){!function(n,e,r){if(!G(r))throw d.console.error("Invalid call to CSS.set. Property ",e,":: Value ",r,":: Element ",n),new Error("CSS value must be a string: "+r);O(n)&&n.style.setProperty(e,r)}(r,e,n)})}function b(n,e){var r=n.dom(),t=d.window.getComputedStyle(r).getPropertyValue(e),o=""!==t||function(n){var e=fn(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)}(n)?t:sn(r,e);return null===o?undefined:o}function C(n){function e(){n.stopPropagation()}function r(){n.preventDefault()}var t=tn.fromDom(n.target),o=function(r,t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return r(t.apply(null,n))}}(r,e);return function(n,e,r,t,o,i,u){return{target:m(n),x:m(e),y:m(r),stop:t,prevent:o,kill:i,raw:m(u)}}(t,n.clientX,n.clientY,e,r,o,n)}function D(n,e){var r=function(n,e){for(var r=0;r<n.length;r++){var t=n[r];if(t.test(e))return t}return undefined}(n,e);if(!r)return{major:0,minor:0};function t(n){return Number(e.replace(r,"$"+n))}return ln(t(1),t(2))}function A(n,e){return function(){return e===n}}function M(n,e){return function(){return e===n}}function _(n,e){var r=String(e).toLowerCase();return function(n,e){for(var r=0,t=n.length;r<t;r++){var o=n[r];if(e(o,r))return Y.some(o)}return Y.none()}(n,function(n){return n.search(r)})}function k(e){return function(n){return S(n,e)}}function F(){return An.get()}function R(n,e,r){return 0!=(n.compareDocumentPosition(e)&r)}function P(n,e){var r=n.dom();if(r.nodeType!==_n)return!1;var t=r;if(t.matches!==undefined)return t.matches(e);if(t.msMatchesSelector!==undefined)return t.msMatchesSelector(e);if(t.webkitMatchesSelector!==undefined)return t.webkitMatchesSelector(e);if(t.mozMatchesSelector!==undefined)return t.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}function I(n,e){var r=e===undefined?d.document:e.dom();return function(n){return n.nodeType!==_n&&n.nodeType!==kn||0===n.childElementCount}(r)?[]:g(r.querySelectorAll(n),tn.fromDom)}function L(e){return function(n){return Y.from(n.dom().parentNode).map(tn.fromDom)}(e).map(Fn).map(function(n){return w(n,function(n){return!function(n,e){return n.dom()===e.dom()}(e,n)})}).getOr([])}function H(n){var e=n===undefined?d.window:n;return Y.from(e.visualViewport)}function U(n,e,r,t){return{x:m(n),y:m(e),width:m(r),height:m(t),right:m(n+r),bottom:m(e+t)}}function W(n){var t=n===undefined?d.window:n,e=t.document,o=function(n){var e=n!==undefined?n.dom():d.document,r=e.body.scrollLeft||e.documentElement.scrollLeft,t=e.body.scrollTop||e.documentElement.scrollTop;return Pn(r,t)}(tn.fromDom(e));return H(t).fold(function(){var n=t.document.documentElement,e=n.clientWidth,r=n.clientHeight;return U(o.left(),o.top(),e,r)},function(n){return U(Math.max(n.pageLeft,o.left()),Math.max(n.pageTop,o.top()),n.width,n.height)})}function B(r,n,e){return H(e).map(function(n){function e(n){return C(n)}return n.addEventListener(r,e),{unbind:function(){return n.removeEventListener(r,e)}}}).getOrThunk(function(){return{unbind:t}})}function j(n,e,r){return w(function(n,e){for(var r=J(e)?e:c,t=n.dom(),o=[];null!==t.parentNode&&t.parentNode!==undefined;){var i=t.parentNode,u=tn.fromDom(i);if(o.push(u),!0===r(u))break;t=i}return o}(n,r),e)}function V(n,e){return function(n,e){return w(L(n),e)}(n,function(n){return P(n,e)})}function q(r,t){return function(e){e.setActive(null!==t.get());function n(n){return e.setActive(n.state)}return r.on("FullscreenStateChanged",n),function(){return r.off("FullscreenStateChanged",n)}}}var X,z=function(r){function n(){return o}function e(n){return n(r)}var t=m(r),o={fold:function(n,e){return e(r)},is:function(n){return r===n},isSome:i,isNone:c,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(n){return z(n(r))},each:function(n){n(r)},bind:e,exists:e,forall:e,filter:function(n){return n(r)?o:f},toArray:function(){return[r]},toString:function(){return"some("+r+")"},equals:function(n){return n.is(r)},equals_:function(n,e){return n.fold(c,function(n){return e(r,n)})}};return o},Y={some:z,none:n,from:function(n){return null===n||n===undefined?f:z(n)}},G=v("string"),$=v("array"),K=v("boolean"),J=v("function"),Q=v("number"),Z=Array.prototype.slice,nn=Array.prototype.push,en=(J(Array.from)&&Array.from,Object.keys),rn=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:m(n)}},tn={fromHtml:function(n,e){var r=(e||d.document).createElement("div");if(r.innerHTML=n,!r.hasChildNodes()||1<r.childNodes.length)throw d.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return rn(r.childNodes[0])},fromTag:function(n,e){var r=(e||d.document).createElement(n);return rn(r)},fromText:function(n,e){var r=(e||d.document).createTextNode(n);return rn(r)},fromDom:rn,fromPoint:function(n,e,r){var t=n.dom();return Y.from(t.elementFromPoint(e,r)).map(rn)}},on=(d.Node.ATTRIBUTE_NODE,d.Node.CDATA_SECTION_NODE,d.Node.COMMENT_NODE,d.Node.DOCUMENT_NODE),un=(d.Node.DOCUMENT_TYPE_NODE,d.Node.DOCUMENT_FRAGMENT_NODE,d.Node.ELEMENT_NODE),cn=d.Node.TEXT_NODE,fn=(d.Node.PROCESSING_INSTRUCTION_NODE,d.Node.ENTITY_REFERENCE_NODE,d.Node.ENTITY_NODE,d.Node.NOTATION_NODE,"undefined"!=typeof d.window?d.window:Function("return this;")(),X=cn,function(n){return function(n){return n.dom().nodeType}(n)===X}),sn=function(n,e){return O(n)?n.style.getPropertyValue(e):""},an=function(){return ln(0,0)},ln=function(n,e){return{major:n,minor:e}},dn={nu:ln,detect:function(n,e){var r=String(e).toLowerCase();return 0===n.length?an():D(n,r)},unknown:an},mn="Firefox",hn=function(n){var e=n.current;return{current:e,version:n.version,isEdge:A("Edge",e),isChrome:A("Chrome",e),isIE:A("IE",e),isOpera:A("Opera",e),isFirefox:A(mn,e),isSafari:A("Safari",e)}},vn={unknown:function(){return hn({current:undefined,version:dn.unknown()})},nu:hn,edge:m("Edge"),chrome:m("Chrome"),ie:m("IE"),opera:m("Opera"),firefox:m(mn),safari:m("Safari")},gn="Windows",pn="Android",wn="Solaris",yn="FreeBSD",Sn="ChromeOS",On=function(n){var e=n.current;return{current:e,version:n.version,isWindows:M(gn,e),isiOS:M("iOS",e),isAndroid:M(pn,e),isOSX:M("OSX",e),isLinux:M("Linux",e),isSolaris:M(wn,e),isFreeBSD:M(yn,e),isChromeOS:M(Sn,e)}},En={unknown:function(){return On({current:undefined,version:dn.unknown()})},nu:On,windows:m(gn),ios:m("iOS"),android:m(pn),linux:m("Linux"),osx:m("OSX"),solaris:m(wn),freebsd:m(yn),chromeos:m(Sn)},Nn=function(n,r){return _(n,r).map(function(n){var e=dn.detect(n.versionRegexes,r);return{current:n.name,version:e}})},Tn=function(n,r){return _(n,r).map(function(n){var e=dn.detect(n.versionRegexes,r);return{current:n.name,version:e}})},xn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,bn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return S(n,"edge/")&&S(n,"chrome")&&S(n,"safari")&&S(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,xn],search:function(n){return S(n,"chrome")&&!S(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return S(n,"msie")||S(n,"trident")}},{name:"Opera",versionRegexes:[xn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:k("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:k("firefox")},{name:"Safari",versionRegexes:[xn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(S(n,"safari")||S(n,"mobile/"))&&S(n,"applewebkit")}}],Cn=[{name:"Windows",search:k("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return S(n,"iphone")||S(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:k("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:k("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:k("linux"),versionRegexes:[]},{name:"Solaris",search:k("sunos"),versionRegexes:[]},{name:"FreeBSD",search:k("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:k("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Dn={browsers:m(bn),oses:m(Cn)},An=u(function(n,e){var r=Dn.browsers(),t=Dn.oses(),o=Nn(r,n).fold(vn.unknown,vn.nu),i=Tn(t,n).fold(En.unknown,En.nu);return{browser:o,os:i,deviceType:function(n,e,r,t){var o=n.isiOS()&&!0===/ipad/i.test(r),i=n.isiOS()&&!o,u=n.isiOS()||n.isAndroid(),c=u||t("(pointer:coarse)"),f=o||!i&&u&&t("(min-device-width:768px)"),s=i||u&&!f,a=e.isSafari()&&n.isiOS()&&!1===/safari/i.test(r),l=!s&&!f&&!a;return{isiPad:m(o),isiPhone:m(i),isTablet:m(f),isPhone:m(s),isTouch:m(c),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:m(a),isDesktop:m(l)}}(i,o,n,e)}}(d.navigator.userAgent,function(n){return d.window.matchMedia(n).matches})),Mn=function(n,e){return R(n,e,d.Node.DOCUMENT_POSITION_CONTAINED_BY)},_n=un,kn=on,Fn=(F().browser.isIE(),function(n){return g(n.dom().childNodes,tn.fromDom)}),Rn=(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e]}("element","offset"),function(r,t){return{left:m(r),top:m(t),translate:function(n,e){return Rn(r+n,t+e)}}}),Pn=Rn,In=(F().browser.isSafari(),tinymce.util.Tools.resolve("tinymce.dom.DOMUtils")),Ln=tinymce.util.Tools.resolve("tinymce.Env"),Hn=tinymce.util.Tools.resolve("tinymce.util.Delay"),Un=function(n,e){n.fire("FullscreenStateChanged",{state:e})},Wn="data-ephox-mobile-fullscreen-style",Bn="position:absolute!important;",jn="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",Vn=Ln.os.isAndroid(),qn=function(o,n,e){function r(t){return function(n){var e=N(n,"style"),r=e===undefined?"no-styles":e.trim();r!==t&&(E(n,Wn,r),x(n,o.parseStyle(t)))}}var t=function(n,e,r){return j(n,function(n){return P(n,e)},r)}(n,"*"),i=y(t,function(n){return V(n,"*:not(.tox-silver-sink)")}),u=function(n){var e=b(n,"background-color");return e!==undefined&&""!==e?"background-color:"+e+"!important":"background-color:rgb(255,255,255)!important;"}(e);p(i,r("display:none!important;")),p(t,r(Bn+jn+u)),r((!0===Vn?"":Bn)+jn+u)(n)},Xn=function(r){var n=function(n){return I(n)}("["+Wn+"]");p(n,function(n){var e=N(n,Wn);"no-styles"!==e?x(n,r.parseStyle(e)):T(n,"style"),T(n,Wn)})},zn=In.DOM,Yn=H().fold(function(){return{bind:t,unbind:t}},function(e){var r=function(){var e=u(Y.none());return{clear:function(){e.set(Y.none())},set:function(n){e.set(Y.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}}(),t=h(),o=h(),i=Hn.throttle(function(){d.document.body.scrollTop=0,d.document.documentElement.scrollTop=0,d.window.requestAnimationFrame(function(){r.on(function(n){return x(n,{top:e.offsetTop+"px",left:e.offsetLeft+"px",height:e.height+"px",width:e.width+"px"})})})},50);return{bind:function(n){r.set(n),i(),t.set(B("resize")),o.set(B("scroll"))},unbind:function(){r.on(function(){t.clear(),o.clear()}),r.clear()}}}),Gn=function(n,e){var r,t,o,i=d.document.body,u=d.document.documentElement;t=n.getContainer();var c=tn.fromDom(t),f=e.get(),s=tn.fromDom(n.getBody()),a=Ln.deviceType.isTouch();if(r=t.style,o=n.getContentAreaContainer().firstChild.style,f)o.width=f.iframeWidth,o.height=f.iframeHeight,r.width=f.containerWidth,r.height=f.containerHeight,r.top=f.containerTop,r.left=f.containerLeft,a&&Xn(n.dom),zn.removeClass(i,"tox-fullscreen"),zn.removeClass(u,"tox-fullscreen"),zn.removeClass(t,"tox-fullscreen"),function(n){d.window.scrollTo(n.x,n.y)}(f.scrollPos),e.set(null),Un(n,!1),Yn.unbind(),n.off("remove",Yn.unbind);else{var l={scrollPos:function(){var n=W(d.window);return{x:n.x(),y:n.y()}}(),containerWidth:r.width,containerHeight:r.height,containerTop:r.top,containerLeft:r.left,iframeWidth:o.width,iframeHeight:o.height};a&&qn(n.dom,c,s),o.width=o.height="100%",r.width=r.height="",zn.addClass(i,"tox-fullscreen"),zn.addClass(u,"tox-fullscreen"),zn.addClass(t,"tox-fullscreen"),Yn.bind(c),n.on("remove",Yn.unbind),e.set(l),Un(n,!0)}},$n=function(n,e){n.addCommand("mceFullScreen",function(){Gn(n,e)})},Kn=function(n,e){n.ui.registry.addToggleMenuItem("fullscreen",{text:"Fullscreen",shortcut:"Meta+Shift+F",onAction:function(){return n.execCommand("mceFullScreen")},onSetup:q(n,e)}),n.ui.registry.addToggleButton("fullscreen",{tooltip:"Fullscreen",icon:"fullscreen",onAction:function(){return n.execCommand("mceFullScreen")},onSetup:q(n,e)})};!function Jn(){r.add("fullscreen",function(n){var e=u(null);return n.settings.inline||($n(n,e),Kn(n,e),n.addShortcut("Meta+Shift+F","","mceFullScreen")),o(e)})}()}(window);