{"_from": "dashdash@^1.12.0", "_id": "dashdash@1.14.1", "_inBundle": false, "_integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "_location": "/dashdash", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dashdash@^1.12.0", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "^1.12.0", "saveSpec": null, "fetchSpec": "^1.12.0"}, "_requiredBy": ["/sshpk"], "_resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "_shasum": "853cfa0f7cbe2fed5de20326b8dd581035f6e2f0", "_spec": "dashdash@^1.12.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\sshpk", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://trentm.com"}, "bugs": {"url": "https://github.com/trentm/node-dashdash/issues"}, "bundleDependencies": false, "dependencies": {"assert-plus": "^1.0.0"}, "deprecated": false, "description": "A light, featureful and explicit option parsing library.", "devDependencies": {"nodeunit": "0.9.x"}, "engines": {"node": ">=0.10"}, "homepage": "https://github.com/trentm/node-dashdash#readme", "keywords": ["option", "parser", "parsing", "cli", "command", "args", "bash", "completion"], "license": "MIT", "main": "./lib/dashdash.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git://github.com/trentm/node-dashdash.git"}, "scripts": {"test": "nodeunit test/*.test.js"}, "version": "1.14.1"}