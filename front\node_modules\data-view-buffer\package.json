{"_from": "data-view-buffer@^1.0.1", "_id": "data-view-buffer@1.0.1", "_inBundle": false, "_integrity": "sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==", "_location": "/data-view-buffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "data-view-buffer@^1.0.1", "name": "data-view-buffer", "escapedName": "data-view-buffer", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.1.tgz", "_shasum": "8ea6326efec17a2e42620696e671d7d5a8bc66b2", "_spec": "data-view-buffer@^1.0.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/data-view-buffer/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.6", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "deprecated": false, "description": "Get the ArrayBuffer out of a DataView, robustly.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/call-bind": "^1.0.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/data-view-buffer#readme", "keywords": ["javascript", "ecmascript", "dataview", "buffer", "robust"], "license": "MIT", "main": "index.js", "name": "data-view-buffer", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/data-view-buffer.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "types": "./index.d.ts", "version": "1.0.1"}