<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ChongwuLiuyanDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.chongwu_id as chongwuId
        ,a.yonghu_id as yonghuId
        ,a.chongwu_liuyan_text as chongwuLiuyanText
        ,a.insert_time as insertTime
        ,a.reply_text as replyText
        ,a.update_time as updateTime
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.ChongwuLiuyanView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段
        ,chongwu.chongwu_name as chongwu<PERSON><PERSON>
        ,chongwu.chongwu_photo as chongwu<PERSON>hoto
        ,chongwu.chongwu_types as chongwuTypes
        ,chongwu.chongwu_content as chongwuContent
        ,yonghu.yonghu_name as yonghuName
        ,yonghu.yonghu_photo as yonghuPhoto
        ,yonghu.yonghu_phone as yonghuPhone
        ,yonghu.yonghu_email as yonghuEmail
        ,yonghu.yonghu_delete as yonghuDelete

        FROM chongwu_liuyan  a
        left JOIN chongwu chongwu ON a.chongwu_id = chongwu.id
        left JOIN yonghu yonghu ON a.yonghu_id = yonghu.id

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test="params.chongwuId != null and params.chongwuId != ''">
                and (
                    a.chongwu_id = #{params.chongwuId}
                )
            </if>
            <if test="params.yonghuId != null and params.yonghuId != ''">
                and (
                    a.yonghu_id = #{params.yonghuId}
                )
            </if>
            <if test=" params.chongwuLiuyanText != '' and params.chongwuLiuyanText != null and params.chongwuLiuyanText != 'null' ">
                and a.chongwu_liuyan_text like CONCAT('%',#{params.chongwuLiuyanText},'%')
            </if>
            <if test=" params.insertTimeStart != '' and params.insertTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) >= UNIX_TIMESTAMP(#{params.insertTimeStart}) ]]>
            </if>
            <if test=" params.insertTimeEnd != '' and params.insertTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.insert_time) <= UNIX_TIMESTAMP(#{params.insertTimeEnd}) ]]>
            </if>
            <if test=" params.replyText != '' and params.replyText != null and params.replyText != 'null' ">
                and a.reply_text like CONCAT('%',#{params.replyText},'%')
            </if>
            <if test=" params.updateTimeStart != '' and params.updateTimeStart != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.update_time) >= UNIX_TIMESTAMP(#{params.updateTimeStart}) ]]>
            </if>
            <if test=" params.updateTimeEnd != '' and params.updateTimeEnd != null ">
                <![CDATA[  and UNIX_TIMESTAMP(a.update_time) <= UNIX_TIMESTAMP(#{params.updateTimeEnd}) ]]>
            </if>

                <!-- 判断宠物信息的id不为空 -->
            <if test=" params.chongwuIdNotNull != '' and params.chongwuIdNotNull != null and params.chongwuIdNotNull != 'null' ">
                and a.chongwu_id IS NOT NULL
            </if>
            <if test=" params.chongwuName != '' and params.chongwuName != null and params.chongwuName != 'null' ">
                and chongwu.chongwu_name like CONCAT('%',#{params.chongwuName},'%')
            </if>
            <if test="params.chongwuTypes != null  and params.chongwuTypes != ''">
                and chongwu.chongwu_types = #{params.chongwuTypes}
            </if>

            <if test=" params.chongwuContent != '' and params.chongwuContent != null and params.chongwuContent != 'null' ">
                and chongwu.chongwu_content like CONCAT('%',#{params.chongwuContent},'%')
            </if>
                <!-- 判断用户的id不为空 -->
            <if test=" params.yonghuIdNotNull != '' and params.yonghuIdNotNull != null and params.yonghuIdNotNull != 'null' ">
                and a.yonghu_id IS NOT NULL
            </if>
            <if test=" params.yonghuName != '' and params.yonghuName != null and params.yonghuName != 'null' ">
                and yonghu.yonghu_name like CONCAT('%',#{params.yonghuName},'%')
            </if>
            <if test=" params.yonghuPhone != '' and params.yonghuPhone != null and params.yonghuPhone != 'null' ">
                and yonghu.yonghu_phone like CONCAT('%',#{params.yonghuPhone},'%')
            </if>
            <if test=" params.yonghuEmail != '' and params.yonghuEmail != null and params.yonghuEmail != 'null' ">
                and yonghu.yonghu_email like CONCAT('%',#{params.yonghuEmail},'%')
            </if>
            <if test="params.yonghuDeleteStart != null  and params.yonghuDeleteStart != '' ">
                <![CDATA[  and yonghu.yonghu_delete >= #{params.yonghuDeleteStart}   ]]>
            </if>
            <if test="params.yonghuDeleteEnd != null  and params.yonghuDeleteEnd != '' ">
                <![CDATA[  and yonghu.yonghu_delete <= #{params.yonghuDeleteEnd}   ]]>
            </if>
            <if test="params.yonghuDelete != null  and params.yonghuDelete != '' ">
                and yonghu.yonghu_delete = #{params.yonghuDelete}
            </if>
        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>