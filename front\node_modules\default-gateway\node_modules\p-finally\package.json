{"_from": "p-finally@^2.0.0", "_id": "p-finally@2.0.1", "_inBundle": false, "_integrity": "sha512-vpm09aKwq6H9phqRQzecoDpD8TmVyGw70qmWlyq5onxY7tqyTTFVvxMykxQSQKILBSFlbXpypIw2T1Ml7+DDtw==", "_location": "/default-gateway/p-finally", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-finally@^2.0.0", "name": "p-finally", "escapedName": "p-finally", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/default-gateway/execa"], "_resolved": "https://registry.npmjs.org/p-finally/-/p-finally-2.0.1.tgz", "_shasum": "bd6fcaa9c559a096b680806f4d657b3f0f240561", "_spec": "p-finally@^2.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\default-gateway\\node_modules\\execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-finally/issues"}, "bundleDependencies": false, "deprecated": false, "description": "`Promise#finally()` ponyfill - Invoked when the promise is settled regardless of outcome", "devDependencies": {"ava": "^1.4.1", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/p-finally#readme", "keywords": ["promise", "finally", "handler", "function", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "license": "MIT", "name": "p-finally", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-finally.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.1"}