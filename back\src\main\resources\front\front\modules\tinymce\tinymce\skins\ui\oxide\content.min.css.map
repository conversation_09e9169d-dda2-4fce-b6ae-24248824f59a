{"version": 3, "sources": ["ui/default/content.css"], "names": [], "mappings": ";;;;;;AAMA,mCACE,WAAY,YAAY,sNAAsN,UAAU,OACxP,OAAQ,QACR,QAAS,aACT,OAAQ,eACR,QAAS,EAAE,IACX,oBAAqB,UACrB,iBAAkB,UAClB,oBAAqB,IACrB,iBAAkB,IAClB,gBAAiB,IACb,YAAa,IACjB,MAAO,cAET,sDACE,eAAgB,IAElB,mCACE,iBAAkB,QAEpB,2CACE,iBAAkB,QAEpB,8CACE,WAAY,KACZ,OAAQ,MAAO,EAEjB,sDACE,iBAAkB,gdAClB,gBAAiB,KACjB,QAAS,GACT,OAAQ,QACR,OAAQ,IACR,YAAa,OACb,WAAY,OACZ,SAAU,SACV,MAAO,IAET,6EACE,iBAAkB,shCAEpB,gEACE,YAAa,EACb,aAAc,OAShB,uBACA,sBACE,MAAO,KACP,WAAY,IACZ,YAAa,EAAE,IAAI,KACnB,YAAa,QAAQ,CAAE,MAAM,CAAE,aAAa,CAAE,aAAa,CAAE,UAC7D,UAAW,IACX,WAAY,KACZ,YAAa,IACb,aAAc,OACd,WAAY,OACZ,UAAW,OACX,YAAa,IACb,cAAe,EACf,SAAU,EACV,gBAAiB,KACjB,YAAa,KACb,QAAS,KAKX,wCADA,uCADA,uCADA,sCAIE,YAAa,KACb,WAAY,QAKd,mCADA,kCADA,kCADA,iCAIE,YAAa,KACb,WAAY,QAEd,aACE,uBACA,sBACE,YAAa,MAIjB,sBACE,QAAS,IACT,OAAQ,KAAM,EACd,SAAU,KAEZ,iCACA,sBACE,WAAY,QAGd,iCACE,QAAS,KACT,cAAe,KACf,YAAa,OAKf,aAHA,eAEA,eADA,cAGE,MAAO,QAET,mBACE,MAAO,KAET,WACE,QAAS,GAIX,eAEA,gBAEA,eAHA,cAHA,gBAKA,cAJA,WAME,MAAO,KAGT,iBAGA,eADA,YAEA,gBALA,gBAEA,cAIE,MAAO,KAKT,4BACA,qBAHA,cADA,gBAEA,WAGE,MAAO,QACP,WAAY,mBAEd,cACA,kBACA,eACE,MAAO,KAGT,kBADA,gBAEE,MAAO,QAGT,iBADA,aAEA,gBACE,MAAO,KAGT,YADA,iBAEE,YAAa,IAEf,cACE,WAAY,OAEd,cACE,OAAQ,KAGV,kBACE,cAAe,WACf,UAAW,WAEb,oCACE,iBAAkB,KAClB,iBAAkB,aAClB,SAAU,SAEZ,2CACE,QAAS,KAEX,mCACE,KAAM,QACN,OAAQ,EACR,QAAS,EACT,SAAU,SACV,MAAO,KACP,IAAK,EAEP,2CACE,KAAM,cACN,UAAW,UACX,SAAU,SAEZ,0CACE,OAAQ,QAEV,yCACE,OAAQ,KAEV,2BACE,OAAQ,qkCAAqkC,CAAE,QAEjlC,oCACE,MAAO,KAET,qCACE,MAAO,MAET,4CACE,QAAS,MACT,YAAa,KACb,aAAc,KAEhB,oBACE,OAAQ,IAAI,MAAM,KAClB,QAAS,aACT,YAAa,EACb,OAAQ,EAAE,IAAI,EAAE,IAChB,SAAU,SAEZ,8BACE,WAAY,oFACZ,OAAQ,KACR,KAAM,EACN,SAAU,SACV,IAAK,EACL,MAAO,KAET,qDACE,QAAS,KAEX,YACE,WAAY,YAAY,0bAA0b,UAAU,OAC5d,OAAQ,IAAI,OAAO,KAErB,eACE,OAAQ,IAAI,OAAO,KACnB,OAAQ,QACR,QAAS,MACT,OAAQ,IACR,WAAY,KACZ,kBAAmB,OACnB,MAAO,KAET,aACE,eACE,OAAQ,GAGZ,0BACE,WAAY,oFACZ,OAAQ,KACR,KAAM,EACN,SAAU,SACV,IAAK,EACL,MAAO,KAET,iDACE,QAAS,KAEX,gBACE,QAAS,aACT,SAAU,SAGZ,uBAEA,sBAHA,uBAEA,sBAEE,QAAS,MACT,SAAU,OACV,QAAS,EACT,SAAU,SACV,MAAO,KAET,uBACE,YAAa,WAEf,uBACE,YAAa,OAEf,sBACE,YAAa,IAEf,sBACE,YAAa,KAGf,8BAEA,6BAHA,8BAEA,6BAEE,OAAQ,EACR,OAAQ,KACR,KAAM,EACN,SAAU,SACV,IAAK,EACL,MAAO,KAET,wCACE,SAAU,SAEZ,uEACE,MAAO,kBACP,QAAS,2BACT,SAAU,SAEZ,sFACE,KAAM,IAER,gFACE,MAAO,IAET,uCACE,iBAAkB,QAClB,aAAc,QACd,aAAc,MACd,aAAc,IACd,WAAY,WACZ,OAAQ,KACR,SAAU,SACV,MAAO,KACP,QAAS,MAEX,6CACE,iBAAkB,QAEpB,sDACE,OAAQ,YAEV,sDACE,OAAQ,YAEV,sDACE,OAAQ,YAEV,sDACE,OAAQ,YAEV,uCACE,QAAS,GACT,QAAS,IAAI,OAAO,KACpB,SAAU,SACV,QAAS,MAEX,qCACE,WAAY,KACZ,WAAY,gBACZ,OAAQ,IACR,cAAe,IACf,MAAO,KACP,QAAS,KACT,YAAa,WACb,UAAW,KACX,YAAa,KACb,OAAQ,IAAI,KACZ,QAAS,IACT,SAAU,SACV,YAAa,OACb,QAAS,MAEX,kBACE,WAAY,KACZ,MAAO,KAET,2BACE,WAAY,KACZ,MAAO,KAET,yCACA,2CACE,QAAS,IAAI,MAAM,QAErB,wCACE,QAAS,IAAI,MAAM,QACnB,eAAgB,IAElB,uEACE,QAAS,IAAI,MAAM,QAErB,uEACE,QAAS,IAAI,MAAM,QAErB,6DACE,OAAQ,YACR,QAAS,IAAI,MAAM,QAErB,oEACA,oEACE,QAAS,EAEX,sDACE,iBAAkB,QAEpB,kCACE,QAAS,IAAI,MAAM,QAErB,wCACA,wCACE,iBAAkB,kBAEpB,wDACA,wDACE,WAAY,IAEd,mDACA,mDACE,WAAY,IAEd,0CACA,0CACE,sBAAuB,KACvB,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,sCACE,WAAY,IAEd,iCACE,WAAY,IAEd,2BACE,iBAAkB,QAClB,QAAS,EAEX,4BACE,OAAQ,WAEV,4BACE,OAAQ,WAEV,8DACE,QAAS,EAEX,uBACE,iBAAkB,oRAClB,oBAAqB,EAAE,iBACvB,kBAAmB,SACnB,gBAAiB,KAAK,IACtB,OAAQ,QACR,OAAQ,KAEV,0BACE,iBAAkB,2PAClB,oBAAqB,EAAE,iBACvB,kBAAmB,SACnB,gBAAiB,KAAK,IACtB,OAAQ,QAEV,SACE,OAAQ,IAAI,MAAM,KAEpB,YACE,OAAQ,IAEV,YACE,gBAAiB,KAEnB,gBAGA,wBAFA,mBACA,mBAEE,OAAQ,IAAI,OAAO,KAarB,0BAFA,0BAOA,wBANA,6BAHA,4CAYA,qBALA,6BADA,yBAZA,qBACA,qBACA,qBACA,qBACA,qBACA,qBASA,yBAGA,qBAlBA,oBAYA,sBAJA,0BASA,qBAGE,kBAAmB,UACnB,OAAQ,IAAI,OAAO,KACnB,YAAa,IACb,YAAa,KAEf,oBACE,iBAAkB,gHAEpB,qBACE,iBAAkB,oHAEpB,qBACE,iBAAkB,wHAEpB,qBACE,iBAAkB,oHAEpB,qBACE,iBAAkB,wHAEpB,qBACE,iBAAkB,wHAEpB,qBACE,iBAAkB,wHAEpB,4CACE,iBAAkB,4HAEpB,0BACE,iBAAkB,gKAEpB,0BACE,iBAAkB,gKAEpB,6BACE,iBAAkB,4LAEpB,0BACE,iBAAkB,wKAEpB,sBACE,iBAAkB,oIAEpB,yBACE,iBAAkB,wJAEpB,6BACE,OAAQ,IAAI,OAAO,KAErB,yBACE,iBAAkB,4JAEpB,wBACE,iBAAkB,gJAEpB,qBACE,iBAAkB,oHAEpB,qBACE,iBAAkB,oHAEpB,qBACE,iBAAkB,oHAapB,yCAFA,yCAOA,uCANA,4CAHA,2DAYA,oCALA,4CADA,wCAZA,oCACA,oCACA,oCACA,oCACA,oCACA,oCASA,wCAGA,oCAlBA,mCAYA,qCAJA,yCASA,oCAGE,YAAa,IAaf,mCAFA,mCAOA,iCANA,sCAHA,qDAYA,8BALA,sCADA,kCAZA,8BACA,8BACA,8BACA,8BACA,8BACA,8BASA,kCAGA,8BAlBA,6BAYA,+BAJA,mCASA,8BAGE,sBAAuB,MACvB,aAAc,IAEhB,UACA,SACE,WAAY,KAEd,gBACE,QAAS,IAEX,KACE,YAAa,WAEf,MACE,gBAAiB", "file": "content.min.css", "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n */\n.mce-content-body .mce-item-anchor {\n  background: transparent url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D'8'%20height%3D'12'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%3E%3Cpath%20d%3D'M0%200L8%200%208%2012%204.09117821%209%200%2012z'%2F%3E%3C%2Fsvg%3E%0A\") no-repeat center;\n  cursor: default;\n  display: inline-block;\n  height: 12px !important;\n  padding: 0 2px;\n  -webkit-user-modify: read-only;\n  -moz-user-modify: read-only;\n  -webkit-user-select: all;\n  -moz-user-select: all;\n  -ms-user-select: all;\n      user-select: all;\n  width: 8px !important;\n}\n.mce-content-body .mce-item-anchor[data-mce-selected] {\n  outline-offset: 1px;\n}\n.tox-comments-visible .tox-comment {\n  background-color: #fff0b7;\n}\n.tox-comments-visible .tox-comment--active {\n  background-color: #ffe168;\n}\n.tox-checklist > li:not(.tox-checklist--hidden) {\n  list-style: none;\n  margin: 0.25em 0;\n}\n.tox-checklist > li:not(.tox-checklist--hidden)::before {\n  background-image: url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cg%20id%3D%22checklist-unchecked%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Crect%20id%3D%22Rectangle%22%20width%3D%2215%22%20height%3D%2215%22%20x%3D%22.5%22%20y%3D%22.5%22%20fill-rule%3D%22nonzero%22%20stroke%3D%22%234C4C4C%22%20rx%3D%222%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%0A\");\n  background-size: 100%;\n  content: '';\n  cursor: pointer;\n  height: 1em;\n  margin-left: -1.5em;\n  margin-top: 0.125em;\n  position: absolute;\n  width: 1em;\n}\n.tox-checklist li:not(.tox-checklist--hidden).tox-checklist--checked::before {\n  background-image: url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cg%20id%3D%22checklist-checked%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Crect%20id%3D%22Rectangle%22%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%234099FF%22%20fill-rule%3D%22nonzero%22%20rx%3D%222%22%2F%3E%3Cpath%20id%3D%22Path%22%20fill%3D%22%23FFF%22%20fill-rule%3D%22nonzero%22%20d%3D%22M11.5703186%2C3.14417309%20C11.8516238%2C2.73724603%2012.4164781%2C2.62829933%2012.83558%2C2.89774797%20C13.260121%2C3.17069355%2013.3759736%2C3.72932262%2013.0909105%2C4.14168582%20L7.7580587%2C11.8560195%20C7.43776896%2C12.3193404%206.76483983%2C12.3852142%206.35607322%2C11.9948725%20L3.02491697%2C8.8138662%20C2.66090143%2C8.46625845%202.65798871%2C7.89594698%203.01850234%2C7.54483354%20C3.373942%2C7.19866177%203.94940006%2C7.19592841%204.30829608%2C7.5386474%20L6.85276923%2C9.9684299%20L11.5703186%2C3.14417309%20Z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%0A\");\n}\n[dir=rtl] .tox-checklist > li:not(.tox-checklist--hidden)::before {\n  margin-left: 0;\n  margin-right: -1.5em;\n}\n/* stylelint-disable */\n/* http://prismjs.com/ */\n/**\n * prism.js default theme for JavaScript, CSS and HTML\n * Based on dabblet (http://dabblet.com)\n * <AUTHOR> Verou\n */\ncode[class*=\"language-\"],\npre[class*=\"language-\"] {\n  color: black;\n  background: none;\n  text-shadow: 0 1px white;\n  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;\n  font-size: 1em;\n  text-align: left;\n  white-space: pre;\n  word-spacing: normal;\n  word-break: normal;\n  word-wrap: normal;\n  line-height: 1.5;\n  -moz-tab-size: 4;\n  tab-size: 4;\n  -webkit-hyphens: none;\n  -ms-hyphens: none;\n  hyphens: none;\n}\npre[class*=\"language-\"]::-moz-selection,\npre[class*=\"language-\"] ::-moz-selection,\ncode[class*=\"language-\"]::-moz-selection,\ncode[class*=\"language-\"] ::-moz-selection {\n  text-shadow: none;\n  background: #b3d4fc;\n}\npre[class*=\"language-\"]::selection,\npre[class*=\"language-\"] ::selection,\ncode[class*=\"language-\"]::selection,\ncode[class*=\"language-\"] ::selection {\n  text-shadow: none;\n  background: #b3d4fc;\n}\n@media print {\n  code[class*=\"language-\"],\n  pre[class*=\"language-\"] {\n    text-shadow: none;\n  }\n}\n/* Code blocks */\npre[class*=\"language-\"] {\n  padding: 1em;\n  margin: 0.5em 0;\n  overflow: auto;\n}\n:not(pre) > code[class*=\"language-\"],\npre[class*=\"language-\"] {\n  background: #f5f2f0;\n}\n/* Inline code */\n:not(pre) > code[class*=\"language-\"] {\n  padding: 0.1em;\n  border-radius: 0.3em;\n  white-space: normal;\n}\n.token.comment,\n.token.prolog,\n.token.doctype,\n.token.cdata {\n  color: slategray;\n}\n.token.punctuation {\n  color: #999;\n}\n.namespace {\n  opacity: 0.7;\n}\n.token.property,\n.token.tag,\n.token.boolean,\n.token.number,\n.token.constant,\n.token.symbol,\n.token.deleted {\n  color: #905;\n}\n.token.selector,\n.token.attr-name,\n.token.string,\n.token.char,\n.token.builtin,\n.token.inserted {\n  color: #690;\n}\n.token.operator,\n.token.entity,\n.token.url,\n.language-css .token.string,\n.style .token.string {\n  color: #9a6e3a;\n  background: hsla(0, 0%, 100%, 0.5);\n}\n.token.atrule,\n.token.attr-value,\n.token.keyword {\n  color: #07a;\n}\n.token.function,\n.token.class-name {\n  color: #DD4A68;\n}\n.token.regex,\n.token.important,\n.token.variable {\n  color: #e90;\n}\n.token.important,\n.token.bold {\n  font-weight: bold;\n}\n.token.italic {\n  font-style: italic;\n}\n.token.entity {\n  cursor: help;\n}\n/* stylelint-enable */\n.mce-content-body {\n  overflow-wrap: break-word;\n  word-wrap: break-word;\n}\n.mce-content-body .mce-visual-caret {\n  background-color: black;\n  background-color: currentcolor;\n  position: absolute;\n}\n.mce-content-body .mce-visual-caret-hidden {\n  display: none;\n}\n.mce-content-body *[data-mce-caret] {\n  left: -1000px;\n  margin: 0;\n  padding: 0;\n  position: absolute;\n  right: auto;\n  top: 0;\n}\n.mce-content-body .mce-offscreen-selection {\n  left: -9999999999px;\n  max-width: 1000000px;\n  position: absolute;\n}\n.mce-content-body *[contentEditable=false] {\n  cursor: default;\n}\n.mce-content-body *[contentEditable=true] {\n  cursor: text;\n}\n.tox-cursor-format-painter {\n  cursor: url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%3E%0A%20%20%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%0A%20%20%20%20%3Cpath%20fill%3D%22%23000%22%20fill-rule%3D%22nonzero%22%20d%3D%22M15%2C6%20C15%2C5.45%2014.55%2C5%2014%2C5%20L6%2C5%20C5.45%2C5%205%2C5.45%205%2C6%20L5%2C10%20C5%2C10.55%205.45%2C11%206%2C11%20L14%2C11%20C14.55%2C11%2015%2C10.55%2015%2C10%20L15%2C9%20L16%2C9%20L16%2C12%20L9%2C12%20L9%2C19%20C9%2C19.55%209.45%2C20%2010%2C20%20L11%2C20%20C11.55%2C20%2012%2C19.55%2012%2C19%20L12%2C14%20L18%2C14%20L18%2C7%20L15%2C7%20L15%2C6%20Z%22%2F%3E%0A%20%20%20%20%3Cpath%20fill%3D%22%23000%22%20fill-rule%3D%22nonzero%22%20d%3D%22M1%2C1%20L8.25%2C1%20C8.66421356%2C1%209%2C1.33578644%209%2C1.75%20L9%2C1.75%20C9%2C2.16421356%208.66421356%2C2.5%208.25%2C2.5%20L2.5%2C2.5%20L2.5%2C8.25%20C2.5%2C8.66421356%202.16421356%2C9%201.75%2C9%20L1.75%2C9%20C1.33578644%2C9%201%2C8.66421356%201%2C8.25%20L1%2C1%20Z%22%2F%3E%0A%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E%0A\"), default;\n}\n.mce-content-body figure.align-left {\n  float: left;\n}\n.mce-content-body figure.align-right {\n  float: right;\n}\n.mce-content-body figure.image.align-center {\n  display: table;\n  margin-left: auto;\n  margin-right: auto;\n}\n.mce-preview-object {\n  border: 1px solid gray;\n  display: inline-block;\n  line-height: 0;\n  margin: 0 2px 0 2px;\n  position: relative;\n}\n.mce-preview-object .mce-shim {\n  background: url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7);\n  height: 100%;\n  left: 0;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n.mce-preview-object[data-mce-selected=\"2\"] .mce-shim {\n  display: none;\n}\n.mce-object {\n  background: transparent url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%3E%3Cpath%20d%3D%22M4%203h16a1%201%200%200%201%201%201v16a1%201%200%200%201-1%201H4a1%201%200%200%201-1-1V4a1%201%200%200%201%201-1zm1%202v14h14V5H5zm4.79%202.565l5.64%204.028a.5.5%200%200%201%200%20.814l-5.64%204.028a.5.5%200%200%201-.79-.407V7.972a.5.5%200%200%201%20.79-.407z%22%2F%3E%3C%2Fsvg%3E%0A\") no-repeat center;\n  border: 1px dashed #aaa;\n}\n.mce-pagebreak {\n  border: 1px dashed #aaa;\n  cursor: default;\n  display: block;\n  height: 5px;\n  margin-top: 15px;\n  page-break-before: always;\n  width: 100%;\n}\n@media print {\n  .mce-pagebreak {\n    border: 0;\n  }\n}\n.tiny-pageembed .mce-shim {\n  background: url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7);\n  height: 100%;\n  left: 0;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n.tiny-pageembed[data-mce-selected=\"2\"] .mce-shim {\n  display: none;\n}\n.tiny-pageembed {\n  display: inline-block;\n  position: relative;\n}\n.tiny-pageembed--21by9,\n.tiny-pageembed--16by9,\n.tiny-pageembed--4by3,\n.tiny-pageembed--1by1 {\n  display: block;\n  overflow: hidden;\n  padding: 0;\n  position: relative;\n  width: 100%;\n}\n.tiny-pageembed--21by9 {\n  padding-top: 42.857143%;\n}\n.tiny-pageembed--16by9 {\n  padding-top: 56.25%;\n}\n.tiny-pageembed--4by3 {\n  padding-top: 75%;\n}\n.tiny-pageembed--1by1 {\n  padding-top: 100%;\n}\n.tiny-pageembed--21by9 iframe,\n.tiny-pageembed--16by9 iframe,\n.tiny-pageembed--4by3 iframe,\n.tiny-pageembed--1by1 iframe {\n  border: 0;\n  height: 100%;\n  left: 0;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n.mce-content-body[data-mce-placeholder] {\n  position: relative;\n}\n.mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {\n  color: rgba(34, 47, 62, 0.7);\n  content: attr(data-mce-placeholder);\n  position: absolute;\n}\n.mce-content-body:not([dir=rtl])[data-mce-placeholder]:not(.mce-visualblocks)::before {\n  left: 1px;\n}\n.mce-content-body[dir=rtl][data-mce-placeholder]:not(.mce-visualblocks)::before {\n  right: 1px;\n}\n.mce-content-body div.mce-resizehandle {\n  background-color: #4099ff;\n  border-color: #4099ff;\n  border-style: solid;\n  border-width: 1px;\n  box-sizing: border-box;\n  height: 10px;\n  position: absolute;\n  width: 10px;\n  z-index: 10000;\n}\n.mce-content-body div.mce-resizehandle:hover {\n  background-color: #4099ff;\n}\n.mce-content-body div.mce-resizehandle:nth-of-type(1) {\n  cursor: nwse-resize;\n}\n.mce-content-body div.mce-resizehandle:nth-of-type(2) {\n  cursor: nesw-resize;\n}\n.mce-content-body div.mce-resizehandle:nth-of-type(3) {\n  cursor: nwse-resize;\n}\n.mce-content-body div.mce-resizehandle:nth-of-type(4) {\n  cursor: nesw-resize;\n}\n.mce-content-body .mce-clonedresizable {\n  opacity: 0.5;\n  outline: 1px dashed black;\n  position: absolute;\n  z-index: 10000;\n}\n.mce-content-body .mce-resize-helper {\n  background: #555;\n  background: rgba(0, 0, 0, 0.75);\n  border: 1px;\n  border-radius: 3px;\n  color: white;\n  display: none;\n  font-family: sans-serif;\n  font-size: 12px;\n  line-height: 14px;\n  margin: 5px 10px;\n  padding: 5px;\n  position: absolute;\n  white-space: nowrap;\n  z-index: 10001;\n}\n.mce-match-marker {\n  background: #aaa;\n  color: #fff;\n}\n.mce-match-marker-selected {\n  background: #39f;\n  color: #fff;\n}\n.mce-content-body img[data-mce-selected],\n.mce-content-body table[data-mce-selected] {\n  outline: 3px solid #b4d7ff;\n}\n.mce-content-body hr[data-mce-selected] {\n  outline: 3px solid #b4d7ff;\n  outline-offset: 1px;\n}\n.mce-content-body *[contentEditable=false] *[contentEditable=true]:focus {\n  outline: 3px solid #b4d7ff;\n}\n.mce-content-body *[contentEditable=false] *[contentEditable=true]:hover {\n  outline: 3px solid #b4d7ff;\n}\n.mce-content-body *[contentEditable=false][data-mce-selected] {\n  cursor: not-allowed;\n  outline: 3px solid #b4d7ff;\n}\n.mce-content-body.mce-content-readonly *[contentEditable=true]:focus,\n.mce-content-body.mce-content-readonly *[contentEditable=true]:hover {\n  outline: none;\n}\n.mce-content-body *[data-mce-selected=\"inline-boundary\"] {\n  background-color: #b4d7ff;\n}\n.mce-content-body .mce-edit-focus {\n  outline: 3px solid #b4d7ff;\n}\n.mce-content-body td[data-mce-selected],\n.mce-content-body th[data-mce-selected] {\n  background-color: #b4d7ff !important;\n}\n.mce-content-body td[data-mce-selected]::-moz-selection,\n.mce-content-body th[data-mce-selected]::-moz-selection {\n  background: none;\n}\n.mce-content-body td[data-mce-selected]::selection,\n.mce-content-body th[data-mce-selected]::selection {\n  background: none;\n}\n.mce-content-body td[data-mce-selected] *,\n.mce-content-body th[data-mce-selected] * {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n}\n.mce-content-body img::-moz-selection {\n  background: none;\n}\n.mce-content-body img::selection {\n  background: none;\n}\n.ephox-snooker-resizer-bar {\n  background-color: #b4d7ff;\n  opacity: 0;\n}\n.ephox-snooker-resizer-cols {\n  cursor: col-resize;\n}\n.ephox-snooker-resizer-rows {\n  cursor: row-resize;\n}\n.ephox-snooker-resizer-bar.ephox-snooker-resizer-bar-dragging {\n  opacity: 1;\n}\n.mce-spellchecker-word {\n  background-image: url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D'4'%20height%3D'4'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%3E%3Cpath%20stroke%3D'%23ff0000'%20fill%3D'none'%20stroke-linecap%3D'round'%20stroke-opacity%3D'.75'%20d%3D'M0%203L2%201%204%203'%2F%3E%3C%2Fsvg%3E%0A\");\n  background-position: 0 calc(100% + 1px);\n  background-repeat: repeat-x;\n  background-size: auto 6px;\n  cursor: default;\n  height: 2rem;\n}\n.mce-spellchecker-grammar {\n  background-image: url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D'4'%20height%3D'4'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%3E%3Cpath%20stroke%3D'%2300A835'%20fill%3D'none'%20stroke-linecap%3D'round'%20d%3D'M0%203L2%201%204%203'%2F%3E%3C%2Fsvg%3E%0A\");\n  background-position: 0 calc(100% + 1px);\n  background-repeat: repeat-x;\n  background-size: auto 6px;\n  cursor: default;\n}\n.mce-toc {\n  border: 1px solid gray;\n}\n.mce-toc h2 {\n  margin: 4px;\n}\n.mce-toc li {\n  list-style-type: none;\n}\n.mce-item-table,\n.mce-item-table td,\n.mce-item-table th,\n.mce-item-table caption {\n  border: 1px dashed #bbb;\n}\n.mce-visualblocks p,\n.mce-visualblocks h1,\n.mce-visualblocks h2,\n.mce-visualblocks h3,\n.mce-visualblocks h4,\n.mce-visualblocks h5,\n.mce-visualblocks h6,\n.mce-visualblocks div:not([data-mce-bogus]),\n.mce-visualblocks section,\n.mce-visualblocks article,\n.mce-visualblocks blockquote,\n.mce-visualblocks address,\n.mce-visualblocks pre,\n.mce-visualblocks figure,\n.mce-visualblocks figcaption,\n.mce-visualblocks hgroup,\n.mce-visualblocks aside,\n.mce-visualblocks ul,\n.mce-visualblocks ol,\n.mce-visualblocks dl {\n  background-repeat: no-repeat;\n  border: 1px dashed #bbb;\n  margin-left: 3px;\n  padding-top: 10px;\n}\n.mce-visualblocks p {\n  background-image: url(data:image/gif;base64,R0lGODlhCQAJAJEAAAAAAP///7u7u////yH5BAEAAAMALAAAAAAJAAkAAAIQnG+CqCN/mlyvsRUpThG6AgA7);\n}\n.mce-visualblocks h1 {\n  background-image: url(data:image/gif;base64,R0lGODlhDQAKAIABALu7u////yH5BAEAAAEALAAAAAANAAoAAAIXjI8GybGu1JuxHoAfRNRW3TWXyF2YiRUAOw==);\n}\n.mce-visualblocks h2 {\n  background-image: url(data:image/gif;base64,R0lGODlhDgAKAIABALu7u////yH5BAEAAAEALAAAAAAOAAoAAAIajI8Hybbx4oOuqgTynJd6bGlWg3DkJzoaUAAAOw==);\n}\n.mce-visualblocks h3 {\n  background-image: url(data:image/gif;base64,R0lGODlhDgAKAIABALu7u////yH5BAEAAAEALAAAAAAOAAoAAAIZjI8Hybbx4oOuqgTynJf2Ln2NOHpQpmhAAQA7);\n}\n.mce-visualblocks h4 {\n  background-image: url(data:image/gif;base64,R0lGODlhDgAKAIABALu7u////yH5BAEAAAEALAAAAAAOAAoAAAIajI8HybbxInR0zqeAdhtJlXwV1oCll2HaWgAAOw==);\n}\n.mce-visualblocks h5 {\n  background-image: url(data:image/gif;base64,R0lGODlhDgAKAIABALu7u////yH5BAEAAAEALAAAAAAOAAoAAAIajI8HybbxIoiuwjane4iq5GlW05GgIkIZUAAAOw==);\n}\n.mce-visualblocks h6 {\n  background-image: url(data:image/gif;base64,R0lGODlhDgAKAIABALu7u////yH5BAEAAAEALAAAAAAOAAoAAAIajI8HybbxIoiuwjan04jep1iZ1XRlAo5bVgAAOw==);\n}\n.mce-visualblocks div:not([data-mce-bogus]) {\n  background-image: url(data:image/gif;base64,R0lGODlhEgAKAIABALu7u////yH5BAEAAAEALAAAAAASAAoAAAIfjI9poI0cgDywrhuxfbrzDEbQM2Ei5aRjmoySW4pAAQA7);\n}\n.mce-visualblocks section {\n  background-image: url(data:image/gif;base64,R0lGODlhKAAKAIABALu7u////yH5BAEAAAEALAAAAAAoAAoAAAI5jI+pywcNY3sBWHdNrplytD2ellDeSVbp+GmWqaDqDMepc8t17Y4vBsK5hDyJMcI6KkuYU+jpjLoKADs=);\n}\n.mce-visualblocks article {\n  background-image: url(data:image/gif;base64,R0lGODlhKgAKAIABALu7u////yH5BAEAAAEALAAAAAAqAAoAAAI6jI+pywkNY3wG0GBvrsd2tXGYSGnfiF7ikpXemTpOiJScasYoDJJrjsG9gkCJ0ag6KhmaIe3pjDYBBQA7);\n}\n.mce-visualblocks blockquote {\n  background-image: url(data:image/gif;base64,R0lGODlhPgAKAIABALu7u////yH5BAEAAAEALAAAAAA+AAoAAAJPjI+py+0Knpz0xQDyuUhvfoGgIX5iSKZYgq5uNL5q69asZ8s5rrf0yZmpNkJZzFesBTu8TOlDVAabUyatguVhWduud3EyiUk45xhTTgMBBQA7);\n}\n.mce-visualblocks address {\n  background-image: url(data:image/gif;base64,R0lGODlhLQAKAIABALu7u////yH5BAEAAAEALAAAAAAtAAoAAAI/jI+pywwNozSP1gDyyZcjb3UaRpXkWaXmZW4OqKLhBmLs+K263DkJK7OJeifh7FicKD9A1/IpGdKkyFpNmCkAADs=);\n}\n.mce-visualblocks pre {\n  background-image: url(data:image/gif;base64,R0lGODlhFQAKAIABALu7uwAAACH5BAEAAAEALAAAAAAVAAoAAAIjjI+ZoN0cgDwSmnpz1NCueYERhnibZVKLNnbOq8IvKpJtVQAAOw==);\n}\n.mce-visualblocks figure {\n  background-image: url(data:image/gif;base64,R0lGODlhJAAKAIAAALu7u////yH5BAEAAAEALAAAAAAkAAoAAAI0jI+py+2fwAHUSFvD3RlvG4HIp4nX5JFSpnZUJ6LlrM52OE7uSWosBHScgkSZj7dDKnWAAgA7);\n}\n.mce-visualblocks figcaption {\n  border: 1px dashed #bbb;\n}\n.mce-visualblocks hgroup {\n  background-image: url(data:image/gif;base64,R0lGODlhJwAKAIABALu7uwAAACH5BAEAAAEALAAAAAAnAAoAAAI3jI+pywYNI3uB0gpsRtt5fFnfNZaVSYJil4Wo03Hv6Z62uOCgiXH1kZIIJ8NiIxRrAZNMZAtQAAA7);\n}\n.mce-visualblocks aside {\n  background-image: url(data:image/gif;base64,R0lGODlhHgAKAIABAKqqqv///yH5BAEAAAEALAAAAAAeAAoAAAItjI+pG8APjZOTzgtqy7I3f1yehmQcFY4WKZbqByutmW4aHUd6vfcVbgudgpYCADs=);\n}\n.mce-visualblocks ul {\n  background-image: url(data:image/gif;base64,R0lGODlhDQAKAIAAALu7u////yH5BAEAAAEALAAAAAANAAoAAAIXjI8GybGuYnqUVSjvw26DzzXiqIDlVwAAOw==);\n}\n.mce-visualblocks ol {\n  background-image: url(data:image/gif;base64,R0lGODlhDQAKAIABALu7u////yH5BAEAAAEALAAAAAANAAoAAAIXjI8GybH6HHt0qourxC6CvzXieHyeWQAAOw==);\n}\n.mce-visualblocks dl {\n  background-image: url(data:image/gif;base64,R0lGODlhDQAKAIABALu7u////yH5BAEAAAEALAAAAAANAAoAAAIXjI8GybEOnmOvUoWznTqeuEjNSCqeGRUAOw==);\n}\n.mce-visualblocks:not([dir=rtl]) p,\n.mce-visualblocks:not([dir=rtl]) h1,\n.mce-visualblocks:not([dir=rtl]) h2,\n.mce-visualblocks:not([dir=rtl]) h3,\n.mce-visualblocks:not([dir=rtl]) h4,\n.mce-visualblocks:not([dir=rtl]) h5,\n.mce-visualblocks:not([dir=rtl]) h6,\n.mce-visualblocks:not([dir=rtl]) div:not([data-mce-bogus]),\n.mce-visualblocks:not([dir=rtl]) section,\n.mce-visualblocks:not([dir=rtl]) article,\n.mce-visualblocks:not([dir=rtl]) blockquote,\n.mce-visualblocks:not([dir=rtl]) address,\n.mce-visualblocks:not([dir=rtl]) pre,\n.mce-visualblocks:not([dir=rtl]) figure,\n.mce-visualblocks:not([dir=rtl]) figcaption,\n.mce-visualblocks:not([dir=rtl]) hgroup,\n.mce-visualblocks:not([dir=rtl]) aside,\n.mce-visualblocks:not([dir=rtl]) ul,\n.mce-visualblocks:not([dir=rtl]) ol,\n.mce-visualblocks:not([dir=rtl]) dl {\n  margin-left: 3px;\n}\n.mce-visualblocks[dir=rtl] p,\n.mce-visualblocks[dir=rtl] h1,\n.mce-visualblocks[dir=rtl] h2,\n.mce-visualblocks[dir=rtl] h3,\n.mce-visualblocks[dir=rtl] h4,\n.mce-visualblocks[dir=rtl] h5,\n.mce-visualblocks[dir=rtl] h6,\n.mce-visualblocks[dir=rtl] div:not([data-mce-bogus]),\n.mce-visualblocks[dir=rtl] section,\n.mce-visualblocks[dir=rtl] article,\n.mce-visualblocks[dir=rtl] blockquote,\n.mce-visualblocks[dir=rtl] address,\n.mce-visualblocks[dir=rtl] pre,\n.mce-visualblocks[dir=rtl] figure,\n.mce-visualblocks[dir=rtl] figcaption,\n.mce-visualblocks[dir=rtl] hgroup,\n.mce-visualblocks[dir=rtl] aside,\n.mce-visualblocks[dir=rtl] ul,\n.mce-visualblocks[dir=rtl] ol,\n.mce-visualblocks[dir=rtl] dl {\n  background-position-x: right;\n  margin-right: 3px;\n}\n.mce-nbsp,\n.mce-shy {\n  background: #aaa;\n}\n.mce-shy::after {\n  content: '-';\n}\nbody {\n  font-family: sans-serif;\n}\ntable {\n  border-collapse: collapse;\n}\n"]}