{"_from": "des.js@^1.0.0", "_id": "des.js@1.1.0", "_inBundle": false, "_integrity": "sha512-r17GxjhUCjSRy8aiJpr8/UadFIzMzJGexI3Nmz4ADi9LYSFx4gTBp80+NaX/YsXWWLhpZ7v/v/ubEc/bCNfKwg==", "_location": "/des.js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "des.js@^1.0.0", "name": "des.js", "escapedName": "des.js", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/browserify-des"], "_resolved": "https://registry.npmjs.org/des.js/-/des.js-1.1.0.tgz", "_shasum": "1d37f5766f3bbff4ee9638e871a8768c173b81da", "_spec": "des.js@^1.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\browserify-des", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/des.js/issues"}, "bundleDependencies": false, "dependencies": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}, "deprecated": false, "description": "DES implementation", "devDependencies": {"jshint": "^2.8.0", "mocha": "^10.2.0"}, "homepage": "https://github.com/indutny/des.js#readme", "keywords": ["DES", "3DES", "EDE", "CBC"], "license": "MIT", "main": "lib/des.js", "name": "des.js", "repository": {"type": "git", "url": "git+ssh://**************/indutny/des.js.git"}, "scripts": {"test": "NODE_OPTIONS=--openssl-legacy-provider mocha --reporter=spec test/*-test.js"}, "version": "1.1.0"}