{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\src\\utils\\http.js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\utils\\http.js", "mtime": 1752474912427}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJzsKaW1wb3J0IHJvdXRlciBmcm9tICdAL3JvdXRlci9yb3V0ZXItc3RhdGljJzsKaW1wb3J0IHN0b3JhZ2UgZnJvbSAnQC91dGlscy9zdG9yYWdlJzsKY29uc3QgaHR0cCA9IGF4aW9zLmNyZWF0ZSh7CiAgdGltZW91dDogMTAwMCAqIDg2NDAwLAogIHdpdGhDcmVkZW50aWFsczogdHJ1ZSwKICBiYXNlVVJMOiBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nID8gJy9saXVsYW5nZG9uZ3d1YmVpaHVhJyA6ICcvbGl1bGFuZ2Rvbmd3dWJlaWh1YScsCiAgaGVhZGVyczogewogICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04JwogIH0KfSk7Ci8vIOivt+axguaLpuaIqgpodHRwLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShjb25maWcgPT4gewogIGNvbmZpZy5oZWFkZXJzWydUb2tlbiddID0gc3RvcmFnZS5nZXQoJ1Rva2VuJyk7IC8vIOivt+axguWktOW4puS4inRva2VuCiAgcmV0dXJuIGNvbmZpZzsKfSwgZXJyb3IgPT4gewogIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7Cn0pOwovLyDlk43lupTmi6bmiKoKaHR0cC5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKHJlc3BvbnNlID0+IHsKICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmNvZGUgPT09IDQwMSkgewogICAgLy8gNDAxLCB0b2tlbuWkseaViAogICAgcm91dGVyLnB1c2goewogICAgICBuYW1lOiAnbG9naW4nCiAgICB9KTsKICB9CiAgcmV0dXJuIHJlc3BvbnNlOwp9LCBlcnJvciA9PiB7CiAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTsKfSk7CmV4cG9ydCBkZWZhdWx0IGh0dHA7"}, {"version": 3, "names": ["axios", "router", "storage", "http", "create", "timeout", "withCredentials", "baseURL", "process", "env", "NODE_ENV", "headers", "interceptors", "request", "use", "config", "get", "error", "Promise", "reject", "response", "data", "code", "push", "name"], "sources": ["D:/xuangmu/yuanma/code1/front/src/utils/http.js"], "sourcesContent": ["import axios from 'axios'\r\nimport router from '@/router/router-static'\r\nimport storage from '@/utils/storage'\r\n\r\nconst http = axios.create({\r\n    timeout: 1000 * 86400,\r\n    withCredentials: true,\r\n    baseURL: process.env.NODE_ENV === 'production' ? '/liulangdongwubeihua' : '/liulangdongwubeihua',\r\n    headers: {\r\n        'Content-Type': 'application/json; charset=utf-8'\r\n    }\r\n})\r\n// 请求拦截\r\nhttp.interceptors.request.use(config => {\r\n    config.headers['Token'] = storage.get('Token') // 请求头带上token\r\n    return config\r\n}, error => {\r\n    return Promise.reject(error)\r\n})\r\n// 响应拦截\r\nhttp.interceptors.response.use(response => {\r\n    if (response.data && response.data.code === 401) { // 401, token失效\r\n        router.push({ name: 'login' })\r\n    }\r\n    return response\r\n}, error => {\r\n    return Promise.reject(error)\r\n})\r\nexport default http"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,wBAAwB;AAC3C,OAAOC,OAAO,MAAM,iBAAiB;AAErC,MAAMC,IAAI,GAAGH,KAAK,CAACI,MAAM,CAAC;EACtBC,OAAO,EAAE,IAAI,GAAG,KAAK;EACrBC,eAAe,EAAE,IAAI;EACrBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,sBAAsB,GAAG,sBAAsB;EAChGC,OAAO,EAAE;IACL,cAAc,EAAE;EACpB;AACJ,CAAC,CAAC;AACF;AACAR,IAAI,CAACS,YAAY,CAACC,OAAO,CAACC,GAAG,CAACC,MAAM,IAAI;EACpCA,MAAM,CAACJ,OAAO,CAAC,OAAO,CAAC,GAAGT,OAAO,CAACc,GAAG,CAAC,OAAO,CAAC,EAAC;EAC/C,OAAOD,MAAM;AACjB,CAAC,EAAEE,KAAK,IAAI;EACR,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CAAC;AACF;AACAd,IAAI,CAACS,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAACM,QAAQ,IAAI;EACvC,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,IAAI,KAAK,GAAG,EAAE;IAAE;IAC/CrB,MAAM,CAACsB,IAAI,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,CAAC;EAClC;EACA,OAAOJ,QAAQ;AACnB,CAAC,EAAEH,KAAK,IAAI;EACR,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CAAC;AACF,eAAed,IAAI", "ignoreList": []}]}