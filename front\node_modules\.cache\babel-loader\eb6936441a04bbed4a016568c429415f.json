{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\xuangmu\\yuanma\\code1\\front\\src\\components\\index\\IndexHeader.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\components\\index\\IndexHeader.vue", "mtime": 1751860002329}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcnVsZUZvcm06IHt9LAogICAgICB1c2VyOiB7fSwKICAgICAgaGVhZHM6IHsKICAgICAgICAiaGVhZExvZ291dEZvbnRIb3ZlckNvbG9yIjogIiNmZmYiLAogICAgICAgICJoZWFkRm9udFNpemUiOiAiMjNweCIsCiAgICAgICAgImhlYWRVc2VySW5mb0ZvbnRDb2xvciI6ICIjMzMzIiwKICAgICAgICAiaGVhZEJveFNoYWRvdyI6ICIwIDFweCA2cHggIzQ0NCIsCiAgICAgICAgImhlYWRUaXRsZUltZ0hlaWdodCI6ICI0NHB4IiwKICAgICAgICAiaGVhZExvZ291dEZvbnRIb3ZlckJnQ29sb3IiOiAiIzMzMyIsCiAgICAgICAgImhlYWRGb250Q29sb3IiOiAicmdiYSgwLCAwLCAwLCAwLjYxKSIsCiAgICAgICAgImhlYWRUaXRsZUltZyI6IGZhbHNlLAogICAgICAgICJoZWFkSGVpZ2h0IjogIjYwcHgiLAogICAgICAgICJoZWFkVGl0bGVJbWdCb3JkZXJSYWRpdXMiOiAiMjJweCIsCiAgICAgICAgImhlYWRUaXRsZUltZ1VybCI6ICJodHRwOi8vY29kZWdlbi5jYWlob25neS5jbi8yMDIwMTAyMS9jYzdkNDVkOWM4MTY0YjU4YjE4MzUxNzY0ZWJhOWJlMS5qcGciLAogICAgICAgICJoZWFkQmdDb2xvciI6ICIjRThDNjZGIiwKICAgICAgICAiaGVhZFRpdGxlSW1nQm94U2hhZG93IjogIjAgMXB4IDZweCAjNDQ0IiwKICAgICAgICAiaGVhZExvZ291dEZvbnRDb2xvciI6ICIjMzMzIiwKICAgICAgICAiaGVhZFVzZXJJbmZvRm9udFNpemUiOiAiMTZweCIsCiAgICAgICAgImhlYWRUaXRsZUltZ1dpZHRoIjogIjQ0cHgiLAogICAgICAgICJoZWFkVGl0bGVTdHlsZSI6ICIxIiwKICAgICAgICAiaGVhZExvZ291dEZvbnRTaXplIjogIjE2cHgiCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5zZXRIZWFkZXJTdHlsZSgpOwogIH0sCiAgbW91bnRlZCgpIHsKICAgIGxldCBzZXNzaW9uVGFibGUgPSB0aGlzLiRzdG9yYWdlLmdldCgic2Vzc2lvblRhYmxlIik7CiAgICB0aGlzLiRodHRwKHsKICAgICAgdXJsOiBzZXNzaW9uVGFibGUgKyAnL3Nlc3Npb24nLAogICAgICBtZXRob2Q6ICJnZXQiCiAgICB9KS50aGVuKCh7CiAgICAgIGRhdGEKICAgIH0pID0+IHsKICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgdGhpcy51c2VyID0gZGF0YS5kYXRhOwogICAgICB9IGVsc2UgewogICAgICAgIGxldCBtZXNzYWdlID0gdGhpcy4kbWVzc2FnZTsKICAgICAgICBtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsKICAgICAgfQogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBvbkxvZ291dCgpIHsKICAgICAgbGV0IHN0b3JhZ2UgPSB0aGlzLiRzdG9yYWdlOwogICAgICBsZXQgcm91dGVyID0gdGhpcy4kcm91dGVyOwogICAgICBzdG9yYWdlLnJlbW92ZSgiVG9rZW4iKTsKICAgICAgcm91dGVyLnJlcGxhY2UoewogICAgICAgIG5hbWU6ICJsb2dpbiIKICAgICAgfSk7CiAgICB9LAogICAgc2V0SGVhZGVyU3R5bGUoKSB7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcubmF2YmFyIC5yaWdodC1tZW51IC5sb2dvdXQnKS5mb3JFYWNoKGVsID0+IHsKICAgICAgICAgIGVsLmFkZEV2ZW50TGlzdGVuZXIoIm1vdXNlZW50ZXIiLCBlID0+IHsKICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTsKICAgICAgICAgICAgZWwuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gdGhpcy5oZWFkcy5oZWFkTG9nb3V0Rm9udEhvdmVyQmdDb2xvcjsKICAgICAgICAgICAgZWwuc3R5bGUuY29sb3IgPSB0aGlzLmhlYWRzLmhlYWRMb2dvdXRGb250SG92ZXJDb2xvcjsKICAgICAgICAgIH0pOwogICAgICAgICAgZWwuYWRkRXZlbnRMaXN0ZW5lcigibW91c2VsZWF2ZSIsIGUgPT4gewogICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpOwogICAgICAgICAgICBlbC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSAidHJhbnNwYXJlbnQiOwogICAgICAgICAgICBlbC5zdHlsZS5jb2xvciA9IHRoaXMuaGVhZHMuaGVhZExvZ291dEZvbnRDb2xvcjsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["data", "dialogVisible", "ruleForm", "user", "heads", "created", "setHeaderStyle", "mounted", "sessionTable", "$storage", "get", "$http", "url", "method", "then", "code", "message", "$message", "error", "msg", "methods", "onLogout", "storage", "router", "$router", "remove", "replace", "name", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "addEventListener", "e", "stopPropagation", "style", "backgroundColor", "headLogoutFontHoverBgColor", "color", "headLogoutFontHoverColor", "headLogoutFontColor"], "sources": ["src/components/index/IndexHeader.vue"], "sourcesContent": ["<template>\r\n    <!-- <el-header>\r\n        <el-menu background-color=\"#00c292\" text-color=\"#FFFFFF\" active-text-color=\"#FFFFFF\" mode=\"horizontal\">\r\n            <div class=\"fl title\">{{this.$project.projectName}}</div>\r\n            <div class=\"fr logout\" style=\"display:flex;\">\r\n                <el-menu-item index=\"3\">\r\n                    <div>{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n                </el-menu-item>\r\n                <el-menu-item @click=\"onLogout\" index=\"2\">\r\n                    <div>退出登录</div>\r\n                </el-menu-item>\r\n            </div>\r\n        </el-menu>\r\n    </el-header> -->\r\n    <div class=\"navbar\" :style=\"{backgroundColor:heads.headBgColor,height:heads.headHeight,boxShadow:heads.headBoxShadow,lineHeight:heads.headHeight}\">\r\n        <div class=\"title-menu\" :style=\"{justifyContent:heads.headTitleStyle=='1'?'flex-start':'center'}\">\r\n            <el-image v-if=\"heads.headTitleImg\" class=\"title-img\" :style=\"{width:heads.headTitleImgWidth,height:heads.headTitleImgHeight,boxShadow:heads.headTitleImgBoxShadow,borderRadius:heads.headTitleImgBorderRadius}\" :src=\"heads.headTitleImgUrl\" fit=\"cover\"></el-image>\r\n            <div class=\"title-name\" :style=\"{color:heads.headFontColor,fontSize:heads.headFontSize}\">{{this.$project.projectName}}</div>\r\n            <!--             <img src=\"../../../../img/logo.jpg\" style=\"width: 60px;height: 60px;border-radius:60px\"> -->\r\n        </div>\r\n        <div class=\"right-menu\">\r\n            <span class=\"user-info\" :style=\"{color:heads.headUserInfoFontColor,fontSize:heads.headUserInfoFontSize}\">{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</span>\r\n            <div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onLogout\">退出登录</div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                dialogVisible: false,\r\n                ruleForm: {},\r\n                user: {},\r\n                heads: {\"headLogoutFontHoverColor\":\"#fff\",\"headFontSize\":\"23px\",\"headUserInfoFontColor\":\"#333\",\"headBoxShadow\":\"0 1px 6px #444\",\"headTitleImgHeight\":\"44px\",\"headLogoutFontHoverBgColor\":\"#333\",\"headFontColor\":\"rgba(0, 0, 0, 0.61)\",\"headTitleImg\":false,\"headHeight\":\"60px\",\"headTitleImgBorderRadius\":\"22px\",\"headTitleImgUrl\":\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\",\"headBgColor\":\"#E8C66F\",\"headTitleImgBoxShadow\":\"0 1px 6px #444\",\"headLogoutFontColor\":\"#333\",\"headUserInfoFontSize\":\"16px\",\"headTitleImgWidth\":\"44px\",\"headTitleStyle\":\"1\",\"headLogoutFontSize\":\"16px\"},\r\n            };\r\n        },\r\n        created() {\r\n            this.setHeaderStyle();\r\n        },\r\n        mounted() {\r\n            let sessionTable = this.$storage.get(\"sessionTable\")\r\n            this.$http({\r\n                url: sessionTable + '/session',\r\n                method: \"get\"\r\n            }).then(({\r\n                         data\r\n                     }) => {\r\n                if (data && data.code === 0) {\r\n                    this.user = data.data;\r\n                } else {\r\n                    let message = this.$message\r\n                    message.error(data.msg);\r\n                }\r\n            });\r\n        },\r\n        methods: {\r\n            onLogout() {\r\n                let storage = this.$storage\r\n                let router = this.$router\r\n                storage.remove(\"Token\");\r\n                router.replace({\r\n                    name: \"login\"\r\n                });\r\n            },\r\n            setHeaderStyle() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.navbar .right-menu .logout').forEach(el=>{\r\n                        el.addEventListener(\"mouseenter\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = this.heads.headLogoutFontHoverBgColor\r\n                            el.style.color = this.heads.headLogoutFontHoverColor\r\n                        })\r\n                        el.addEventListener(\"mouseleave\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = \"transparent\"\r\n                            el.style.color = this.heads.headLogoutFontColor\r\n                        })\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n    .navbar {\r\n        height: 60px;\r\n        line-height: 60px;\r\n        width: 100%;\r\n        padding: 0 34px;\r\n        box-sizing: border-box;\r\n        background-color: #ff00ff;\r\n        position: relative;\r\n        z-index: 111;\r\n\r\n    .right-menu {\r\n        position: absolute;\r\n        right: 34px;\r\n        top: 0;\r\n        height: 100%;\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        align-items: center;\r\n        z-index: 111;\r\n\r\n    .user-info {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n    }\r\n\r\n    .logout {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n        cursor: pointer;\r\n    }\r\n\r\n    }\r\n\r\n    .title-menu {\r\n        display: flex;\r\n        justify-content: flex-start;\r\n        align-items: center;\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n    .title-img {\r\n        width: 44px;\r\n        height: 44px;\r\n        border-radius: 22px;\r\n        box-shadow: 0 1px 6px #444;\r\n        margin-right: 16px;\r\n    }\r\n\r\n    .title-name {\r\n        font-size: 24px;\r\n        color: #fff;\r\n        font-weight: 700;\r\n    }\r\n    }\r\n    }\r\n    // .el-header .fr {\r\n       // \tfloat: right;\r\n       // }\r\n\r\n    // .el-header .fl {\r\n       // \tfloat: left;\r\n       // }\r\n\r\n    // .el-header {\r\n       // \twidth: 100%;\r\n       // \tcolor: #333;\r\n       // \ttext-align: center;\r\n       // \tline-height: 60px;\r\n       // \tpadding: 0;\r\n       // \tz-index: 99;\r\n       // }\r\n\r\n    // .logo {\r\n       // \twidth: 60px;\r\n       // \theight: 60px;\r\n       // \tmargin-left: 70px;\r\n       // }\r\n\r\n    // .avator {\r\n       // \twidth: 40px;\r\n       // \theight: 40px;\r\n       // \tbackground: #ffffff;\r\n       // \tborder-radius: 50%;\r\n       // }\r\n\r\n    // .title {\r\n       // \tcolor: #ffffff;\r\n       // \tfont-size: 20px;\r\n       // \tfont-weight: bold;\r\n       // \tmargin-left: 20px;\r\n       // }\r\n</style>\r\n"], "mappings": "AA4BA;EACAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,QAAA;IACA,IAAAC,YAAA,QAAAC,QAAA,CAAAC,GAAA;IACA,KAAAC,KAAA;MACAC,GAAA,EAAAJ,YAAA;MACAK,MAAA;IACA,GAAAC,IAAA;MACAd;IACA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAe,IAAA;QACA,KAAAZ,IAAA,GAAAH,IAAA,CAAAA,IAAA;MACA;QACA,IAAAgB,OAAA,QAAAC,QAAA;QACAD,OAAA,CAAAE,KAAA,CAAAlB,IAAA,CAAAmB,GAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,SAAA;MACA,IAAAC,OAAA,QAAAb,QAAA;MACA,IAAAc,MAAA,QAAAC,OAAA;MACAF,OAAA,CAAAG,MAAA;MACAF,MAAA,CAAAG,OAAA;QACAC,IAAA;MACA;IACA;IACArB,eAAA;MACA,KAAAsB,SAAA;QACAC,QAAA,CAAAC,gBAAA,gCAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,gBAAA,eAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAI,KAAA,CAAAC,eAAA,QAAAjC,KAAA,CAAAkC,0BAAA;YACAN,EAAA,CAAAI,KAAA,CAAAG,KAAA,QAAAnC,KAAA,CAAAoC,wBAAA;UACA;UACAR,EAAA,CAAAC,gBAAA,eAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAI,KAAA,CAAAC,eAAA;YACAL,EAAA,CAAAI,KAAA,CAAAG,KAAA,QAAAnC,KAAA,CAAAqC,mBAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}