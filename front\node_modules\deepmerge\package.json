{"_from": "deepmerge@^1.2.0", "_id": "deepmerge@1.5.2", "_inBundle": false, "_integrity": "sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==", "_location": "/deepmerge", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "deepmerge@^1.2.0", "name": "deepmerge", "escapedName": "deepmerge", "rawSpec": "^1.2.0", "saveSpec": null, "fetchSpec": "^1.2.0"}, "_requiredBy": ["/element-ui", "/webpack-chain"], "_resolved": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.2.tgz", "_shasum": "10499d868844cdad4fee0842df8c7f6f0c95a753", "_spec": "deepmerge@^1.2.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\element-ui", "author": {"name": "<PERSON>"}, "browser": "dist/cjs.js", "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A library for deep (recursive) merging of Javascript objects", "devDependencies": {"is-mergeable-object": "1.1.0", "jsmd": "0.3.1", "rollup": "0.49.3", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0", "tap": "~7.1.2"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/KyleAMathews/deepmerge", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "license": "MIT", "main": "dist/umd.js", "module": "dist/es.js", "name": "deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "scripts": {"build": "rollup -c", "test": "npm run build && tap test/*.js && jsmd README.markdown"}, "version": "1.5.2"}