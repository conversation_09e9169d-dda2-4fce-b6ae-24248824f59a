<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="utf-8">
    <title>宠物寄养</title>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../xznstatic/css/common.css"/>
    <link rel="stylesheet" href="../../xznstatic/css/style.css"/>
    <script type="text/javascript" src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
    <link rel="stylesheet" href="../../xznstatic/css/bootstrap.min.css" />

    <link rel="stylesheet" href="../../css/theme.css"/>
</head>
<style>
    html::after {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        content: '';
        display: block;
        background-attachment: fixed;
        background-size: cover;
        background-position: center;
    }

    /*轮播图相关 start*/
    #swiper {
        overflow: hidden;
    }

    #swiper .layui-carousel-ind li {
        width: 20px;
        height: 10px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0, 0, 0, .3);
        border-radius: 6px;
        background-color: #f7f7f7;
        box-shadow: 0 0 6px rgba(255, 0, 0, .8);
    }

    #swiper .layui-carousel-ind li.layui-this {
        width: 30px;
        height: 10px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0, 0, 0, .3);
        border-radius: 6px;
    }
    /*轮播图相关 end*/

    /*列表*/
    .recommend {
        padding: 10px 0;
        display: flex;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
    }

    .recommend .box {
        width: 1002px;
        margin: 0 auto;
    }

    .recommend .box .title {
        padding: 10px 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
    }

    .recommend .box .title span {
        padding: 0 10px;
        font-size: 16px;
        line-height: 1.4;
    }

    .recommend .box .filter {
        padding: 0 10px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        width: 100%;
        flex-wrap: wrap;
    }

    .recommend .box .filter .item-list {
        display: flex;
        align-items: center;
    }

    .recommend .box .filter .item-list .lable {
        font-size: 14px;
        color: #333;
        box-sizing: border-box;
    }

    .recommend .box .filter .item-list input {
        padding: 0 10px;
        box-sizing: border-box;
        outline: none;
    }

    .recommend .box .filter button {
        display: flex;
        padding: 0 10px;
        box-sizing: border-box;
        align-items: center;
        justify-content: center;
        outline: none;
    }

    .recommend .box .filter button i {
        margin-right: 4px;
    }

    .recommend .box .list {
        display: flex;
        flex-wrap: wrap;
    }

    .recommend .box .list .list-item {
        flex: 0 0 25%;
        padding: 0 5px;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item .list-item-body {
        cursor: pointer;
        border: 1px solid rgba(0, 0, 0, 3);
        padding: 5px;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item-body img {
        width: 100%;
        height: 100px;
        display: block;
        margin: 0 auto;
    }

    .recommend .box .list .list-item-body .info {
        display: flex;
        flex-wrap: wrap;
    }

    .recommend .box .list .list-item-body .info .price {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        text-align: center;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item-body .info .name {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        text-align: center;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item3 {
        flex: 0 0 33.33%;
    }

    .recommend .box .list .list-item5 {
        flex: 0 0 25%;
    }

    .recommend .box .news {
        display: flex;
        flex-wrap: wrap;
        padding: 0;
        width: 100%;
    }

    .recommend .box .news .list-item {
        flex: 0 0 50%;
        padding: 0 10px;
        box-sizing: border-box;
    }

    .recommend .box .news .list-item .list-item-body {
        cursor: pointer;
        border: 1px solid rgba(0, 0, 0, 3);
        padding: 10px;
        box-sizing: border-box;
        display: flex;
    }

    .recommend .box .news .list-item .list-item-body img {
        width: 120px;
        height: 100%;
        display: block;
        margin: 0 auto;
    }

    .recommend .box .news .list-item .list-item-body .item-info {
        flex: 1;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        padding-left: 10px;
        box-sizing: border-box;
    }

    .recommend .box .news .list-item .list-item-body .item-info .name {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    .recommend .box .news .list-item .list-item-body .item-info .time {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        box-sizing: border-box;
    }

    .recommend .box .news .list-item1 {
        flex: 0 0 100%;
    }

    .recommend .box .news .list-item3 {
        flex: 0 0 33.33%;
    }

     .index-pv1 .animation-box:hover {
         transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(1.01) rotate(0deg) skew(-2deg, 0deg);
         transition: all 0.3s;
     }


    .layui-laypage .layui-laypage-count {
        padding: 0 10px;
    }

    .layui-laypage .layui-laypage-skip {
        padding-left: 10px;
    }
</style>
<body>
<div id="app">
    <div class="banner">
        <div class="layui-carousel" id="swiper"
             :style='{"boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"0 auto","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
            <div carousel-item>
                <div v-for="(item,index) in swiperList" :key="index">
                    <img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img"/>
                </div>
            </div>
        </div>
    </div>

    <div class="recommend index-pv1"
         :style='{"padding":"10px 0 10px 0","boxShadow":"0 0 0px ","margin":"10px 0 0 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(255, 0, 0, 0)","borderRadius":"0","borderWidth":"0","borderStyle":"solid"}'>
        <div class="box" style='width:80%'>

            <div class="title sub_backgroundColor sub_borderColor"
                 :style='{"padding":"10px 0 10px 0","margin":"10px 0 10px 0","borderRadius":"4px","borderWidth":"1px","borderStyle":"solid","justifyContent":"space-between","height":"54px"}'>
                    <span :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(255,0,0,1)","backgroundColor":"rgba(0,0,0,0)","color":"rgba(11, 11, 11, 1)","borderRadius":"0 0 2px 0","borderWidth":"0","fontSize":"18px","borderStyle":"solid"}'>
                        宠物寄养
                    </span>
                <span :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(0,0,0,0)","backgroundColor":"rgba(0,0,0,0)","color":"rgba(255, 255, 255, 1)","borderRadius":"0","borderWidth":"0","fontSize":"16px","borderStyle":"solid"}'>
                        您现在的位置：宠物寄养
                    </span>
            </div>

            <form class="layui-form filter main_backgroundColor"
                  :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"10px 0 10px 0","borderColor":"rgba(0,0,0,.3)","borderRadius":"4px","alignItems":"center","borderWidth":"0","borderStyle":"solid","justifyContent":"flex-end","height":"64px"}'>

                         
                <div class="item-list">
                    <div class="lable"
                         :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"0","borderColor":"rgba(0,0,0,0)","backgroundColor":"transparent","color":"rgba(17, 16, 16, 1)","borderRadius":"0","textAlign":"right","borderWidth":"0","width":"auto","fontSize":"16px","borderStyle":"solid"}'>
                        宠物名称
                    </div>
                    <input type="text" v-model="searchForm.chongwuName"
                           :style='{"boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"#ccc","backgroundColor":"#fff","color":"rgba(135, 206, 250, 1)","borderRadius":"8px","textAlign":"center","borderWidth":"0","width":"140px","fontSize":"14px","borderStyle":"solid","height":"44px"}'
                           placeholder="宠物名称" autocomplete="off"
                           class="layui-input">
                </div>
                 
                 <div class="item-list">
                     <div class="lable"
                          :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"0","borderColor":"rgba(0,0,0,0)","backgroundColor":"transparent","color":"rgba(17, 16, 16, 1)","borderRadius":"0","textAlign":"right","borderWidth":"0","width":"auto","fontSize":"16px","borderStyle":"solid"}'>
                         宠物类型</div>
                     <select :style='{"boxShadow":"0 0 0px rgba(0,0,0,0)","borderColor":"rgba(0, 0, 0, 0)","backgroundColor":"#ffffff","color":"#333","borderRadius":"8px","textAlign":"center","borderWidth":"2px","width":"140px","fontSize":"14px","borderStyle":"solid","height":"44px"}'
                             style="display:block"  v-model="searchForm.chongwuTypes">
                         <option value="">请选择</option>
                         <option v-for="(item,index) in chongwuTypesList" v-bind:key="index"
                                 :value="item.codeIndex" :key="item.codeIndex">{{ item.indexName }}
                         </option>
                     </select>
                 </div>
                                                         
                 <div class="item-list">
                     <div class="lable"
                          :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"0","borderColor":"rgba(0,0,0,0)","backgroundColor":"transparent","color":"rgba(17, 16, 16, 1)","borderRadius":"0","textAlign":"right","borderWidth":"0","width":"auto","fontSize":"16px","borderStyle":"solid"}'>
                         审核状态</div>
                     <select :style='{"boxShadow":"0 0 0px rgba(0,0,0,0)","borderColor":"rgba(0, 0, 0, 0)","backgroundColor":"#ffffff","color":"#333","borderRadius":"8px","textAlign":"center","borderWidth":"2px","width":"140px","fontSize":"14px","borderStyle":"solid","height":"44px"}'
                             style="display:block"  v-model="searchForm.chongwujiyangYesnoTypes">
                         <option value="">请选择</option>
                         <option v-for="(item,index) in chongwujiyangYesnoTypesList" v-bind:key="index"
                                 :value="item.codeIndex" :key="item.codeIndex">{{ item.indexName }}
                         </option>
                     </select>
                 </div>
                        
                <button id="btn-search" :style='{"padding":"0 15px","boxShadow":"0 0 8px rgba(0,0,0,0)","margin":"0 0 0 10px","borderColor":"rgba(135, 206, 250, 1)","color":"#fff","borderRadius":"4px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"40px"}' type="button" class="layui-btn layui-btn-normal sub_backgroundColor">
                    <i v-if="true" class="layui-icon layui-icon-search"></i>搜索
                </button>
                <button v-if="isAuth('chongwujiyang','新增')" @click="jump('../chongwujiyang/add.html')" :style='{"padding":"0 15px","boxShadow":"0 0 8px rgba(0,0,0,0)","margin":"0 0 0 10px","borderColor":"rgba(135, 206, 250, 1)","backgroundColor":"rgba(135, 206, 250, 1)","color":"#fff","borderRadius":"4px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"40px"}' type="button" class="layui-btn btn-theme">
                    <i v-if="true" class="layui-icon">&#xe654;</i>添加
                </button>
            </form>

        </div>
    </div>
    <div class="pager" id="pager" :style="{textAlign:'center'}"></div>
</div>

<script src="../../xznstatic/js/bootstrap.min.js" type="text/javascript" charset="utf-8"></script>
<script src="../../layui/layui.js"></script>
<script src="../../js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="../../xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="../../xznstatic/css/element.min.css">
<script src="../../js/config.js"></script>
<script src="../../modules/config.js"></script>
<script src="../../js/utils.js"></script>

<script type="text/javascript">
    var vue = new Vue({
        el: '#app',
        data: {
            swiperList: [],
            chongwuTypesList: [],
            chongwujiyangYesnoTypesList: [],

            //查询条件
            searchForm: {
                page: 1
                ,limit: 8
                ,chongwuName: ""
                ,chongwuTypes: ""
                ,chongwujiyangYesnoTypes: ""
            },


            dataList: [],
        },
        filters: {
            subString: function(val) {
                if (val) {
                    val = val.replace(/<[^<>]+>/g, '').replace(/undefined/g, '');
                    if (val.length > 60) {
                        val = val.substring(0, 60);
                        val+='...';
                    }
                    return val;
                }
                return '';
            }
        },
        computed: {
        },
        methods: {
            isAuth(tablename, button) {
                return isFrontAuth(tablename, button);
            }
            ,jump(url) {
                jump(url);
            }
            ,jumpCheck(url,check1,check2) {
                if(check1 == "2" || check1 == 2){//级联表的逻辑删除字段[1:未删除 2:已删除]
                    layui.layer.msg("已经被删除", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                if(check2 == "2"  || check2 == 2){//是否下架[1:上架 2:下架]
                    layui.layer.msg("已经下架", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                this.jump(url);
            }
        }
    });

    layui.use(['layer', 'element', 'carousel', 'laypage', 'http', 'jquery', 'laydate', 'tinymce'], function() {
        var layer = layui.layer;
        var element = layui.element;
        var carousel = layui.carousel;
        var laypage = layui.laypage;
        var http = layui.http;
        var laydate = layui.laydate;
        var tinymce = layui.tinymce;
        window.jQuery = window.$ = jquery = layui.jquery;

        // var id = http.getParam('id');

        // 获取轮播图 数据
        http.request('config/list', 'get', {
            page: 1,
            limit: 5
        }, function (res) {
            if (res.data.list.length > 0) {
                let swiperList = [];
                res.data.list.forEach(element => {
                    if(element.value != null){
                        swiperList.push({
                            img: element.value
                        });
                    }
                });
                vue.swiperList = swiperList;

                vue.$nextTick(() => {
                    carousel.render({
                        elem: '#swiper',
                        width: '100%',
                        height: '450px',
                        arrow: 'hover',
                        anim: 'default',
                        autoplay: 'true',
                        interval: '3000',
                        indicator: 'inside'
                    });
                });
            }
        });


            //当前表的 宠物类型 字段 字典表查询
            chongwuTypesSelect();
            //当前表的 审核状态 字段 字典表查询
            chongwujiyangYesnoTypesSelect();
           //当前表的 宠物类型 字段 字典表查询方法
           function chongwuTypesSelect() {
               http.request("dictionary/page?page=1&limit=100&sort=&order=&dicCode=chongwu_types", 'get', {}, function (res) {
                   if(res.code == 0){
                       vue.chongwuTypesList = res.data.list;
                   }
               });
           }
           //当前表的 审核状态 字段 字典表查询方法
           function chongwujiyangYesnoTypesSelect() {
               http.request("dictionary/page?page=1&limit=100&sort=&order=&dicCode=chongwujiyang_yesno_types", 'get', {}, function (res) {
                   if(res.code == 0){
                       vue.chongwujiyangYesnoTypesList = res.data.list;
                   }
               });
           }


            // 分页列表
            pageList();

            // 搜索按钮
            jquery('#btn-search').click(function (e) {
                pageList();
            });

            function pageList() {
                // 获取列表数据
                http.request('chongwujiyang/list', 'get', vue.searchForm, function (res) {
                    vue.dataList = res.data.list;
                    // 分页
                    laypage.render({
                        elem: 'pager',
                        count: res.data.total,
                        limit: vue.searchForm.limit,
                        groups: 3,
                        layout: ["prev", "page", "next"],
                        jump: function (obj, first) {
                            vue.searchForm.page = obj.curr;//翻页
                            //首次不执行
                            if (!first) {
                                http.request('chongwujiyang/list', 'get', vue.searchForm, function (res1) {
                                    vue.dataList = res1.data.list;
                                })
                            }
                        }
                    });
                });
            }
    });

    window.xznSlide = function () {
        jQuery(".banner").slide({mainCell: ".bd ul", autoPlay: true, interTime: 5000});
        jQuery("#ifocus").slide({
            titCell: "#ifocus_btn li",
            mainCell: "#ifocus_piclist ul",
            effect: "leftLoop",
            delayTime: 200,
            autoPlay: true,
            triggerTime: 0
        });
        jQuery("#ifocus").slide({titCell: "#ifocus_btn li", mainCell: "#ifocus_tx ul", delayTime: 0, autoPlay: true});
        jQuery(".product_list").slide({
            mainCell: ".bd ul",
            autoPage: true,
            effect: "leftLoop",
            autoPlay: true,
            vis: 5,
            trigger: "click",
            interTime: 4000
        });
    };
</script>
</body>
</html>
