{"_from": "bn.js@^4.1.0", "_id": "bn.js@4.12.0", "_inBundle": false, "_integrity": "sha512-c98Bf3tPniI+scsdk237ku1Dc3ujXQTSgyiPUDEOe7tRkhrqridvh8klBv0HCEso1OLOYcHuCv/cS6DNxKH+ZA==", "_location": "/diffie-hellman/bn.js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "bn.js@^4.1.0", "name": "bn.js", "escapedName": "bn.js", "rawSpec": "^4.1.0", "saveSpec": null, "fetchSpec": "^4.1.0"}, "_requiredBy": ["/diffie-hellman"], "_resolved": "https://registry.npmjs.org/bn.js/-/bn.js-4.12.0.tgz", "_shasum": "775b3f278efbb9718eec7361f483fb36fbbfea88", "_spec": "bn.js@^4.1.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\diffie-hellman", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": {"buffer": false}, "bugs": {"url": "https://github.com/indutny/bn.js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Big number implementation in pure javascript", "devDependencies": {"istanbul": "^0.3.5", "mocha": "^2.1.0", "semistandard": "^7.0.4"}, "homepage": "https://github.com/indutny/bn.js", "keywords": ["BN", "BigNum", "Big number", "<PERSON><PERSON><PERSON>", "<PERSON>"], "license": "MIT", "main": "lib/bn.js", "name": "bn.js", "repository": {"type": "git", "url": "git+ssh://**************/indutny/bn.js.git"}, "scripts": {"lint": "semistandard", "test": "npm run lint && npm run unit", "unit": "mocha --reporter=spec test/*-test.js"}, "version": "4.12.0"}