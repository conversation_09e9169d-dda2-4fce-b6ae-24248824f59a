<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="752px" height="890px" version="1.1" content="&lt;mxfile userAgent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36&quot; version=&quot;7.9.8&quot; editor=&quot;www.draw.io&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;c7558073-3199-34d8-9f00-42111426c3f3&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-e1d5e7-1-ffffff-1-s-0"><stop offset="0%" style="stop-color:#e1d5e7"/><stop offset="100%" style="stop-color:#ffffff"/></linearGradient></defs><g transform="translate(0.5,0.5)"><rect x="519" y="299" width="131" height="87" fill="url(#mx-gradient-e1d5e7-1-ffffff-1-s-0)" stroke="#9673a6" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(564.5,306.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="38" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><u>Walker</u></b></div></div></foreignObject><text x="19" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="535" y="332" width="99" height="38" rx="5.7" ry="5.7" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(566.5,344.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 36px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">walk()</div></div></foreignObject><text x="17" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">walk()</text></switch></g><path d="M 650 343 L 650 343" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 650 343 L 650 343 L 650 343 L 650 343 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="519" y="158" width="131" height="87" fill="url(#mx-gradient-e1d5e7-1-ffffff-1-s-0)" stroke="#9673a6" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(554.5,165.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="58" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 58px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><u>Generator</u></b></div></div></foreignObject><text x="29" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;b&gt;&lt;u&gt;Generator&lt;/u&gt;&lt;/b&gt;</text></switch></g><rect x="535" y="191" width="99" height="38" rx="5.7" ry="5.7" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(554.5,203.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="58" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 60px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">generate()</div></div></foreignObject><text x="29" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">generate()</text></switch></g><path d="M 650 202 L 650 202" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 650 202 L 650 202 L 650 202 L 650 202 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="264" y="126" width="120" height="70" fill="url(#mx-gradient-e1d5e7-1-ffffff-1-s-0)" stroke="#9673a6" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(304.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="37" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 39px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><u>Parser</u></b></div></div></foreignObject><text x="19" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="279" y="154" width="90" height="29" rx="4.35" ry="4.35" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(302.5,161.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="41" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 41px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">parse()</div></div></foreignObject><text x="21" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">parse()</text></switch></g><rect x="112" y="237" width="144" height="164" fill="url(#mx-gradient-e1d5e7-1-ffffff-1-s-0)" stroke="#9673a6" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(170.5,244.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="25" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 25px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><u>utils</u></b></div></div></foreignObject><text x="13" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 279 169 L 189 169 L 189 130.24" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 189 122.24 L 193 130.24 L 185 130.24 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="1" y="467" width="375" height="260" fill="url(#mx-gradient-e1d5e7-1-ffffff-1-s-0)" stroke="#9673a6" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(148.5,474.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="78" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 80px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><u>Lexer</u> (lexer.*)</b></div></div></foreignObject><text x="39" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;b&gt;&lt;u&gt;Lexer&lt;/u&gt; (lexer.*)&lt;/b&gt;</text></switch></g><rect x="401" y="467" width="350" height="260" fill="url(#mx-gradient-e1d5e7-1-ffffff-1-s-0)" stroke="#9673a6" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(497.5,474.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="155" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 155px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><u>Grammar</u> (lexer.grammar.*)</b></div></div></foreignObject><text x="78" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;b&gt;&lt;u&gt;Grammar&lt;/u&gt; (lexer.grammar.*)&lt;/b&gt;</text></switch></g><rect x="635" y="546" width="97" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(655.5,562.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="54" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 54px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Grammar<br />(String)</div></div></foreignObject><text x="27" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">Grammar&lt;br/&gt;(String)</text></switch></g><rect x="534" y="500" width="100" height="32" rx="4.8" ry="4.8" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(562.5,509.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="41" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 41px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">parse()</div></div></foreignObject><text x="21" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">parse()</text></switch></g><path d="M 684 546 L 684 516 L 644.24 516" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 636.24 516 L 644.24 512 L 644.24 520 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="423" y="546" width="90" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(433.5,569.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="67" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 67px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Syntax AST</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">Syntax AST</text></switch></g><path d="M 534 516 L 491 516 L 491 535.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 491 543.76 L 487 535.76 L 495 535.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="423" y="678" width="90" height="32" rx="4.8" ry="4.8" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(449.5,687.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="35" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 35px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">walk()</div></div></foreignObject><text x="18" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">walk()</text></switch></g><path d="M 468 606 L 468 667.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 468 675.76 L 464 667.76 L 472 667.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 634 637 L 684 637 L 684 616.24" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 684 608.24 L 688 616.24 L 680 616.24 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="534" y="621" width="100" height="32" rx="4.8" ry="4.8" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(553.5,630.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="59" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 59px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">generate()</div></div></foreignObject><text x="30" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">generate()</text></switch></g><path d="M 491 606 L 491 637 L 523.76 637" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 531.76 637 L 523.76 641 L 523.76 633 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="1" y="313" width="90" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(28.5,336.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="33" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 33px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">JSON</div></div></foreignObject><text x="17" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">JSON</text></switch></g><path d="M 424 91 L 259.24 91" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 251.24 91 L 259.24 87 L 259.24 95 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 424 91 L 397 91 L 397 169 L 379.24 169" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 371.24 169 L 379.24 165 L 379.24 173 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="424" y="61" width="154" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(478.5,77.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="43" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 43px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">CSS<br />(String)</div></div></foreignObject><text x="22" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">CSS&lt;br/&gt;(String)</text></switch></g><path d="M 274 328 L 249.24 328" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 241.24 328 L 249.24 324 L 249.24 332 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 349 306 L 349 268 L 585 268 L 585 239.24" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 585 231.24 L 589 239.24 L 581 239.24 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 274 373 L 249.24 373" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 241.24 373 L 249.24 369 L 249.24 377 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 374 351 L 524.76 351" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 532.76 351 L 524.76 355 L 524.76 347 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 324 395 L 324 447 L 124 447 L 124 565.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 124 573.76 L 120 565.76 L 128 565.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 324 395 L 324 447 L 291 447 L 291 489.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 291 497.76 L 287 489.76 L 295 489.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 324 395 L 324 447 L 52 447 L 52 642.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 52 650.76 L 48 642.76 L 56 642.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="274" y="306" width="100" height="89" rx="13.35" ry="13.35" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(311.5,343.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="24" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 25px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">AST</div></div></foreignObject><text x="12" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">AST</text></switch></g><rect x="129" y="267" width="110" height="32" rx="4.8" ry="4.8" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(132.5,276.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="101" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 101px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">fromPlainObject()</div></div></foreignObject><text x="51" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">fromPlainObject()</text></switch></g><rect x="129" y="312" width="110" height="32" rx="4.8" ry="4.8" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(140.5,321.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="85" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 87px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">toPlainObject()</div></div></foreignObject><text x="43" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">toPlainObject()</text></switch></g><path d="M 129 328 L 101.24 328" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 93.24 328 L 101.24 324 L 101.24 332 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 46 313 L 46 283 L 118.76 283" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 126.76 283 L 118.76 287 L 118.76 279 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 239 283 L 299 283 L 299 295.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 299 303.76 L 295 295.76 L 303 295.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="594" y="61" width="154" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(604.5,77.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="131" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 131px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Source Map<br />(SourceMapGenerator)</div></div></foreignObject><text x="66" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">Source Map&lt;br/&gt;(SourceMapGenerator)</text></switch></g><path d="M 535 210 L 501 210 L 501 131.24" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 501 123.24 L 505 131.24 L 497 131.24 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 324 183 L 324 295.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 324 303.76 L 320 295.76 L 328 295.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 634 210 L 671 210 L 671 131.24" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 671 123.24 L 675 131.24 L 667 131.24 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 423 576 L 401 576 L 401 597 L 386.24 597" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 378.24 597 L 386.24 593 L 386.24 601 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 184 389 L 184 420 L 299 420 L 299 405.24" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 299 397.24 L 303 405.24 L 295 405.24 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="129" y="357" width="110" height="32" rx="4.8" ry="4.8" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(163.5,366.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="39" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 41px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">clone()</div></div></foreignObject><text x="20" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">clone()</text></switch></g><path d="M 328 560 L 328 742.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 328 750.76 L 324 742.76 L 332 742.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="218" y="500" width="147" height="60" rx="9" ry="9" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(236.5,509.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="108" height="40" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 110px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>matchDeclaration()</div><div>matchProperty()</div><div>matchType()</div></div></div></foreignObject><text x="54" y="26" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">[Not supported by viewer]</text></switch></g><path d="M 196 637 L 196 742.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 196 750.76 L 192 742.76 L 200 742.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="69" y="576" width="220" height="60" rx="9" ry="9" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(83.5,585.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="189" height="40" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 189px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div>findValueFragments()</div><div>findDeclarationValueFragments()</div><div>findAllFragments()</div></div></div></foreignObject><text x="95" y="26" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">[Not supported by viewer]</text></switch></g><path d="M 89 713 L 89 817.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 89 825.76 L 85 817.76 L 93 817.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="15" y="653" width="147" height="60" rx="9" ry="9" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(39.5,676.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="96" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 96px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">checkStructure()<br /></div></div></foreignObject><text x="48" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">checkStructure()&lt;br&gt;</text></switch></g><rect x="283" y="753" width="90" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(291.5,776.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="71" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 73px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">MatchResult</div></div></foreignObject><text x="36" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">MatchResult</text></switch></g><rect x="132" y="753" width="128" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(142.5,776.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 105px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Array of Fragment</div></div></foreignObject><text x="53" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">Array of Fragment</text></switch></g><rect x="35" y="828" width="107" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(48.5,851.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="78" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 80px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Array of Error</div></div></foreignObject><text x="39" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">Array of Error</text></switch></g><rect x="37" y="149" width="121" height="60" rx="9" ry="9" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(47.5,172.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="98" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 98px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Stream of tokens</div></div></foreignObject><text x="49" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">Stream of tokens</text></switch></g><path d="M 129 91 L 98 91 L 98 138.76" fill="none" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 98 146.76 L 94 138.76 L 102 138.76 Z" fill="#990000" stroke="#990000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(249.5,-0.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="229" height="31" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 28px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 231px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">CSSTree API map</div></div></foreignObject><text x="115" y="30" fill="#000000" text-anchor="middle" font-size="28px" font-family="Helvetica">CSSTree API map</text></switch></g><rect x="129" y="62" width="120" height="58" fill="url(#mx-gradient-e1d5e7-1-ffffff-1-s-0)" stroke="#9673a6" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(160.5,69.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="55" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 57px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><u>Tokenizer</u></b></div></div></foreignObject><text x="28" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="641" y="754" width="100" height="32" rx="4.8" ry="4.8" fill="#ffffcc" stroke="#c2a54e" stroke-width="2" pointer-events="none"/><g transform="translate(676.5,763.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="27" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 27px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Data</div></div></foreignObject><text x="14" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">Data</text></switch></g><rect x="641" y="795" width="100" height="35" rx="5.25" ry="5.25" fill="#e6ffcc" stroke="#82b366" stroke-width="2" pointer-events="none"/><g transform="translate(656.5,805.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="67" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 67px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">API method<br /></div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">API method&lt;br&gt;</text></switch></g><rect x="641" y="839" width="100" height="35" fill="url(#mx-gradient-e1d5e7-1-ffffff-1-s-0)" stroke="#9673a6" stroke-width="2" stroke-dasharray="2 2" pointer-events="none"/><g transform="translate(669.5,849.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="42" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 43px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><u>Module</u></b></div></div></foreignObject><text x="21" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g></g></svg>