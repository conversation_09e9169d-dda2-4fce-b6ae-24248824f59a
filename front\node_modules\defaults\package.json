{"_from": "defaults@^1.0.3", "_id": "defaults@1.0.4", "_inBundle": false, "_integrity": "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==", "_location": "/defaults", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "defaults@^1.0.3", "name": "defaults", "escapedName": "defaults", "rawSpec": "^1.0.3", "saveSpec": null, "fetchSpec": "^1.0.3"}, "_requiredBy": ["/wcwidth"], "_resolved": "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz", "_shasum": "b0b02062c1e2aa62ff5d9528f0f98baa90978d7a", "_spec": "defaults@^1.0.3", "_where": "C:\\code\\t\\t101\\front\\node_modules\\wcwidth", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/sindresorhus/node-defaults/issues"}, "bundleDependencies": false, "dependencies": {"clone": "^1.0.2"}, "deprecated": false, "description": "merge single level defaults over a config object", "devDependencies": {"tap": "^2.0.0"}, "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/node-defaults#readme", "keywords": ["config", "defaults", "options", "object", "merge", "assign", "properties", "deep"], "license": "MIT", "main": "index.js", "name": "defaults", "repository": {"type": "git", "url": "git://github.com/sindresorhus/node-defaults.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.4"}