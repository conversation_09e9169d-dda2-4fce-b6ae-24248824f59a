{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\xuangmu\\yuanma\\code1\\front\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\views\\register.vue", "mtime": 1752474908934}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBydWxlRm9ybToge30sCiAgICAgIHRhYmxlTmFtZTogIiIsCiAgICAgIHJ1bGVzOiB7fSwKICAgICAgc2V4VHlwZXNPcHRpb25zOiBbXQogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICBsZXQgdGFibGUgPSB0aGlzLiRzdG9yYWdlLmdldCgibG9naW5UYWJsZSIpOwogICAgdGhpcy50YWJsZU5hbWUgPSB0YWJsZTsKCiAgICAvL+e6p+iBlOihqOeahOS4i+a<PERSON>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"}, {"version": 3, "names": ["data", "ruleForm", "tableName", "rules", "sexTypesOptions", "mounted", "table", "$storage", "get", "methods", "getUUID", "Date", "getTime", "close", "$router", "push", "path", "login", "username", "$message", "error", "password", "repetitionPassword", "yo<PERSON><PERSON><PERSON><PERSON>", "yonghuPhone", "$validate", "isMobile", "yonghuEmail", "isEmail", "ziyuanzheName", "ziyuanzhePhone", "ziyuanzheEmail", "$http", "url", "method", "then", "code", "message", "type", "duration", "onClose", "replace", "msg"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\r\n    <div :style=\"{ backgroundImage: 'url(' + require('@/assets/img/bg.jpg') + ')', backgroundSize: 'cover' }\">\r\n        <div class=\"container\">\r\n            <div class=\"login-form\" style=\"backgroundColor:rgba(183, 174, 174, 0.5);borderRadius:22px\">\r\n                <h1 class=\"h1\" style=\"color:#000;fontSize:28px;\">流浪动物管理系统注册</h1>\r\n                <el-form class=\"rgs-form\" label-width=\"120px\">\r\n                        <el-form-item label=\"账号\" class=\"input\">\r\n                            <el-input v-model=\"ruleForm.username\" autocomplete=\"off\" placeholder=\"账号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"密码\" class=\"input\">\r\n                            <el-input type=\"password\" v-model=\"ruleForm.password\" autocomplete=\"off\" show-password/>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"重复密码\" class=\"input\">\r\n                            <el-input type=\"password\" v-model=\"ruleForm.repetitionPassword\" autocomplete=\"off\" show-password/>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"用户姓名\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuName\" autocomplete=\"off\" placeholder=\"用户姓名\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"手机号\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuPhone\" autocomplete=\"off\" placeholder=\"手机号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"电子邮箱\" class=\"input\" v-if=\"tableName=='yonghu'\">\r\n                            <el-input v-model=\"ruleForm.yonghuEmail\" autocomplete=\"off\" placeholder=\"电子邮箱\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"自愿者姓名\" class=\"input\" v-if=\"tableName=='ziyuanzhe'\">\r\n                            <el-input v-model=\"ruleForm.ziyuanzheName\" autocomplete=\"off\" placeholder=\"自愿者姓名\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"手机号\" class=\"input\" v-if=\"tableName=='ziyuanzhe'\">\r\n                            <el-input v-model=\"ruleForm.ziyuanzhePhone\" autocomplete=\"off\" placeholder=\"手机号\"  />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"电子邮箱\" class=\"input\" v-if=\"tableName=='ziyuanzhe'\">\r\n                            <el-input v-model=\"ruleForm.ziyuanzheEmail\" autocomplete=\"off\" placeholder=\"电子邮箱\"  />\r\n                        </el-form-item>\r\n                        <div style=\"display: flex;flex-wrap: wrap;width: 100%;justify-content: center;\">\r\n                            <el-button class=\"btn\" type=\"primary\" @click=\"login()\">注册</el-button>\r\n                            <el-button class=\"btn close\" type=\"primary\" @click=\"close()\">取消</el-button>\r\n                        </div>\r\n                </el-form>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                ruleForm: {\r\n                },\r\n                tableName:\"\",\r\n                rules: {},\r\n                sexTypesOptions : [],\r\n            };\r\n        },\r\n        mounted(){\r\n            let table = this.$storage.get(\"loginTable\");\r\n            this.tableName = table;\r\n\r\n            //级联表的下拉框查询\r\n\r\n        },\r\n        methods: {\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            close(){\r\n                this.$router.push({ path: \"/login\" });\r\n            },\r\n            // 注册\r\n            login() {\r\n\r\n                            if((!this.ruleForm.username)){\r\n                                this.$message.error('账号不能为空');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.password)){\r\n                                this.$message.error('密码不能为空');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.repetitionPassword)){\r\n                                this.$message.error('重复密码不能为空');\r\n                                return\r\n                            }\r\n                            if(this.ruleForm.repetitionPassword != this.ruleForm.password){\r\n                                this.$message.error('密码和重复密码不一致');\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.yonghuName)&& 'yonghu'==this.tableName){\r\n                                this.$message.error('用户姓名不能为空');\r\n                                return\r\n                            }\r\n                             if('yonghu' == this.tableName && this.ruleForm.yonghuPhone&&(!this.$validate.isMobile(this.ruleForm.yonghuPhone))){\r\n                                 this.$message.error('手机应输入手机格式');\r\n                                 return\r\n                             }\r\n                            if('yonghu' == this.tableName && this.ruleForm.yonghuEmail&&(!this.$validate.isEmail(this.ruleForm.yonghuEmail))){\r\n                                this.$message.error(\"邮箱应输入邮件格式\");\r\n                                return\r\n                            }\r\n                            if((!this.ruleForm.ziyuanzheName)&& 'ziyuanzhe'==this.tableName){\r\n                                this.$message.error('自愿者姓名不能为空');\r\n                                return\r\n                            }\r\n                             if('ziyuanzhe' == this.tableName && this.ruleForm.ziyuanzhePhone&&(!this.$validate.isMobile(this.ruleForm.ziyuanzhePhone))){\r\n                                 this.$message.error('手机应输入手机格式');\r\n                                 return\r\n                             }\r\n                            if('ziyuanzhe' == this.tableName && this.ruleForm.ziyuanzheEmail&&(!this.$validate.isEmail(this.ruleForm.ziyuanzheEmail))){\r\n                                this.$message.error(\"邮箱应输入邮件格式\");\r\n                                return\r\n                            }\r\n                this.$http({\r\n                    url: `${this.tableName}/register`,\r\n                    method: \"post\",\r\n                    data:this.ruleForm\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                    this.$message({\r\n                        message: \"注册成功,请登录后在个人中心页面补充个人数据\",\r\n                        type: \"success\",\r\n                        duration: 1500,\r\n                        onClose: () => {\r\n                        this.$router.replace({ path: \"/login\" });\r\n                }\r\n                });\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked .el-radio__inner {\r\n        border-color: #00c292;\r\n        background: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .el-radio__input.is-checked+.el-radio__label {\r\n        color: #00c292;\r\n    }\r\n\r\n    .h1 {\r\n        margin-top: 10px;\r\n    }\r\n\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    // .container {\r\n       //    min-height: 100vh;\r\n       //    text-align: center;\r\n       //    // background-color: #00c292;\r\n       //    padding-top: 20vh;\r\n       //    background-image: url(../assets/img/bg.jpg);\r\n       //    background-size: 100% 100%;\r\n       //    opacity: 0.9;\r\n       //  }\r\n\r\n    // .login-form:before {\r\n       // \tvertical-align: middle;\r\n       // \tdisplay: inline-block;\r\n       // }\r\n\r\n    // .login-form {\r\n       // \tmax-width: 500px;\r\n       // \tpadding: 20px 0;\r\n       // \twidth: 80%;\r\n       // \tposition: relative;\r\n       // \tmargin: 0 auto;\r\n\r\n    // \t.label {\r\n          // \t\tmin-width: 60px;\r\n          // \t}\r\n\r\n    // \t.input-group {\r\n          // \t\tmax-width: 500px;\r\n          // \t\tpadding: 20px 0;\r\n          // \t\twidth: 80%;\r\n          // \t\tposition: relative;\r\n          // \t\tmargin: 0 auto;\r\n          // \t\tdisplay: flex;\r\n          // \t\talign-items: center;\r\n\r\n    // \t\t.input-container {\r\n              // \t\t\tdisplay: inline-block;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\ttext-align: left;\r\n              // \t\t\tmargin-left: 10px;\r\n              // \t\t}\r\n\r\n    // \t\t.icon {\r\n              // \t\t\twidth: 30px;\r\n              // \t\t\theight: 30px;\r\n              // \t\t}\r\n\r\n    // \t\t.input {\r\n              // \t\t\tposition: relative;\r\n              // \t\t\tz-index: 2;\r\n              // \t\t\tfloat: left;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\tmargin-bottom: 0;\r\n              // \t\t\tbox-shadow: none;\r\n              // \t\t\tborder-top: 0px solid #ccc;\r\n              // \t\t\tborder-left: 0px solid #ccc;\r\n              // \t\t\tborder-right: 0px solid #ccc;\r\n              // \t\t\tborder-bottom: 1px solid #ccc;\r\n              // \t\t\tpadding: 0px;\r\n              // \t\t\tresize: none;\r\n              // \t\t\tborder-radius: 0px;\r\n              // \t\t\tdisplay: block;\r\n              // \t\t\twidth: 100%;\r\n              // \t\t\theight: 34px;\r\n              // \t\t\tpadding: 6px 12px;\r\n              // \t\t\tfont-size: 14px;\r\n              // \t\t\tline-height: 1.42857143;\r\n              // \t\t\tcolor: #555;\r\n              // \t\t\tbackground-color: #fff;\r\n              // \t\t}\r\n\r\n    // \t}\r\n    // }\r\n\r\n    .nk-navigation {\r\n        margin-top: 15px;\r\n\r\n    a {\r\n        display: inline-block;\r\n        color: #fff;\r\n        background: rgba(255, 255, 255, .2);\r\n        width: 100px;\r\n        height: 50px;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        justify-content: center;\r\n        padding: 0 20px;\r\n    }\r\n\r\n    .icon {\r\n        margin-left: 10px;\r\n        width: 30px;\r\n        height: 30px;\r\n    }\r\n    }\r\n\r\n    .register-container {\r\n        margin-top: 10px;\r\n\r\n    a {\r\n        display: inline-block;\r\n        color: #fff;\r\n        max-width: 500px;\r\n        height: 50px;\r\n        border-radius: 30px;\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        justify-content: center;\r\n        padding: 0 20px;\r\n\r\n    div {\r\n        margin-left: 10px;\r\n    }\r\n    }\r\n    }\r\n\r\n    .container {\r\n        height: 100vh;\r\n        background-position: center center;\r\n        background-size: cover;\r\n        background-repeat: no-repeat;\r\n\r\n    .login-form {\r\n        right: 50%;\r\n        top: 50%;\r\n        height: auto;\r\n        transform: translate3d(50%, -50%, 0);\r\n        border-radius: 10px;\r\n        background-color: rgba(255,255,255,.5);\r\n        width: 420px;\r\n        padding: 30px 30px 40px 30px;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n\r\n    .h1 {\r\n        margin: 0;\r\n        text-align: center;\r\n        line-height: 54px;\r\n        font-size: 24px;\r\n        color: #000;\r\n    }\r\n\r\n    .rgs-form {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: center;\r\n\r\n    .input {\r\n        width: 100%;\r\n\r\n    & ::v-deep .el-form-item__label {\r\n          line-height: 40px;\r\n          color: rgba(17, 16, 16, 1);\r\n          font-size: #606266;\r\n      }\r\n\r\n    & ::v-deep .el-input__inner {\r\n          height: 40px;\r\n          color: rgba(23, 24, 26, 1);\r\n          font-size: 14px;\r\n          border-width: 1px;\r\n          border-style: solid;\r\n          border-color: #606266;\r\n          border-radius: 22px;\r\n          background-color: #fff;\r\n      }\r\n    }\r\n\r\n    .btn {\r\n        margin: 0 10px;\r\n        width: 88px;\r\n        height: 44px;\r\n        color: #fff;\r\n        font-size: 14px;\r\n        border-width: 1px;\r\n        border-style: solid;\r\n        border-color: #409EFF;\r\n        border-radius: 22px;\r\n        background-color: #409EFF;\r\n    }\r\n\r\n    .close {\r\n        margin: 0 10px;\r\n        width: 88px;\r\n        height: 44px;\r\n        color: #409EFF;\r\n        font-size: 14px;\r\n        border-width: 1px;\r\n        border-style: solid;\r\n        border-color: #409EFF;\r\n        border-radius: 22px;\r\n        background-color: #FFF;\r\n    }\r\n\r\n    }\r\n    }\r\n    }\r\n</style>\r\n"], "mappings": "AA2CA;EACAA,KAAA;IACA;MACAC,QAAA,GACA;MACAC,SAAA;MACAC,KAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACA,IAAAC,KAAA,QAAAC,QAAA,CAAAC,GAAA;IACA,KAAAN,SAAA,GAAAI,KAAA;;IAEA;EAEA;EACAG,OAAA;IACA;IACAC,QAAA;MACA,WAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAC,MAAA;MAEA,UAAAhB,QAAA,CAAAiB,QAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAnB,QAAA,CAAAoB,QAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAnB,QAAA,CAAAqB,kBAAA;QACA,KAAAH,QAAA,CAAAC,KAAA;QACA;MACA;MACA,SAAAnB,QAAA,CAAAqB,kBAAA,SAAArB,QAAA,CAAAoB,QAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAnB,QAAA,CAAAsB,UAAA,qBAAArB,SAAA;QACA,KAAAiB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,qBAAAlB,SAAA,SAAAD,QAAA,CAAAuB,WAAA,UAAAC,SAAA,CAAAC,QAAA,MAAAzB,QAAA,CAAAuB,WAAA;QACA,KAAAL,QAAA,CAAAC,KAAA;QACA;MACA;MACA,qBAAAlB,SAAA,SAAAD,QAAA,CAAA0B,WAAA,UAAAF,SAAA,CAAAG,OAAA,MAAA3B,QAAA,CAAA0B,WAAA;QACA,KAAAR,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAnB,QAAA,CAAA4B,aAAA,wBAAA3B,SAAA;QACA,KAAAiB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,wBAAAlB,SAAA,SAAAD,QAAA,CAAA6B,cAAA,UAAAL,SAAA,CAAAC,QAAA,MAAAzB,QAAA,CAAA6B,cAAA;QACA,KAAAX,QAAA,CAAAC,KAAA;QACA;MACA;MACA,wBAAAlB,SAAA,SAAAD,QAAA,CAAA8B,cAAA,UAAAN,SAAA,CAAAG,OAAA,MAAA3B,QAAA,CAAA8B,cAAA;QACA,KAAAZ,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAY,KAAA;QACAC,GAAA,UAAA/B,SAAA;QACAgC,MAAA;QACAlC,IAAA,OAAAC;MACA,GAAAkC,IAAA;QAAAnC;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAoC,IAAA;UACA,KAAAjB,QAAA;YACAkB,OAAA;YACAC,IAAA;YACAC,QAAA;YACAC,OAAA,EAAAA,CAAA;cACA,KAAA1B,OAAA,CAAA2B,OAAA;gBAAAzB,IAAA;cAAA;YACA;UACA;QACA;UACA,KAAAG,QAAA,CAAAC,KAAA,CAAApB,IAAA,CAAA0C,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}