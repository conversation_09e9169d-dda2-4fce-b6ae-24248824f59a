{"_from": "npm-run-path@^4.0.0", "_id": "npm-run-path@4.0.1", "_inBundle": false, "_integrity": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==", "_location": "/default-gateway/npm-run-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "npm-run-path@^4.0.0", "name": "npm-run-path", "escapedName": "npm-run-path", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/default-gateway/execa"], "_resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "_shasum": "b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea", "_spec": "npm-run-path@^4.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\default-gateway\\node_modules\\execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/npm-run-path/issues"}, "bundleDependencies": false, "dependencies": {"path-key": "^3.0.0"}, "deprecated": false, "description": "Get your PATH prepended with locally installed binaries", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/npm-run-path#readme", "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "license": "MIT", "name": "npm-run-path", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/npm-run-path.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.0.1"}