{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\front\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\src\\App.vue", "mtime": 1649064848557}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJhcHAiCn07"}, {"version": 3, "names": ["name"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\" class=\"\">\r\n    <router-view></router-view>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"app\",\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n*{\r\n  padding: 0;\r\n  margin:0;\r\n}\r\nhtml,body{\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n#app{\r\n  height:100%;\r\n}\r\nbody {\r\n  padding: 0;\r\n  margin: 0;\r\n  \r\n}\r\n</style>\r\n"], "mappings": "AAOA;EACAA,IAAA;AACA", "ignoreList": []}]}