{"_from": "detect-node@^2.0.4", "_id": "detect-node@2.1.0", "_inBundle": false, "_integrity": "sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==", "_location": "/detect-node", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "detect-node@^2.0.4", "name": "detect-node", "escapedName": "detect-node", "rawSpec": "^2.0.4", "saveSpec": null, "fetchSpec": "^2.0.4"}, "_requiredBy": ["/spdy-transport"], "_resolved": "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz", "_shasum": "c9c70775a49c3d03bc2c06d9a73be550f978f8b1", "_spec": "detect-node@^2.0.4", "_where": "C:\\code\\t\\t101\\front\\node_modules\\spdy-transport", "author": {"name": "<PERSON><PERSON>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/iliakan/detect-node/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Detect Node.JS (as opposite to browser environment) (reliable)", "homepage": "https://github.com/iliakan/detect-node", "keywords": ["detect", "node"], "license": "MIT", "main": "index.js", "module": "index.esm.js", "name": "detect-node", "repository": {"type": "git", "url": "git+https://github.com/iliakan/detect-node.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "2.1.0"}