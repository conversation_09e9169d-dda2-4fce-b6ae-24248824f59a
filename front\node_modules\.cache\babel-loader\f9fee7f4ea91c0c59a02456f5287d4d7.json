{"remainingRequest": "C:\\code\\t\\t101\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\code\\t\\t101\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\code\\t\\t101\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\code\\t\\t101\\front\\src\\components\\index\\IndexHeader.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\code\\t\\t101\\front\\src\\components\\index\\IndexHeader.vue", "mtime": 1620012264000}, {"path": "C:\\code\\t\\t101\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\t101\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\code\\t\\t101\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\t101\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcnVsZUZvcm06IHt9LAogICAgICB1c2VyOiB7fSwKICAgICAgaGVhZHM6IHsKICAgICAgICAiaGVhZExvZ291dEZvbnRIb3ZlckNvbG9yIjogIiNmZmYiLAogICAgICAgICJoZWFkRm9udFNpemUiOiAiMjBweCIsCiAgICAgICAgImhlYWRVc2VySW5mb0ZvbnRDb2xvciI6ICIjMzMzIiwKICAgICAgICAiaGVhZEJveFNoYWRvdyI6ICIwIDFweCA2cHggIzQ0NCIsCiAgICAgICAgImhlYWRUaXRsZUltZ0hlaWdodCI6ICI0NHB4IiwKICAgICAgICAiaGVhZExvZ291dEZvbnRIb3ZlckJnQ29sb3IiOiAiIzMzMyIsCiAgICAgICAgImhlYWRGb250Q29sb3IiOiAiIzAwMCIsCiAgICAgICAgImhlYWRUaXRsZUltZyI6IGZhbHNlLAogICAgICAgICJoZWFkSGVpZ2h0IjogIjYwcHgiLAogICAgICAgICJoZWFkVGl0bGVJbWdCb3JkZXJSYWRpdXMiOiAiMjJweCIsCiAgICAgICAgImhlYWRUaXRsZUltZ1VybCI6ICJodHRwOi8vY29kZWdlbi5jYWlob25neS5jbi8yMDIwMTAyMS9jYzdkNDVkOWM4MTY0YjU4YjE4MzUxNzY0ZWJhOWJlMS5qcGciLAogICAgICAgICJoZWFkQmdDb2xvciI6ICIjQzU5RjIyIiwKICAgICAgICAiaGVhZFRpdGxlSW1nQm94U2hhZG93IjogIjAgMXB4IDZweCAjNDQ0IiwKICAgICAgICAiaGVhZExvZ291dEZvbnRDb2xvciI6ICIjMzMzIiwKICAgICAgICAiaGVhZFVzZXJJbmZvRm9udFNpemUiOiAiMTZweCIsCiAgICAgICAgImhlYWRUaXRsZUltZ1dpZHRoIjogIjQ0cHgiLAogICAgICAgICJoZWFkVGl0bGVTdHlsZSI6ICIyIiwKICAgICAgICAiaGVhZExvZ291dEZvbnRTaXplIjogIjE2cHgiCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5zZXRIZWFkZXJTdHlsZSgpOwogIH0sCiAgbW91bnRlZCgpIHsKICAgIGxldCBzZXNzaW9uVGFibGUgPSB0aGlzLiRzdG9yYWdlLmdldCgic2Vzc2lvblRhYmxlIik7CiAgICB0aGlzLiRodHRwKHsKICAgICAgdXJsOiBzZXNzaW9uVGFibGUgKyAnL3Nlc3Npb24nLAogICAgICBtZXRob2Q6ICJnZXQiCiAgICB9KS50aGVuKCh7CiAgICAgIGRhdGEKICAgIH0pID0+IHsKICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgdGhpcy51c2VyID0gZGF0YS5kYXRhOwogICAgICB9IGVsc2UgewogICAgICAgIGxldCBtZXNzYWdlID0gdGhpcy4kbWVzc2FnZTsKICAgICAgICBtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsKICAgICAgfQogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBvbkxvZ291dCgpIHsKICAgICAgbGV0IHN0b3JhZ2UgPSB0aGlzLiRzdG9yYWdlOwogICAgICBsZXQgcm91dGVyID0gdGhpcy4kcm91dGVyOwogICAgICBzdG9yYWdlLmNsZWFyKCk7CiAgICAgIHJvdXRlci5yZXBsYWNlKHsKICAgICAgICBuYW1lOiAibG9naW4iCiAgICAgIH0pOwogICAgfSwKICAgIG9uSW5kZXhUYXAoKSB7CiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gYCR7dGhpcy4kYmFzZS5pbmRleFVybH1gOwogICAgfSwKICAgIHNldEhlYWRlclN0eWxlKCkgewogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLm5hdmJhciAucmlnaHQtbWVudSAubG9nb3V0JykuZm9yRWFjaChlbCA9PiB7CiAgICAgICAgICBlbC5hZGRFdmVudExpc3RlbmVyKCJtb3VzZWVudGVyIiwgZSA9PiB7CiAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7CiAgICAgICAgICAgIGVsLnN0eWxlLmJhY2tncm91bmRDb2xvciA9IHRoaXMuaGVhZHMuaGVhZExvZ291dEZvbnRIb3ZlckJnQ29sb3I7CiAgICAgICAgICAgIGVsLnN0eWxlLmNvbG9yID0gdGhpcy5oZWFkcy5oZWFkTG9nb3V0Rm9udEhvdmVyQ29sb3I7CiAgICAgICAgICB9KTsKICAgICAgICAgIGVsLmFkZEV2ZW50TGlzdGVuZXIoIm1vdXNlbGVhdmUiLCBlID0+IHsKICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTsKICAgICAgICAgICAgZWwuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gInRyYW5zcGFyZW50IjsKICAgICAgICAgICAgZWwuc3R5bGUuY29sb3IgPSB0aGlzLmhlYWRzLmhlYWRMb2dvdXRGb250Q29sb3I7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["data", "dialogVisible", "ruleForm", "user", "heads", "created", "setHeaderStyle", "mounted", "sessionTable", "$storage", "get", "$http", "url", "method", "then", "code", "message", "$message", "error", "msg", "methods", "onLogout", "storage", "router", "$router", "clear", "replace", "name", "onIndexTap", "window", "location", "href", "$base", "indexUrl", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "addEventListener", "e", "stopPropagation", "style", "backgroundColor", "headLogoutFontHoverBgColor", "color", "headLogoutFontHoverColor", "headLogoutFontColor"], "sources": ["src/components/index/IndexHeader.vue"], "sourcesContent": ["<template>\r\n\t<!-- <el-header>\r\n\t\t<el-menu background-color=\"#00c292\" text-color=\"#FFFFFF\" active-text-color=\"#FFFFFF\" mode=\"horizontal\">\r\n\t\t\t<div class=\"fl title\">{{this.$project.projectName}}</div>\r\n\t\t\t<div class=\"fr logout\" style=\"display:flex;\">\r\n\t\t\t\t<el-menu-item index=\"3\">\r\n\t\t\t\t\t<div>{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n\t\t\t\t</el-menu-item>\r\n\t\t\t\t<el-menu-item @click=\"onLogout\" index=\"2\">\r\n\t\t\t\t\t<div>退出登录</div>\r\n\t\t\t\t</el-menu-item>\r\n\t\t\t</div>\r\n\t\t</el-menu>\r\n\t</el-header> -->\r\n\t<div class=\"navbar\" :style=\"{backgroundColor:heads.headBgColor,height:heads.headHeight,boxShadow:heads.headBoxShadow,lineHeight:heads.headHeight}\">\r\n\t\t<div class=\"title-menu\" :style=\"{justifyContent:heads.headTitleStyle=='1'?'flex-start':'center'}\">\r\n\t\t\t<el-image v-if=\"heads.headTitleImg\" class=\"title-img\" :style=\"{width:heads.headTitleImgWidth,height:heads.headTitleImgHeight,boxShadow:heads.headTitleImgBoxShadow,borderRadius:heads.headTitleImgBorderRadius}\" :src=\"heads.headTitleImgUrl\" fit=\"cover\"></el-image>\r\n\t\t\t<div class=\"title-name\" :style=\"{color:heads.headFontColor,fontSize:heads.headFontSize}\">{{this.$project.projectName}}</div>\r\n\t\t</div>\r\n\t\t<div class=\"right-menu\">\r\n\t\t\t<div class=\"user-info\" :style=\"{color:heads.headUserInfoFontColor,fontSize:heads.headUserInfoFontSize}\">{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n\t\t\t<div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onLogout\">退出登录</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdialogVisible: false,\r\n\t\t\t\truleForm: {},\r\n\t\t\t\tuser: {},\r\n\t\t\t\theads: {\"headLogoutFontHoverColor\":\"#fff\",\"headFontSize\":\"20px\",\"headUserInfoFontColor\":\"#333\",\"headBoxShadow\":\"0 1px 6px #444\",\"headTitleImgHeight\":\"44px\",\"headLogoutFontHoverBgColor\":\"#333\",\"headFontColor\":\"#000\",\"headTitleImg\":false,\"headHeight\":\"60px\",\"headTitleImgBorderRadius\":\"22px\",\"headTitleImgUrl\":\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\",\"headBgColor\":\"#C59F22\",\"headTitleImgBoxShadow\":\"0 1px 6px #444\",\"headLogoutFontColor\":\"#333\",\"headUserInfoFontSize\":\"16px\",\"headTitleImgWidth\":\"44px\",\"headTitleStyle\":\"2\",\"headLogoutFontSize\":\"16px\"},\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.setHeaderStyle()\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet sessionTable = this.$storage.get(\"sessionTable\")\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: sessionTable + '/session',\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({\r\n\t\t\t\tdata\r\n\t\t\t}) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.user = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet message = this.$message\r\n\t\t\t\t\tmessage.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLogout() {\r\n\t\t\t\tlet storage = this.$storage\r\n\t\t\t\tlet router = this.$router\r\n\t\t\t\tstorage.clear()\n\t\t\t\trouter.replace({\r\n\t\t\t\t\tname: \"login\"\r\n\t\t\t\t});\r\n\t\t\t},\r\n      \t\t\tonIndexTap(){\n      \t\t\t\twindow.location.href = `${this.$base.indexUrl}`\n    \t\t\t},\n\t\t\tsetHeaderStyle() {\r\n\t\t\t  this.$nextTick(()=>{\r\n\t\t\t    document.querySelectorAll('.navbar .right-menu .logout').forEach(el=>{\r\n\t\t\t      el.addEventListener(\"mouseenter\", e => {\r\n\t\t\t        e.stopPropagation()\r\n\t\t\t        el.style.backgroundColor = this.heads.headLogoutFontHoverBgColor\r\n\t\t\t\t\tel.style.color = this.heads.headLogoutFontHoverColor\r\n\t\t\t      })\r\n\t\t\t      el.addEventListener(\"mouseleave\", e => {\r\n\t\t\t        e.stopPropagation()\r\n\t\t\t        el.style.backgroundColor = \"transparent\"\r\n\t\t\t\t\tel.style.color = this.heads.headLogoutFontColor\r\n\t\t\t      })\r\n\t\t\t    })\r\n\t\t\t  })\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.navbar {\r\n\t\theight: 60px;\r\n\t\tline-height: 60px;\r\n\t\twidth: 100%;\r\n\t\tpadding: 0 34px;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #ff00ff;\r\n\t\tposition: relative;\r\n\t\tz-index: 111;\r\n\t\t\r\n\t\t.right-menu {\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 34px;\r\n\t\t\ttop: 0;\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t\talign-items: center;\r\n\t\t\tz-index: 111;\r\n\t\t\t\r\n\t\t\t.user-info {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tcolor: red;\r\n\t\t\t\tpadding: 0 12px;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.logout {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tcolor: red;\r\n\t\t\t\tpadding: 0 12px;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t\t\r\n\t\t.title-menu {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t\talign-items: center;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\t\r\n\t\t\t.title-img {\r\n\t\t\t\twidth: 44px;\r\n\t\t\t\theight: 44px;\r\n\t\t\t\tborder-radius: 22px;\r\n\t\t\t\tbox-shadow: 0 1px 6px #444;\r\n\t\t\t\tmargin-right: 16px;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.title-name {\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// .el-header .fr {\r\n\t// \tfloat: right;\r\n\t// }\r\n\r\n\t// .el-header .fl {\r\n\t// \tfloat: left;\r\n\t// }\r\n\r\n\t// .el-header {\r\n\t// \twidth: 100%;\r\n\t// \tcolor: #333;\r\n\t// \ttext-align: center;\r\n\t// \tline-height: 60px;\r\n\t// \tpadding: 0;\r\n\t// \tz-index: 99;\r\n\t// }\r\n\r\n\t// .logo {\r\n\t// \twidth: 60px;\r\n\t// \theight: 60px;\r\n\t// \tmargin-left: 70px;\r\n\t// }\r\n\r\n\t// .avator {\r\n\t// \twidth: 40px;\r\n\t// \theight: 40px;\r\n\t// \tbackground: #ffffff;\r\n\t// \tborder-radius: 50%;\r\n\t// }\r\n\r\n\t// .title {\r\n\t// \tcolor: #ffffff;\r\n\t// \tfont-size: 20px;\r\n\t// \tfont-weight: bold;\r\n\t// \tmargin-left: 20px;\r\n\t// }\r\n</style>\n"], "mappings": "AA2BA;EACAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,QAAA;IACA,IAAAC,YAAA,QAAAC,QAAA,CAAAC,GAAA;IACA,KAAAC,KAAA;MACAC,GAAA,EAAAJ,YAAA;MACAK,MAAA;IACA,GAAAC,IAAA;MACAd;IACA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAe,IAAA;QACA,KAAAZ,IAAA,GAAAH,IAAA,CAAAA,IAAA;MACA;QACA,IAAAgB,OAAA,QAAAC,QAAA;QACAD,OAAA,CAAAE,KAAA,CAAAlB,IAAA,CAAAmB,GAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,SAAA;MACA,IAAAC,OAAA,QAAAb,QAAA;MACA,IAAAc,MAAA,QAAAC,OAAA;MACAF,OAAA,CAAAG,KAAA;MACAF,MAAA,CAAAG,OAAA;QACAC,IAAA;MACA;IACA;IACAC,WAAA;MACAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,WAAAC,KAAA,CAAAC,QAAA;IACA;IACA3B,eAAA;MACA,KAAA4B,SAAA;QACAC,QAAA,CAAAC,gBAAA,gCAAAC,OAAA,CAAAC,EAAA;UACAA,EAAA,CAAAC,gBAAA,eAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAI,KAAA,CAAAC,eAAA,QAAAvC,KAAA,CAAAwC,0BAAA;YACAN,EAAA,CAAAI,KAAA,CAAAG,KAAA,QAAAzC,KAAA,CAAA0C,wBAAA;UACA;UACAR,EAAA,CAAAC,gBAAA,eAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAI,KAAA,CAAAC,eAAA;YACAL,EAAA,CAAAI,KAAA,CAAAG,KAAA,QAAAzC,KAAA,CAAA2C,mBAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}