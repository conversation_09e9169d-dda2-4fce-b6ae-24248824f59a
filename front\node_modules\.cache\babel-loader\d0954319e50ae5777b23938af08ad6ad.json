{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\front\\src\\views\\modules\\dictionaryNews\\list.vue?vue&type=template&id=ceaaec9c&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\src\\views\\modules\\dictionaryNews\\list.vue", "mtime": 1751514458861}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "showFlag", "attrs", "inline", "model", "searchForm", "style", "justifyContent", "contents", "searchBoxPosition", "gutter", "label", "placeholder", "clearable", "value", "indexNameSearch", "callback", "$$v", "$set", "expression", "icon", "type", "on", "click", "$event", "search", "_v", "btnAdAllBoxPosition", "isAuth", "addOrUpdateHandler", "_e", "disabled", "dataListSelections", "length", "delete<PERSON><PERSON><PERSON>", "directives", "name", "rawName", "dataListLoading", "width", "fontSize", "tableContentFontSize", "color", "tableContentFontColor", "size", "tableSize", "tableShowHeader", "headerRowStyle", "headerCellStyle", "border", "tableBorder", "fit", "tableFit", "stripe", "tableStripe", "rowStyle", "cellStyle", "data", "dataList", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tableSelection", "align", "tableIndex", "sortable", "tableSortable", "tableAlign", "prop", "scopedSlots", "_u", "key", "fn", "scope", "_s", "row", "codeIndex", "indexName", "id", "textAlign", "pagePosition", "clsss", "layout", "layouts", "pageIndex", "Number", "pageEachNum", "total", "totalPage", "small", "pageStyle", "background", "pageBtnBG", "sizeChangeHandle", "currentChangeHandle", "addOrUpdateFlag", "ref", "parent", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/code/front/src/views/modules/dictionaryNews/list.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"main-content\" },\n    [\n      _vm.showFlag\n        ? _c(\n            \"div\",\n            [\n              _c(\n                \"el-form\",\n                {\n                  staticClass: \"form-content\",\n                  attrs: { inline: true, model: _vm.searchForm },\n                },\n                [\n                  _c(\n                    \"el-row\",\n                    {\n                      staticClass: \"slt\",\n                      style: {\n                        justifyContent:\n                          _vm.contents.searchBoxPosition == \"1\"\n                            ? \"flex-start\"\n                            : _vm.contents.searchBoxPosition == \"2\"\n                            ? \"center\"\n                            : \"flex-end\",\n                      },\n                      attrs: { gutter: 20 },\n                    },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"公告类型\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              \"prefix-icon\": \"el-icon-search\",\n                              placeholder: \"公告类型\",\n                              clearable: \"\",\n                            },\n                            model: {\n                              value: _vm.searchForm.indexNameSearch,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.searchForm, \"indexNameSearch\", $$v)\n                              },\n                              expression: \"searchForm.indexNameSearch\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-form-item\",\n                        [\n                          _c(\n                            \"el-button\",\n                            {\n                              attrs: {\n                                icon: \"el-icon-search\",\n                                type: \"success\",\n                              },\n                              on: {\n                                click: function ($event) {\n                                  return _vm.search()\n                                },\n                              },\n                            },\n                            [_vm._v(\"查询\")]\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"el-row\",\n                    {\n                      staticClass: \"ad\",\n                      style: {\n                        justifyContent:\n                          _vm.contents.btnAdAllBoxPosition == \"1\"\n                            ? \"flex-start\"\n                            : _vm.contents.btnAdAllBoxPosition == \"2\"\n                            ? \"center\"\n                            : \"flex-end\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        [\n                          _vm.isAuth(\"dictionaryNews\", \"新增\")\n                            ? _c(\n                                \"el-button\",\n                                {\n                                  attrs: {\n                                    type: \"success\",\n                                    icon: \"el-icon-plus\",\n                                  },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.addOrUpdateHandler()\n                                    },\n                                  },\n                                },\n                                [_vm._v(\"新增\")]\n                              )\n                            : _vm._e(),\n                          _vm.isAuth(\"dictionaryNews\", \"删除\")\n                            ? _c(\n                                \"el-button\",\n                                {\n                                  attrs: {\n                                    disabled:\n                                      _vm.dataListSelections.length <= 0,\n                                    type: \"danger\",\n                                    icon: \"el-icon-delete\",\n                                  },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.deleteHandler()\n                                    },\n                                  },\n                                },\n                                [_vm._v(\"删除\")]\n                              )\n                            : _vm._e(),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"table-content\" },\n                [\n                  _vm.isAuth(\"dictionaryNews\", \"查看\")\n                    ? _c(\n                        \"el-table\",\n                        {\n                          directives: [\n                            {\n                              name: \"loading\",\n                              rawName: \"v-loading\",\n                              value: _vm.dataListLoading,\n                              expression: \"dataListLoading\",\n                            },\n                          ],\n                          staticClass: \"tables\",\n                          style: {\n                            width: \"100%\",\n                            fontSize: _vm.contents.tableContentFontSize,\n                            color: _vm.contents.tableContentFontColor,\n                          },\n                          attrs: {\n                            size: _vm.contents.tableSize,\n                            \"show-header\": _vm.contents.tableShowHeader,\n                            \"header-row-style\": _vm.headerRowStyle,\n                            \"header-cell-style\": _vm.headerCellStyle,\n                            border: _vm.contents.tableBorder,\n                            fit: _vm.contents.tableFit,\n                            stripe: _vm.contents.tableStripe,\n                            \"row-style\": _vm.rowStyle,\n                            \"cell-style\": _vm.cellStyle,\n                            data: _vm.dataList,\n                          },\n                          on: {\n                            \"selection-change\": _vm.selectionChangeHandler,\n                          },\n                        },\n                        [\n                          _vm.contents.tableSelection\n                            ? _c(\"el-table-column\", {\n                                attrs: {\n                                  type: \"selection\",\n                                  \"header-align\": \"center\",\n                                  align: \"center\",\n                                  width: \"50\",\n                                },\n                              })\n                            : _vm._e(),\n                          _vm.contents.tableIndex\n                            ? _c(\"el-table-column\", {\n                                attrs: {\n                                  label: \"索引\",\n                                  type: \"index\",\n                                  width: \"50\",\n                                },\n                              })\n                            : _vm._e(),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              sortable: _vm.contents.tableSortable,\n                              align: _vm.contents.tableAlign,\n                              prop: \"codeIndex\",\n                              \"header-align\": \"center\",\n                              label: \"公告类型编码\",\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _vm._v(\n                                        \" \" + _vm._s(scope.row.codeIndex) + \" \"\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              false,\n                              1708797774\n                            ),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              sortable: _vm.contents.tableSortable,\n                              align: _vm.contents.tableAlign,\n                              prop: \"indexName\",\n                              \"header-align\": \"center\",\n                              label: \"公告类型名称\",\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _vm._v(\n                                        \" \" + _vm._s(scope.row.indexName) + \" \"\n                                      ),\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              false,\n                              3024456996\n                            ),\n                          }),\n                          _c(\"el-table-column\", {\n                            attrs: {\n                              width: \"300\",\n                              align: _vm.contents.tableAlign,\n                              \"header-align\": \"center\",\n                              label: \"操作\",\n                            },\n                            scopedSlots: _vm._u(\n                              [\n                                {\n                                  key: \"default\",\n                                  fn: function (scope) {\n                                    return [\n                                      _vm.isAuth(\"dictionaryNews\", \"查看\")\n                                        ? _c(\n                                            \"el-button\",\n                                            {\n                                              attrs: {\n                                                type: \"success\",\n                                                icon: \"el-icon-tickets\",\n                                                size: \"mini\",\n                                              },\n                                              on: {\n                                                click: function ($event) {\n                                                  return _vm.addOrUpdateHandler(\n                                                    scope.row.id,\n                                                    \"info\"\n                                                  )\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\"详情\")]\n                                          )\n                                        : _vm._e(),\n                                      _vm.isAuth(\"dictionaryNews\", \"修改\")\n                                        ? _c(\n                                            \"el-button\",\n                                            {\n                                              attrs: {\n                                                type: \"primary\",\n                                                icon: \"el-icon-edit\",\n                                                size: \"mini\",\n                                              },\n                                              on: {\n                                                click: function ($event) {\n                                                  return _vm.addOrUpdateHandler(\n                                                    scope.row.id\n                                                  )\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\"修改\")]\n                                          )\n                                        : _vm._e(),\n                                      _vm.isAuth(\"dictionaryNews\", \"删除\")\n                                        ? _c(\n                                            \"el-button\",\n                                            {\n                                              attrs: {\n                                                type: \"danger\",\n                                                icon: \"el-icon-delete\",\n                                                size: \"mini\",\n                                              },\n                                              on: {\n                                                click: function ($event) {\n                                                  return _vm.deleteHandler(\n                                                    scope.row.id\n                                                  )\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\"删除\")]\n                                          )\n                                        : _vm._e(),\n                                    ]\n                                  },\n                                },\n                              ],\n                              null,\n                              false,\n                              3337567359\n                            ),\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _c(\"el-pagination\", {\n                    staticClass: \"pagination-content\",\n                    style: {\n                      textAlign:\n                        _vm.contents.pagePosition == 1\n                          ? \"left\"\n                          : _vm.contents.pagePosition == 2\n                          ? \"center\"\n                          : \"right\",\n                    },\n                    attrs: {\n                      clsss: \"pages\",\n                      layout: _vm.layouts,\n                      \"current-page\": _vm.pageIndex,\n                      \"page-sizes\": [10, 20, 50, 100],\n                      \"page-size\": Number(_vm.contents.pageEachNum),\n                      total: _vm.totalPage,\n                      small: _vm.contents.pageStyle,\n                      background: _vm.contents.pageBtnBG,\n                    },\n                    on: {\n                      \"size-change\": _vm.sizeChangeHandle,\n                      \"current-change\": _vm.currentChangeHandle,\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm.addOrUpdateFlag\n        ? _c(\"add-or-update\", { ref: \"addOrUpdate\", attrs: { parent: this } })\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEH,GAAG,CAACI,QAAQ,GACRH,EAAE,CACA,KAAK,EACL,CACEA,EAAE,CACA,SAAS,EACT;IACEE,WAAW,EAAE,cAAc;IAC3BE,KAAK,EAAE;MAAEC,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAEP,GAAG,CAACQ;IAAW;EAC/C,CAAC,EACD,CACEP,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,KAAK;IAClBM,KAAK,EAAE;MACLC,cAAc,EACZV,GAAG,CAACW,QAAQ,CAACC,iBAAiB,IAAI,GAAG,GACjC,YAAY,GACZZ,GAAG,CAACW,QAAQ,CAACC,iBAAiB,IAAI,GAAG,GACrC,QAAQ,GACR;IACR,CAAC;IACDP,KAAK,EAAE;MAAEQ,MAAM,EAAE;IAAG;EACtB,CAAC,EACD,CACEZ,EAAE,CACA,cAAc,EACd;IAAEI,KAAK,EAAE;MAAES,KAAK,EAAE;IAAO;EAAE,CAAC,EAC5B,CACEb,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACL,aAAa,EAAE,gBAAgB;MAC/BU,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE;IACb,CAAC;IACDT,KAAK,EAAE;MACLU,KAAK,EAAEjB,GAAG,CAACQ,UAAU,CAACU,eAAe;MACrCC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBpB,GAAG,CAACqB,IAAI,CAACrB,GAAG,CAACQ,UAAU,EAAE,iBAAiB,EAAEY,GAAG,CAAC;MAClD,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDrB,EAAE,CACA,cAAc,EACd,CACEA,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MACLkB,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE;IACR,CAAC;IACDC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAO3B,GAAG,CAAC4B,MAAM,CAAC,CAAC;MACrB;IACF;EACF,CAAC,EACD,CAAC5B,GAAG,CAAC6B,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD5B,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,IAAI;IACjBM,KAAK,EAAE;MACLC,cAAc,EACZV,GAAG,CAACW,QAAQ,CAACmB,mBAAmB,IAAI,GAAG,GACnC,YAAY,GACZ9B,GAAG,CAACW,QAAQ,CAACmB,mBAAmB,IAAI,GAAG,GACvC,QAAQ,GACR;IACR;EACF,CAAC,EACD,CACE7B,EAAE,CACA,cAAc,EACd,CACED,GAAG,CAAC+B,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAC9B9B,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MACLmB,IAAI,EAAE,SAAS;MACfD,IAAI,EAAE;IACR,CAAC;IACDE,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAO3B,GAAG,CAACgC,kBAAkB,CAAC,CAAC;MACjC;IACF;EACF,CAAC,EACD,CAAChC,GAAG,CAAC6B,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,GACD7B,GAAG,CAACiC,EAAE,CAAC,CAAC,EACZjC,GAAG,CAAC+B,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAC9B9B,EAAE,CACA,WAAW,EACX;IACEI,KAAK,EAAE;MACL6B,QAAQ,EACNlC,GAAG,CAACmC,kBAAkB,CAACC,MAAM,IAAI,CAAC;MACpCZ,IAAI,EAAE,QAAQ;MACdD,IAAI,EAAE;IACR,CAAC;IACDE,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAO3B,GAAG,CAACqC,aAAa,CAAC,CAAC;MAC5B;IACF;EACF,CAAC,EACD,CAACrC,GAAG,CAAC6B,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,GACD7B,GAAG,CAACiC,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEH,GAAG,CAAC+B,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAC9B9B,EAAE,CACA,UAAU,EACV;IACEqC,UAAU,EAAE,CACV;MACEC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBvB,KAAK,EAAEjB,GAAG,CAACyC,eAAe;MAC1BnB,UAAU,EAAE;IACd,CAAC,CACF;IACDnB,WAAW,EAAE,QAAQ;IACrBM,KAAK,EAAE;MACLiC,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE3C,GAAG,CAACW,QAAQ,CAACiC,oBAAoB;MAC3CC,KAAK,EAAE7C,GAAG,CAACW,QAAQ,CAACmC;IACtB,CAAC;IACDzC,KAAK,EAAE;MACL0C,IAAI,EAAE/C,GAAG,CAACW,QAAQ,CAACqC,SAAS;MAC5B,aAAa,EAAEhD,GAAG,CAACW,QAAQ,CAACsC,eAAe;MAC3C,kBAAkB,EAAEjD,GAAG,CAACkD,cAAc;MACtC,mBAAmB,EAAElD,GAAG,CAACmD,eAAe;MACxCC,MAAM,EAAEpD,GAAG,CAACW,QAAQ,CAAC0C,WAAW;MAChCC,GAAG,EAAEtD,GAAG,CAACW,QAAQ,CAAC4C,QAAQ;MAC1BC,MAAM,EAAExD,GAAG,CAACW,QAAQ,CAAC8C,WAAW;MAChC,WAAW,EAAEzD,GAAG,CAAC0D,QAAQ;MACzB,YAAY,EAAE1D,GAAG,CAAC2D,SAAS;MAC3BC,IAAI,EAAE5D,GAAG,CAAC6D;IACZ,CAAC;IACDpC,EAAE,EAAE;MACF,kBAAkB,EAAEzB,GAAG,CAAC8D;IAC1B;EACF,CAAC,EACD,CACE9D,GAAG,CAACW,QAAQ,CAACoD,cAAc,GACvB9D,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLmB,IAAI,EAAE,WAAW;MACjB,cAAc,EAAE,QAAQ;MACxBwC,KAAK,EAAE,QAAQ;MACftB,KAAK,EAAE;IACT;EACF,CAAC,CAAC,GACF1C,GAAG,CAACiC,EAAE,CAAC,CAAC,EACZjC,GAAG,CAACW,QAAQ,CAACsD,UAAU,GACnBhE,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLS,KAAK,EAAE,IAAI;MACXU,IAAI,EAAE,OAAO;MACbkB,KAAK,EAAE;IACT;EACF,CAAC,CAAC,GACF1C,GAAG,CAACiC,EAAE,CAAC,CAAC,EACZhC,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACL6D,QAAQ,EAAElE,GAAG,CAACW,QAAQ,CAACwD,aAAa;MACpCH,KAAK,EAAEhE,GAAG,CAACW,QAAQ,CAACyD,UAAU;MAC9BC,IAAI,EAAE,WAAW;MACjB,cAAc,EAAE,QAAQ;MACxBvD,KAAK,EAAE;IACT,CAAC;IACDwD,WAAW,EAAEtE,GAAG,CAACuE,EAAE,CACjB,CACE;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL1E,GAAG,CAAC6B,EAAE,CACJ,GAAG,GAAG7B,GAAG,CAAC2E,EAAE,CAACD,KAAK,CAACE,GAAG,CAACC,SAAS,CAAC,GAAG,GACtC,CAAC,CACF;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,UACF;EACF,CAAC,CAAC,EACF5E,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACL6D,QAAQ,EAAElE,GAAG,CAACW,QAAQ,CAACwD,aAAa;MACpCH,KAAK,EAAEhE,GAAG,CAACW,QAAQ,CAACyD,UAAU;MAC9BC,IAAI,EAAE,WAAW;MACjB,cAAc,EAAE,QAAQ;MACxBvD,KAAK,EAAE;IACT,CAAC;IACDwD,WAAW,EAAEtE,GAAG,CAACuE,EAAE,CACjB,CACE;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL1E,GAAG,CAAC6B,EAAE,CACJ,GAAG,GAAG7B,GAAG,CAAC2E,EAAE,CAACD,KAAK,CAACE,GAAG,CAACE,SAAS,CAAC,GAAG,GACtC,CAAC,CACF;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,UACF;EACF,CAAC,CAAC,EACF7E,EAAE,CAAC,iBAAiB,EAAE;IACpBI,KAAK,EAAE;MACLqC,KAAK,EAAE,KAAK;MACZsB,KAAK,EAAEhE,GAAG,CAACW,QAAQ,CAACyD,UAAU;MAC9B,cAAc,EAAE,QAAQ;MACxBtD,KAAK,EAAE;IACT,CAAC;IACDwD,WAAW,EAAEtE,GAAG,CAACuE,EAAE,CACjB,CACE;MACEC,GAAG,EAAE,SAAS;MACdC,EAAE,EAAE,SAAAA,CAAUC,KAAK,EAAE;QACnB,OAAO,CACL1E,GAAG,CAAC+B,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAC9B9B,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLmB,IAAI,EAAE,SAAS;YACfD,IAAI,EAAE,iBAAiB;YACvBwB,IAAI,EAAE;UACR,CAAC;UACDtB,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvB,OAAO3B,GAAG,CAACgC,kBAAkB,CAC3B0C,KAAK,CAACE,GAAG,CAACG,EAAE,EACZ,MACF,CAAC;YACH;UACF;QACF,CAAC,EACD,CAAC/E,GAAG,CAAC6B,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,GACD7B,GAAG,CAACiC,EAAE,CAAC,CAAC,EACZjC,GAAG,CAAC+B,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAC9B9B,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLmB,IAAI,EAAE,SAAS;YACfD,IAAI,EAAE,cAAc;YACpBwB,IAAI,EAAE;UACR,CAAC;UACDtB,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvB,OAAO3B,GAAG,CAACgC,kBAAkB,CAC3B0C,KAAK,CAACE,GAAG,CAACG,EACZ,CAAC;YACH;UACF;QACF,CAAC,EACD,CAAC/E,GAAG,CAAC6B,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,GACD7B,GAAG,CAACiC,EAAE,CAAC,CAAC,EACZjC,GAAG,CAAC+B,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAC9B9B,EAAE,CACA,WAAW,EACX;UACEI,KAAK,EAAE;YACLmB,IAAI,EAAE,QAAQ;YACdD,IAAI,EAAE,gBAAgB;YACtBwB,IAAI,EAAE;UACR,CAAC;UACDtB,EAAE,EAAE;YACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;cACvB,OAAO3B,GAAG,CAACqC,aAAa,CACtBqC,KAAK,CAACE,GAAG,CAACG,EACZ,CAAC;YACH;UACF;QACF,CAAC,EACD,CAAC/E,GAAG,CAAC6B,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,GACD7B,GAAG,CAACiC,EAAE,CAAC,CAAC,CACb;MACH;IACF,CAAC,CACF,EACD,IAAI,EACJ,KAAK,EACL,UACF;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,GACDjC,GAAG,CAACiC,EAAE,CAAC,CAAC,EACZhC,EAAE,CAAC,eAAe,EAAE;IAClBE,WAAW,EAAE,oBAAoB;IACjCM,KAAK,EAAE;MACLuE,SAAS,EACPhF,GAAG,CAACW,QAAQ,CAACsE,YAAY,IAAI,CAAC,GAC1B,MAAM,GACNjF,GAAG,CAACW,QAAQ,CAACsE,YAAY,IAAI,CAAC,GAC9B,QAAQ,GACR;IACR,CAAC;IACD5E,KAAK,EAAE;MACL6E,KAAK,EAAE,OAAO;MACdC,MAAM,EAAEnF,GAAG,CAACoF,OAAO;MACnB,cAAc,EAAEpF,GAAG,CAACqF,SAAS;MAC7B,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;MAC/B,WAAW,EAAEC,MAAM,CAACtF,GAAG,CAACW,QAAQ,CAAC4E,WAAW,CAAC;MAC7CC,KAAK,EAAExF,GAAG,CAACyF,SAAS;MACpBC,KAAK,EAAE1F,GAAG,CAACW,QAAQ,CAACgF,SAAS;MAC7BC,UAAU,EAAE5F,GAAG,CAACW,QAAQ,CAACkF;IAC3B,CAAC;IACDpE,EAAE,EAAE;MACF,aAAa,EAAEzB,GAAG,CAAC8F,gBAAgB;MACnC,gBAAgB,EAAE9F,GAAG,CAAC+F;IACxB;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACD/F,GAAG,CAACiC,EAAE,CAAC,CAAC,EACZjC,GAAG,CAACgG,eAAe,GACf/F,EAAE,CAAC,eAAe,EAAE;IAAEgG,GAAG,EAAE,aAAa;IAAE5F,KAAK,EAAE;MAAE6F,MAAM,EAAE;IAAK;EAAE,CAAC,CAAC,GACpElG,GAAG,CAACiC,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIkE,eAAe,GAAG,EAAE;AACxBpG,MAAM,CAACqG,aAAa,GAAG,IAAI;AAE3B,SAASrG,MAAM,EAAEoG,eAAe", "ignoreList": []}]}