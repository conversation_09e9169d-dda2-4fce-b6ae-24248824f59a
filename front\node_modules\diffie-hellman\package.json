{"_from": "diffie-hellman@^5.0.0", "_id": "diffie-hellman@5.0.3", "_inBundle": false, "_integrity": "sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==", "_location": "/diffie-hellman", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "diffie-hellman@^5.0.0", "name": "diffie-hellman", "escapedName": "diffie-hellman", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/crypto-browserify"], "_resolved": "https://registry.npmjs.org/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "_shasum": "40e8ee98f55a2149607146921c63e1ae5f3d2875", "_spec": "diffie-hellman@^5.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\crypto-browserify", "author": {"name": "<PERSON>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/crypto-browserify/diffie-hellman/issues"}, "bundleDependencies": false, "dependencies": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}, "deprecated": false, "description": "pure js diffie-hellman", "devDependencies": {"tap-spec": "^1.0.1", "tape": "^3.0.1"}, "homepage": "https://github.com/crypto-browserify/diffie-hellman", "keywords": ["diffie", "hellman", "di<PERSON><PERSON><PERSON><PERSON>", "dh"], "license": "MIT", "main": "index.js", "name": "diffie-hellman", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/diffie-hellman.git"}, "scripts": {"test": "node test.js | tspec"}, "version": "5.0.3"}