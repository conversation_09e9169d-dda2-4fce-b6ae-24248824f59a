{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\src\\utils\\validate.js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\utils\\validate.js", "mtime": 1649064848740}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqDQogKiDpgq7nrrENCiAqIEBwYXJhbSB7Kn0gcw0KICovCmV4cG9ydCBmdW5jdGlvbiBpc0VtYWlsKHMpIHsKICByZXR1cm4gL14oW2EtekEtWjAtOV8tXSkrQChbYS16QS1aMC05Xy1dKSsoKC5bYS16QS1aMC05Xy1dezIsM30pezEsMn0pJC8udGVzdChzKTsKfQoKLyoqDQogKiDmiYvmnLrlj7fnoIENCiAqIEBwYXJhbSB7Kn0gcw0KICovCmV4cG9ydCBmdW5jdGlvbiBpc01vYmlsZShzKSB7CiAgcmV0dXJuIC9eMVswLTldezEwfSQvLnRlc3Qocyk7Cn0KCi8qKg0KICog55S16K+d5Y+356CBDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNQaG9uZShzKSB7CiAgcmV0dXJuIC9eKFswLTldezMsNH0tKT9bMC05XXs3LDh9JC8udGVzdChzKTsKfQoKLyoqDQogKiBVUkzlnLDlnYANCiAqIEBwYXJhbSB7Kn0gcw0KICovCmV4cG9ydCBmdW5jdGlvbiBpc1VSTChzKSB7CiAgcmV0dXJuIC9eaHR0cFtzXT86XC9cLy4qLy50ZXN0KHMpOwp9CgovKioNCiAqIOWMuemFjeaVsOWtl++8jOWPr+S7peaYr+Wwj+aVsO+8jOS4jeWPr+S7peaYr+i0n+aVsCzlj6/ku6XkuLrnqboNCiAqIEBwYXJhbSB7Kn0gcyANCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNOdW1iZXIocykgewogIHJldHVybiAvKF4tP1srLV0/KFswLTldKlwuP1swLTldK3xbMC05XStcLj9bMC05XSopKFtlRV1bKy1dP1swLTldKyk/JCl8KF4kKS8udGVzdChzKTsKfQovKioNCiAqIOWMuemFjeaVtOaVsO+8jOWPr+S7peS4uuepug0KICogQHBhcmFtIHsqfSBzIA0KICovCmV4cG9ydCBmdW5jdGlvbiBpc0ludE51bWVyKHMpIHsKICByZXR1cm4gLyheLT9cZCskKXwoXiQpLy50ZXN0KHMpOwp9Ci8qKg0KICog6Lqr5Lu96K+B5qCh6aqMDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGNoZWNrSWRDYXJkKGlkY2FyZCkgewogIGNvbnN0IHJlZ0lkQ2FyZCA9IC8oXlxkezE1fSQpfCheXGR7MTh9JCl8KF5cZHsxN30oXGR8WHx4KSQpLzsKICBpZiAoIXJlZ0lkQ2FyZC50ZXN0KGlkY2FyZCkpIHsKICAgIHJldHVybiBmYWxzZTsKICB9IGVsc2UgewogICAgcmV0dXJuIHRydWU7CiAgfQp9"}, {"version": 3, "names": ["isEmail", "s", "test", "isMobile", "isPhone", "isURL", "isNumber", "isIntNumer", "checkIdCard", "idcard", "regIdCard"], "sources": ["D:/xuangmu/yuanma/code1/front/src/utils/validate.js"], "sourcesContent": ["/**\r\n * 邮箱\r\n * @param {*} s\r\n */\r\nexport function isEmail (s) {\r\n  return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)\r\n}\r\n\r\n/**\r\n * 手机号码\r\n * @param {*} s\r\n */\r\nexport function isMobile (s) {\r\n  return /^1[0-9]{10}$/.test(s)\r\n}\r\n\r\n/**\r\n * 电话号码\r\n * @param {*} s\r\n */\r\nexport function isPhone (s) {\r\n  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)\r\n}\r\n\r\n/**\r\n * URL地址\r\n * @param {*} s\r\n */\r\nexport function isURL (s) {\r\n  return /^http[s]?:\\/\\/.*/.test(s)\r\n}\r\n\r\n/**\r\n * 匹配数字，可以是小数，不可以是负数,可以为空\r\n * @param {*} s \r\n */\r\nexport function isNumber(s){\r\n  return  /(^-?[+-]?([0-9]*\\.?[0-9]+|[0-9]+\\.?[0-9]*)([eE][+-]?[0-9]+)?$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 匹配整数，可以为空\r\n * @param {*} s \r\n */\r\nexport function isIntNumer(s){\r\n  return  /(^-?\\d+$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 身份证校验\r\n */\r\nexport function checkIdCard(idcard) {\r\n  const regIdCard = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n  if (!regIdCard.test(idcard)) {\r\n      return false;\r\n  } else {\r\n      return true;\r\n  }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAAEC,CAAC,EAAE;EAC1B,OAAO,iEAAiE,CAACC,IAAI,CAACD,CAAC,CAAC;AAClF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,QAAQA,CAAEF,CAAC,EAAE;EAC3B,OAAO,cAAc,CAACC,IAAI,CAACD,CAAC,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAAEH,CAAC,EAAE;EAC1B,OAAO,4BAA4B,CAACC,IAAI,CAACD,CAAC,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,KAAKA,CAAEJ,CAAC,EAAE;EACxB,OAAO,kBAAkB,CAACC,IAAI,CAACD,CAAC,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASK,QAAQA,CAACL,CAAC,EAAC;EACzB,OAAQ,qEAAqE,CAACC,IAAI,CAACD,CAAC,CAAC;AACvF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,UAAUA,CAACN,CAAC,EAAC;EAC3B,OAAQ,gBAAgB,CAACC,IAAI,CAACD,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA,OAAO,SAASO,WAAWA,CAACC,MAAM,EAAE;EAClC,MAAMC,SAAS,GAAG,0CAA0C;EAC5D,IAAI,CAACA,SAAS,CAACR,IAAI,CAACO,MAAM,CAAC,EAAE;IACzB,OAAO,KAAK;EAChB,CAAC,MAAM;IACH,OAAO,IAAI;EACf;AACF", "ignoreList": []}]}