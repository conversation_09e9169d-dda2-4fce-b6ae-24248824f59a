/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.0 (2020-02-13)
 */
!function(){"use strict";function d(e,t){var n=e.getBody();n&&(n.style.overflowY=t?"":"hidden",t||(n.scrollTop=0))}function h(e,t,n,i){var o=parseInt(e.getStyle(t,n,i),10);return isNaN(o)?0:o}var i=function(e){function t(){return n}var n=e;return{get:t,set:function(e){n=e},clone:function(){return i(t())}}},e=tinymce.util.Tools.resolve("tinymce.PluginManager"),v=tinymce.util.Tools.resolve("tinymce.Env"),r=tinymce.util.Tools.resolve("tinymce.util.Delay"),p=function(e){return e.fire("ResizeEditor")},y=function(e){return e.getParam("min_height",e.getElement().offsetHeight,"number")},z=function(e){return e.getParam("max_height",0,"number")},n=function(e){return e.getParam("autoresize_overflow_padding",1,"number")},b=function(e){return e.getParam("autoresize_bottom_margin",50,"number")},o=function(e){return e.getParam("autoresize_on_init",!0,"boolean")},u=function(e,t,n,i,o){r.setEditorTimeout(e,function(){C(e,t),n--?u(e,t,n,i,o):o&&o()},i)},C=function(e,t){var n,i,o,r=e.dom,u=e.getDoc();if(u)if(function(e){return e.plugins.fullscreen&&e.plugins.fullscreen.isFullscreen()}(e))d(e,!0);else{var s=u.documentElement,a=b(e);i=y(e);var f=h(r,s,"margin-top",!0),c=h(r,s,"margin-bottom",!0);(o=s.offsetHeight+f+c+a)<0&&(o=0);var g=e.getContainer().offsetHeight-e.getContentAreaContainer().offsetHeight;o+g>y(e)&&(i=o+g);var l=z(e);if(l&&l<i?(i=l,d(e,!0)):d(e,!1),i!==t.get()){if(n=i-t.get(),r.setStyle(e.getContainer(),"height",i+"px"),t.set(i),p(e),v.browser.isSafari()&&v.mac){var m=e.getWin();m.scrollTo(m.pageXOffset,m.pageYOffset)}e.hasFocus()&&e.selection.scrollIntoView(e.selection.getNode()),v.webkit&&n<0&&C(e,t)}}},s={setup:function(t,e){t.on("init",function(){var e=n(t);t.dom.setStyles(t.getBody(),{paddingLeft:e,paddingRight:e,"min-height":0})}),t.on("NodeChange SetContent keyup FullscreenStateChanged ResizeContent",function(){C(t,e)}),o(t)&&t.on("init",function(){u(t,e,20,100,function(){u(t,e,5,1e3)})})},resize:C},a=function(e,t){e.addCommand("mceAutoResize",function(){s.resize(e,t)})};!function t(){e.add("autoresize",function(e){if(e.settings.hasOwnProperty("resize")||(e.settings.resize=!1),!e.inline){var t=i(0);a(e,t),s.setup(e,t)}})}()}();