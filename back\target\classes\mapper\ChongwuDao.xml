<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ChongwuDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.chongwu_name as chongwuName
        ,a.chongwu_photo as chongwuPhoto
        ,a.chongwu_types as chongwuTypes
        ,a.chongwu_content as chongwuContent
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.ChongwuView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段

        FROM chongwu  a

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test=" params.chongwuName != '' and params.chongwuName != null and params.chongwuName != 'null' ">
                and a.chongwu_name like CONCAT('%',#{params.chongwuName},'%')
            </if>
            <if test="params.chongwuTypes != null and params.chongwuTypes != ''">
                and a.chongwu_types = #{params.chongwuTypes}
            </if>
            <if test=" params.chongwuContent != '' and params.chongwuContent != null and params.chongwuContent != 'null' ">
                and a.chongwu_content like CONCAT('%',#{params.chongwuContent},'%')
            </if>

        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>