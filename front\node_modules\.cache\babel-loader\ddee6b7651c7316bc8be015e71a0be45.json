{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\front\\src\\views\\modules\\forum\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\src\\views\\modules\\forum\\list.vue", "mtime": 1751514458859}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["AddOrUpdate", "styleJs", "utilsJs", "data", "searchForm", "key", "sessionTable", "role", "userId", "form", "id", "forumName", "yonghuId", "ziyuanzheId", "usersId", "forumContent", "superIds", "forumStateTypes", "insertTime", "updateTime", "createTime", "dataList", "pageIndex", "pageSize", "totalPage", "dataListLoading", "dataListSelections", "showFlag", "sfshVisiable", "shForm", "chartVisiable", "echartsDate", "Date", "addOrUpdateFlag", "contents", "layouts", "forumReplyDialogVisible", "forumReplyInfoDialogVisible", "forumTitle", "forumReplyContent", "forumReplyInfoContent", "forumData", "json_fields", "created", "listStyle", "init", "getDataList", "contentStyleChange", "mounted", "$storage", "get", "filters", "htmlfilter", "val", "replace", "components", "computed", "methods", "chartDialog", "_this", "params", "dateFormat", "riqi", "getFullYear", "thisTable", "tableName", "sumColum", "date", "$nextTick", "statistic", "$echarts", "document", "getElementById", "$http", "url", "method", "then", "code", "yAxisName", "xAxisName", "series", "yAxis", "for<PERSON>ach", "item", "index", "tempMap", "name", "legend", "type", "push", "option", "tooltip", "trigger", "axisPointer", "crossStyle", "color", "toolbox", "feature", "magicType", "show", "saveAsImage", "xAxis", "axisLabel", "formatter", "setOption", "window", "onresize", "resize", "$message", "message", "duration", "onClose", "search", "contentSearchStyleChange", "contentBtnAdAllStyleChange", "contentSearchBtnStyleChange", "contentTableBtnStyleChange", "contentPageStyleChange", "querySelectorAll", "el", "textAlign", "inputFontPosition", "style", "height", "inputHeight", "lineHeight", "inputFontColor", "fontSize", "inputFontSize", "borderWidth", "inputBorderWidth", "borderStyle", "inputBorderStyle", "borderColor", "inputBorderColor", "borderRadius", "inputBorderRadius", "backgroundColor", "inputBgColor", "inputTitle", "inputTitleColor", "inputTitleSize", "setTimeout", "inputIconColor", "searchBtnHeight", "searchBtnFontColor", "searchBtnFontSize", "searchBtnBorderWidth", "searchBtnBorderStyle", "searchBtnBorderColor", "searchBtnBorderRadius", "searchBtnBgColor", "btnAdAllHeight", "btnAdAllAddFontColor", "btnAdAllFontSize", "btnAdAllBorderWidth", "btnAdAllBorderStyle", "btnAdAllBorderColor", "btnAdAllBorderRadius", "btnAdAllAddBgColor", "btnAdAllDelFontColor", "btnAdAllDelBgColor", "btnAdAllWarnFontColor", "btnAdAllWarnBgColor", "rowStyle", "row", "rowIndex", "tableStripe", "tableStripeFontColor", "cellStyle", "tableStripeBgColor", "headerRowStyle", "tableHeaderFontColor", "headerCellStyle", "tableHeaderBgColor", "arr", "pageTotal", "pageSizes", "pagePrevNext", "pagePager", "pageJumper", "join", "pageEachNum", "page", "limit", "sort", "yo<PERSON><PERSON><PERSON><PERSON>", "undefined", "ziyuanzheName", "list", "total", "sizeChangeHandle", "currentChangeHandle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addOrUpdateHandler", "crossAddOrUpdateFlag", "$refs", "addOrUpdate", "download", "file", "open", "delete<PERSON><PERSON><PERSON>", "ids", "Number", "map", "$confirm", "confirmButtonText", "cancelButtonText", "error", "msg", "forumUploadSuccess", "forumUploadError", "openReplyForum", "forum", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yonghuPhone", "yonghuEmail", "yonghuDelete", "ziyuanzhePhoto", "ziyuanzhePhone", "ziyuanzheEmail", "ziyuanzheDelete", "username", "password", "addtime", "see<PERSON><PERSON><PERSON><PERSON>nt", "deleteForumData", "forumReply", "alert"], "sources": ["src/views/modules/forum/list.vue"], "sourcesContent": ["<template>\r\n    <div class=\"main-content\">\r\n        <el-dialog title=\"帖子回复详情\" :visible.sync=\"forumReplyDialogVisible\">\r\n            <el-dialog\r\n                    width=\"30%\"\r\n                    :title=\"forumReplyInfoContent\"\r\n                    :visible.sync=\"forumReplyInfoDialogVisible\"\r\n                    append-to-body>\r\n            </el-dialog>\r\n            <div class=\"demo-input-suffix\">\r\n                <span style=\"width: 20%\">帖子标题：</span><el-input v-model=\"forumTitle\" :disabled=\"true\" placeholder=\"帖子标题\" style=\"width: 80%\"></el-input>\r\n            </div>\r\n            <div class=\"demo-input-suffix\">\r\n                <span style=\"width: 20%\">帖子内容：</span><el-input v-model=\"forumContent\" :disabled=\"true\" placeholder=\"帖子内容\" style=\"width: 80%\" type=\"textarea\"></el-input>\r\n            </div>\r\n            <el-table :data=\"forumData\" height=\"250\">\r\n                <!--<el-table-column label=\"id\" width=\"40\"></el-table-column>-->\r\n                <el-table-column label=\"身份\">\r\n                    <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.row.yonghuId\">\r\n                            用户\r\n                        </span>\r\n                        <span v-if=\"scope.row.ziyuanzheId\">\r\n                            自愿者\r\n                        </span>\r\n                        <span v-if=\"scope.row.usersId\">\r\n                            管理员\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.row.yonghuId\">\r\n                            {{scope.row.yonghuName}}\r\n                        </span>\r\n                        <span v-if=\"scope.row.ziyuanzheId\">\r\n                            {{scope.row.ziyuanzheName}}\r\n                        </span>\r\n                        <span v-if=\"scope.row.usersId\">\r\n                            管理员\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"手机号\">\r\n                    <template slot-scope=\"scope\">\r\n                        <span v-if=\"scope.row.yonghuId\">\r\n                            {{scope.row.yonghuPhone}}\r\n                        </span>\r\n                        <span v-if=\"scope.row.ziyuanzheId\">\r\n                            {{scope.row.ziyuanzhePhone}}\r\n                        </span>\r\n                        <span v-if=\"scope.row.usersId\">\r\n                            管理员\r\n                        </span>\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"回复内容\">\r\n                    <template slot-scope=\"scope\">\r\n                        {{scope.row.forumContent.length>20?(scope.row.forumContent.substring(0,20)+'...'):scope.row.forumContent}}\r\n                    </template>\r\n                </el-table-column>\r\n                <el-table-column property=\"insertTime\" label=\"回帖时间\"></el-table-column>\r\n                <el-table-column property=\"caozuo\" label=\"操作\">\r\n                    <template slot-scope=\"scope\">\r\n                        <el-button type=\"info\"                          @click=\"seeForumContent(scope.row.forumContent)\">回帖详情</el-button>\r\n                            <el-button v-if=\"true &&( false|| (sessionTable == 'yonghu' && scope.row.yonghuId ==userId)\r\n|| (sessionTable == 'ziyuanzhe' && scope.row.ziyuanzheId ==userId)\r\n|| sessionTable == 'users')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除帖子</el-button>\r\n                    </template>\r\n                </el-table-column>\r\n            </el-table>\r\n            <div class=\"demo-input-suffix\">\r\n                <span style=\"width: 20%\">回帖内容：</span>\r\n                <el-input v-model=\"forumReplyContent\" placeholder=\"回帖内容\" style=\"width:80%\" type=\"textarea\"></el-input>\r\n            </div>\r\n\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button @click=\"forumReplyDialogVisible = false\">取 消</el-button>\r\n                <el-button type=\"primary\" @click=\"forumReply\">回 帖</el-button>\r\n            </div>\r\n        </el-dialog>\r\n        <!-- 条件查询 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-form :inline=\"true\" :model=\"searchForm\" class=\"form-content\">\r\n                <el-row :gutter=\"20\" class=\"slt\" :style=\"{justifyContent:contents.searchBoxPosition=='1'?'flex-start':contents.searchBoxPosition=='2'?'center':'flex-end'}\">\r\n                 \r\n                     <el-form-item :label=\"contents.inputTitle == 1 ? '帖子标题' : ''\">\r\n                         <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.forumName\" placeholder=\"帖子标题\" clearable></el-input>\r\n                     </el-form-item>\r\n                                                                        \r\n                                         \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '用户姓名' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.yonghuName\" placeholder=\"用户姓名\" clearable></el-input>\r\n                    </el-form-item>\r\n                                                                                                                                             \r\n                    <el-form-item :label=\"contents.inputTitle == 1 ? '自愿者姓名' : ''\">\r\n                        <el-input prefix-icon=\"el-icon-search\" v-model=\"searchForm.ziyuanzheName\" placeholder=\"自愿者姓名\" clearable></el-input>\r\n                    </el-form-item>\r\n                                                                                                                                                                                                        \r\n\r\n                    <el-form-item>\r\n                        <el-button type=\"success\" @click=\"search()\">查询<i class=\"el-icon-search el-icon--right\"/></el-button>\r\n                    </el-form-item>\r\n                </el-row>\r\n                <el-row class=\"ad\" :style=\"{justifyContent:contents.btnAdAllBoxPosition=='1'?'flex-start':contents.btnAdAllBoxPosition=='2'?'center':'flex-end'}\">\r\n                    <el-form-item>\r\n                        <el-button\r\n                                v-if=\"isAuth('forum','新增')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-plus\"\r\n                                @click=\"addOrUpdateHandler()\"\r\n                        >新增</el-button>\r\n                        &nbsp;\r\n                        <el-button\r\n                                v-if=\"isAuth('forum','报表')\"\r\n                                type=\"success\"\r\n                                icon=\"el-icon-pie-chart\"\r\n                                @click=\"chartDialog()\"\r\n                        >报表</el-button>\r\n                        &nbsp;\r\n                        <a style=\"text-decoration:none\" class=\"el-button el-button--success\"\r\n                           v-if=\"isAuth('forum','导入导出')\"\r\n                           icon=\"el-icon-download\"\r\n                           href=\"http://localhost:8080/liulangdongwubeihua/upload/forumMuBan.xls\"\r\n                        >批量导入论坛数据模板</a>\r\n                        &nbsp;\r\n                        <el-upload\r\n                                v-if=\"isAuth('forum','导入导出')\"\r\n                                style=\"display: inline-block\"\r\n                                action=\"liulangdongwubeihua/file/upload\"\r\n                                :on-success=\"forumUploadSuccess\"\r\n                                :on-error=\"forumUploadError\"\r\n                                :show-file-list = false>\r\n                            <el-button\r\n                                    v-if=\"isAuth('forum','导入导出')\"\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-upload2\"\r\n                            >批量导入论坛数据</el-button>\r\n                        </el-upload>\r\n                        &nbsp;\r\n                        <!-- 导出excel -->\r\n                        <download-excel v-if=\"isAuth('forum','导入导出')\" style=\"display: inline-block\" class = \"export-excel-wrapper\" :data = \"dataList\" :fields = \"json_fields\" name = \"forum.xls\">\r\n                            <!-- 导出excel -->\r\n                            <el-button\r\n                                    type=\"success\"\r\n                                    icon=\"el-icon-download\"\r\n                            >导出</el-button>\r\n                        </download-excel>\r\n                        &nbsp;\r\n                    </el-form-item>\r\n                </el-row>\r\n            </el-form>\r\n            <div class=\"table-content\">\r\n                <el-table class=\"tables\" :size=\"contents.tableSize\" :show-header=\"contents.tableShowHeader\"\r\n                          :header-row-style=\"headerRowStyle\" :header-cell-style=\"headerCellStyle\"\r\n                          :border=\"contents.tableBorder\"\r\n                          :fit=\"contents.tableFit\"\r\n                          :stripe=\"contents.tableStripe\"\r\n                          :row-style=\"rowStyle\"\r\n                          :cell-style=\"cellStyle\"\r\n                          :style=\"{width: '100%',fontSize:contents.tableContentFontSize,color:contents.tableContentFontColor}\"\r\n                          v-if=\"isAuth('forum','查看')\"\r\n                          :data=\"dataList\"\r\n                          v-loading=\"dataListLoading\"\r\n                          @selection-change=\"selectionChangeHandler\">\r\n                    <el-table-column label=\"索引\" v-if=\"contents.tableIndex\" type=\"index\" width=\"50\" />\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      header-align=\"center\"\r\n                                      label=\"身份\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.yonghuId\">\r\n                                用户\r\n                            </span>\r\n                            <span v-if=\"scope.row.ziyuanzheId\">\r\n                                自愿者\r\n                            </span>\r\n                            <span v-if=\"scope.row.usersId\">\r\n                                管理员\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      header-align=\"center\"\r\n                                      label=\"姓名\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.yonghuId\">\r\n                                {{scope.row.yonghuName}}\r\n                            </span>\r\n                            <span v-if=\"scope.row.ziyuanzheId\">\r\n                                {{scope.row.ziyuanzheName}}\r\n                            </span>\r\n                            <span v-if=\"scope.row.usersId\">\r\n                                管理员\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      header-align=\"center\"\r\n                                      label=\"手机号\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.yonghuId\">\r\n                                {{scope.row.yonghuPhone}}\r\n                            </span>\r\n                            <span v-if=\"scope.row.ziyuanzheId\">\r\n                                {{scope.row.ziyuanzhePhone}}\r\n                            </span>\r\n                            <span v-if=\"scope.row.usersId\">\r\n                                管理员\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      header-align=\"center\"\r\n                                      label=\"头像\">\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.row.yonghuId\">\r\n                                <div v-if=\"scope.row.yonghuPhoto\">\r\n                                    <img :src=\"scope.row.yonghuPhoto\" width=\"100\" height=\"100\">\r\n                                </div>\r\n                                <div v-else>无图片</div>\r\n                            </span>\r\n                            <span v-if=\"scope.row.ziyuanzheId\">\r\n                                <div v-if=\"scope.row.ziyuanzhePhoto\">\r\n                                    <img :src=\"scope.row.ziyuanzhePhoto\" width=\"100\" height=\"100\">\r\n                                </div>\r\n                                <div v-else>无图片</div>\r\n                            </span>\r\n                            <span v-if=\"scope.row.usersId\">\r\n                                管理员\r\n                            </span>\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"forumName\"\r\n                                      header-align=\"center\"\r\n                                      label=\"帖子标题\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.forumName}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                                      prop=\"forumContent\"\r\n                                      header-align=\"center\"\r\n                                      label=\"帖子内容\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.forumContent.length>20?(scope.row.forumContent.substring(0,20)+'...'):scope.row.forumContent}}\r\n                        </template>\r\n                    </el-table-column>\r\n                    <el-table-column  :sortable=\"contents.tableSortable\" :align=\"contents.tableAlign\"\r\n                      prop=\"insertTime\"\r\n                      header-align=\"center\"\r\n                      label=\"发帖时间\">\r\n                        <template slot-scope=\"scope\">\r\n                            {{scope.row.insertTime}}\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column width=\"300\" :align=\"contents.tableAlign\"\r\n                                     header-align=\"center\"\r\n                                     label=\"操作\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button v-if=\"isAuth('forum','查看')\" type=\"success\" icon=\"el-icon-tickets\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">详情</el-button>\r\n                            <el-button v-if=\"isAuth('forum','查看')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"openReplyForum(scope.row.id,scope.row.forumName,scope.row.forumContent)\">查看论坛回复</el-button>\r\n                            <el-button v-if=\"isAuth('forum','修改')\" type=\"primary\" icon=\"el-icon-edit\" size=\"mini\" @click=\"addOrUpdateHandler(scope.row.id)\">修改</el-button>\r\n\r\n\r\n                            <el-button v-if=\"true &&( false|| (sessionTable == 'yonghu' && scope.row.yonghuId ==userId)\r\n|| (sessionTable == 'ziyuanzhe' && scope.row.ziyuanzheId ==userId)\r\n|| sessionTable == 'users')\" type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteHandler(scope.row.id)\">删除帖子</el-button>\r\n\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n                <el-pagination\r\n                        clsss=\"pages\"\r\n                        :layout=\"layouts\"\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"Number(contents.pageEachNum)\"\r\n                        :total=\"totalPage\"\r\n                        :small=\"contents.pageStyle\"\r\n                        class=\"pagination-content\"\r\n                        :background=\"contents.pageBtnBG\"\r\n                        :style=\"{textAlign:contents.pagePosition==1?'left':contents.pagePosition==2?'center':'right'}\"\r\n                ></el-pagination>\r\n            </div>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n\r\n\r\n        <el-dialog title=\"统计报表\" :visible.sync=\"chartVisiable\" width=\"800\">\r\n            <el-date-picker\r\n                    v-model=\"echartsDate\"\r\n                    type=\"year\"\r\n                    placeholder=\"选择年\">\r\n            </el-date-picker>\r\n            <el-button @click=\"chartDialog()\">查询</el-button>\r\n            <div id=\"statistic\" style=\"width:100%;height:600px;\"></div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n\t\t\t\t<el-button @click=\"chartVisiable = false\">关闭</el-button>\r\n\t\t\t</span>\r\n        </el-dialog>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    import utilsJs from \"../../../utils/utils.js\";\r\n\r\n    export default {\r\n        data() {\r\n        return {\r\n            searchForm: {\r\n                key: \"\"\r\n            },\r\n            sessionTable : \"\",//登录账户所在表名\r\n            role : \"\",//权限\r\n            userId:\"\",//当前登录人的id\r\n    //级联表下拉框搜索条件\r\n    //当前表下拉框搜索条件\r\n            form:{\r\n                id : null,\r\n                forumName : null,\r\n                yonghuId : null,\r\n                ziyuanzheId : null,\r\n                usersId : null,\r\n                forumContent : null,\r\n                superIds : null,\r\n                forumStateTypes : null,\r\n                insertTime : null,\r\n                updateTime : null,\r\n                createTime : null,\r\n            },\r\n            dataList: [],\r\n            pageIndex: 1,\r\n            pageSize: 10,\r\n            totalPage: 0,\r\n            dataListLoading: false,\r\n            dataListSelections: [],\r\n            showFlag: true,\r\n            sfshVisiable: false,\r\n            shForm: {},\r\n            chartVisiable: false,\r\n            echartsDate: new Date(),//echarts的时间查询字段\r\n            addOrUpdateFlag:false,\r\n            contents:null,\r\n            layouts: '',\r\n\r\n            forumReplyDialogVisible : false,//论坛回复模态框\r\n            forumReplyInfoDialogVisible : false,//论坛回复详情模态框\r\n            superIds : \"\",//帖子id\r\n            forumTitle : \"\",//帖子标题\r\n            forumContent : \"\",//帖子内容\r\n            forumReplyContent : \"\",//帖子回复内容\r\n            forumReplyInfoContent : \"\",//帖子某个回复详情 全\r\n            forumData : [],//论坛回复数据集合\r\n\r\n            //导出excel\r\n            json_fields: {\r\n                //级联表字段\r\n                     '用户姓名': 'yonghuName',\r\n                     '头像': 'yonghuPhoto',\r\n                     '手机号': 'yonghuPhone',\r\n                     '电子邮箱': 'yonghuEmail',\r\n                     '自愿者姓名': 'ziyuanzheName',\r\n                     '头像': 'ziyuanzhePhoto',\r\n                     '手机号': 'ziyuanzhePhone',\r\n                     '电子邮箱': 'ziyuanzheEmail',\r\n                     '用户名': 'username',\r\n                     '角色': 'role',\r\n                     '新增时间': 'addtime',\r\n                //本表字段\r\n                     '帖子标题': \"forumName\",\r\n                     '父id': \"superIds\",\r\n                     '帖子状态': \"forumStateTypes\",\r\n                     '发帖时间': \"insertTime\",\r\n                     '修改时间': \"updateTime\",\r\n            },\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n            this.userId = this.$storage.get(\"userId\");\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        computed: {\r\n        },\r\n        methods: {\r\n            chartDialog() {\r\n                let _this = this;\r\n                let params = {\r\n                    dateFormat :\"%Y\", //%Y-%m\r\n                    riqi :_this.echartsDate.getFullYear(),\r\n                    // riqi :_this.echartsDate.getFullYear()+\"-\"+(_this.echartsDate.getMonth() + 1 < 10 ? '0' + (_this.echartsDate.getMonth() + 1) : _this.echartsDate.getMonth() + 1),\r\n                    thisTable : {//当前表\r\n                        tableName :'forum',//当前表表名,\r\n                        sumColum : 'forum_number', //求和字段\r\n                        date : 'insert_time',//分组日期字段\r\n                        // string : 'forum_name',//分组字符串字段\r\n                        // types : 'forum_types',//分组下拉框字段\r\n                    },\r\n                    // joinTable : {//级联表（可以不存在）\r\n                    //     tableName :'yonghu',//级联表表名\r\n                    //     // date : 'insert_time',//分组日期字段\r\n                    //     string : 'yonghu_name',//分组字符串字段\r\n                    //     // types : 'yonghu_types',//分组下拉框字段\r\n                    // }\r\n                }\r\n                _this.chartVisiable = true;\r\n                _this.$nextTick(() => {\r\n                    var statistic = this.$echarts.init(document.getElementById(\"statistic\"), 'macarons');\r\n                    this.$http({\r\n                        url: \"barSum\",\r\n                        method: \"get\",\r\n                        params: params\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n\r\n                            //柱状图 求和 已成功使用\r\n                            //start\r\n                            let yAxisName = \"数值\";//根据查询数据具体改(单列要改,多列不改)\r\n                            let xAxisName = \"月份\";\r\n                            let series = [];//具体数据值\r\n                            data.data.yAxis.forEach(function (item,index) {\r\n                                let tempMap = {};\r\n                                // tempMap.name= [\"数值\"];//根据查询数据具体改(单列要改,多列不改)\r\n                                tempMap.name=data.data.legend[index];\r\n                                tempMap.type='bar';\r\n                                tempMap.data=item;\r\n                                series.push(tempMap);\r\n\r\n                            })\r\n\r\n                            var option = {\r\n                                tooltip: {\r\n                                    trigger: 'axis',\r\n                                    axisPointer: {\r\n                                        type: 'cross',\r\n                                        crossStyle: {\r\n                                            color: '#999'\r\n                                        }\r\n                                    }\r\n                                },\r\n                                toolbox: {\r\n                                    feature: {\r\n                                        // dataView: { show: true, readOnly: false },  // 数据查看\r\n                                        magicType: { show: true, type: ['line', 'bar'] },//切换图形展示方式\r\n                                        // restore: { show: true }, // 刷新\r\n                                        saveAsImage: { show: true }//保存\r\n                                    }\r\n                                },\r\n                                legend: {\r\n                                    data: data.data.legend//标题  可以点击导致某一列数据消失\r\n                                },\r\n                                xAxis: [\r\n                                    {\r\n                                        type: 'category',\r\n                                        name: xAxisName,\r\n                                        data: data.data.xAxis,\r\n                                        axisPointer: {\r\n                                            type: 'shadow'\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                yAxis: [\r\n                                    {\r\n                                        type: 'value',//不能改\r\n                                        name: yAxisName,//y轴单位\r\n                                        axisLabel: {\r\n                                            formatter: '{value}' // 后缀\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                series:series//具体数据\r\n                            };\r\n                            // 使用刚指定的配置项和数据显示图表。\r\n                            statistic.setOption(option,true);\r\n                            //根据窗口的大小变动图表\r\n                            window.onresize = function () {\r\n                                statistic.resize();\r\n                            };\r\n                            //end\r\n                        }else {\r\n                            this.$message({\r\n                                message: \"报表未查询到数据\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n                ////饼状图\r\n                //_this.chartVisiable = true;\r\n                // this.$nextTick(()=>{\r\n                //     var statistic = this.$echarts.init(document.getElementById(\"statistic\"),'macarons');\r\n                //     let params = {\r\n                //         tableName: \"forum\",\r\n                //         groupColumn: \"forum_types\",\r\n                //     }\r\n                //     this.$http({\r\n                //         url: \"newSelectGroupCount\",\r\n                //         method: \"get\",\r\n                //         params: params\r\n                //     }).then(({data}) => {\r\n                //         if (data && data.code === 0) {\r\n                //             let res = data.data;\r\n                //             let xAxis = [];\r\n                //             let yAxis = [];\r\n                //             let pArray = []\r\n                //             var option = {};\r\n                //             for(let i=0;i<res.length;i++){\r\n                //                 xAxis.push(res[i].name);\r\n                //                 yAxis.push(res[i].value);\r\n                //                 pArray.push({\r\n                //                     value: res[i].value,\r\n                //                     name: res[i].name\r\n                //                 })\r\n                //                 option = {\r\n                //                     title: {\r\n                //                         text: '保险合同类型统计',\r\n                //                         left: 'center'\r\n                //                     },\r\n                //                     tooltip: {\r\n                //                         trigger: 'item',\r\n                //                         formatter: '{b} : {c} ({d}%)'\r\n                //                     },\r\n                //                     series: [\r\n                //                         {\r\n                //                             type: 'pie',\r\n                //                             radius: '55%',\r\n                //                             center: ['50%', '60%'],\r\n                //                             data: pArray,\r\n                //                             emphasis: {\r\n                //                                 itemStyle: {\r\n                //                                     shadowBlur: 10,\r\n                //                                     shadowOffsetX: 0,\r\n                //                                     shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                //                                 }\r\n                //                             }\r\n                //                         }\r\n                //                     ]\r\n                //                 };\r\n                //             }\r\n                //                 statistic.setOption(option);\r\n                //                 window.onresize = function() {\r\n                //                     statistic.resize();\r\n                //                 };\r\n                //         }\r\n                //     });\r\n                // })\r\n            },\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el => {\r\n                        let textAlign = 'left'\r\n                        if(this.contents.inputFontPosition == 2)\r\n                            textAlign = 'center'\r\n                            if (this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                                el.style.textAlign = textAlign\r\n                            el.style.height = this.contents.inputHeight\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                            el.style.color = this.contents.inputFontColor\r\n                            el.style.fontSize = this.contents.inputFontSize\r\n                            el.style.borderWidth = this.contents.inputBorderWidth\r\n                            el.style.borderStyle = this.contents.inputBorderStyle\r\n                            el.style.borderColor = this.contents.inputBorderColor\r\n                            el.style.borderRadius = this.contents.inputBorderRadius\r\n                            el.style.backgroundColor = this.contents.inputBgColor\r\n                    })\r\n                    if (this.contents.inputTitle) {\r\n                        document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el => {\r\n                            el.style.color = this.contents.inputTitleColor\r\n                            el.style.fontSize = this.contents.inputTitleSize\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }\r\n                    setTimeout(() => {\r\n                        document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el => {\r\n                            el.style.color = this.contents.inputIconColor\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                        document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el => {\r\n                            el.style.lineHeight = this.contents.inputHeight\r\n                        })\r\n                    }, 10 )\r\n                })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.searchBtnHeight\r\n                        el.style.color = this.contents.searchBtnFontColor\r\n                        el.style.fontSize = this.contents.searchBtnFontSize\r\n                        el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                        el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                        el.style.borderColor = this.contents.searchBtnBorderColor\r\n                        el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                        el.style.backgroundColor = this.contents.searchBtnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(() => {\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllAddFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllDelFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n                    })\r\n                    document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el => {\r\n                        el.style.height = this.contents.btnAdAllHeight\r\n                        el.style.color = this.contents.btnAdAllWarnFontColor\r\n                        el.style.fontSize = this.contents.btnAdAllFontSize\r\n                        el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                        el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                        el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                        el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                        el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n                    })\r\n                })\r\n            },\r\n            // 表格\r\n            rowStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {color: this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if (this.contents.tableStripe) {\r\n                        return {backgroundColor: this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({row, rowIndex}) {\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({row, rowIndex}) {\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange() {\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange() {\r\n                let arr = []\r\n                if (this.contents.pageTotal) arr.push('total')\r\n                if (this.contents.pageSizes) arr.push('sizes')\r\n                if (this.contents.pagePrevNext) {\r\n                    arr.push('prev')\r\n                    if (this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if (this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init() {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                    forumStateTypes:1\r\n                }\r\n\r\n                                         \r\n                if (this.searchForm.yonghuName!= '' && this.searchForm.yonghuName!= undefined) {\r\n                    params['yonghuName'] = '%' + this.searchForm.yonghuName + '%'\r\n                }\r\n                                                                                                                                             \r\n                if (this.searchForm.ziyuanzheName!= '' && this.searchForm.ziyuanzheName!= undefined) {\r\n                    params['ziyuanzheName'] = '%' + this.searchForm.ziyuanzheName + '%'\r\n                }\r\n                                                                                                                                                                                                                         \r\n                if (this.searchForm.forumName!= '' && this.searchForm.forumName!= undefined) {\r\n                    params['forumName'] = '%' + this.searchForm.forumName + '%'\r\n                }\r\n                                                                        \r\n                params['forumDelete'] = 1// 逻辑删除字段 1 未删除 2 删除\r\n\r\n\r\n                this.$http({\r\n                    url: \"forum/list\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        this.dataList = data.data.list;\r\n                        this.totalPage = data.data.total;\r\n                    }else{\r\n                        this.dataList = [];\r\n                        this.totalPage = 0;\r\n                    }\r\n                    this.dataListLoading = false;\r\n                });\r\n\r\n                //查询级联表搜索条件所有列表\r\n                //查询当前表搜索条件所有列表\r\n            },\r\n            //每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id, type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if (type != 'info') {\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id, type);\r\n                });\r\n            },\r\n            // 下载\r\n            download(file) {\r\n                window.open(\" ${file} \")\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id ? [Number(id)] : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n                });\r\n\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: \"forum/delete\",\r\n                        method: \"post\",\r\n                        data: ids\r\n                    }).then(({data}) => {\r\n                        if(data && data.code === 0){\r\n                            this.$message({\r\n                                message: \"操作成功\",\r\n                                type: \"success\",\r\n                                duration: 1500,\r\n                                onClose: () => {\r\n                                    this.forumReplyDialogVisible = false;\r\n                                    this.search();\r\n                                }\r\n                            });\r\n                        }else{\r\n                            this.$message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n            // 导入功能上传文件成功后调用导入方法\r\n            forumUploadSuccess(data){\r\n                let _this = this;\r\n                _this.$http({\r\n                    url: \"forum/batchInsert?fileName=\" + data.file,\r\n                    method: \"get\"\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"导入论坛数据成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.search();\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n\r\n            },\r\n            // 导入功能上传文件失败后调用导入方法\r\n            forumUploadError(data){\r\n                this.$message.error('上传失败');\r\n            },\r\n            // 打开回帖模态框\r\n            openReplyForum(id,forumName,forumContent) {\r\n                let _this = this;\r\n            // 当前帖子相关 start\r\n                _this.superIds = id;\r\n                _this.forumTitle = forumName;\r\n                _this.forumContent = forumContent;\r\n            // 当前帖子相关 end\r\n                _this.forumReplyContent = \"\";//帖子回复\r\n                _this.forumReplyDialogVisible = true;//论坛回复模态框\r\n                _this.forumReplyInfoDialogVisible = false;//论坛回复详情模态框\r\n\r\n\r\n                // 查看当前帖子的回复列表\r\n                let params = {\r\n                    page: 1,\r\n                    limit: 10000,\r\n                    sort: 'id',\r\n                    forumStateTypes:2,\r\n                    superIds:_this.superIds\r\n                }\r\n                _this.$http({\r\n                    url: \"forum/list\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({data}) => {\r\n                    if (data && data.code === 0) {\r\n                        _this.forumData = [];\r\n                        data.data.list.forEach(el=>{\r\n                            let forum  = {};\r\n                            forum.id = el.id;\r\n                            forum.forumName = el.forumName;\r\n                            forum.yonghuId = el.yonghuId;//---\r\n                            forum.yonghuName = el.yonghuName;\r\n                            forum.yonghuPhoto = el.yonghuPhoto;\r\n                            forum.yonghuPhone = el.yonghuPhone;\r\n                            forum.yonghuEmail = el.yonghuEmail;\r\n                            forum.yonghuDelete = el.yonghuDelete;\r\n                            forum.createTime = el.createTime;\r\n                            forum.ziyuanzheId = el.ziyuanzheId;//---\r\n                            forum.ziyuanzheName = el.ziyuanzheName;\r\n                            forum.ziyuanzhePhoto = el.ziyuanzhePhoto;\r\n                            forum.ziyuanzhePhone = el.ziyuanzhePhone;\r\n                            forum.ziyuanzheEmail = el.ziyuanzheEmail;\r\n                            forum.ziyuanzheDelete = el.ziyuanzheDelete;\r\n                            forum.createTime = el.createTime;\r\n                            forum.usersId = el.usersId;//---\r\n                            forum.username = el.username;\r\n                            forum.password = el.password;\r\n                            forum.role = el.role;\r\n                            forum.addtime = el.addtime;\r\n                            forum.forumContent = el.forumContent;\r\n                            forum.superIds = el.superIds;\r\n                            forum.forumStateTypes = el.forumStateTypes;\r\n                            forum.insertTime = el.insertTime;\r\n                            forum.updateTime = el.updateTime;\r\n                            forum.createTime = el.createTime;\r\n                            _this.forumData.push(forum);\r\n                        })\r\n                    }\r\n                });\r\n            },\r\n\r\n            // 查看某个回复帖子的帖子内容全部\r\n            seeForumContent(forumContent) {\r\n                let _this = this;\r\n                _this.forumReplyInfoContent = forumContent;//帖子某个回复详情 全\r\n                _this.forumReplyInfoDialogVisible = true;//论坛回复详情模态框\r\n            },\r\n            // 删除数据\r\n            deleteForumData(id){\r\n                let _this = this;\r\n                let ids = [];\r\n                ids.push(Number(id));\r\n                _this.$http({\r\n                    url: \"forum/delete\",\r\n                    method: \"post\",\r\n                    data: ids\r\n                }).then(({data}) => {\r\n                    if(data && data.code === 0){\r\n                        _this.$message({\r\n                            message: \"删除回帖成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.openReplyForum(_this.superIds,_this.forumTitle,_this.forumContent);\r\n                            }\r\n                        });\r\n                    }else{\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n\r\n            // 回帖\r\n            forumReply() {\r\n                let _this = this;\r\n                if(_this.forumReplyContent == \"\"){\r\n                    alert(\"请输入回帖内容\");\r\n                    return false;\r\n                }\r\n                let data = {\"superIds\":_this.superIds,\"forumStateTypes\":2,\"forumContent\":_this.forumReplyContent};\r\n                _this.$http({\r\n                    url:`forum/save`,\r\n                    method: \"post\",\r\n                    data: data\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        _this.$message({\r\n                            message: \"回帖成功\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                _this.openReplyForum(_this.superIds,_this.forumTitle,_this.forumContent);\r\n                            }\r\n                        });\r\n                    } else {\r\n                        _this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.slt {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .ad {\r\n    margin: 0 !important;\r\n    display: flex;\r\n  }\r\n\r\n  .pages {\r\n    & ::v-deep el-pagination__sizes{\r\n      & ::v-deep el-input__inner {\r\n        height: 22px;\r\n        line-height: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .el-button+.el-button {\r\n    margin:0;\r\n  }\r\n\r\n  .tables {\r\n\t& ::v-deep .el-button--success {\r\n\t\theight: 40px;\r\n\t\tcolor: rgba(52, 51, 47, 0.93);\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 8px;\r\n\t\tbackground-color: rgba(232, 198, 111, 1);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--primary {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 8px;\r\n\t\tbackground-color: rgba(102, 130, 214, 0.51);\r\n\t}\r\n\r\n\t& ::v-deep .el-button--danger {\r\n\t\theight: 40px;\r\n\t\tcolor: #333;\r\n\t\tfont-size: 14px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: #DCDFE6;\r\n\t\tborder-radius: 8px;\r\n\t\tbackground-color: rgba(245, 83, 185, 0.72);\r\n\t}\r\n\r\n    & ::v-deep .el-button {\r\n      margin: 4px;\r\n    }\r\n  }\r\n</style>\r\n\r\n\r\n"], "mappings": "AAsTA,OAAAA,WAAA;AACA,OAAAC,OAAA;AACA,OAAAC,OAAA;AAEA;EACAC,KAAA;IACA;MACAC,UAAA;QACAC,GAAA;MACA;MACAC,YAAA;MAAA;MACAC,IAAA;MAAA;MACAC,MAAA;MAAA;MACA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,OAAA;QACAC,YAAA;QACAC,QAAA;QACAC,eAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,YAAA;MACAC,MAAA;MACAC,aAAA;MACAC,WAAA,MAAAC,IAAA;MAAA;MACAC,eAAA;MACAC,QAAA;MACAC,OAAA;MAEAC,uBAAA;MAAA;MACAC,2BAAA;MAAA;MACArB,QAAA;MAAA;MACAsB,UAAA;MAAA;MACAvB,YAAA;MAAA;MACAwB,iBAAA;MAAA;MACAC,qBAAA;MAAA;MACAC,SAAA;MAAA;;MAEA;MACAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAT,QAAA,GAAAjC,OAAA,CAAA2C,SAAA;IACA,KAAAC,IAAA;IACA,KAAAC,WAAA;IACA,KAAAC,kBAAA;EACA;EACAC,QAAA;IACA;IACA,KAAA1C,YAAA,QAAA2C,QAAA,CAAAC,GAAA;IACA,KAAA3C,IAAA,QAAA0C,QAAA,CAAAC,GAAA;IACA,KAAA1C,MAAA,QAAAyC,QAAA,CAAAC,GAAA;EAEA;EACAC,OAAA;IACAC,UAAA,WAAAA,CAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,OAAA,aAAAA,OAAA;IACA;EACA;EACAC,UAAA;IACAvD;EACA;EACAwD,QAAA,GACA;EACAC,OAAA;IACAC,YAAA;MACA,IAAAC,KAAA;MACA,IAAAC,MAAA;QACAC,UAAA;QAAA;QACAC,IAAA,EAAAH,KAAA,CAAA5B,WAAA,CAAAgC,WAAA;QACA;QACAC,SAAA;UAAA;UACAC,SAAA;UAAA;UACAC,QAAA;UAAA;UACAC,IAAA;UACA;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAR,KAAA,CAAA7B,aAAA;MACA6B,KAAA,CAAAS,SAAA;QACA,IAAAC,SAAA,QAAAC,QAAA,CAAAzB,IAAA,CAAA0B,QAAA,CAAAC,cAAA;QACA,KAAAC,KAAA;UACAC,GAAA;UACAC,MAAA;UACAf,MAAA,EAAAA;QACA,GAAAgB,IAAA;UAAAzE;QAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA0E,IAAA;YAEA;YACA;YACA,IAAAC,SAAA;YACA,IAAAC,SAAA;YACA,IAAAC,MAAA;YACA7E,IAAA,CAAAA,IAAA,CAAA8E,KAAA,CAAAC,OAAA,WAAAC,IAAA,EAAAC,KAAA;cACA,IAAAC,OAAA;cACA;cACAA,OAAA,CAAAC,IAAA,GAAAnF,IAAA,CAAAA,IAAA,CAAAoF,MAAA,CAAAH,KAAA;cACAC,OAAA,CAAAG,IAAA;cACAH,OAAA,CAAAlF,IAAA,GAAAgF,IAAA;cACAH,MAAA,CAAAS,IAAA,CAAAJ,OAAA;YAEA;YAEA,IAAAK,MAAA;cACAC,OAAA;gBACAC,OAAA;gBACAC,WAAA;kBACAL,IAAA;kBACAM,UAAA;oBACAC,KAAA;kBACA;gBACA;cACA;cACAC,OAAA;gBACAC,OAAA;kBACA;kBACAC,SAAA;oBAAAC,IAAA;oBAAAX,IAAA;kBAAA;kBAAA;kBACA;kBACAY,WAAA;oBAAAD,IAAA;kBAAA;gBACA;cACA;cACAZ,MAAA;gBACApF,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAAoF,MAAA;cACA;cACAc,KAAA,GACA;gBACAb,IAAA;gBACAF,IAAA,EAAAP,SAAA;gBACA5E,IAAA,EAAAA,IAAA,CAAAA,IAAA,CAAAkG,KAAA;gBACAR,WAAA;kBACAL,IAAA;gBACA;cACA,EACA;cACAP,KAAA,GACA;gBACAO,IAAA;gBAAA;gBACAF,IAAA,EAAAR,SAAA;gBAAA;gBACAwB,SAAA;kBACAC,SAAA;gBACA;cACA,EACA;cACAvB,MAAA,EAAAA,MAAA;YACA;YACA;YACAX,SAAA,CAAAmC,SAAA,CAAAd,MAAA;YACA;YACAe,MAAA,CAAAC,QAAA;cACArC,SAAA,CAAAsC,MAAA;YACA;YACA;UACA;YACA,KAAAC,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,EAAAA,CAAA;gBACA,KAAAC,MAAA;cACA;YACA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAjE,mBAAA;MACA,KAAAkE,wBAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,sBAAA;IACA;IACAJ,yBAAA;MACA,KAAA7C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,wCAAApC,OAAA,CAAAqC,EAAA;UACA,IAAAC,SAAA;UACA,SAAAtF,QAAA,CAAAuF,iBAAA,OACAD,SAAA;UACA,SAAAtF,QAAA,CAAAuF,iBAAA,OAAAD,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAF,SAAA,GAAAA,SAAA;UACAD,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAzF,QAAA,CAAA0F,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAA3F,QAAA,CAAA0F,WAAA;UACAL,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAA7D,QAAA,CAAA4F,cAAA;UACAP,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAA7F,QAAA,CAAA8F,aAAA;UACAT,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA/F,QAAA,CAAAgG,gBAAA;UACAX,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAAjG,QAAA,CAAAkG,gBAAA;UACAb,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAAnG,QAAA,CAAAoG,gBAAA;UACAf,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAArG,QAAA,CAAAsG,iBAAA;UACAjB,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAvG,QAAA,CAAAwG,YAAA;QACA;QACA,SAAAxG,QAAA,CAAAyG,UAAA;UACApE,QAAA,CAAA+C,gBAAA,4CAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAA7D,QAAA,CAAA0G,eAAA;YACArB,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAA7F,QAAA,CAAA2G,cAAA;YACAtB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAA3F,QAAA,CAAA0F,WAAA;UACA;QACA;QACAkB,UAAA;UACAvE,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAA7D,QAAA,CAAA6G,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAA3F,QAAA,CAAA0F,WAAA;UACA;UACArD,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAA7D,QAAA,CAAA6G,cAAA;YACAxB,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAA3F,QAAA,CAAA0F,WAAA;UACA;UACArD,QAAA,CAAA+C,gBAAA,uCAAApC,OAAA,CAAAqC,EAAA;YACAA,EAAA,CAAAG,KAAA,CAAAG,UAAA,QAAA3F,QAAA,CAAA0F,WAAA;UACA;QACA;MACA;IACA;IACA;IACAT,4BAAA;MACA,KAAA/C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,2CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAzF,QAAA,CAAA8G,eAAA;UACAzB,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAA7D,QAAA,CAAA+G,kBAAA;UACA1B,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAA7F,QAAA,CAAAgH,iBAAA;UACA3B,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA/F,QAAA,CAAAiH,oBAAA;UACA5B,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAAjG,QAAA,CAAAkH,oBAAA;UACA7B,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAAnG,QAAA,CAAAmH,oBAAA;UACA9B,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAArG,QAAA,CAAAoH,qBAAA;UACA/B,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAvG,QAAA,CAAAqH,gBAAA;QACA;MACA;IACA;IACA;IACArC,2BAAA;MACA,KAAA9C,SAAA;QACAG,QAAA,CAAA+C,gBAAA,0CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAzF,QAAA,CAAAsH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAA7D,QAAA,CAAAuH,oBAAA;UACAlC,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAA7F,QAAA,CAAAwH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA/F,QAAA,CAAAyH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAAjG,QAAA,CAAA0H,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAAnG,QAAA,CAAA2H,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAArG,QAAA,CAAA4H,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAvG,QAAA,CAAA6H,kBAAA;QACA;QACAxF,QAAA,CAAA+C,gBAAA,yCAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAzF,QAAA,CAAAsH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAA7D,QAAA,CAAA8H,oBAAA;UACAzC,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAA7F,QAAA,CAAAwH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA/F,QAAA,CAAAyH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAAjG,QAAA,CAAA0H,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAAnG,QAAA,CAAA2H,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAArG,QAAA,CAAA4H,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAvG,QAAA,CAAA+H,kBAAA;QACA;QACA1F,QAAA,CAAA+C,gBAAA,0CAAApC,OAAA,CAAAqC,EAAA;UACAA,EAAA,CAAAG,KAAA,CAAAC,MAAA,QAAAzF,QAAA,CAAAsH,cAAA;UACAjC,EAAA,CAAAG,KAAA,CAAA3B,KAAA,QAAA7D,QAAA,CAAAgI,qBAAA;UACA3C,EAAA,CAAAG,KAAA,CAAAK,QAAA,QAAA7F,QAAA,CAAAwH,gBAAA;UACAnC,EAAA,CAAAG,KAAA,CAAAO,WAAA,QAAA/F,QAAA,CAAAyH,mBAAA;UACApC,EAAA,CAAAG,KAAA,CAAAS,WAAA,QAAAjG,QAAA,CAAA0H,mBAAA;UACArC,EAAA,CAAAG,KAAA,CAAAW,WAAA,QAAAnG,QAAA,CAAA2H,mBAAA;UACAtC,EAAA,CAAAG,KAAA,CAAAa,YAAA,QAAArG,QAAA,CAAA4H,oBAAA;UACAvC,EAAA,CAAAG,KAAA,CAAAe,eAAA,QAAAvG,QAAA,CAAAiI,mBAAA;QACA;MACA;IACA;IACA;IACAC,SAAA;MAAAC,GAAA;MAAAC;IAAA;MACA,IAAAA,QAAA;QACA,SAAApI,QAAA,CAAAqI,WAAA;UACA;YAAAxE,KAAA,OAAA7D,QAAA,CAAAsI;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,UAAA;MAAAJ,GAAA;MAAAC;IAAA;MACA,IAAAA,QAAA;QACA,SAAApI,QAAA,CAAAqI,WAAA;UACA;YAAA9B,eAAA,OAAAvG,QAAA,CAAAwI;UAAA;QACA;MACA;QACA;MACA;IACA;IACAC,eAAA;MAAAN,GAAA;MAAAC;IAAA;MACA;QAAAvE,KAAA,OAAA7D,QAAA,CAAA0I;MAAA;IACA;IACAC,gBAAA;MAAAR,GAAA;MAAAC;IAAA;MACA;QAAA7B,eAAA,OAAAvG,QAAA,CAAA4I;MAAA;IACA;IACA;IACA1D,2BAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;IAAA,CACA;IACA;IACAC,uBAAA;MACA,IAAA0D,GAAA;MACA,SAAA7I,QAAA,CAAA8I,SAAA,EAAAD,GAAA,CAAAtF,IAAA;MACA,SAAAvD,QAAA,CAAA+I,SAAA,EAAAF,GAAA,CAAAtF,IAAA;MACA,SAAAvD,QAAA,CAAAgJ,YAAA;QACAH,GAAA,CAAAtF,IAAA;QACA,SAAAvD,QAAA,CAAAiJ,SAAA,EAAAJ,GAAA,CAAAtF,IAAA;QACAsF,GAAA,CAAAtF,IAAA;MACA;MACA,SAAAvD,QAAA,CAAAkJ,UAAA,EAAAL,GAAA,CAAAtF,IAAA;MACA,KAAAtD,OAAA,GAAA4I,GAAA,CAAAM,IAAA;MACA,KAAAnJ,QAAA,CAAAoJ,WAAA;IACA;IAEAzI,KAAA,GACA;IACAmE,OAAA;MACA,KAAA1F,SAAA;MACA,KAAAwB,WAAA;IACA;IACA;IACAA,YAAA;MACA,KAAArB,eAAA;MACA,IAAAmC,MAAA;QACA2H,IAAA,OAAAjK,SAAA;QACAkK,KAAA,OAAAjK,QAAA;QACAkK,IAAA;QACAxK,eAAA;MACA;MAGA,SAAAb,UAAA,CAAAsL,UAAA,eAAAtL,UAAA,CAAAsL,UAAA,IAAAC,SAAA;QACA/H,MAAA,4BAAAxD,UAAA,CAAAsL,UAAA;MACA;MAEA,SAAAtL,UAAA,CAAAwL,aAAA,eAAAxL,UAAA,CAAAwL,aAAA,IAAAD,SAAA;QACA/H,MAAA,+BAAAxD,UAAA,CAAAwL,aAAA;MACA;MAEA,SAAAxL,UAAA,CAAAO,SAAA,eAAAP,UAAA,CAAAO,SAAA,IAAAgL,SAAA;QACA/H,MAAA,2BAAAxD,UAAA,CAAAO,SAAA;MACA;MAEAiD,MAAA;;MAGA,KAAAa,KAAA;QACAC,GAAA;QACAC,MAAA;QACAf,MAAA,EAAAA;MACA,GAAAgB,IAAA;QAAAzE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA0E,IAAA;UACA,KAAAxD,QAAA,GAAAlB,IAAA,CAAAA,IAAA,CAAA0L,IAAA;UACA,KAAArK,SAAA,GAAArB,IAAA,CAAAA,IAAA,CAAA2L,KAAA;QACA;UACA,KAAAzK,QAAA;UACA,KAAAG,SAAA;QACA;QACA,KAAAC,eAAA;MACA;;MAEA;MACA;IACA;IACA;IACAsK,iBAAA1I,GAAA;MACA,KAAA9B,QAAA,GAAA8B,GAAA;MACA,KAAA/B,SAAA;MACA,KAAAwB,WAAA;IACA;IACA;IACAkJ,oBAAA3I,GAAA;MACA,KAAA/B,SAAA,GAAA+B,GAAA;MACA,KAAAP,WAAA;IACA;IACA;IACAmJ,uBAAA5I,GAAA;MACA,KAAA3B,kBAAA,GAAA2B,GAAA;IACA;IACA;IACA6I,mBAAAxL,EAAA,EAAA8E,IAAA;MACA,KAAA7D,QAAA;MACA,KAAAM,eAAA;MACA,KAAAkK,oBAAA;MACA,IAAA3G,IAAA;QACAA,IAAA;MACA;MACA,KAAApB,SAAA;QACA,KAAAgI,KAAA,CAAAC,WAAA,CAAAxJ,IAAA,CAAAnC,EAAA,EAAA8E,IAAA;MACA;IACA;IACA;IACA8G,SAAAC,IAAA;MACA9F,MAAA,CAAA+F,IAAA;IACA;IACA;IACAC,cAAA/L,EAAA;MACA,IAAAgM,GAAA,GAAAhM,EAAA,IAAAiM,MAAA,CAAAjM,EAAA,UAAAgB,kBAAA,CAAAkL,GAAA,CAAAzH,IAAA;QACA,OAAAwH,MAAA,CAAAxH,IAAA,CAAAzE,EAAA;MACA;MAEA,KAAAmM,QAAA,SAAAnM,EAAA;QACAoM,iBAAA;QACAC,gBAAA;QACAvH,IAAA;MACA,GAAAZ,IAAA;QACA,KAAAH,KAAA;UACAC,GAAA;UACAC,MAAA;UACAxE,IAAA,EAAAuM;QACA,GAAA9H,IAAA;UAAAzE;QAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA0E,IAAA;YACA,KAAA+B,QAAA;cACAC,OAAA;cACArB,IAAA;cACAsB,QAAA;cACAC,OAAA,EAAAA,CAAA;gBACA,KAAA3E,uBAAA;gBACA,KAAA4E,MAAA;cACA;YACA;UACA;YACA,KAAAJ,QAAA,CAAAoG,KAAA,CAAA7M,IAAA,CAAA8M,GAAA;UACA;QACA;MACA;IACA;IACA;IACAC,mBAAA/M,IAAA;MACA,IAAAwD,KAAA;MACAA,KAAA,CAAAc,KAAA;QACAC,GAAA,kCAAAvE,IAAA,CAAAoM,IAAA;QACA5H,MAAA;MACA,GAAAC,IAAA;QAAAzE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA0E,IAAA;UACAlB,KAAA,CAAAiD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,EAAAA,CAAA;cACApD,KAAA,CAAAqD,MAAA;YACA;UACA;QACA;UACArD,KAAA,CAAAiD,QAAA,CAAAoG,KAAA,CAAA7M,IAAA,CAAA8M,GAAA;QACA;MACA;IAEA;IACA;IACAE,iBAAAhN,IAAA;MACA,KAAAyG,QAAA,CAAAoG,KAAA;IACA;IACA;IACAI,eAAA1M,EAAA,EAAAC,SAAA,EAAAI,YAAA;MACA,IAAA4C,KAAA;MACA;MACAA,KAAA,CAAA3C,QAAA,GAAAN,EAAA;MACAiD,KAAA,CAAArB,UAAA,GAAA3B,SAAA;MACAgD,KAAA,CAAA5C,YAAA,GAAAA,YAAA;MACA;MACA4C,KAAA,CAAApB,iBAAA;MACAoB,KAAA,CAAAvB,uBAAA;MACAuB,KAAA,CAAAtB,2BAAA;;MAGA;MACA,IAAAuB,MAAA;QACA2H,IAAA;QACAC,KAAA;QACAC,IAAA;QACAxK,eAAA;QACAD,QAAA,EAAA2C,KAAA,CAAA3C;MACA;MACA2C,KAAA,CAAAc,KAAA;QACAC,GAAA;QACAC,MAAA;QACAf,MAAA,EAAAA;MACA,GAAAgB,IAAA;QAAAzE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA0E,IAAA;UACAlB,KAAA,CAAAlB,SAAA;UACAtC,IAAA,CAAAA,IAAA,CAAA0L,IAAA,CAAA3G,OAAA,CAAAqC,EAAA;YACA,IAAA8F,KAAA;YACAA,KAAA,CAAA3M,EAAA,GAAA6G,EAAA,CAAA7G,EAAA;YACA2M,KAAA,CAAA1M,SAAA,GAAA4G,EAAA,CAAA5G,SAAA;YACA0M,KAAA,CAAAzM,QAAA,GAAA2G,EAAA,CAAA3G,QAAA;YACAyM,KAAA,CAAA3B,UAAA,GAAAnE,EAAA,CAAAmE,UAAA;YACA2B,KAAA,CAAAC,WAAA,GAAA/F,EAAA,CAAA+F,WAAA;YACAD,KAAA,CAAAE,WAAA,GAAAhG,EAAA,CAAAgG,WAAA;YACAF,KAAA,CAAAG,WAAA,GAAAjG,EAAA,CAAAiG,WAAA;YACAH,KAAA,CAAAI,YAAA,GAAAlG,EAAA,CAAAkG,YAAA;YACAJ,KAAA,CAAAjM,UAAA,GAAAmG,EAAA,CAAAnG,UAAA;YACAiM,KAAA,CAAAxM,WAAA,GAAA0G,EAAA,CAAA1G,WAAA;YACAwM,KAAA,CAAAzB,aAAA,GAAArE,EAAA,CAAAqE,aAAA;YACAyB,KAAA,CAAAK,cAAA,GAAAnG,EAAA,CAAAmG,cAAA;YACAL,KAAA,CAAAM,cAAA,GAAApG,EAAA,CAAAoG,cAAA;YACAN,KAAA,CAAAO,cAAA,GAAArG,EAAA,CAAAqG,cAAA;YACAP,KAAA,CAAAQ,eAAA,GAAAtG,EAAA,CAAAsG,eAAA;YACAR,KAAA,CAAAjM,UAAA,GAAAmG,EAAA,CAAAnG,UAAA;YACAiM,KAAA,CAAAvM,OAAA,GAAAyG,EAAA,CAAAzG,OAAA;YACAuM,KAAA,CAAAS,QAAA,GAAAvG,EAAA,CAAAuG,QAAA;YACAT,KAAA,CAAAU,QAAA,GAAAxG,EAAA,CAAAwG,QAAA;YACAV,KAAA,CAAA9M,IAAA,GAAAgH,EAAA,CAAAhH,IAAA;YACA8M,KAAA,CAAAW,OAAA,GAAAzG,EAAA,CAAAyG,OAAA;YACAX,KAAA,CAAAtM,YAAA,GAAAwG,EAAA,CAAAxG,YAAA;YACAsM,KAAA,CAAArM,QAAA,GAAAuG,EAAA,CAAAvG,QAAA;YACAqM,KAAA,CAAApM,eAAA,GAAAsG,EAAA,CAAAtG,eAAA;YACAoM,KAAA,CAAAnM,UAAA,GAAAqG,EAAA,CAAArG,UAAA;YACAmM,KAAA,CAAAlM,UAAA,GAAAoG,EAAA,CAAApG,UAAA;YACAkM,KAAA,CAAAjM,UAAA,GAAAmG,EAAA,CAAAnG,UAAA;YACAuC,KAAA,CAAAlB,SAAA,CAAAgD,IAAA,CAAA4H,KAAA;UACA;QACA;MACA;IACA;IAEA;IACAY,gBAAAlN,YAAA;MACA,IAAA4C,KAAA;MACAA,KAAA,CAAAnB,qBAAA,GAAAzB,YAAA;MACA4C,KAAA,CAAAtB,2BAAA;IACA;IACA;IACA6L,gBAAAxN,EAAA;MACA,IAAAiD,KAAA;MACA,IAAA+I,GAAA;MACAA,GAAA,CAAAjH,IAAA,CAAAkH,MAAA,CAAAjM,EAAA;MACAiD,KAAA,CAAAc,KAAA;QACAC,GAAA;QACAC,MAAA;QACAxE,IAAA,EAAAuM;MACA,GAAA9H,IAAA;QAAAzE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA0E,IAAA;UACAlB,KAAA,CAAAiD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,EAAAA,CAAA;cACApD,KAAA,CAAAyJ,cAAA,CAAAzJ,KAAA,CAAA3C,QAAA,EAAA2C,KAAA,CAAArB,UAAA,EAAAqB,KAAA,CAAA5C,YAAA;YACA;UACA;QACA;UACA4C,KAAA,CAAAiD,QAAA,CAAAoG,KAAA,CAAA7M,IAAA,CAAA8M,GAAA;QACA;MACA;IACA;IAEA;IACAkB,WAAA;MACA,IAAAxK,KAAA;MACA,IAAAA,KAAA,CAAApB,iBAAA;QACA6L,KAAA;QACA;MACA;MACA,IAAAjO,IAAA;QAAA,YAAAwD,KAAA,CAAA3C,QAAA;QAAA;QAAA,gBAAA2C,KAAA,CAAApB;MAAA;MACAoB,KAAA,CAAAc,KAAA;QACAC,GAAA;QACAC,MAAA;QACAxE,IAAA,EAAAA;MACA,GAAAyE,IAAA;QAAAzE;MAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA0E,IAAA;UACAlB,KAAA,CAAAiD,QAAA;YACAC,OAAA;YACArB,IAAA;YACAsB,QAAA;YACAC,OAAA,EAAAA,CAAA;cACApD,KAAA,CAAAyJ,cAAA,CAAAzJ,KAAA,CAAA3C,QAAA,EAAA2C,KAAA,CAAArB,UAAA,EAAAqB,KAAA,CAAA5C,YAAA;YACA;UACA;QACA;UACA4C,KAAA,CAAAiD,QAAA,CAAAoG,KAAA,CAAA7M,IAAA,CAAA8M,GAAA;QACA;MACA;IACA;EAAA;AACA", "ignoreList": []}]}