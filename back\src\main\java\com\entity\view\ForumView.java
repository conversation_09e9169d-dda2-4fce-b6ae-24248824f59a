package com.entity.view;

import com.entity.ForumEntity;
import com.baomidou.mybatisplus.annotations.TableName;
import org.apache.commons.beanutils.BeanUtils;
import java.lang.reflect.InvocationTargetException;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

/**
 * 论坛
 * 后端返回视图实体辅助类
 * （通常后端关联的表或者自定义的字段需要返回使用）
 */
@TableName("forum")
public class ForumView extends ForumEntity implements Serializable {
    private static final long serialVersionUID = 1L;

		/**
		* 帖子状态的值
		*/
		private String forumStateValue;



		//级联表 yonghu
			/**
			* 用户姓名
			*/
			private String yonghuName;
			/**
			* 头像
			*/
			private String yonghuPhoto;
			/**
			* 手机号
			*/
			private String yonghuPhone;
			/**
			* 电子邮箱
			*/
			private String yonghuEmail;
			/**
			* 假删
			*/
			private Integer yonghuDelete;

		//级联表 ziyuanzhe
			/**
			* 自愿者姓名
			*/
			private String ziyuanzheName;
			/**
			* 头像
			*/
			private String ziyuanzhePhoto;
			/**
			* 手机号
			*/
			private String ziyuanzhePhone;
			/**
			* 电子邮箱
			*/
			private String ziyuanzheEmail;
			/**
			* 假删
			*/
			private Integer ziyuanzheDelete;

		//级联表 users
			/**
			* 用户名
			*/
			private String uusername;
			/**
			* 密码
			*/
			private String upassword;
			/**
			* 角色
			*/
			private String urole;
			/**
			* 新增时间
			*/
			private Date uaddtime;

	public ForumView() {

	}

	public ForumView(ForumEntity forumEntity) {
		try {
			BeanUtils.copyProperties(this, forumEntity);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}



			/**
			* 获取： 帖子状态的值
			*/
			public String getForumStateValue() {
				return forumStateValue;
			}
			/**
			* 设置： 帖子状态的值
			*/
			public void setForumStateValue(String forumStateValue) {
				this.forumStateValue = forumStateValue;
			}








































				//级联表的get和set yonghu

					/**
					* 获取： 用户姓名
					*/
					public String getYonghuName() {
						return yonghuName;
					}
					/**
					* 设置： 用户姓名
					*/
					public void setYonghuName(String yonghuName) {
						this.yonghuName = yonghuName;
					}

					/**
					* 获取： 头像
					*/
					public String getYonghuPhoto() {
						return yonghuPhoto;
					}
					/**
					* 设置： 头像
					*/
					public void setYonghuPhoto(String yonghuPhoto) {
						this.yonghuPhoto = yonghuPhoto;
					}

					/**
					* 获取： 手机号
					*/
					public String getYonghuPhone() {
						return yonghuPhone;
					}
					/**
					* 设置： 手机号
					*/
					public void setYonghuPhone(String yonghuPhone) {
						this.yonghuPhone = yonghuPhone;
					}

					/**
					* 获取： 电子邮箱
					*/
					public String getYonghuEmail() {
						return yonghuEmail;
					}
					/**
					* 设置： 电子邮箱
					*/
					public void setYonghuEmail(String yonghuEmail) {
						this.yonghuEmail = yonghuEmail;
					}

					/**
					* 获取： 假删
					*/
					public Integer getYonghuDelete() {
						return yonghuDelete;
					}
					/**
					* 设置： 假删
					*/
					public void setYonghuDelete(Integer yonghuDelete) {
						this.yonghuDelete = yonghuDelete;
					}





				//级联表的get和set ziyuanzhe

					/**
					* 获取： 自愿者姓名
					*/
					public String getZiyuanzheName() {
						return ziyuanzheName;
					}
					/**
					* 设置： 自愿者姓名
					*/
					public void setZiyuanzheName(String ziyuanzheName) {
						this.ziyuanzheName = ziyuanzheName;
					}

					/**
					* 获取： 头像
					*/
					public String getZiyuanzhePhoto() {
						return ziyuanzhePhoto;
					}
					/**
					* 设置： 头像
					*/
					public void setZiyuanzhePhoto(String ziyuanzhePhoto) {
						this.ziyuanzhePhoto = ziyuanzhePhoto;
					}

					/**
					* 获取： 手机号
					*/
					public String getZiyuanzhePhone() {
						return ziyuanzhePhone;
					}
					/**
					* 设置： 手机号
					*/
					public void setZiyuanzhePhone(String ziyuanzhePhone) {
						this.ziyuanzhePhone = ziyuanzhePhone;
					}

					/**
					* 获取： 电子邮箱
					*/
					public String getZiyuanzheEmail() {
						return ziyuanzheEmail;
					}
					/**
					* 设置： 电子邮箱
					*/
					public void setZiyuanzheEmail(String ziyuanzheEmail) {
						this.ziyuanzheEmail = ziyuanzheEmail;
					}

					/**
					* 获取： 假删
					*/
					public Integer getZiyuanzheDelete() {
						return ziyuanzheDelete;
					}
					/**
					* 设置： 假删
					*/
					public void setZiyuanzheDelete(Integer ziyuanzheDelete) {
						this.ziyuanzheDelete = ziyuanzheDelete;
					}





			//级联表的get和set users

					/**
					* 获取： 用户名
					*/
					public String getUusername() {
						return uusername;
					}
					/**
					* 设置： 用户名
					*/
					public void setUusername(String uusername) {
						this.uusername = uusername;
					}

					/**
					* 获取： 密码
					*/
					public String getUpassword() {
						return upassword;
					}
					/**
					* 设置： 密码
					*/
					public void setUpassword(String upassword) {
						this.upassword = upassword;
					}

					/**
					* 获取： 角色
					*/
					public String getUrole() {
						return urole;
					}
					/**
					* 设置： 角色
					*/
					public void setUrole(String urole) {
						this.urole = urole;
					}

					/**
					* 获取： 新增时间
					*/
					public Date getUaddtime() {
						return uaddtime;
					}
					/**
					* 设置： 新增时间
					*/
					public void setUaddtime(Date uaddtime) {
						this.uaddtime = uaddtime;
					}
}
