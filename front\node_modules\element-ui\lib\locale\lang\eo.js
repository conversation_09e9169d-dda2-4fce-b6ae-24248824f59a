'use strict';

exports.__esModule = true;
exports.default = {
  el: {
    colorpicker: {
      confirm: 'Bone',
      clear: '<PERSON>ple<PERSON><PERSON>'
    },
    datepicker: {
      now: 'Nun',
      today: 'Ho<PERSON><PERSON>',
      cancel: 'Nuligi',
      clear: '<PERSON><PERSON>nigi',
      confirm: '<PERSON>',
      selectDate: '<PERSON><PERSON><PERSON> daton',
      selectTime: '<PERSON>ek<PERSON> horon',
      startDate: 'Komenca Dato',
      startTime: 'Komenca Horo',
      endDate: 'Fina Dato',
      endTime: 'Fin<PERSON> Horo',
      prevYear: 'Antaŭa Jaro',
      nextYear: 'Sekva <PERSON>',
      prevMonth: 'Antaŭa Mona<PERSON>',
      nextMonth: 'Sek<PERSON>',
      year: 'Jaro',
      month1: 'Januaro',
      month2: 'Februaro',
      month3: 'Mart<PERSON>',
      month4: 'Aprilo',
      month5: 'Majo',
      month6: 'Junio',
      month7: 'Julio',
      month8: 'Aŭgusto',
      month9: 'Septembro',
      month10: 'Okto<PERSON>',
      month11: 'Novembro',
      month12: 'Decembro',
      week: '<PERSON><PERSON><PERSON><PERSON>',
      weeks: {
        sun: 'Dim',
        mon: 'Lun',
        tue: 'Mar',
        wed: 'Mer',
        thu: 'Ĵaŭ',
        fri: 'Ven',
        sat: 'Sab'
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'Maj',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aŭg',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Dec'
      }
    },
    select: {
      loading: 'Ŝarĝante',
      noMatch: 'Neniuj kongruaj datumoj',
      noData: 'Neniuj datumoj',
      placeholder: 'Bonvolu elekti'
    },
    cascader: {
      noMatch: 'Neniuj kongruaj datumoj',
      loading: 'Ŝarĝante',
      placeholder: 'Bonvolu elekti',
      noData: 'Neniuj datumoj'
    },
    pagination: {
      goto: 'Iru al',
      pagesize: '/ paĝo',
      total: 'Entute {total}',
      pageClassifier: ''
    },
    messagebox: {
      title: 'Mesaĝo',
      confirm: 'Bone',
      cancel: 'Nuligi',
      error: 'Nevalida Enigo!'
    },
    upload: {
      deleteTip: 'Premu "Delete" por forigi',
      delete: 'Forigi',
      preview: 'Antaŭrigardi',
      continue: 'Daŭrigi'
    },
    table: {
      emptyText: 'Neniuj datumoj',
      confirmFilter: 'Konfirmi',
      resetFilter: 'Restarigi',
      clearFilter: 'Ĉiuj',
      sumText: 'Sumo'
    },
    tree: {
      emptyText: 'Neniuj datumoj'
    },
    transfer: {
      noMatch: 'Neniuj kongruaj datumoj',
      noData: 'Neniuj datumoj',
      titles: ['Listo 1', 'Listo 2'],
      filterPlaceholder: 'Enigu ŝlosilvorton',
      noCheckedFormat: '{total} elementoj',
      hasCheckedFormat: '{checked}/{total} elektitaj'
    },
    image: {
      error: 'MALSUKCESIS'
    },
    pageHeader: {
      title: 'Reen'
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No' // to be translated
    },
    empty: {
      description: 'Neniuj datumoj'
    }
  }
};