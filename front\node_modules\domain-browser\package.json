{"_from": "domain-browser@^1.1.1", "_id": "domain-browser@1.2.0", "_inBundle": false, "_integrity": "sha512-jnjyiM6eRyZl2H+W8Q/zLMA481hzi0eszAaBUzIVnmYVDBbnLxVNnfu1HgEBvCbL+71FrxMl3E6lpKH7Ge3OXA==", "_location": "/domain-browser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "domain-browser@^1.1.1", "name": "domain-browser", "escapedName": "domain-browser", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmjs.org/domain-browser/-/domain-browser-1.2.0.tgz", "_shasum": "3d31f50191a6749dd1375a7f522e823d42e54eda", "_spec": "domain-browser@^1.1.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\node-libs-browser", "author": {"name": "2013+ Bevry Pty Ltd", "email": "<EMAIL>", "url": "http://bevry.me"}, "badges": {"list": ["travis<PERSON>", "npmversion", "npmdownloads", "da<PERSON><PERSON><PERSON>", "<PERSON><PERSON>dm<PERSON>v", "---", "patreon", "opencollective", "gratipay", "flattr", "paypal", "bitcoin", "wishlist", "---", "slackin"], "config": {"patreonUsername": "bevry", "opencollectiveUsername": "bevry", "gratipayUsername": "bevry", "flattrUsername": "bal<PERSON><PERSON>", "paypalURL": "https://bevry.me/paypal", "bitcoinURL": "https://bevry.me/bitcoin", "wishlistURL": "https://bevry.me/wishlist", "slackinURL": "https://slack.bevry.me"}}, "browser": "source/index.js", "bugs": {"url": "https://github.com/bevry/domain-browser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://balupton.com"}, {"name": "<PERSON>", "url": "http://evansolomon.me"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.neocities.org/"}, {"name": "<PERSON>", "email": "guy<PERSON><EMAIL>", "url": "twitter.com/guybedford"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TrySound"}], "dependencies": {}, "deprecated": false, "description": "<PERSON>de's domain module for the web browser. This is merely an evented try...catch with the same API as node, nothing more.", "devDependencies": {"assert-helpers": "^4.5.0", "eslint": "^4.16.0", "joe": "^2.0.2", "joe-reporter-console": "^2.0.1", "projectz": "^1.4.0"}, "editions": [{"description": "Source + ES5 + Require", "directory": "source", "entry": "index.js", "syntaxes": ["javascript", "es5", "require"]}], "engines": {"node": ">=0.4", "npm": ">=1.2"}, "homepage": "https://github.com/bevry/domain-browser", "jspm": {"map": {"source/index.js": {"node": "@node/domain"}}}, "keywords": ["domain", "trycatch", "try", "catch", "node-compat", "ender.js", "component", "component.io", "umd", "amd", "require.js", "browser"], "license": "MIT", "main": "source/index.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://balupton.com"}], "name": "domain-browser", "repository": {"type": "git", "url": "git+https://github.com/bevry/domain-browser.git"}, "scripts": {"our:clean": "rm -Rf ./docs ./es2015 ./es5 ./out", "our:compile": "echo no need for this project", "our:meta": "npm run our:meta:projectz", "our:meta:projectz": "projectz compile", "our:release": "npm run our:release:prepare && npm run our:release:check && npm run our:release:tag && npm run our:release:push", "our:release:check": "npm run our:release:check:changelog && npm run our:release:check:dirty", "our:release:check:changelog": "cat ./HISTORY.md | grep v$npm_package_version || (echo add a changelog entry for v$npm_package_version && exit -1)", "our:release:check:dirty": "git diff --exit-code", "our:release:prepare": "npm run our:clean && npm run our:compile && npm run our:test && npm run our:meta", "our:release:push": "git push origin master && git push origin --tags", "our:release:tag": "export MESSAGE=$(cat ./HISTORY.md | sed -n \"/## v$npm_package_version/,/##/p\" | sed 's/## //' | awk 'NR>1{print buf}{buf = $0}') && test \"$MESSAGE\" || (echo 'proper changelog entry not found' && exit -1) && git tag v$npm_package_version -am \"$MESSAGE\"", "our:setup": "npm run our:setup:npm", "our:setup:npm": "npm install", "our:test": "npm run our:verify && npm test", "our:verify": "npm run our:verify:eslint", "our:verify:eslint": "eslint --fix ./source", "test": "node --harmony source/test.js --joe-reporter=console"}, "version": "1.2.0"}