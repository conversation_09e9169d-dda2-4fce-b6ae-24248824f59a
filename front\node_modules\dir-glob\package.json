{"_from": "dir-glob@^2.0.0", "_id": "dir-glob@2.2.2", "_inBundle": false, "_integrity": "sha512-f9LBi5QWzIW3I6e//uxZoLBlUt9kcp66qo0sSCxL6YZKc75R1c4MFCoe/LaZiBGmgujvQdxc5Bn3QhfyvK5Hsw==", "_location": "/dir-glob", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dir-glob@^2.0.0", "name": "dir-glob", "escapedName": "dir-glob", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/copy-webpack-plugin/globby", "/globby"], "_resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-2.2.2.tgz", "_shasum": "fa09f0694153c8918b18ba0deafae94769fc50c4", "_spec": "dir-glob@^2.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\copy-webpack-plugin\\node_modules\\globby", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "bugs": {"url": "https://github.com/kevva/dir-glob/issues"}, "bundleDependencies": false, "dependencies": {"path-type": "^3.0.0"}, "deprecated": false, "description": "Convert directories to glob compatible strings", "devDependencies": {"ava": "^0.25.0", "del": "^3.0.0", "make-dir": "^1.0.0", "rimraf": "^2.5.0", "xo": "^0.20.3"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/kevva/dir-glob#readme", "keywords": ["convert", "directory", "extensions", "files", "glob"], "license": "MIT", "name": "dir-glob", "repository": {"type": "git", "url": "git+https://github.com/kevva/dir-glob.git"}, "scripts": {"test": "xo && ava"}, "version": "2.2.2"}