{"remainingRequest": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\code\\t\\157\\front\\src\\icons\\index.js", "dependencies": [{"path": "C:\\code\\t\\157\\front\\src\\icons\\index.js", "mtime": 1645616829699}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgU3ZnSWNvbiBmcm9tICdAL2NvbXBvbmVudHMvU3ZnSWNvbic7IC8vIHN2ZyBjb21wb25lbnQKCi8vIHJlZ2lzdGVyIGdsb2JhbGx5ClZ1ZS5jb21wb25lbnQoJ3N2Zy1pY29uJywgU3ZnSWNvbik7CmNvbnN0IHJlcSA9IHJlcXVpcmUuY29udGV4dCgnLi9zdmcvc3ZnJywgZmFsc2UsIC9cLnN2ZyQvKTsKY29uc3QgcmVxdWlyZUFsbCA9IHJlcXVpcmVDb250ZXh0ID0+IHJlcXVpcmVDb250ZXh0LmtleXMoKS5tYXAocmVxdWlyZUNvbnRleHQpOwpyZXF1aXJlQWxsKHJlcSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "SvgIcon", "component", "req", "require", "context", "requireAll", "requireContext", "keys", "map"], "sources": ["C:/code/t/157/front/src/icons/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport SvgIcon from '@/components/SvgIcon'// svg component\r\n\r\n// register globally\r\nVue.component('svg-icon', SvgIcon)\r\n\r\nconst req = require.context('./svg/svg', false, /\\.svg$/)\r\nconst requireAll = requireContext => requireContext.keys().map(requireContext)\r\nrequireAll(req)\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,OAAO,MAAM,sBAAsB;;AAE1C;AACAD,GAAG,CAACE,SAAS,CAAC,UAAU,EAAED,OAAO,CAAC;AAElC,MAAME,GAAG,GAAGC,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,CAAC;AACzD,MAAMC,UAAU,GAAGC,cAAc,IAAIA,cAAc,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAACF,cAAc,CAAC;AAC9ED,UAAU,CAACH,GAAG,CAAC", "ignoreList": []}]}