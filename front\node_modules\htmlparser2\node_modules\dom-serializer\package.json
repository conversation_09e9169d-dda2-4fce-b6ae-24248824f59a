{"_from": "dom-serializer@^1.0.1", "_id": "dom-serializer@1.4.1", "_inBundle": false, "_integrity": "sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==", "_location": "/htmlparser2/dom-serializer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dom-serializer@^1.0.1", "name": "dom-serializer", "escapedName": "dom-serializer", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/htmlparser2/domutils"], "_resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz", "_shasum": "de5d41b1aea290215dc45a6dae8adcf1d32e2d30", "_spec": "dom-serializer@^1.0.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\htmlparser2\\node_modules\\domutils", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "bundleDependencies": false, "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "deprecated": false, "description": "render domhandler DOM nodes to a string", "devDependencies": {"@types/jest": "^26.0.23", "@types/node": "^15.3.0", "@typescript-eslint/eslint-plugin": "^4.23.0", "@typescript-eslint/parser": "^4.23.0", "cheerio": "^1.0.0-rc.9", "coveralls": "^3.0.5", "eslint": "^7.26.0", "eslint-config-prettier": "^8.3.0", "htmlparser2": "^6.1.0", "jest": "^26.0.1", "prettier": "^2.3.0", "ts-jest": "^26.5.6", "typescript": "^4.0.2"}, "files": ["lib/**/*"], "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "keywords": ["html", "xml", "render"], "license": "MIT", "main": "lib/index.js", "name": "dom-serializer", "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "scripts": {"build": "tsc", "coverage": "cat coverage/lcov.info | coveralls", "format": "prettier --write '**/*.{ts,md,json}'", "lint": "eslint src", "prepare": "npm run build", "test": "jest --coverage && npm run lint"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "1.4.1"}