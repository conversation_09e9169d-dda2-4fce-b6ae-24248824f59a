{"_from": "data-view-byte-offset@^1.0.0", "_id": "data-view-byte-offset@1.0.0", "_inBundle": false, "_integrity": "sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==", "_location": "/data-view-byte-offset", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "data-view-byte-offset@^1.0.0", "name": "data-view-byte-offset", "escapedName": "data-view-byte-offset", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.0.tgz", "_shasum": "5e0bbfb4828ed2d1b9b400cd8a7d119bca0ff18a", "_spec": "data-view-byte-offset@^1.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/data-view-byte-offset/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.6", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "deprecated": false, "description": "Get the byteOffset out of a DataView, robustly.", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/call-bind": "^1.0.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/data-view-byte-offset#readme", "keywords": ["javascript", "ecmascript", "dataView", "data", "view", "byte", "offset", "byteOffset", "robust"], "license": "MIT", "main": "index.js", "name": "data-view-byte-offset", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/data-view-byte-offset.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "types": "./index.d.ts", "version": "1.0.0"}