{"_from": "mimic-fn@^2.1.0", "_id": "mimic-fn@2.1.0", "_inBundle": false, "_integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "_location": "/default-gateway/mimic-fn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "mimic-fn@^2.1.0", "name": "mimic-fn", "escapedName": "mimic-fn", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/default-gateway/onetime"], "_resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "_shasum": "7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b", "_spec": "mimic-fn@^2.1.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\default-gateway\\node_modules\\onetime", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Make a function mimic another one", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "license": "MIT", "name": "mimic-fn", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "2.1.0"}