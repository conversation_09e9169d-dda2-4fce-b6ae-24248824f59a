{"_from": "globby@^6.1.0", "_id": "globby@6.1.0", "_inBundle": false, "_integrity": "sha512-KVbFv2TQtbzCoxAnfD6JcHZTYCzyliEaaeM/gH8qQdkKr5s0OP9scEgvdcngyk7AVdY6YVW/TJHd+lQ/Df3Daw==", "_location": "/del/globby", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "globby@^6.1.0", "name": "globby", "escapedName": "globby", "rawSpec": "^6.1.0", "saveSpec": null, "fetchSpec": "^6.1.0"}, "_requiredBy": ["/del"], "_resolved": "https://registry.npmjs.org/globby/-/globby-6.1.0.tgz", "_shasum": "f5a6d70e8395e21c858fb0489d64df02424d506c", "_spec": "globby@^6.1.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\del", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/globby/issues"}, "bundleDependencies": false, "dependencies": {"array-union": "^1.0.1", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "deprecated": false, "description": "Extends `glob` with support for multiple patterns and exposes a Promise API", "devDependencies": {"ava": "*", "glob-stream": "github:gulpjs/glob-stream#master", "globby": "github:sindresorhus/globby#master", "matcha": "^0.7.0", "rimraf": "^2.2.8", "xo": "^0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/globby#readme", "keywords": ["all", "array", "directories", "dirs", "expand", "files", "filesystem", "filter", "find", "fnmatch", "folders", "fs", "glob", "globbing", "globs", "gulpfriendly", "match", "matcher", "minimatch", "multi", "multiple", "paths", "pattern", "patterns", "traverse", "util", "utility", "wildcard", "wildcards", "promise"], "license": "MIT", "name": "globby", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/globby.git"}, "scripts": {"bench": "npm update glob-stream && matcha bench.js", "test": "xo && ava"}, "version": "6.1.0"}