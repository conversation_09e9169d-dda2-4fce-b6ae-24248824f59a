# TinyMCE

TinyMCE is the world’s most popular open source web-based WYSIWYG editor.

Trusted and loved by millions of developers, and integrated into thousands of applications, such as:

- Content management systems (CMSs)
- Learning management systems (LMSs)
- Customer relationship management (CRM) and marketing automation systems
- Email marketing systems
- Content creation in SaaS systems

<p align="center">
  <img alt="Screenshot of the TinyMCE Editor" src="https://www.tiny.cloud/storage/github-readme-images/tinymce-editor.png"\>
</p>

## Our Tiny contribution

Content is powerful. It can be used to inform, educate, connect, learn, and inspire change.

More online applications are providing a platform for people to benefit from the transformational power of content.

With this in mind, as technology continues to advance and evolve, we want to make sure people will always have access to the best tools for creating content, enabling them to participate freely and seamlessly without barriers.

TinyMCE easily integrates into applications of all types and sizes, to provide the best content creation experience possible.

<p align="center">
  <img alt="Tiny Technologies Logo" src="https://www.tiny.cloud/storage/github-readme-images/tiny-image.png"\>
</p>

## Try the demo

You can access a [full featured demo of TinyMCE](https://www.tiny.cloud/docs/demo/full-featured/) in the docs on the Tiny website.

## Get started with TinyMCE

Get an instance of TinyMCE up and running quickly with the [TinyMCE quick start guide](https://www.tiny.cloud/docs/quick-start/).

TinyMCE provides a range of configuration options that allow you to integrate it into your application. Start customizing with a [basic setup](https://www.tiny.cloud/docs/general-configuration-guide/basic-setup/).

Configure it for one of three modes of editing:

- [TinyMCE classic editing mode](https://www.tiny.cloud/docs/general-configuration-guide/use-tinymce-classic/).
- [TinyMCE inline editing mode](https://www.tiny.cloud/docs/general-configuration-guide/use-tinymce-inline/).
- [TinyMCE distraction-free editing mode](https://www.tiny.cloud/docs/general-configuration-guide/use-tinymce-distraction-free/).

## Features

### Integration

TinyMCE is easily integrated into your projects with the help of components such as:

- [tinymce-react](https://github.com/tinymce/tinymce-react)
- [tinymce-vue](https://github.com/tinymce/tinymce-vue)
- [tinymce-angular](https://github.com/tinymce/tinymce-angular)

See the Tiny docs for a full list of [integration components](https://www.tiny.cloud/docs/integrations/).

### Customization

It is easy to [configure the UI](https://www.tiny.cloud/docs/general-configuration-guide/customize-ui/) to match the design of your site and applications, and you can [configure the editor](https://www.tiny.cloud/docs/general-configuration-guide/basic-setup/) with as much or as little functionality as you like, depending on your users and requirements.

With [50+ powerful plugins available](https://www.tiny.cloud/apps/), adding additional functionality is as simple as including a single line of code. Realizing the full power of most plugins requires only a few lines more.

### Extensibility

Sometimes your business requirements can be quite unique, and you need the freedom and flexibility to innovate. View the source code and develop your own extensions for custom functionality to meet your own requirements. The [API](https://www.tiny.cloud/docs/api/) is exposed to make it easier for you to write custom functionality that fits within the existing framework of TinyMCE [UI components](https://www.tiny.cloud/docs/ui-components/).

## Compiling and contributing

As TinyMCE transitioned to a modern codebase through 2017 and 2018, many external dependencies were added from previously closed-source projects. This became unwieldy to develop, so in June 2019 the decision was made to bring those projects together in a monorepo.

For information on compiling and contributing, see: [contribution guidelines](https://github.com/tinymce/tinymce/blob/master/CONTRIBUTING.md).

## Want more information?

Visit the [TinyMCE home page](https://tiny.cloud/) and check out the [TinyMCE documentation](https://www.tiny.cloud/docs/).
