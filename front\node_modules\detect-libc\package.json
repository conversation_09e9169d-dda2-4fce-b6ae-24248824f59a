{"_from": "detect-libc@^2.0.0", "_id": "detect-libc@2.0.4", "_inBundle": false, "_integrity": "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==", "_location": "/detect-libc", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "detect-libc@^2.0.0", "name": "detect-libc", "escapedName": "detect-libc", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/@mapbox/node-pre-gyp"], "_resolved": "https://mirrors.huaweicloud.com/repository/npm/detect-libc/-/detect-libc-2.0.4.tgz", "_shasum": "f04715b8ba815e53b4d8109655b6508a6865a7e8", "_spec": "detect-libc@^2.0.0", "_where": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\@mapbox\\node-pre-gyp", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Node.js module to detect the C standard library (libc) implementation family and version", "devDependencies": {"ava": "^2.4.0", "benchmark": "^2.1.4", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "semistandard": "^14.2.3"}, "engines": {"node": ">=8"}, "files": ["lib/", "index.d.ts"], "homepage": "https://github.com/lovell/detect-libc#readme", "keywords": ["libc", "glibc", "musl"], "license": "Apache-2.0", "main": "lib/detect-libc.js", "name": "detect-libc", "repository": {"type": "git", "url": "git://github.com/lovell/detect-libc.git"}, "scripts": {"bench": "node benchmark/detect-libc", "bench:calls": "node benchmark/call-familySync.js && sleep 1 && node benchmark/call-isNonGlibcLinuxSync.js && sleep 1 && node benchmark/call-versionSync.js", "test": "semistandard && nyc --reporter=text --check-coverage --branches=100 ava test/unit.js"}, "types": "index.d.ts", "version": "2.0.4"}