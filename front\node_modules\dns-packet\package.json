{"_from": "dns-packet@^1.3.1", "_id": "dns-packet@1.3.4", "_inBundle": false, "_integrity": "sha512-BQ6F4vycLXBvdrJZ6S3gZewt6rcrks9KBgM9vrhW+knGRqc8uEdT7fuCwloc7nny5xNoMJ17HGH0R/6fpo8ECA==", "_location": "/dns-packet", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dns-packet@^1.3.1", "name": "dns-packet", "escapedName": "dns-packet", "rawSpec": "^1.3.1", "saveSpec": null, "fetchSpec": "^1.3.1"}, "_requiredBy": ["/multicast-dns"], "_resolved": "https://registry.npmjs.org/dns-packet/-/dns-packet-1.3.4.tgz", "_shasum": "e3455065824a2507ba886c55a89963bb107dec6f", "_spec": "dns-packet@^1.3.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\multicast-dns", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/mafintosh/dns-packet/issues"}, "bundleDependencies": false, "dependencies": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}, "deprecated": false, "description": "An abstract-encoding compliant module for encoding / decoding DNS packets", "devDependencies": {"eslint": "^4.15.0", "standard": "^6.0.5", "tape": "^4.4.0"}, "files": ["index.js", "types.js", "rcodes.js", "opcodes.js"], "homepage": "https://github.com/mafintosh/dns-packet", "keywords": ["dns", "packet", "encodings", "encoding", "encoder", "abstract-encoding"], "license": "MIT", "main": "index.js", "name": "dns-packet", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/dns-packet.git"}, "scripts": {"test": "standard && eslint --color *.js && tape test.js"}, "version": "1.3.4"}