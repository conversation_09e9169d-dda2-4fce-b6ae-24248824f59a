{"_from": "decompress-response@^4.2.0", "_id": "decompress-response@4.2.1", "_inBundle": false, "_integrity": "sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==", "_location": "/decompress-response", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "decompress-response@^4.2.0", "name": "decompress-response", "escapedName": "decompress-response", "rawSpec": "^4.2.0", "saveSpec": null, "fetchSpec": "^4.2.0"}, "_requiredBy": ["/simple-get"], "_resolved": "https://mirrors.huaweicloud.com/repository/npm/decompress-response/-/decompress-response-4.2.1.tgz", "_shasum": "414023cc7a302da25ce2ec82d0d5238ccafd8986", "_spec": "decompress-response@^4.2.0", "_where": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\simple-get", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "bundleDependencies": false, "dependencies": {"mimic-response": "^2.0.0"}, "deprecated": false, "description": "Decompress a HTTP response if needed", "devDependencies": {"@types/node": "^12.7.1", "ava": "^2.2.0", "get-stream": "^5.0.0", "pify": "^4.0.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/decompress-response#readme", "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "license": "MIT", "name": "decompress-response", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.2.1"}