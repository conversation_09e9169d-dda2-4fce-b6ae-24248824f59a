{"_from": "cssnano-util-raw-cache@^4.0.1", "_id": "cssnano-util-raw-cache@4.0.1", "_inBundle": false, "_integrity": "sha512-qLuYtWK2b2Dy55I8ZX3ky1Z16WYsx544Q0UWViebptpwn/xDBmog2TLg4f+DBMg1rJ6JDWtn96WHbOKDWt1WQA==", "_location": "/cssnano-util-raw-cache", "_phantomChildren": {"source-map": "0.6.1"}, "_requested": {"type": "range", "registry": true, "raw": "cssnano-util-raw-cache@^4.0.1", "name": "cssnano-util-raw-cache", "escapedName": "cssnano-util-raw-cache", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/cssnano-preset-default"], "_resolved": "https://registry.npmjs.org/cssnano-util-raw-cache/-/cssnano-util-raw-cache-4.0.1.tgz", "_shasum": "b26d5fd5f72a11dfe7a7846fb4c67260f96bf282", "_spec": "cssnano-util-raw-cache@^4.0.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\cssnano-preset-default", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "dependencies": {"postcss": "^7.0.0"}, "deprecated": false, "description": "Manages the raw value formatting for generated AST nodes.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "license": "MIT", "main": "dist/index.js", "name": "cssnano-util-raw-cache", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.1"}