{"_from": "cssnano-util-same-parent@^4.0.0", "_id": "cssnano-util-same-parent@4.0.1", "_inBundle": false, "_integrity": "sha512-WcKx5OY+KoSIAxBW6UBBRay1U6vkYheCdjyVNDm85zt5K9mHoGOfsOsqIszfAqrQQFIIKgjh2+FDgIj/zsl21Q==", "_location": "/cssnano-util-same-parent", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cssnano-util-same-parent@^4.0.0", "name": "cssnano-util-same-parent", "escapedName": "cssnano-util-same-parent", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/postcss-merge-rules"], "_resolved": "https://registry.npmjs.org/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz", "_shasum": "574082fb2859d2db433855835d9a8456ea18bbf3", "_spec": "cssnano-util-same-parent@^4.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\postcss-merge-rules", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check that two PostCSS nodes share the same parent.", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}, "files": ["LICENSE-MIT", "dist"], "homepage": "https://github.com/cssnano/cssnano", "license": "MIT", "main": "dist/index.js", "name": "cssnano-util-same-parent", "repository": {"type": "git", "url": "git+https://github.com/cssnano/cssnano.git"}, "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "version": "4.0.1"}