{"_from": "html-webpack-plugin@^3.2.0", "_id": "html-webpack-plugin@3.2.0", "_inBundle": false, "_integrity": "sha512-Br4ifmjQojUP4EmHnRBoUIYcZ9J7M4bTMcm7u6xoIAIuq2Nte4TzXX0533owvkQKQD1WeMTTTyD4Ni4QKxS0Bg==", "_location": "/html-webpack-plugin", "_phantomChildren": {"define-properties": "1.2.1", "object-assign": "4.1.1", "object.getownpropertydescriptors": "2.1.8"}, "_requested": {"type": "range", "registry": true, "raw": "html-webpack-plugin@^3.2.0", "name": "html-webpack-plugin", "escapedName": "html-webpack-plugin", "rawSpec": "^3.2.0", "saveSpec": null, "fetchSpec": "^3.2.0"}, "_requiredBy": ["/@vue/cli-service", "/svg-sprite-loader"], "_resolved": "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-3.2.0.tgz", "_shasum": "b01abbd723acaaa7b37b6af4492ebda03d9dd37b", "_spec": "html-webpack-plugin@^3.2.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/ampedandwired"}, "bugs": {"url": "https://github.com/jantimon/html-webpack-plugin/issues"}, "bundleDependencies": false, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"html-minifier": "^3.2.3", "loader-utils": "^0.2.16", "lodash": "^4.17.3", "pretty-error": "^2.0.2", "tapable": "^1.0.0", "toposort": "^1.0.0", "util.promisify": "1.0.0"}, "deprecated": "3.x is no longer supported", "description": "Simplifies creation of HTML files to serve your webpack bundles", "devDependencies": {"appcache-webpack-plugin": "^1.3.0", "commitizen": "2.9.6", "css-loader": "^0.26.1", "cz-conventional-changelog": "2.1.0", "dir-compare": "1.3.0", "es6-promise": "^4.0.5", "extract-text-webpack-plugin": "^1.0.1", "file-loader": "^0.9.0", "html-loader": "^0.4.4", "jade": "^1.11.0", "jade-loader": "^0.8.0", "jasmine": "^2.5.2", "jasmine-diff-matchers": "^2.0.0", "rimraf": "^2.5.4", "semistandard": "8.0.0", "standard-version": "^4.3.0", "style-loader": "^0.13.1", "underscore-template-loader": "^0.7.3", "url-loader": "^0.5.7", "webpack": "^1.14.0", "webpack-recompilation-simulator": "^1.3.0"}, "engines": {"node": ">=6.9"}, "files": ["lib/", "index.js", "default_index.ejs"], "homepage": "https://github.com/jantimon/html-webpack-plugin", "keywords": ["webpack", "plugin", "html", "html-webpack-plugin"], "license": "MIT", "main": "index.js", "name": "html-webpack-plugin", "peerDependencies": {"webpack": "^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/jantimon/html-webpack-plugin.git"}, "scripts": {"build-examples": "node examples/build-examples.js", "commit": "git-cz", "pretest": "semistandard", "release": "standard-version", "test": "jasmine"}, "semistandard": {"ignore": ["examples/*/dist/**/*.*"]}, "version": "3.2.0"}