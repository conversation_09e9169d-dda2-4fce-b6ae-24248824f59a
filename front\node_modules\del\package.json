{"_from": "del@^4.1.1", "_id": "del@4.1.1", "_inBundle": false, "_integrity": "sha512-QwGuEUouP2kVwQenAsOof5Fv8K9t3D8Ca8NxcXKrIpEHjTXK5J2nXLdP+ALI1cgv8wj7KuwBhTwBkOZSJKM5XQ==", "_location": "/del", "_phantomChildren": {"array-union": "1.0.2", "glob": "7.2.3", "object-assign": "4.1.1", "pinkie-promise": "2.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "del@^4.1.1", "name": "del", "escapedName": "del", "rawSpec": "^4.1.1", "saveSpec": null, "fetchSpec": "^4.1.1"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/del/-/del-4.1.1.tgz", "_shasum": "9e8f117222ea44a31ff3a156c049b99052a9f0b4", "_spec": "del@^4.1.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\webpack-dev-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/del/issues"}, "bundleDependencies": false, "dependencies": {"@types/glob": "^7.1.1", "globby": "^6.1.0", "is-path-cwd": "^2.0.0", "is-path-in-cwd": "^2.0.0", "p-map": "^2.0.0", "pify": "^4.0.1", "rimraf": "^2.6.3"}, "deprecated": false, "description": "Delete files and folders", "devDependencies": {"ava": "^1.4.1", "make-dir": "^2.1.0", "tempy": "^0.2.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/del#readme", "keywords": ["delete", "files", "folders", "directories", "del", "remove", "destroy", "trash", "unlink", "clean", "cleaning", "cleanup", "rm", "rmrf", "<PERSON><PERSON><PERSON>", "rmdir", "glob", "gulpfriendly", "file", "folder", "directory", "dir", "fs", "filesystem"], "license": "MIT", "name": "del", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/del.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.1.1"}