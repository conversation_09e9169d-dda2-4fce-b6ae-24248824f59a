{"_from": "deep-equal@^1.0.1", "_id": "deep-equal@1.1.2", "_inBundle": false, "_integrity": "sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==", "_location": "/deep-equal", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "deep-equal@^1.0.1", "name": "deep-equal", "escapedName": "deep-equal", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/bonjour", "/quill", "/quill-delta"], "_resolved": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.2.tgz", "_shasum": "78a561b7830eef3134c7f6f3a3d6af272a678761", "_spec": "deep-equal@^1.0.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\quill", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"assert.js": false}, "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "bundleDependencies": false, "dependencies": {"is-arguments": "^1.1.1", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "object-is": "^1.1.5", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.5.1"}, "deprecated": false, "description": "node's assert.deepEqual algorithm", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "define-data-property": "^1.1.1", "eslint": "=8.8.0", "has-typed-arrays": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.7", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.7.2"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "keywords": ["equality", "equal", "compare"], "license": "MIT", "main": "index.js", "name": "deep-equal", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "version": "1.1.2"}