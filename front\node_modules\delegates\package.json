{"_from": "delegates@^1.0.0", "_id": "delegates@1.0.0", "_inBundle": false, "_integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==", "_location": "/delegates", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "delegates@^1.0.0", "name": "delegates", "escapedName": "delegates", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/are-we-there-yet"], "_resolved": "https://mirrors.huaweicloud.com/repository/npm/delegates/-/delegates-1.0.0.tgz", "_shasum": "84c6e159b81904fdca59a0ef44cd870d31250f9a", "_spec": "delegates@^1.0.0", "_where": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\are-we-there-yet", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "delegate methods and accessors to another property", "devDependencies": {"mocha": "*", "should": "*"}, "homepage": "https://github.com/visionmedia/node-delegates#readme", "keywords": ["delegate", "delegation"], "license": "MIT", "name": "delegates", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-delegates.git"}, "version": "1.0.0"}