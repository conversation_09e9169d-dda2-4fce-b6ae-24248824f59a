# List

<!-- MarkdownTOC -->

- [Static methods](#static-methods)
    - [List.createItem](#listcreateitem)
- [Properties](#properties)
    - [List#head](#listhead)
    - [List#tail](#listtail)
    - [List#cursor](#listcursor)
- [Methods](#methods)
    - [List#createItem\(\)](#listcreateitem-1)
    - [List#updateCursors\(\)](#listupdatecursors)
    - [List#getSize\(\)](#listgetsize)
    - [List#fromArray\(\)](#listfromarray)
    - [List#toArray\(\)](#listtoarray)
    - [List#toJSON\(\)](#listtojson)
    - [List#isEmpty\(\)](#listisempty)
    - [List#first\(\)](#listfirst)
    - [List#last\(\)](#listlast)
    - [List#each\(\)](#listeach)
    - [List#forEach\(\)](#listforeach)
    - [List#eachRight\(\)](#listeachright)
    - [List#forEachRight\(\)](#listforeachright)
    - [List#nextUntil\(\)](#listnextuntil)
    - [List#prevUntil\(\)](#listprevuntil)
    - [List#some\(\)](#listsome)
    - [List#map\(\)](#listmap)
    - [List#filter\(\)](#listfilter)
    - [List#clear\(\)](#listclear)
    - [List#copy\(\)](#listcopy)
    - [List#prepend\(\)](#listprepend)
    - [List#prependData\(\)](#listprependdata)
    - [List#append\(\)](#listappend)
    - [List#appendData\(\)](#listappenddata)
    - [List#insert\(\)](#listinsert)
    - [List#insertData\(\)](#listinsertdata)
    - [List#remove\(\)](#listremove)
    - [List#push\(\)](#listpush)
    - [List#pop\(\)](#listpop)
    - [List#unshift\(\)](#listunshift)
    - [List#shift\(\)](#listshift)
    - [List#prependList\(\)](#listprependlist)
    - [List#appendList\(\)](#listappendlist)
    - [List#insertList\(\)](#listinsertlist)
    - [List#replace\(\)](#listreplace)

<!-- /MarkdownTOC -->

## Static methods

### List.createItem

## Properties

### List#head

### List#tail

### List#cursor

## Methods

### List#createItem()

### List#updateCursors()

### List#getSize()

### List#fromArray()

### List#toArray()

### List#toJSON()

### List#isEmpty()

### List#first()

### List#last()

### List#each()

### List#forEach()

### List#eachRight()

### List#forEachRight()

### List#nextUntil()

### List#prevUntil()

### List#some()

### List#map()

### List#filter()

### List#clear()

### List#copy()

### List#prepend()

### List#prependData()

### List#append()

### List#appendData()

### List#insert()

### List#insertData()

### List#remove()

### List#push()

### List#pop()

### List#unshift()

### List#shift()

### List#prependList()

### List#appendList()

### List#insertList()

### List#replace()
