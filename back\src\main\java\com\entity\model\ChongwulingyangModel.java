package com.entity.model;

import com.entity.ChongwulingyangEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;


/**
 * 宠物领养
 * 接收传参的实体类
 *（实际开发中配合移动端接口开发手动去掉些没用的字段， 后端一般用entity就够用了）
 * 取自ModelAndView 的model名称
 */
public class ChongwulingyangModel implements Serializable {
    private static final long serialVersionUID = 1L;




    /**
     * 主键
     */
    private Integer id;


    /**
     * 标题
     */
    private String chongwulingyangName;


    /**
     * 宠物类型
     */
    private Integer chongwuTypes;


    /**
     * 宠物图片
     */
    private String chongwulingyangPhoto;


    /**
     * 是否被认领
     */
    private Integer jieshuTypes;


    /**
     * 宠物详情
     */
    private String chongwulingyangContent;


    /**
     * 创建时间  show1 show2 photoShow
     */
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
    private Date createTime;


    /**
	 * 获取：主键
	 */
    public Integer getId() {
        return id;
    }


    /**
	 * 设置：主键
	 */
    public void setId(Integer id) {
        this.id = id;
    }
    /**
	 * 获取：标题
	 */
    public String getChongwulingyangName() {
        return chongwulingyangName;
    }


    /**
	 * 设置：标题
	 */
    public void setChongwulingyangName(String chongwulingyangName) {
        this.chongwulingyangName = chongwulingyangName;
    }
    /**
	 * 获取：宠物类型
	 */
    public Integer getChongwuTypes() {
        return chongwuTypes;
    }


    /**
	 * 设置：宠物类型
	 */
    public void setChongwuTypes(Integer chongwuTypes) {
        this.chongwuTypes = chongwuTypes;
    }
    /**
	 * 获取：宠物图片
	 */
    public String getChongwulingyangPhoto() {
        return chongwulingyangPhoto;
    }


    /**
	 * 设置：宠物图片
	 */
    public void setChongwulingyangPhoto(String chongwulingyangPhoto) {
        this.chongwulingyangPhoto = chongwulingyangPhoto;
    }
    /**
	 * 获取：是否被认领
	 */
    public Integer getJieshuTypes() {
        return jieshuTypes;
    }


    /**
	 * 设置：是否被认领
	 */
    public void setJieshuTypes(Integer jieshuTypes) {
        this.jieshuTypes = jieshuTypes;
    }
    /**
	 * 获取：宠物详情
	 */
    public String getChongwulingyangContent() {
        return chongwulingyangContent;
    }


    /**
	 * 设置：宠物详情
	 */
    public void setChongwulingyangContent(String chongwulingyangContent) {
        this.chongwulingyangContent = chongwulingyangContent;
    }
    /**
	 * 获取：创建时间  show1 show2 photoShow
	 */
    public Date getCreateTime() {
        return createTime;
    }


    /**
	 * 设置：创建时间  show1 show2 photoShow
	 */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    }
