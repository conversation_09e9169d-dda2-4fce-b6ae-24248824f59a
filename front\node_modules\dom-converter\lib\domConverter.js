// Generated by CoffeeScript 1.12.7
var domToMarkup, object, objectToSaneObject, saneObjectToDom, self;

objectToSaneObject = require('./objectToSaneObject');

saneObjectToDom = require('./saneObjectToDom');

domToMarkup = require('./domToMarkup');

object = require('utila').object;

module.exports = self = {
  objectToDom: function(o) {
    o = self._object2SaneObject(o);
    return saneObjectToDom.convert(o);
  },
  object2markup: function(o) {
    var dom;
    dom = self.objectToDom(o);
    return domToMarkup.convert(dom);
  },
  domToMarkup: function(dom) {
    return domToMarkup.convert(dom);
  },
  _object2SaneObject: function(o) {
    if (!Array.isArray(o)) {
      if (!object.isBareObject(o)) {
        throw Error("toDom() only accepts arrays and bare objects as input");
      }
    }
    return objectToSaneObject.sanitize(o);
  }
};
