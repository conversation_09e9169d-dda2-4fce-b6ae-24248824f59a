{"remainingRequest": "C:\\code\\t\\t101\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\code\\t\\t101\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\code\\t\\t101\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\code\\t\\t101\\front\\src\\views\\404.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\code\\t\\t101\\front\\src\\views\\404.vue", "mtime": 1620012252000}, {"path": "C:\\code\\t\\t101\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\t101\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\code\\t\\t101\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\t101\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG1ldGhvZHM6IHsKICAgIGJhY2soKSB7CiAgICAgIHdpbmRvdy5oaXN0b3J5Lmxlbmd0aCA+IDEgPyB0aGlzLiRyb3V0ZXIuZ28oLTEpIDogdGhpcy4kcm91dGVyLnB1c2goIi8iKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["methods", "back", "window", "history", "length", "$router", "go", "push"], "sources": ["src/views/404.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <img class=\"backgroud\" src=\"@/assets/img/404.png\" alt>\r\n    <div class=\"text main-text\">出错了...页面失踪了</div>\r\n    <div>\r\n      <el-button class=\"text\" @click=\"back()\" type=\"text\" icon=\"el-icon-back\">返回</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  methods: {\r\n    back() {\r\n      window.history.length > 1 ? this.$router.go(-1) : this.$router.push(\"/\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 900px;\r\n  text-align: center;\r\n  .backgroud {\r\n    display: inline-block;\r\n    width: 200px;\r\n    height: 200px;\r\n    margin-top: 80px;\r\n  }\r\n  .main-text{\r\n    margin-top: 80px;\r\n  }\r\n  .text {\r\n    font-size: 24px;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": "AAWA;EACAA,OAAA;IACAC,KAAA;MACAC,MAAA,CAAAC,OAAA,CAAAC,MAAA,YAAAC,OAAA,CAAAC,EAAA,YAAAD,OAAA,CAAAE,IAAA;IACA;EACA;AACA", "ignoreList": []}]}