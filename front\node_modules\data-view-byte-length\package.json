{"_from": "data-view-byte-length@^1.0.1", "_id": "data-view-byte-length@1.0.1", "_inBundle": false, "_integrity": "sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==", "_location": "/data-view-byte-length", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "data-view-byte-length@^1.0.1", "name": "data-view-byte-length", "escapedName": "data-view-byte-length", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.1.tgz", "_shasum": "90721ca95ff280677eb793749fce1011347669e2", "_spec": "data-view-byte-length@^1.0.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/data-view-byte-length/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "deprecated": false, "description": "Get the byteLength out of a DataView, robustly.", "devDependencies": {"@arethetypeswrong/cli": "^0.15.1", "@ljharb/eslint-config": "^21.1.0", "@ljharb/tsconfig": "^0.1.1", "@types/call-bind": "^1.0.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/data-view-byte-length#readme", "keywords": ["javascript", "ecmascript", "dataview", "data", "view", "byte", "length", "byteLength", "robust"], "license": "MIT", "main": "index.js", "name": "data-view-byte-length", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/data-view-byte-length.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "types": "./index.d.ts", "version": "1.0.1"}