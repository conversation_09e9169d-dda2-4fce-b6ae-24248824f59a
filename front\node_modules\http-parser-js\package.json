{"_from": "http-parser-js@>=0.5.1", "_id": "http-parser-js@0.5.8", "_inBundle": false, "_integrity": "sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==", "_location": "/http-parser-js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "http-parser-js@>=0.5.1", "name": "http-parser-js", "escapedName": "http-parser-js", "rawSpec": ">=0.5.1", "saveSpec": null, "fetchSpec": ">=0.5.1"}, "_requiredBy": ["/websocket-driver"], "_resolved": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.8.tgz", "_shasum": "af23090d9ac4e24573de6f6aecc9d84a48bf20e3", "_spec": "http-parser-js@>=0.5.1", "_where": "C:\\code\\t\\t101\\front\\node_modules\\websocket-driver", "author": {"name": "<PERSON>", "url": "https://github.com/creationix"}, "bugs": {"url": "https://github.com/creationix/http-parser-js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/Jimbly"}, {"name": "<PERSON>", "url": "https://github.com/lrowe"}, {"name": "<PERSON>", "url": "https://github.com/jscissr"}, {"name": "<PERSON>", "url": "https://github.com/paulrutter"}], "deprecated": false, "description": "A pure JS HTTP parser for node.", "files": ["http-parser.js", "http-parser.d.ts"], "homepage": "https://github.com/creationix/http-parser-js#readme", "keywords": ["http"], "license": "MIT", "main": "http-parser.js", "name": "http-parser-js", "repository": {"type": "git", "url": "git://github.com/creationix/http-parser-js.git"}, "scripts": {"test": "python tests/test.py && node tests/iojs/test-http-parser-durability.js", "testv12": "python tests/test.py --node-args=\"--http-parser=legacy\" && node --http-parser=legacy tests/iojs/test-http-parser-durability.js"}, "types": "http-parser.d.ts", "version": "0.5.8"}