{"remainingRequest": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\xuangmu\\yuanma\\code1\\front\\src\\components\\common\\FileUpload.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\xuangmu\\yuanma\\code1\\front\\src\\components\\common\\FileUpload.vue", "mtime": 1649064848585}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\xuangmu\\yuanma\\code1\\front\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["storage", "base", "data", "dialogVisible", "dialogImageUrl", "fileList", "fileUrlList", "myHeaders", "props", "mounted", "init", "get", "watch", "fileUrls", "val", "oldVal", "computed", "getActionUrl", "$base", "name", "action", "methods", "split", "fileArray", "for<PERSON>ach", "item", "index", "url", "file", "push", "setFileList", "handleBeforeUpload", "handleUploadSuccess", "res", "code", "length", "response", "$emit", "join", "$message", "error", "msg", "handleUploadErr", "err", "handleRemove", "handleUploadPreview", "handleExceed", "files", "warning", "limit", "fileUrlArray", "token"], "sources": ["src/components/common/FileUpload.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 上传文件组件 -->\r\n    <el-upload\r\n      ref=\"upload\"\r\n      :action=\"getActionUrl\"\r\n      list-type=\"picture-card\"\r\n      :multiple=\"multiple\"\r\n      :limit=\"limit\"\r\n      :headers=\"myHeaders\"\r\n      :file-list=\"fileList\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-preview=\"handleUploadPreview\"\r\n      :on-remove=\"handleRemove\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadErr\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n      <div slot=\"tip\" class=\"el-upload__tip\" style=\"color:#838fa1;\">{{tip}}</div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\" size=\"tiny\" append-to-body>\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport storage from \"@/utils/storage\";\r\nimport base from \"@/utils/base\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 查看大图\r\n      dialogVisible: false,\r\n      // 查看大图\r\n      dialogImageUrl: \"\",\r\n      // 组件渲染图片的数组字段，有特殊格式要求\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      myHeaders:{}\r\n    };\r\n  },\r\n  props: [\"tip\", \"action\", \"limit\", \"multiple\", \"fileUrls\"],\r\n  mounted() {\r\n    this.init();\r\n    this.myHeaders= {\r\n      'Token':storage.get(\"Token\")\r\n    }\r\n  },\r\n  watch: {\r\n    fileUrls: function(val, oldVal) {\r\n      //   console.log(\"new: %s, old: %s\", val, oldVal);\r\n      this.init();\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n      // return base.url + this.action + \"?token=\" + storage.get(\"token\");\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化\r\n    init() {\r\n      //   console.log(this.fileUrls);\r\n      if (this.fileUrls) {\r\n        this.fileUrlList = this.fileUrls.split(\",\");\r\n        let fileArray = [];\r\n        this.fileUrlList.forEach(function(item, index) {\r\n          var url = item;\r\n          var name = index;\r\n          var file = {\r\n            name: name,\r\n            url: url\r\n          };\r\n          fileArray.push(file);\r\n        });\r\n        this.setFileList(fileArray);\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n\t\r\n    },\r\n    // 上传文件成功后执行\r\n    handleUploadSuccess(res, file, fileList) {\r\n      if (res && res.code === 0) {\r\n        fileList[fileList.length - 1][\"url\"] =\r\n          this.$base.url + \"upload/\" + file.response.file;\r\n        this.setFileList(fileList);\r\n        this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    // 图片上传失败\r\n    handleUploadErr(err, file, fileList) {\r\n      this.$message.error(\"文件上传失败\");\r\n    },\r\n    // 移除图片\r\n    handleRemove(file, fileList) {\r\n      this.setFileList(fileList);\r\n      this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n    },\r\n    // 查看大图\r\n    handleUploadPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 限制图片数量\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`最多上传${this.limit}张图片`);\r\n    },\r\n    // 重新对fileList进行赋值\r\n    setFileList(fileList) {\r\n      var fileArray = [];\r\n      var fileUrlArray = [];\r\n      // 有些图片不是公开的，所以需要携带token信息做权限校验\r\n      var token = storage.get(\"token\");\r\n      fileList.forEach(function(item, index) {\r\n        var url = item.url.split(\"?\")[0];\r\n        var name = item.name;\r\n        var file = {\r\n          name: name,\r\n          url: url + \"?token=\" + token\r\n        };\r\n        fileArray.push(file);\r\n        fileUrlArray.push(url);\r\n      });\r\n      this.fileList = fileArray;\r\n      this.fileUrlList = fileUrlArray;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"], "mappings": "AA2BA,OAAAA,OAAA;AACA,OAAAC,IAAA;AACA;EACAC,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;EACAC,QAAA;IACA,KAAAC,IAAA;IACA,KAAAH,SAAA;MACA,SAAAP,OAAA,CAAAW,GAAA;IACA;EACA;EACAC,KAAA;IACAC,QAAA,WAAAA,CAAAC,GAAA,EAAAC,MAAA;MACA;MACA,KAAAL,IAAA;IACA;EACA;EACAM,QAAA;IACA;IACAC,YAAA,WAAAA,CAAA;MACA;MACA,gBAAAC,KAAA,CAAAC,IAAA,WAAAC,MAAA;IACA;EACA;EACAC,OAAA;IACA;IACAX,KAAA;MACA;MACA,SAAAG,QAAA;QACA,KAAAP,WAAA,QAAAO,QAAA,CAAAS,KAAA;QACA,IAAAC,SAAA;QACA,KAAAjB,WAAA,CAAAkB,OAAA,WAAAC,IAAA,EAAAC,KAAA;UACA,IAAAC,GAAA,GAAAF,IAAA;UACA,IAAAN,IAAA,GAAAO,KAAA;UACA,IAAAE,IAAA;YACAT,IAAA,EAAAA,IAAA;YACAQ,GAAA,EAAAA;UACA;UACAJ,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACA;QACA,KAAAE,WAAA,CAAAP,SAAA;MACA;IACA;IACAQ,mBAAAH,IAAA,GAEA;IACA;IACAI,oBAAAC,GAAA,EAAAL,IAAA,EAAAvB,QAAA;MACA,IAAA4B,GAAA,IAAAA,GAAA,CAAAC,IAAA;QACA7B,QAAA,CAAAA,QAAA,CAAA8B,MAAA,eACA,KAAAjB,KAAA,CAAAS,GAAA,eAAAC,IAAA,CAAAQ,QAAA,CAAAR,IAAA;QACA,KAAAE,WAAA,CAAAzB,QAAA;QACA,KAAAgC,KAAA,gBAAA/B,WAAA,CAAAgC,IAAA;MACA;QACA,KAAAC,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAAQ,GAAA;MACA;IACA;IACA;IACAC,gBAAAC,GAAA,EAAAf,IAAA,EAAAvB,QAAA;MACA,KAAAkC,QAAA,CAAAC,KAAA;IACA;IACA;IACAI,aAAAhB,IAAA,EAAAvB,QAAA;MACA,KAAAyB,WAAA,CAAAzB,QAAA;MACA,KAAAgC,KAAA,gBAAA/B,WAAA,CAAAgC,IAAA;IACA;IACA;IACAO,oBAAAjB,IAAA;MACA,KAAAxB,cAAA,GAAAwB,IAAA,CAAAD,GAAA;MACA,KAAAxB,aAAA;IACA;IACA;IACA2C,aAAAC,KAAA,EAAA1C,QAAA;MACA,KAAAkC,QAAA,CAAAS,OAAA,aAAAC,KAAA;IACA;IACA;IACAnB,YAAAzB,QAAA;MACA,IAAAkB,SAAA;MACA,IAAA2B,YAAA;MACA;MACA,IAAAC,KAAA,GAAAnD,OAAA,CAAAW,GAAA;MACAN,QAAA,CAAAmB,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA,CAAAL,KAAA;QACA,IAAAH,IAAA,GAAAM,IAAA,CAAAN,IAAA;QACA,IAAAS,IAAA;UACAT,IAAA,EAAAA,IAAA;UACAQ,GAAA,EAAAA,GAAA,eAAAwB;QACA;QACA5B,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACAsB,YAAA,CAAArB,IAAA,CAAAF,GAAA;MACA;MACA,KAAAtB,QAAA,GAAAkB,SAAA;MACA,KAAAjB,WAAA,GAAA4C,YAAA;IACA;EACA;AACA", "ignoreList": []}]}