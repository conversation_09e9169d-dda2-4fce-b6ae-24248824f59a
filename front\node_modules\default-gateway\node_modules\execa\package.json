{"_from": "execa@^3.3.0", "_id": "execa@3.4.0", "_inBundle": false, "_integrity": "sha512-r9vdGQk4bmCuK1yKQu1KTwcT2zwfWdbdaXfCtAh+5nU/4fSX+JAb7vZGvI5naJrQlvONrEB20jeruESI69530g==", "_location": "/default-gateway/execa", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "execa@^3.3.0", "name": "execa", "escapedName": "execa", "rawSpec": "^3.3.0", "saveSpec": null, "fetchSpec": "^3.3.0"}, "_requiredBy": ["/default-gateway"], "_resolved": "https://registry.npmjs.org/execa/-/execa-3.4.0.tgz", "_shasum": "c08ed4550ef65d858fac269ffc8572446f37eb89", "_spec": "execa@^3.3.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\default-gateway", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "bundleDependencies": false, "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "p-finally": "^2.0.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "deprecated": false, "description": "Process execution for humans", "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.1.0", "coveralls": "^3.0.4", "get-node": "^5.0.0", "is-running": "^2.1.0", "nyc": "^14.1.1", "p-event": "^4.1.0", "tempfile": "^3.0.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "files": ["index.js", "index.d.ts", "lib"], "homepage": "https://github.com/sindresorhus/execa#readme", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "license": "MIT", "name": "execa", "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/execa.git"}, "scripts": {"test": "xo && nyc ava && tsd"}, "version": "3.4.0"}