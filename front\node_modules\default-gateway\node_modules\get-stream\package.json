{"_from": "get-stream@^5.0.0", "_id": "get-stream@5.2.0", "_inBundle": false, "_integrity": "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==", "_location": "/default-gateway/get-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "get-stream@^5.0.0", "name": "get-stream", "escapedName": "get-stream", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/default-gateway/execa"], "_resolved": "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz", "_shasum": "4966a1795ee5ace65e706c4b7beb71257d6e22d3", "_spec": "get-stream@^5.0.0", "_where": "C:\\code\\t\\t101\\front\\node_modules\\default-gateway\\node_modules\\execa", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "bundleDependencies": false, "dependencies": {"pump": "^3.0.0"}, "deprecated": false, "description": "Get a stream as a string, buffer, or array", "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.0.0", "into-stream": "^5.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts", "buffer-stream.js"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/get-stream#readme", "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "license": "MIT", "name": "get-stream", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "5.2.0"}