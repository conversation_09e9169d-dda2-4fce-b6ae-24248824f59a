/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.2.0 (2020-02-13)
 */
!function(v){"use strict";function Z(){}function i(e,o){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e(o.apply(null,n))}}function l(n){return n}var nn=function(n){return function(){return n}};function d(o){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var e=r.concat(n);return o.apply(null,e)}}function b(e){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return!e.apply(null,n)}}function r(n){return function(){throw new Error(n)}}var u=nn(!1),a=nn(!0),n=tinymce.util.Tools.resolve("tinymce.ThemeManager"),P=function(){return(P=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n}).apply(this,arguments)};function c(n,t){var e={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&t.indexOf(o)<0&&(e[o]=n[o]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(n);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(e[o[r]]=n[o[r]])}return e}function p(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;var o=Array(n),r=0;for(t=0;t<e;t++)for(var i=arguments[t],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}function t(){return s}var e,s=(e={fold:function(n,t){return n()},is:u,isSome:u,isNone:a,getOr:m,getOrThunk:f,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:nn(null),getOrUndefined:nn(undefined),or:m,orThunk:f,map:t,each:Z,bind:t,exists:u,forall:a,filter:t,equals:o,equals_:o,toArray:function(){return[]},toString:nn("none()")},Object.freeze&&Object.freeze(e),e);function o(n){return n.isNone()}function f(n){return n()}function m(n){return n}function g(t){return function(n){return function(n){if(null===n)return"null";var t=typeof n;return"object"==t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t}(n)===t}}function h(n,t){if(Q(n)){for(var e=0,o=n.length;e<o;++e)if(!0!==t(n[e]))return!1;return!0}return!1}function y(n,t){return an.call(n,t)}function x(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return!0}return!1}function w(n,t){for(var e=[],o=0;o<n.length;o+=t){var r=un.call(n,o,o+t);e.push(r)}return e}function S(n,t){for(var e=n.length,o=new Array(e),r=0;r<e;r++){var i=n[r];o[r]=t(i,r)}return o}function C(n,t){for(var e=[],o=0,r=n.length;o<r;o++){var i=n[o];t(i,o)&&e.push(i)}return e}function k(n,t,e){return function(n,t){for(var e=n.length-1;0<=e;e--){t(n[e],e)}}(n,function(n){e=t(e,n)}),e}function O(n,t,e){return fn(n,function(n){e=t(e,n)}),e}function _(n,t){for(var e=0,o=n.length;e<o;e++){var r=n[e];if(t(r,e))return tn.some(r)}return tn.none()}function T(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return tn.some(e)}return tn.none()}function z(n){for(var t=[],e=0,o=n.length;e<o;++e){if(!Q(n[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+n);cn.apply(t,n[e])}return t}function E(n,t){return z(S(n,t))}function B(n,t){for(var e=0,o=n.length;e<o;++e){if(!0!==t(n[e],e))return!1}return!0}function D(n){var t=un.call(n,0);return t.reverse(),t}function A(n,t){return C(n,function(n){return!sn(t,n)})}function M(n){return[n]}function F(n,t){var e=un.call(n,0);return e.sort(t),e}function I(n){return 0===n.length?tn.none():tn.some(n[n.length-1])}function R(n,t){for(var e=0;e<n.length;e++){var o=t(n[e],e);if(o.isSome())return o}return tn.none()}function L(n,e){return hn(n,function(n,t){return{k:t,v:e(n,t)}})}function V(n,t){for(var e=mn(n),o=0,r=e.length;o<r;o++){var i=e[o],u=n[i];if(t(u,i,n))return tn.some(u)}return tn.none()}function H(n){return vn(n,function(n){return n})}function N(n,t){return yn(n,t)&&n[t]!==undefined&&null!==n[t]}function j(u){return function(){for(var n=new Array(arguments.length),t=0;t<n.length;t++)n[t]=arguments[t];if(0===n.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<n.length;o++){var r=n[o];for(var i in r)wn.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}}function U(e){var o,r=!1;return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return r||(r=!0,o=e.apply(null,n)),o}}var W,G,X=function(e){function n(){return r}function t(n){return n(e)}var o=nn(e),r={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:a,isNone:u,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:n,orThunk:n,map:function(n){return X(n(e))},each:function(n){n(e)},bind:t,exists:t,forall:t,filter:function(n){return n(e)?r:s},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(u,function(n){return t(e,n)})}};return r},tn={some:X,none:t,from:function(n){return null===n||n===undefined?s:X(n)}},Y=function(e){return{is:function(n){return e===n},isValue:a,isError:u,getOr:nn(e),getOrThunk:nn(e),getOrDie:nn(e),or:function(n){return Y(e)},orThunk:function(n){return Y(e)},fold:function(n,t){return t(e)},map:function(n){return Y(n(e))},mapError:function(n){return Y(e)},each:function(n){n(e)},bind:function(n){return n(e)},exists:function(n){return n(e)},forall:function(n){return n(e)},toOption:function(){return tn.some(e)}}},q=function(e){return{is:u,isValue:u,isError:a,getOr:l,getOrThunk:function(n){return n()},getOrDie:function(){return r(String(e))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,t){return n(e)},map:function(n){return q(e)},mapError:function(n){return q(n(e))},each:Z,bind:function(n){return q(e)},exists:u,forall:a,toOption:tn.none}},K={value:Y,error:q,fromOption:function(n,t){return n.fold(function(){return q(t)},Y)}},J=g("string"),$=g("object"),Q=g("array"),en=g("boolean"),on=g("function"),rn=g("number"),un=Array.prototype.slice,an=Array.prototype.indexOf,cn=Array.prototype.push,sn=function(n,t){return-1<y(n,t)},fn=function(n,t){for(var e=0,o=n.length;e<o;e++){t(n[e],e)}},ln=function(n){return 0===n.length?tn.none():tn.some(n[0])},dn=on(Array.from)?Array.from:function(n){return un.call(n)},mn=Object.keys,gn=Object.hasOwnProperty,pn=function(n,t){for(var e=mn(n),o=0,r=e.length;o<r;o++){var i=e[o];t(n[i],i)}},hn=function(n,o){var r={};return pn(n,function(n,t){var e=o(n,t);r[e.k]=e.v}),r},vn=function(n,e){var o=[];return pn(n,function(n,t){o.push(e(n,t))}),o},bn=function(n,t){return yn(n,t)?tn.from(n[t]):tn.none()},yn=function(n,t){return gn.call(n,t)},xn=function(u){if(!Q(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return fn(u,function(n,o){var t=mn(n);if(1!==t.length)throw new Error("one and only one name per case");var r=t[0],i=n[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!Q(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);for(var e=new Array(n),t=0;t<e.length;t++)e[t]=arguments[t];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(n){var t=mn(n);if(a.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+t.join(","));if(!B(a,function(n){return sn(t,n)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+a.join(", "));return n[r].apply(null,e)},log:function(n){v.console.log(n,{constructors:a,constructor:r,params:e})}}}}),e},wn=Object.prototype.hasOwnProperty,Sn=j(function(n,t){return $(n)&&$(t)?Sn(n,t):t}),Cn=j(function(n,t){return t});(G=W=W||{})[G.Error=0]="Error",G[G.Value=1]="Value";function kn(n,t,e){return n.stype===W.Error?t(n.serror):e(n.svalue)}function On(n){return{stype:W.Value,svalue:n}}function _n(n){return{stype:W.Error,serror:n}}function Tn(n){return Io.defaultedThunk(nn(n))}function En(n,t){var e;return(e={})[n]=t,e}function Bn(n,t){return function(n,e){var o={};return pn(n,function(n,t){sn(e,t)||(o[t]=n)}),o}(n,t)}function Dn(n,t){return En(n,t)}function An(n){return function(n){var t={};return fn(n,function(n){t[n.key]=n.value}),t}(n)}function Mn(n,t){var e=function(n){var t=[],e=[];return fn(n,function(n){n.fold(function(n){t.push(n)},function(n){e.push(n)})}),{errors:t,values:e}}(n);return 0<e.errors.length?function(n){return K.error(z(n))}(e.errors):function(n,t){return 0===n.length?K.value(t):K.value(Sn(t,Cn.apply(undefined,n)))}(e.values,t)}function Fn(n){return i(Bo,z)(n)}function In(n){return $(n)&&100<mn(n).length?" removed due to size":JSON.stringify(n,null,2)}function Rn(n,t){return Bo([{path:n,getErrorInfo:t}])}function Vn(n,t,e){return bn(t,e).fold(function(){return function(n,t,e){return Rn(n,function(){return'Could not find valid *strict* value for "'+t+'" in '+In(e)})}(n,e,t)},To)}function Hn(n,t,e){var o=bn(n,t).fold(function(){return e(n)},l);return To(o)}function Nn(u,a,n,c){return n.fold(function(o,e,n,r){function i(n){var t=r.extract(u.concat([o]),c,n);return Mo(t,function(n){return En(e,c(n))})}function t(n){return n.fold(function(){var n=En(e,c(tn.none()));return To(n)},function(n){var t=r.extract(u.concat([o]),c,n);return Mo(t,function(n){return En(e,c(tn.some(n)))})})}return n.fold(function(){return Do(Vn(u,a,o),i)},function(n){return Do(Hn(a,o,n),i)},function(){return Do(function(n,t){return To(bn(n,t))}(a,o),t)},function(n){return Do(function(t,n,e){var o=bn(t,n).map(function(n){return!0===n?e(t):n});return To(o)}(a,o,n),t)},function(n){var t=n(a),e=Mo(Hn(a,o,nn({})),function(n){return Sn(t,n)});return Do(e,i)})},function(n,t){var e=t(a);return To(En(n,c(e)))})}function Pn(o){return{extract:function(t,n,e){return Ao(o(e,n),function(n){return function(n,t){return Rn(n,function(){return t})}(t,n)})},toString:function(){return"val"}}}function zn(n){var i=jo(n),u=k(n,function(t,n){return n.fold(function(n){return Sn(t,Dn(n,!0))},nn(t))},{});return{extract:function(n,t,e){var o=en(e)?[]:function(t){var n=mn(t);return C(n,function(n){return N(t,n)})}(e),r=C(o,function(n){return!N(u,n)});return 0===r.length?i.extract(n,t,e):function(n,t){return Rn(n,function(){return"There are unsupported fields: ["+t.join(", ")+"] specified"})}(n,r)},toString:i.toString}}function Ln(r){return{extract:function(e,o,n){var t=S(n,function(n,t){return r.extract(e.concat(["["+t+"]"]),o,n)});return zo(t)},toString:function(){return"array("+r.toString()+")"}}}function jn(i,u){return{extract:function(e,o,r){var n=mn(r),t=function(n,t){return Ln(Pn(i)).extract(n,l,t)}(e,n);return Do(t,function(n){var t=S(n,function(n){return Lo.field(n,n,Ro(),u)});return jo(t).extract(e,o,r)})},toString:function(){return"setOf("+u.toString()+")"}}}function Un(t,e,o,n,r){return bn(n,r).fold(function(){return function(n,t,e){return Rn(n,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+In(t)})}(t,n,r)},function(n){return n.extract(t.concat(["branch: "+r]),e,o)})}function Wn(n,r){return{extract:function(t,e,o){return bn(o,n).fold(function(){return function(n,t){return Rn(n,function(){return'Choice schema did not contain choice key: "'+t+'"'})}(t,n)},function(n){return Un(t,e,o,r,n)})},toString:function(){return"chooseOn("+n+"). Possible values: "+mn(r)}}}function Gn(n){return Wo(n)}function Xn(t){return Pn(function(n){return t(n).fold(Bo,To)})}function Yn(t,n){return jn(function(n){return Oo(t(n))},n)}function qn(n,t,e){return _o(function(n,t,e,o){var r=t.extract([n],e,o);return Fo(r,function(n){return{input:o,errors:n}})}(n,t,l,e))}function Kn(n){return n.fold(function(n){throw new Error(Ko(n))},l)}function Jn(n,t,e){return Kn(qn(n,t,e))}function $n(n,t){return Wn(n,t)}function Qn(n,t){return Wn(n,L(t,jo))}function Zn(e,o){return Pn(function(n){var t=typeof n;return e(n)?To(n):Bo("Expected type: "+o+" but got: "+t)})}function nt(t){return Xn(function(n){return sn(t,n)?K.value(n):K.error('Unsupported value: "'+n+'", choose one of "'+t.join(", ")+'".')})}function tt(n){return Xo(n,n,Ro(),Uo())}function et(n,t){return Xo(n,n,Ro(),t)}function ot(n){return et(n,Qo)}function rt(n,t){return Xo(n,n,Ro(),nt(t))}function it(n){return et(n,nr)}function ut(n,t){return Xo(n,n,Ro(),jo(t))}function at(n,t){return Xo(n,n,Ro(),Wo(t))}function ct(n,t){return Xo(n,n,Ro(),Ln(t))}function st(n){return Xo(n,n,Vo(),Uo())}function ft(n,t){return Xo(n,n,Vo(),t)}function lt(n){return ft(n,$o)}function dt(n){return ft(n,Qo)}function mt(n){return ft(n,nr)}function gt(n,t){return ft(n,jo(t))}function pt(n,t){return Xo(n,n,Tn(t),Uo())}function ht(n,t,e){return Xo(n,n,Tn(t),e)}function vt(n,t){return ht(n,t,$o)}function bt(n,t){return ht(n,t,Qo)}function yt(n,t,e){return ht(n,t,nt(e))}function xt(n,t){return ht(n,t,Zo)}function wt(n,t){return ht(n,t,nr)}function St(n,t,e){return ht(n,t,jo(e))}function Ct(n,t){return Go(n,t)}function kt(n,t,e){return 0!=(n.compareDocumentPosition(t)&e)}function Ot(n,t){var e=function(n,t){for(var e=0;e<n.length;e++){var o=n[e];if(o.test(t))return o}return undefined}(n,t);if(!e)return{major:0,minor:0};function o(n){return Number(t.replace(e,"$"+n))}return cr(o(1),o(2))}function _t(n,t){return function(){return t===n}}function Tt(n,t){return function(){return t===n}}function Et(n,t){var e=String(t).toLowerCase();return _(n,function(n){return n.search(e)})}function Bt(n,t){return-1!==n.indexOf(t)}function Dt(t){return function(n){return Bt(n,t)}}function At(){return _r.get()}function Mt(n,t){var e=n.dom();if(e.nodeType!==Dr)return!1;var o=e;if(o.matches!==undefined)return o.matches(t);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(t);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(t);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}function Ft(n){return n.nodeType!==Dr&&n.nodeType!==Ar||0===n.childElementCount}function It(n,t){var e=t===undefined?v.document:t.dom();return Ft(e)?[]:S(e.querySelectorAll(n),ir.fromDom)}function Rt(n,t){return n.dom()===t.dom()}function Vt(n,t,e){for(var o=n.dom(),r=on(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=ir.fromDom(o),u=t(i);if(u.isSome())return u;if(r(i))break}return tn.none()}function Ht(n,t){return Rt(n.element(),t.event().target())}function Nt(n){if(!N(n,"can")&&!N(n,"abort")&&!N(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return Jn("Extracting event.handler",zn([pt("can",nn(!0)),pt("abort",nn(!1)),pt("run",Z)]),n)}function Pt(e){var n=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return O(t,function(n,t){return n&&o(t).apply(undefined,e)},!0)}}(e,function(n){return n.can}),t=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return O(t,function(n,t){return n||o(t).apply(undefined,e)},!1)}}(e,function(n){return n.abort});return Nt({can:n,abort:t,run:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];fn(e,function(n){n.run.apply(undefined,t)})}})}function zt(n,t){ki(n,n.element(),t,{})}function Lt(n,t,e){ki(n,n.element(),t,e)}function jt(n){zt(n,oi())}function Ut(n,t,e){ki(n,t,e,{})}function Wt(n,t,e,o){n.getSystem().triggerEvent(e,t,o.event())}function Gt(n){return An(n)}function Xt(n,t){return{key:n,value:Nt({abort:t})}}function Yt(n){return{key:n,value:Nt({run:function(n,t){t.event().prevent()}})}}function qt(n,t){return{key:n,value:Nt({run:t})}}function Kt(n,e,o){return{key:n,value:Nt({run:function(n,t){e.apply(undefined,[n,t].concat(o))}})}}function Jt(n){return function(e){return{key:n,value:Nt({run:function(n,t){Ht(n,t)&&e(n,t)}})}}}function $t(n,t,e){return function(e,o){return qt(e,function(n,t){n.getSystem().getByUid(o).each(function(n){Wt(n,n.element(),e,t)})})}(n,t.partUids[e])}function Qt(n,r){return qt(n,function(t,n){var e=n.event(),o=t.getSystem().getByDom(e.target()).fold(function(){return Fr(e.target(),function(n){return t.getSystem().getByDom(n).toOption()},nn(!1)).getOr(t)},function(n){return n});r(t,o,n)})}function Zt(n){return qt(n,function(n,t){t.cut()})}function ne(n,t){return Jt(n)(t)}function te(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(t.length!==e.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+e.length+" arguments");var o={};return fn(t,function(n,t){o[n]=nn(e[t])}),o}}function ee(n){return n.slice(0).sort()}function oe(t,n){if(!Q(n))throw new Error("The "+t+" fields must be an array. Was: "+n+".");fn(n,function(n){if(!J(n))throw new Error("The value "+n+" in the "+t+" fields was not a string.")})}function re(r,i){var u=r.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return oe("required",r),oe("optional",i),function(n){var e=ee(n);_(e,function(n,t){return t<e.length-1&&n===e[t+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})}(u),function(t){var e=mn(t);B(r,function(n){return sn(e,n)})||function(n,t){throw new Error("All required keys ("+ee(n).join(", ")+") were not specified. Specified keys were: "+ee(t).join(", ")+".")}(r,e);var n=C(e,function(n){return!sn(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+ee(n).join(", "))}(n);var o={};return fn(r,function(n){o[n]=nn(t[n])}),fn(i,function(n){o[n]=nn(Object.prototype.hasOwnProperty.call(t,n)?tn.some(t[n]):tn.none())}),o}}function ie(n){return ir.fromDom(n.dom().ownerDocument)}function ue(n){return ir.fromDom(n.dom().ownerDocument.documentElement)}function ae(n){return ir.fromDom(n.dom().ownerDocument.defaultView)}function ce(n){return tn.from(n.dom().parentNode).map(ir.fromDom)}function se(n){return tn.from(n.dom().offsetParent).map(ir.fromDom)}function fe(n){return S(n.dom().childNodes,ir.fromDom)}function le(n,t){var e=n.dom().childNodes;return tn.from(e[t]).map(ir.fromDom)}function de(t,e){ce(t).each(function(n){n.dom().insertBefore(e.dom(),t.dom())})}function me(n,t){(function(n){return tn.from(n.dom().nextSibling).map(ir.fromDom)})(n).fold(function(){ce(n).each(function(n){Bi(n,t)})},function(n){de(n,t)})}function ge(t,e){(function(n){return le(n,0)})(t).fold(function(){Bi(t,e)},function(n){t.dom().insertBefore(e.dom(),n.dom())})}function pe(t,n){fn(n,function(n){Bi(t,n)})}function he(n){n.dom().textContent="",fn(fe(n),function(n){Di(n)})}function ve(n){var t=fe(n);0<t.length&&function(t,n){fn(n,function(n){de(t,n)})}(n,t),Di(n)}function be(n){return n.dom().innerHTML}function ye(n,t){var e=ie(n).dom(),o=ir.fromDom(e.createDocumentFragment()),r=function(n,t){var e=(t||v.document).createElement("div");return e.innerHTML=n,fe(ir.fromDom(e))}(t,e);pe(o,r),he(n),Bi(n,o)}function xe(n){return n.dom().nodeName.toLowerCase()}function we(t){return function(n){return function(n){return n.dom().nodeType}(n)===t}}function Se(n,t,e){if(!(J(e)||en(e)||rn(e)))throw v.console.error("Invalid call to Attr.set. Key ",t,":: Value ",e,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(t,e+"")}function Ce(n,t,e){Se(n.dom(),t,e)}function ke(n,t){var e=n.dom().getAttribute(t);return null===e?undefined:e}function Oe(n,t){var e=n.dom();return!(!e||!e.hasAttribute)&&e.hasAttribute(t)}function _e(n,t){n.dom().removeAttribute(t)}function Te(n){return function(n,t){return ir.fromDom(n.dom().cloneNode(t))}(n,!1)}function Ee(n){return function(n){var t=ir.fromTag("div"),e=ir.fromDom(n.dom().cloneNode(!0));return Bi(t,e),be(t)}(Te(n))}function Be(n){return Ee(n)}function De(n){var t=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Ri+String(t)}function Ae(n){return De(n)}function Me(t){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+(t?"\n"+Be(t().element())+" is not in context.":""))}}return{debugInfo:nn("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:nn(!1)}}function Fe(n,t){var e=n.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:Wi(i)}},n}function Ie(n){return Dn(Gi,n)}function Re(o){return function(n,t){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Wi(i.slice(1))}},n}(function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return o.apply(undefined,[n.getApis()].concat([n].concat(t)))},o)}function Ve(n,r){var i={};return pn(n,function(n,o){pn(n,function(n,t){var e=bn(i,t).getOr([]);i[t]=e.concat([r(o,n)])})}),i}function He(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}}function Ne(n){return n.cHandler}function Pe(n,t){return{name:nn(n),handler:nn(t)}}function ze(n,t,e){var o=P(P({},e),function(n,t){var e={};return fn(n,function(n){e[n.name()]=n.handlers(t)}),e}(t,n));return Ve(o,Pe)}function Le(n){var i=function(n){return on(n)?{can:nn(!0),abort:nn(!1),run:n}:n}(n);return function(n,t){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[n,t].concat(e);i.abort.apply(undefined,r)?t.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}}function je(n,t,e){var o=t[e];return o?function(u,a,n,c){try{var t=F(n,function(n,t){var e=n[a](),o=t[a](),r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return K.value(t)}catch(e){return K.error([e])}}("Event: "+e,"name",n,o).map(function(n){var t=S(n,function(n){return n.handler()});return Pt(t)}):function(n,t){return K.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(S(t,function(n){return n.name()}),null,2)])}(e,n)}function Ue(n){return qn("custom.definition",jo([Xo("dom","dom",Ro(),jo([tt("tag"),pt("styles",{}),pt("classes",[]),pt("attributes",{}),st("value"),st("innerHtml")])),tt("components"),tt("uid"),pt("events",{}),pt("apis",{}),Xo("eventOrder","eventOrder",function(n){return Io.mergeWithThunk(nn(n))}({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]}),Jo()),st("domModification")]),n)}function We(n,t){var e=ke(n,t);return e===undefined||""===e?[]:e.split(" ")}function Ge(n){return n.dom().classList!==undefined}function Xe(n,t){return function(n,t,e){var o=We(n,t).concat([e]);return Ce(n,t,o.join(" ")),!0}(n,"class",t)}function Ye(n,t){return function(n,t,e){var o=C(We(n,t),function(n){return n!==e});return 0<o.length?Ce(n,t,o.join(" ")):_e(n,t),!1}(n,"class",t)}function qe(n,t){Ge(n)?n.dom().classList.add(t):Xe(n,t)}function Ke(n){0===(Ge(n)?n.dom().classList:function(n){return We(n,"class")}(n)).length&&_e(n,"class")}function Je(n,t){Ge(n)?n.dom().classList.remove(t):Ye(n,t),Ke(n)}function $e(n,t){return Ge(n)&&n.dom().classList.contains(t)}function Qe(t,n){fn(n,function(n){qe(t,n)})}function Ze(t,n){fn(n,function(n){Je(t,n)})}function no(n){return n.style!==undefined&&on(n.style.getPropertyValue)}function to(n){var t=Mi(n)?n.dom().parentNode:n.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}function eo(n,t,e){if(!J(e))throw v.console.error("Invalid call to CSS.set. Property ",t,":: Value ",e,":: Element ",n),new Error("CSS value must be a string: "+e);no(n)&&n.style.setProperty(t,e)}function oo(n,t){no(n)&&n.style.removeProperty(t)}function ro(n,t,e){var o=n.dom();eo(o,t,e)}function io(n,t){var e=n.dom();pn(t,function(n,t){eo(e,t,n)})}function uo(n,t){var e=n.dom();pn(t,function(n,t){n.fold(function(){oo(e,t)},function(n){eo(e,t,n)})})}function ao(n,t){var e=n.dom(),o=v.window.getComputedStyle(e).getPropertyValue(t),r=""!==o||to(n)?o:Qi(e,t);return null===r?undefined:r}function co(n,t){var e=n.dom(),o=Qi(e,t);return tn.from(o).filter(function(n){return 0<n.length})}function so(n,t,e){var o=ir.fromTag(n);return ro(o,t,e),co(o,t).isSome()}function fo(n,t){var e=n.dom();oo(e,t),Oe(n,"style")&&""===function(n){return n.replace(/^\s+|\s+$/g,"")}(ke(n,"style"))&&_e(n,"style")}function lo(n){return n.dom().offsetWidth}function mo(n){return n.dom().value}function go(n,t){if(t===undefined)throw new Error("Value.set was undefined");n.dom().value=t}function po(n){var t=ir.fromTag(n.tag);!function(n,t){var e=n.dom();pn(t,function(n,t){Se(e,t,n)})}(t,n.attributes),Qe(t,n.classes),io(t,n.styles),n.innerHtml.each(function(n){return ye(t,n)});var e=n.domChildren;return pe(t,e),n.value.each(function(n){go(t,n)}),n.uid,zi(t,n.uid),t}function ho(n,t){return function(t,n){var e=S(n,function(n){return gt(n.name(),[tt("config"),pt("state",Xi)])}),o=qn("component.behaviours",jo(e),t.behaviours).fold(function(n){throw new Error(Ko(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))},function(n){return n});return{list:n,data:L(o,function(n){var t=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return t}})}}(n,t)}function vo(n){var t=function(n){var t=bn(n,"behaviours").getOr({}),e=C(mn(t),function(n){return t[n]!==undefined});return S(e,function(n){return t[n].me})}(n);return ho(n,t)}function bo(n,t,e){var o=function(n){return P(P({},n.dom),{uid:n.uid,domChildren:S(n.components,function(n){return n.element()})})}(n),r=function(n){return n.domModification.fold(function(){return He({})},He)}(n),i={"alloy.base.modification":r};return function(n,t){return P(P({},n),{attributes:P(P({},n.attributes),t.attributes),styles:P(P({},n.styles),t.styles),classes:n.classes.concat(t.classes)})}(o,0<t.length?function(t,n,e,o){var r=P({},n);fn(e,function(n){r[n.name()]=n.exhibit(t,o)});function i(n){return k(n,function(n,t){return P(P({},t.modification),n)},{})}var u=Ve(r,function(n,t){return{name:n,modification:t}}),a=k(u.classes,function(n,t){return t.modification.concat(n)},[]),c=i(u.attributes),s=i(u.styles);return He({classes:a,attributes:c,styles:s})}(e,i,t,o):r)}function yo(n,t,e){var o={"alloy.base.behaviour":function(n){return n.events}(n)};return function(n,t,e,o){var r=ze(n,e,o);return Ki(r,t)}(e,n.eventOrder,t,o).getOrDie()}function xo(n){var t=ji(n),e=t.events,o=c(t,["events"]),r=function(n){var t=bn(n,"components").getOr([]);return S(t,tu)}(o),i=P(P({},o),{events:P(P({},Ii),e),components:r});return K.value(function(e){function n(){return l}var o=or(Ui),t=Kn(Ue(e)),r=vo(e),i=function(n){return n.list}(r),u=function(n){return n.data}(r),a=bo(t,i,u),c=po(a),s=yo(t,i,u),f=or(t.components),l={getSystem:o.get,config:function(n){var t=u;return(on(t[n.name()])?t[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(n){return on(u[n.name()])},spec:nn(e),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return t.apis},connect:function(n){o.set(n)},disconnect:function(){o.set(Me(n))},element:nn(c),syncComponents:function(){var n=fe(c),t=E(n,function(n){return o.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(t)},components:f.get,events:nn(s)};return l}(i))}function wo(n){var t=ir.fromText(n);return Zi({element:t})}var So,Co,ko,Oo=function(n){return n.fold(_n,On)},_o=function(n){return kn(n,K.error,K.value)},To=On,Eo=function(n){var t=[],e=[];return fn(n,function(n){kn(n,function(n){return e.push(n)},function(n){return t.push(n)})}),{values:t,errors:e}},Bo=_n,Do=function(n,t){return n.stype===W.Value?t(n.svalue):n},Ao=function(n,t){return n.stype===W.Error?t(n.serror):n},Mo=function(n,t){return n.stype===W.Value?{stype:W.Value,svalue:t(n.svalue)}:n},Fo=function(n,t){return n.stype===W.Error?{stype:W.Error,serror:t(n.serror)}:n},Io=xn([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Ro=Io.strict,Vo=Io.asOption,Ho=Io.defaultedThunk,No=(Io.asDefaultedOptionThunk,Io.mergeWithThunk),Po=(xn([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n,t){var e=Eo(n);return 0<e.errors.length?Fn(e.errors):function(n,t){return 0<n.length?To(Sn(t,Cn.apply(undefined,n))):To(t)}(e.values,t)}),zo=function(n){var t=Eo(n);return 0<t.errors.length?Fn(t.errors):To(t.values)},Lo=xn([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),jo=function(o){return{extract:function(n,t,e){return function(t,e,n,o){var r=S(n,function(n){return Nn(t,e,n,o)});return Po(r,{})}(n,e,o,t)},toString:function(){return"obj{\n"+S(o,function(n){return n.fold(function(n,t,e,o){return n+" -> "+o.toString()},function(n,t){return"state("+n+")"})}).join("\n")+"}"}}},Uo=nn(Pn(To)),Wo=i(Ln,jo),Go=Lo.state,Xo=Lo.field,Yo=Pn(To),qo=function(o){return{extract:function(n,t,e){return o().extract(n,t,e)},toString:function(){return o().toString()}}},Ko=function(n){return"Errors: \n"+function(n){var t=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return S(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors).join("\n")+"\n\nInput object: "+In(n.input)},Jo=nn(Yo),$o=Zn(rn,"number"),Qo=Zn(J,"string"),Zo=Zn(en,"boolean"),nr=Zn(on,"function"),tr=function(t){function n(n,t){for(var e=n.next();!e.done;){if(!t(e.value))return!1;e=n.next()}return!0}if(Object(t)!==t)return!0;switch({}.toString.call(t).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(t).every(function(n){return tr(t[n])});case"Map":return n(t.keys(),tr)&&n(t.values(),tr);case"Set":return n(t.keys(),tr);default:return!1}},er=Pn(function(n){return tr(n)?To(n):Bo("Expected value to be acceptable for sending via postMessage")}),or=function(n){function t(){return e}var e=n;return{get:t,set:function(n){e=n},clone:function(){return or(t())}}},rr=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:nn(n)}},ir={fromHtml:function(n,t){var e=(t||v.document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw v.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return rr(e.childNodes[0])},fromTag:function(n,t){var e=(t||v.document).createElement(n);return rr(e)},fromText:function(n,t){var e=(t||v.document).createTextNode(n);return rr(e)},fromDom:rr,fromPoint:function(n,t,e){var o=n.dom();return tn.from(o.elementFromPoint(t,e)).map(rr)}},ur=function(n,t){return kt(n,t,v.Node.DOCUMENT_POSITION_CONTAINED_BY)},ar=function(){return cr(0,0)},cr=function(n,t){return{major:n,minor:t}},sr={nu:cr,detect:function(n,t){var e=String(t).toLowerCase();return 0===n.length?ar():Ot(n,e)},unknown:ar},fr="Firefox",lr=function(n){var t=n.current;return{current:t,version:n.version,isEdge:_t("Edge",t),isChrome:_t("Chrome",t),isIE:_t("IE",t),isOpera:_t("Opera",t),isFirefox:_t(fr,t),isSafari:_t("Safari",t)}},dr={unknown:function(){return lr({current:undefined,version:sr.unknown()})},nu:lr,edge:nn("Edge"),chrome:nn("Chrome"),ie:nn("IE"),opera:nn("Opera"),firefox:nn(fr),safari:nn("Safari")},mr="Windows",gr="Android",pr="Solaris",hr="FreeBSD",vr="ChromeOS",br=function(n){var t=n.current;return{current:t,version:n.version,isWindows:Tt(mr,t),isiOS:Tt("iOS",t),isAndroid:Tt(gr,t),isOSX:Tt("OSX",t),isLinux:Tt("Linux",t),isSolaris:Tt(pr,t),isFreeBSD:Tt(hr,t),isChromeOS:Tt(vr,t)}},yr={unknown:function(){return br({current:undefined,version:sr.unknown()})},nu:br,windows:nn(mr),ios:nn("iOS"),android:nn(gr),linux:nn("Linux"),osx:nn("OSX"),solaris:nn(pr),freebsd:nn(hr),chromeos:nn(vr)},xr=function(n,e){return Et(n,e).map(function(n){var t=sr.detect(n.versionRegexes,e);return{current:n.name,version:t}})},wr=function(n,e){return Et(n,e).map(function(n){var t=sr.detect(n.versionRegexes,e);return{current:n.name,version:t}})},Sr=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Cr=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Bt(n,"edge/")&&Bt(n,"chrome")&&Bt(n,"safari")&&Bt(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Sr],search:function(n){return Bt(n,"chrome")&&!Bt(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Bt(n,"msie")||Bt(n,"trident")}},{name:"Opera",versionRegexes:[Sr,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Dt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Dt("firefox")},{name:"Safari",versionRegexes:[Sr,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Bt(n,"safari")||Bt(n,"mobile/"))&&Bt(n,"applewebkit")}}],kr=[{name:"Windows",search:Dt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Bt(n,"iphone")||Bt(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Dt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Dt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Dt("linux"),versionRegexes:[]},{name:"Solaris",search:Dt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Dt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Dt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Or={browsers:nn(Cr),oses:nn(kr)},_r=or(function(n,t){var e=Or.browsers(),o=Or.oses(),r=xr(e,n).fold(dr.unknown,dr.nu),i=wr(o,n).fold(yr.unknown,yr.nu);return{browser:r,os:i,deviceType:function(n,t,e,o){var r=n.isiOS()&&!0===/ipad/i.test(e),i=n.isiOS()&&!r,u=n.isiOS()||n.isAndroid(),a=u||o("(pointer:coarse)"),c=r||!i&&u&&o("(min-device-width:768px)"),s=i||u&&!c,f=t.isSafari()&&n.isiOS()&&!1===/safari/i.test(e),l=!s&&!c&&!f;return{isiPad:nn(r),isiPhone:nn(i),isTablet:nn(c),isPhone:nn(s),isTouch:nn(a),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:nn(f),isDesktop:nn(l)}}(i,r,n,t)}}(v.navigator.userAgent,function(n){return v.window.matchMedia(n).matches})),Tr=(v.Node.ATTRIBUTE_NODE,v.Node.CDATA_SECTION_NODE,v.Node.COMMENT_NODE,v.Node.DOCUMENT_NODE),Er=(v.Node.DOCUMENT_TYPE_NODE,v.Node.DOCUMENT_FRAGMENT_NODE,v.Node.ELEMENT_NODE),Br=v.Node.TEXT_NODE,Dr=(v.Node.PROCESSING_INSTRUCTION_NODE,v.Node.ENTITY_REFERENCE_NODE,v.Node.ENTITY_NODE,v.Node.NOTATION_NODE,Er),Ar=Tr,Mr=At().browser.isIE()?function(n,t){return ur(n.dom(),t.dom())}:function(n,t){var e=n.dom(),o=t.dom();return e!==o&&e.contains(o)},Fr=function(n,t,e){return t(n).orThunk(function(){return e(n)?tn.none():Vt(n,t,e)})},Ir=nn("touchstart"),Rr=nn("touchmove"),Vr=nn("touchend"),Hr=nn("touchcancel"),Nr=nn("mousedown"),Pr=nn("mousemove"),zr=nn("mouseout"),Lr=nn("mouseup"),jr=nn("mouseover"),Ur=nn("focusin"),Wr=nn("focusout"),Gr=nn("keydown"),Xr=nn("keyup"),Yr=nn("input"),qr=nn("change"),Kr=nn("click"),Jr=nn("transitionend"),$r=nn("selectstart"),Qr={tap:nn("alloy.tap")},Zr=nn("alloy.focus"),ni=nn("alloy.blur.post"),ti=nn("alloy.paste.post"),ei=nn("alloy.receive"),oi=nn("alloy.execute"),ri=nn("alloy.focus.item"),ii=Qr.tap,ui=nn("alloy.longpress"),ai=nn("alloy.sandbox.close"),ci=nn("alloy.typeahead.cancel"),si=nn("alloy.system.init"),fi=nn("alloy.system.touchmove"),li=nn("alloy.system.touchend"),di=nn("alloy.system.scroll"),mi=nn("alloy.system.resize"),gi=nn("alloy.system.attached"),pi=nn("alloy.system.detached"),hi=nn("alloy.system.dismissRequested"),vi=nn("alloy.system.repositionRequested"),bi=nn("alloy.focusmanager.shifted"),yi=nn("alloy.slotcontainer.visibility"),xi=nn("alloy.change.tab"),wi=nn("alloy.dismiss.tab"),Si=nn("alloy.highlight"),Ci=nn("alloy.dehighlight"),ki=function(n,t,e,o){var r=P({target:t},o);n.getSystem().triggerEvent(e,t,L(r,nn))},Oi=Jt(gi()),_i=Jt(pi()),Ti=Jt(si()),Ei=(So=oi(),function(n){return qt(So,n)}),Bi=(te("element","offset"),function(n,t){n.dom().appendChild(t.dom())}),Di=function(n){var t=n.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Ai=("undefined"!=typeof v.window?v.window:Function("return this;")(),we(Er)),Mi=we(Br),Fi=Gt([(Co=Zr(),ko=function(n,t){var e=t.event().originator(),o=t.event().target();return!function(n,t,e){return Rt(t,n.element())&&!Rt(t,e)}(n,e,o)||(v.console.warn(Zr()+" did not get interpreted by the desired target. \nOriginator: "+Be(e)+"\nTarget: "+Be(o)+"\nCheck the "+Zr()+" event handlers"),!1)},{key:Co,value:Nt({can:ko})})]),Ii=/* */Object.freeze({__proto__:null,events:Fi}),Ri=0,Vi=nn("alloy-id-"),Hi=nn("data-alloy-id"),Ni=Vi(),Pi=Hi(),zi=function(n,t){Object.defineProperty(n.dom(),Pi,{value:t,writable:!0})},Li=function(n){var t=Ai(n)?n.dom()[Pi]:null;return tn.from(t)},ji=l,Ui=Me(),Wi=function(n){return S(n,function(n){return function(n,t){return function(n,t,e){return""===t||!(n.length<t.length)&&n.substr(e,e+t.length)===t}(n,t,n.length-t.length)}(n,"/*")?n.substring(0,n.length-"/*".length):n})},Gi=De("alloy-premade"),Xi={init:function(){return Yi({readState:function(){return"No State required"}})}},Yi=function(n){return n},qi=function(n,t){return function(n,t){return{cHandler:n,purpose:nn(t)}}(d.apply(undefined,[n.handler].concat(t)),n.purpose())},Ki=function(n,i){var t=vn(n,function(o,r){return(1===o.length?K.value(o[0].handler()):je(o,i,r)).map(function(n){var t=Le(n),e=1<o.length?C(i[r],function(t){return x(o,function(n){return n.name()===t})}).join(" > "):o[0].name();return Dn(r,function(n,t){return{handler:n,purpose:nn(t)}}(t,e))})});return Mn(t,{})},Ji=U(function(){return $i(ir.fromDom(v.document))}),$i=function(n){var t=n.dom().body;if(null===t||t===undefined)throw new Error("Body is not available yet");return ir.fromDom(t)},Qi=function(n,t){return no(n)?n.style.getPropertyValue(t):""},Zi=function(n){var t=Jn("external.component",zn([tt("element"),st("uid")]),n),e=or(Me());t.uid.each(function(n){zi(t.element,n)});var o={getSystem:e.get,config:tn.none,hasConfigured:nn(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(Me(function(){return o}))},getApis:function(){return{}},element:nn(t.element),spec:nn(n),readState:nn("No state"),syncComponents:Z,components:nn([]),events:nn({})};return Ie(o)},nu=Ae,tu=function(t){return function(n){return bn(n,Gi)}(t).fold(function(){var n=t.hasOwnProperty("uid")?t:P({uid:nu("")},t);return xo(n).getOrDie()},function(n){return n})},eu=Ie;function ou(o,r){function n(n){var t=r(n);if(t<=0||null===t){var e=ao(n,o);return parseFloat(e)||0}return t}function i(r,n){return O(n,function(n,t){var e=ao(r,t),o=e===undefined?0:parseInt(e,10);return isNaN(o)?n:n+o},0)}return{set:function(n,t){if(!rn(t)&&!t.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+t);var e=n.dom();no(e)&&(e.style[o]=t+"px")},get:n,getOuter:n,aggregate:i,max:function(n,t,e){var o=i(n,e);return o<t?t-o:0}}}function ru(n){return wu.get(n)}function iu(n){return wu.getOuter(n)}function uu(n,t){return n!==undefined?n:t!==undefined?t:0}function au(n){var t=n.dom().ownerDocument,e=t.body,o=t.defaultView,r=t.documentElement;if(e===n.dom())return Cu(e.offsetLeft,e.offsetTop);var i=uu(o.pageYOffset,r.scrollTop),u=uu(o.pageXOffset,r.scrollLeft),a=uu(r.clientTop,e.clientTop),c=uu(r.clientLeft,e.clientLeft);return ku(n).translate(u-c,i-a)}function cu(n){return Ou.get(n)}function su(n){return Ou.getOuter(n)}function fu(n){function t(){n.stopPropagation()}function e(){n.preventDefault()}var o=ir.fromDom(n.target),r=i(e,t);return function(n,t,e,o,r,i,u){return{target:nn(n),x:nn(t),y:nn(e),stop:o,prevent:r,kill:i,raw:nn(u)}}(o,n.clientX,n.clientY,t,e,r,n)}function lu(n,t,e,o,r){var i=function(t,e){return function(n){t(n)&&e(fu(n))}}(e,o);return n.dom().addEventListener(t,i,r),{unbind:d(_u,n,t,i,r)}}function du(n){var t=n!==undefined?n.dom():v.document,e=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return Cu(e,o)}function mu(n,t,e){(e!==undefined?e.dom():v.document).defaultView.scrollTo(n,t)}function gu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function pu(n){var o=n===undefined?v.window:n,t=o.document,r=du(ir.fromDom(t));return function(n){var t=n===undefined?v.window:n;return tn.from(t.visualViewport)}(o).fold(function(){var n=o.document.documentElement,t=n.clientWidth,e=n.clientHeight;return gu(r.left(),r.top(),t,e)},function(n){return gu(Math.max(n.pageLeft,r.left()),Math.max(n.pageTop,r.top()),n.width,n.height)})}function hu(o){var n=ir.fromDom(v.document),r=du(n);return function(n,t){var e=t.owner(n),o=Tu(t,e);return tn.some(o)}(o,Eu).fold(d(au,o),function(n){var t=ku(o),e=k(n,function(n,t){var e=ku(t);return{left:n.left+e.left(),top:n.top+e.top()}},{left:0,top:0});return Cu(e.left+t.left()+r.left(),e.top+t.top()+r.top())})}function vu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function bu(n){var t=au(n),e=su(n),o=iu(n);return vu(t.left(),t.top(),e,o)}function yu(n){var t=hu(n),e=su(n),o=iu(n);return vu(t.left(),t.top(),e,o)}function xu(){return pu(v.window)}var wu=ou("height",function(n){var t=n.dom();return to(n)?t.getBoundingClientRect().height:t.offsetHeight}),Su=function(e,o){return{left:nn(e),top:nn(o),translate:function(n,t){return Su(e+n,o+t)}}},Cu=Su,ku=function(n){var t=n.dom(),e=t.ownerDocument.body;return e===t?Cu(e.offsetLeft,e.offsetTop):to(n)?function(n){var t=n.getBoundingClientRect();return Cu(t.left,t.top)}(t):Cu(0,0)},Ou=ou("width",function(n){return n.dom().offsetWidth}),_u=function(n,t,e,o){n.dom().removeEventListener(t,e,o)},Tu=(At().browser.isSafari(),function(o,n){return o.view(n).fold(nn([]),function(n){var t=o.owner(n),e=Tu(o,t);return[n].concat(e)})}),Eu=/* */Object.freeze({__proto__:null,view:function(n){return(n.dom()===v.document?tn.none():tn.from(n.dom().defaultView.frameElement)).map(ir.fromDom)},owner:function(n){return ie(n)}}),Bu=te("point","width","height"),Du=te("x","y","width","height");function Au(n,t,e,o,r){return n(e,o)?tn.some(e):on(r)&&r(e)?tn.none():t(e,o,r)}function Mu(n,t,e){for(var o=n.dom(),r=on(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=ir.fromDom(o);if(t(i))return tn.some(i);if(r(i))break}return tn.none()}function Fu(n,t,e){return Au(function(n,t){return t(n)},Mu,n,t,e)}function Iu(n,t,e){return Fu(n,t,e).isSome()}function Ru(n,t,e){return Mu(n,function(n){return Mt(n,t)},e)}function Vu(n,t){return function(n,t){var e=t===undefined?v.document:t.dom();return Ft(e)?tn.none():tn.from(e.querySelector(n)).map(ir.fromDom)}(t,n)}function Hu(n,t,e){return Au(Mt,Ru,n,t,e)}function Nu(){var t=De("aria-owns");return{id:nn(t),link:function(n){Ce(n,"aria-owns",t)},unlink:function(n){_e(n,"aria-owns")}}}function Pu(t,n){return function(n){return Fu(n,function(n){if(!Ai(n))return!1;var t=ke(n,"id");return t!==undefined&&-1<t.indexOf("aria-owns")}).bind(function(n){var t=ke(n,"id"),e=ie(n);return Vu(e,'[aria-owns="'+t+'"]')})}(n).exists(function(n){return ju(t,n)})}var zu,Lu,ju=function(t,n){return Iu(n,function(n){return Rt(n,t.element())},nn(!1))||Pu(t,n)},Uu="unknown";(Lu=zu=zu||{})[Lu.STOP=0]="STOP",Lu[Lu.NORMAL=1]="NORMAL",Lu[Lu.LOGGING=2]="LOGGING";function Wu(t,n,e){switch(bn(Xa.get(),t).orThunk(function(){var n=mn(Xa.get());return R(n,function(n){return-1<t.indexOf(n)?tn.some(Xa.get()[n]):tn.none()})}).getOr(zu.NORMAL)){case zu.NORMAL:return e(qa());case zu.LOGGING:var o=function(t,e){var o=[],r=(new Date).getTime();return{logEventCut:function(n,t,e){o.push({outcome:"cut",target:t,purpose:e})},logEventStopped:function(n,t,e){o.push({outcome:"stopped",target:t,purpose:e})},logNoParent:function(n,t,e){o.push({outcome:"no-parent",target:t,purpose:e})},logEventNoHandlers:function(n,t){o.push({outcome:"no-handlers-left",target:t})},logEventResponse:function(n,t,e){o.push({outcome:"response",purpose:e,target:t})},write:function(){var n=(new Date).getTime();sn(["mousemove","mouseover","mouseout",si()],t)||v.console.log(t,{event:t,time:n-r,target:e.dom(),sequence:S(o,function(n){return sn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Be(n.target)+")":n.outcome})})}}}(t,n),r=e(o);return o.write(),r;case zu.STOP:return!0}}function Gu(n,t,e){return Wu(n,t,e)}function Xu(){return ut("markers",[tt("backgroundMenu")].concat(Ka()).concat(Ja()))}function Yu(n){return ut("markers",S(n,tt))}function qu(n,t,e){return function(){var n=new Error;if(n.stack===undefined)return;var t=n.stack.split("\n");_(t,function(t){return 0<t.indexOf("alloy")&&!x(Ya,function(n){return-1<t.indexOf(n)})}).getOr(Uu)}(),Xo(t,t,e,Xn(function(e){return K.value(function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e.apply(undefined,n)})}))}function Ku(n){return qu(0,n,Tn(Z))}function Ju(n){return qu(0,n,Tn(tn.none))}function $u(n){return qu(0,n,Ro())}function Qu(n){return qu(0,n,Ro())}function Zu(n,t){return Ct(n,nn(t))}function na(n){return Ct(n,l)}function ta(n){return n.x()}function ea(n,t){return n.x()+n.width()/2-t.width()/2}function oa(n,t){return n.x()+n.width()-t.width()}function ra(n,t){return n.y()-t.height()}function ia(n){return n.y()+n.height()}function ua(n,t){return n.y()+n.height()/2-t.height()/2}function aa(n,t,e){return nc(ta(n),ia(n),e.southeast(),ec(),"layout-se")}function ca(n,t,e){return nc(oa(n,t),ia(n),e.southwest(),oc(),"layout-sw")}function sa(n,t,e){return nc(ta(n),ra(n,t),e.northeast(),rc(),"layout-ne")}function fa(n,t,e){return nc(oa(n,t),ra(n,t),e.northwest(),ic(),"layout-nw")}function la(n,t,e){return nc(function(n){return n.x()+n.width()}(n),ua(n,t),e.east(),cc(),"layout-e")}function da(n,t,e){return nc(function(n,t){return n.x()-t.width()}(n,t),ua(n,t),e.west(),sc(),"layout-w")}function ma(){return[aa,ca,sa,fa,lc,fc,la,da]}function ga(){return[ca,aa,fa,sa,lc,fc,la,da]}function pa(){return[sa,fa,aa,ca,fc,lc]}function ha(){return[aa,ca,sa,fa,lc,fc]}function va(){return[ca,aa,fa,sa,lc,fc]}function ba(e,o,r){return Ti(function(n,t){r(n,e,o)})}function ya(n,t,e,o,r,i){var u=zn(n),a=gt(t,[function(n,t){return ft(n,zn(t))}("config",n)]);return dc(u,a,t,e,o,r,i)}function xa(r,i,u){return function(n,t,e){var o=e.toString(),r=o.indexOf(")")+1,i=o.indexOf("("),u=o.substring(i+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:Wi(u.slice(0,1).concat(u.slice(3)))}},n}(function(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];var o=[e].concat(n);return e.config({name:nn(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(n){var t=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,n.config,n.state].concat(t))})},u,i)}function wa(n){return{key:n,value:undefined}}function Sa(n){return An(n)}function Ca(n){var t=Jn("Creating behaviour: "+n.name,mc,n);return ya(t.fields,t.name,t.active,t.apis,t.extra,t.state)}function ka(n){var t=Jn("Creating behaviour: "+n.name,gc,n);return function(n,t,e,o,r,i){var u=n,a=gt(t,[ft("config",n)]);return dc(u,a,t,e,o,r,i)}(Qn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)}function Oa(n){n.dom().focus()}function _a(n){var t=n!==undefined?n.dom():v.document;return tn.from(t.activeElement).map(ir.fromDom)}function Ta(t){return _a(ie(t)).filter(function(n){return t.dom().contains(n.dom())})}function Ea(n,e){var o=ie(e),t=_a(o).bind(function(t){function n(n){return Rt(t,n)}return n(e)?tn.some(e):function(n,r){var i=function(n){for(var t=0;t<n.childNodes.length;t++){var e=ir.fromDom(n.childNodes[t]);if(r(e))return tn.some(e);var o=i(n.childNodes[t]);if(o.isSome())return o}return tn.none()};return i(n.dom())}(e,n)}),r=n(e);return t.each(function(t){_a(o).filter(function(n){return Rt(n,t)}).fold(function(){Oa(t)},Z)}),r}function Ba(n,t){function e(n){return n+"px"}uo(n,{position:tn.some(t.position()),left:t.left().map(e),top:t.top().map(e),right:t.right().map(e),bottom:t.bottom().map(e)})}function Da(n,t,e,o,r,i){var u=t.x()-e,a=t.y()-o,c=r-(u+t.width()),s=i-(a+t.height()),f=tn.some(u),l=tn.some(a),d=tn.some(c),m=tn.some(s),g=tn.none();return function(n,t,e,o,r,i,u,a,c){return n.fold(t,e,o,r,i,u,a,c)}(t.direction(),function(){return xc(n,f,l,g,g)},function(){return xc(n,g,l,d,g)},function(){return xc(n,f,g,g,m)},function(){return xc(n,g,g,d,m)},function(){return xc(n,f,l,g,g)},function(){return xc(n,f,g,g,m)},function(){return xc(n,f,l,g,g)},function(){return xc(n,g,l,d,g)})}function Aa(n,t){var e=d(hu,t),o=n.fold(e,e,function(){var n=du();return hu(t).translate(-n.left(),-n.top())}),r=su(t),i=iu(t);return vu(o.left(),o.top(),r,i)}function Ma(n,t,e,o){var r=n+t;return o<r?e:r<e?o:r}function Fa(n,t,e){return Math.min(Math.max(n,t),e)}function Ia(n,t,e,o){var r=n.x(),i=n.y(),u=n.bubble().offset().left(),a=n.bubble().offset().top(),c=o.y(),s=o.bottom(),f=o.x(),l=o.right(),d=i+a,m=function(n,t,e,o,r){var i=r.x(),u=r.y(),a=r.width(),c=r.height(),s=i<=n,f=u<=t,l=s&&f,d=n+e<=i+a&&t+o<=u+c,m=Math.abs(Math.min(e,s?i+a-n:i-(n+e))),g=Math.abs(Math.min(o,f?u+c-t:u-(t+o)));return{originInBounds:l,sizeInBounds:d,limitX:Fa(n,r.x(),r.right()),limitY:Fa(t,r.y(),r.bottom()),deltaW:m,deltaH:g}}(r+u,d,t,e,o),g=m.originInBounds,p=m.sizeInBounds,h=m.limitX,v=m.limitY,b=m.deltaW,y=m.deltaH,x=nn(v+y-c),w=nn(s-v),S=function(n,t,e,o){return n.fold(t,t,o,o,t,o,e,e)}(n.direction(),w,w,x),C=nn(h+b-f),k=nn(l-h),O=function(n,t,e,o){return n.fold(t,o,t,o,e,e,t,o)}(n.direction(),k,k,C),_=_c({x:h,y:v,width:b,height:y,maxHeight:S,maxWidth:O,direction:n.direction(),classes:{on:n.bubble().classesOn(),off:n.bubble().classesOff()},label:n.label(),candidateYforTest:d});return g&&p?Tc.fit(_):Tc.nofit(_,b,y)}function Ra(n,t,e,o){fo(t,"max-height"),fo(t,"max-width");var r=function(n){return{width:nn(su(n)),height:nn(iu(n))}}(t);return function(n,e,u,a,c){function o(n,o,r,i){var t=n(e,u,a);return Ia(t,s,f,c).fold(Tc.fit,function(n,t,e){return i<e||r<t?Tc.nofit(n,t,e):Tc.nofit(o,r,i)})}var s=u.width(),f=u.height();return O(n,function(n,t){var e=d(o,t);return n.fold(Tc.fit,e)},Tc.nofit(_c({x:e.x(),y:e.y(),width:u.width(),height:u.height(),maxHeight:u.height(),maxWidth:u.width(),direction:ec(),classes:{on:[],off:[]},label:"none",candidateYforTest:e.y()}),-1,-1)).fold(l,l)}(o.preference(),n,r,e,o.bounds())}function Va(n,t,e){Ba(n,function(n,r){return n.fold(function(){return xc("absolute",tn.some(r.x()),tn.some(r.y()),tn.none(),tn.none())},function(n,t,e,o){return Da("absolute",r,n,t,e,o)},function(n,t,e,o){return Da("fixed",r,n,t,e,o)})}(e.origin(),t))}function Ha(n,t){!function(n,t){var e=wu.max(n,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);ro(n,"max-height",e+"px")}(n,Math.floor(t))}function Na(n,t,e){return n[t]===undefined?e:n[t]}function Pa(n,t,e,o,r,i){var u=Na(i,"maxHeightFunction",Ec()),a=Na(i,"maxWidthFunction",Z),c=n.anchorBox(),s=n.origin(),f=Dc({bounds:function(o,n){return n.fold(function(){return o.fold(xu,xu,vu)},function(e){return o.fold(e,e,function(){var n=e(),t=Sc(o,n.x(),n.y());return vu(t.left(),t.top(),n.width(),n.height())})})}(s,r),origin:s,preference:o,maxHeightFunction:u,maxWidthFunction:a});Ac(c,t,e,f)}function za(n,t,e){function r(n){return bn(e,n).getOr([])}function o(n,t,e){var o=A(Mc,e);return{offset:function(){return Cu(n,t)},classesOn:function(){return E(e,r)},classesOff:function(){return E(o,r)}}}return{southeast:function(){return o(-n,t,["top","alignLeft"])},southwest:function(){return o(n,t,["top","alignRight"])},south:function(){return o(-n/2,t,["top","alignCentre"])},northeast:function(){return o(-n,-t,["bottom","alignLeft"])},northwest:function(){return o(n,-t,["bottom","alignRight"])},north:function(){return o(-n/2,-t,["bottom","alignCentre"])},east:function(){return o(n,-t/2,["valignCentre","left"])},west:function(){return o(-n,-t/2,["valignCentre","right"])},innerNorthwest:function(){return o(-n,t,["top","alignRight"])},innerNortheast:function(){return o(n,t,["top","alignLeft"])},innerNorth:function(){return o(-n/2,t,["top","alignCentre"])},innerSouthwest:function(){return o(-n,-t,["bottom","alignRight"])},innerSoutheast:function(){return o(n,-t,["bottom","alignLeft"])},innerSouth:function(){return o(-n/2,-t,["bottom","alignCentre"])},innerWest:function(){return o(n,-t/2,["valignCentre","right"])},innerEast:function(){return o(-n,-t/2,["valignCentre","left"])}}}function La(){return za(0,0,{})}function ja(n){return n}function Ua(t,e){return function(n){return"rtl"===Fc(n)?e:t}}var Wa,Ga,Xa=or({}),Ya=["alloy/data/Fields","alloy/debugging/Debugging"],qa=nn({logEventCut:Z,logEventStopped:Z,logNoParent:Z,logEventNoHandlers:Z,logEventResponse:Z,write:Z}),Ka=nn([tt("menu"),tt("selectedMenu")]),Ja=nn([tt("item"),tt("selectedItem")]),$a=(nn(jo(Ja().concat(Ka()))),nn(jo(Ja()))),Qa=ut("initSize",[tt("numColumns"),tt("numRows")]),Za=nn(Qa),nc=te("x","y","bubble","direction","label"),tc=xn([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),ec=tc.southeast,oc=tc.southwest,rc=tc.northeast,ic=tc.northwest,uc=tc.south,ac=tc.north,cc=tc.east,sc=tc.west,fc=function(n,t,e){return nc(ea(n,t),ra(n,t),e.north(),ac(),"layout-n")},lc=function(n,t,e){return nc(ea(n,t),ia(n),e.south(),uc(),"layout-s")},dc=function(e,n,o,r,t,i,u){function a(n){return N(n,o)?n[o]():tn.none()}var c=L(t,function(n,t){return xa(o,n,t)}),s=L(i,function(n,t){return Fe(n,t)}),f=P(P(P({},s),c),{revoke:d(wa,o),config:function(n){var t=Jn(o+"-config",e,n);return{key:o,value:{config:t,me:f,configAsRaw:U(function(){return Jn(o+"-config",e,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,e){return a(n).bind(function(t){return bn(r,"exhibit").map(function(n){return n(e,t.config,t.state)})}).getOr(He({}))},name:function(){return o},handlers:function(n){return a(n).map(function(n){return bn(r,"events").getOr(function(){return{}})(n.config,n.state)}).getOr({})}});return f},mc=zn([tt("fields"),tt("name"),pt("active",{}),pt("apis",{}),pt("state",Xi),pt("extra",{})]),gc=zn([tt("branchKey"),tt("branches"),tt("name"),pt("active",{}),pt("apis",{}),pt("state",Xi),pt("extra",{})]),pc=nn(undefined),hc=/* */Object.freeze({__proto__:null,events:function(o){return Gt([qt(ei(),function(r,n){var i=o.channels,t=mn(i),u=n,e=function(n,t){return t.universal()?n:C(n,function(n){return sn(t.channels(),n)})}(t,u);fn(e,function(n){var t=i[n],e=t.schema,o=Jn("channel["+n+"] data\nReceiver: "+Be(r.element()),e,u.data());t.onReceive(r,o)})})])}}),vc=[et("channels",Yn(K.value,zn([$u("onReceive"),pt("schema",Jo())])))],bc=Ca({fields:vc,name:"receiving",active:hc}),yc=/* */Object.freeze({__proto__:null,exhibit:function(n,t){return He({classes:[],styles:t.useFixed()?{}:{position:"relative"}})}}),xc=te("position","left","top","right","bottom"),wc=xn([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Sc=function(n,t,e){var o=Cu(t,e);return n.fold(nn(o),nn(o),function(){var n=du();return o.translate(-n.left(),-n.top())})},Cc=(wc.none,wc.relative),kc=wc.fixed,Oc=te("anchorBox","origin"),_c=re(["x","y","width","height","maxHeight","maxWidth","direction","classes","label","candidateYforTest"],[]),Tc=xn([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),Ec=nn(function(n,t){Ha(n,t),io(n,{"overflow-x":"hidden","overflow-y":"auto"})}),Bc=nn(function(n,t){Ha(n,t)}),Dc=re(["bounds","origin","preference","maxHeightFunction","maxWidthFunction"],[]),Ac=function(n,t,e,o){var r=Ra(n,t,e,o);Va(t,r,o),function(n,t){var e=t.classes();Ze(n,e.off),Qe(n,e.on)}(t,r),function(n,t,e){e.maxHeightFunction()(n,t.maxHeight())}(t,r,o),function(n,t,e){e.maxWidthFunction()(n,t.maxWidth())}(t,r,o)},Mc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],Fc=function(n){return"rtl"===ao(n,"direction")?"rtl":"ltr"};(Ga=Wa=Wa||{}).TopToBottom="toptobottom",Ga.BottomToTop="bottomtotop";function Ic(n){return Iu(n,function(n){return Ai(n)&&ke(n,jc)===Wa.BottomToTop})}function Rc(){return gt("layouts",[tt("onLtr"),tt("onRtl"),st("onBottomLtr"),st("onBottomRtl")])}function Vc(t,n,e,o,r,i,u){var a=u.map(Ic).getOr(!1),c=n.layouts.map(function(n){return n.onLtr(t)}),s=n.layouts.map(function(n){return n.onRtl(t)}),f=a?n.layouts.bind(function(n){return n.onBottomLtr.map(function(n){return n(t)})}).or(c).getOr(r):c.getOr(e),l=a?n.layouts.bind(function(n){return n.onBottomRtl.map(function(n){return n(t)})}).or(s).getOr(i):s.getOr(o);return Ua(f,l)(t)}function Hc(n,t,e){var o=n.document.createRange();return function(e,n){n.fold(function(n){e.setStartBefore(n.dom())},function(n,t){e.setStart(n.dom(),t)},function(n){e.setStartAfter(n.dom())})}(o,t),function(e,n){n.fold(function(n){e.setEndBefore(n.dom())},function(n,t){e.setEnd(n.dom(),t)},function(n){e.setEndAfter(n.dom())})}(o,e),o}function Nc(n,t,e,o,r){var i=n.document.createRange();return i.setStart(t.dom(),e),i.setEnd(o.dom(),r),i}function Pc(n){return{left:nn(n.left),top:nn(n.top),right:nn(n.right),bottom:nn(n.bottom),width:nn(n.width),height:nn(n.height)}}function zc(n,t,e){return t(ir.fromDom(e.startContainer),e.startOffset,ir.fromDom(e.endContainer),e.endOffset)}function Lc(n,t){return function(n,t){var e=t.ltr();return e.collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Jc.rtl(ir.fromDom(n.endContainer),n.endOffset,ir.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return zc(0,Jc.ltr,e)}):zc(0,Jc.ltr,e)}(0,function(r,n){return n.match({domRange:function(n){return{ltr:nn(n),rtl:tn.none}},relative:function(n,t){return{ltr:U(function(){return Hc(r,n,t)}),rtl:U(function(){return tn.some(Hc(r,t,n))})}},exact:function(n,t,e,o){return{ltr:U(function(){return Nc(r,n,t,e,o)}),rtl:U(function(){return tn.some(Nc(r,e,o,n,t))})}}})}(n,t))}var jc="data-alloy-vertical-dir",Uc=[tt("hotspot"),st("bubble"),pt("overrides",{}),Rc(),Zu("placement",function(n,t,e){var o=t.hotspot,r=Aa(e,o.element()),i=Vc(n.element(),t,ha(),va(),pa(),[fa,sa,ca,aa,fc,lc],tn.some(t.hotspot.element()));return tn.some(ja({anchorBox:r,bubble:t.bubble.getOr(La()),overrides:t.overrides,layouts:i,placer:tn.none()}))})],Wc=[tt("x"),tt("y"),pt("height",0),pt("width",0),pt("bubble",La()),pt("overrides",{}),Rc(),Zu("placement",function(n,t,e){var o=Sc(e,t.x,t.y),r=vu(o.left(),o.top(),t.width,t.height),i=Vc(n.element(),t,ma(),ga(),ma(),ga(),tn.none());return tn.some(ja({anchorBox:r,bubble:t.bubble,overrides:t.overrides,layouts:i,placer:tn.none()}))})],Gc={create:te("start","soffset","finish","foffset")},Xc=xn([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Yc=(Xc.before,Xc.on,Xc.after,function(n){return n.fold(l,l,l)}),qc=xn([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Kc={domRange:qc.domRange,relative:qc.relative,exact:qc.exact,exactFromRange:function(n){return qc.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){var t=function(n){return n.match({domRange:function(n){return ir.fromDom(n.startContainer)},relative:function(n,t){return Yc(n)},exact:function(n,t,e,o){return n}})}(n);return ae(t)},range:Gc.create},Jc=xn([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);Jc.ltr,Jc.rtl;function $c(n){return gf.getOption(n)}function Qc(n){return function(n){return $c(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()}(n)||sn(pf,xe(n))}function Zc(n,t){return It(t,n)}function ns(n,t,e,o){var r=function(n,t,e,o){var r=ie(n).dom().createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r}(n,t,e,o),i=Rt(n,e)&&t===o;return r.collapsed&&!i}function ts(n){var t=ir.fromDom(n.anchorNode),e=ir.fromDom(n.focusNode);return ns(t,n.anchorOffset,e,n.focusOffset)?tn.some(Gc.create(t,n.anchorOffset,e,n.focusOffset)):function(n){if(0<n.rangeCount){var t=n.getRangeAt(0),e=n.getRangeAt(n.rangeCount-1);return tn.some(Gc.create(ir.fromDom(t.startContainer),t.startOffset,ir.fromDom(e.endContainer),e.endOffset))}return tn.none()}(n)}function es(n,t){return function(n){var t=n.getClientRects(),e=0<t.length?t[0]:n.getBoundingClientRect();return 0<e.width||0<e.height?tn.some(e).map(Pc):tn.none()}(function(i,n){return Lc(i,n).match({ltr:function(n,t,e,o){var r=i.document.createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r},rtl:function(n,t,e,o){var r=i.document.createRange();return r.setStart(e.dom(),o),r.setEnd(n.dom(),t),r}})}(n,t))}function os(n,t){var e=fe(n);if(0===e.length)return vf(n,t);if(t<e.length)return vf(e[t],0);var o=e[e.length-1],r=Mi(o)?function(n){return gf.get(n)}(o).length:fe(o).length;return vf(o,r)}function rs(n){return n.fold(l,function(n,t,e){return n.translate(-t,-e)})}function is(n){return n.fold(l,l)}function us(n){return O(n,function(n,t){return n.translate(t.left(),t.top())},Cu(0,0))}function as(n){var t=S(n,is);return us(t)}function cs(n,t,e){var o=ie(n.element()),r=du(o),i=function(o,n,t){var e=ae(t.root).dom();return tn.from(e.frameElement).map(ir.fromDom).filter(function(n){var t=ie(n),e=ie(o.element());return Rt(t,e)}).map(au)}(n,0,e).getOr(r);return xf(i,r.left(),r.top())}function ss(n,t){return Mi(n)?Cf(n,t):os(n,t)}function fs(n,t){return t.getSelection.getOrThunk(function(){return function(){return function(n){return tn.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(ts)}(n)}})().map(function(n){var t=ss(n.start(),n.soffset()),e=ss(n.finish(),n.foffset());return Kc.range(t.element(),t.offset(),e.element(),e.offset())})}function ls(n){return n.x()+n.width()}function ds(n,t){return n.x()-t.width()}function ms(n,t){return n.y()-t.height()+n.height()}function gs(n){return n.y()}function ps(n,t,e){return nc(ls(n),gs(n),e.southeast(),ec(),"link-layout-se")}function hs(n,t,e){return nc(ds(n,t),gs(n),e.southwest(),oc(),"link-layout-sw")}function vs(n,t,e){return nc(ls(n),ms(n,t),e.northeast(),rc(),"link-layout-ne")}function bs(n,t,e){return nc(ds(n,t),ms(n,t),e.northwest(),ic(),"link-layout-nw")}function ys(){return[ps,hs,vs,bs]}function xs(){return[hs,ps,bs,vs]}function ws(n,t,e,o,r){var i=function(n,t){return Oc(n,t)}(e.anchorBox,t);Pa(i,r.element(),e.bubble,e.layouts,o,e.overrides)}function Ss(n,t){Bi(n.element(),t.element())}function Cs(t,n){var e=t.components();!function(n){fn(n.components(),function(n){return Di(n.element())}),he(n.element()),n.syncComponents()}(t);var o=A(e,n);fn(o,function(n){Ff(n),t.getSystem().removeFromWorld(n)}),fn(n,function(n){n.getSystem().isConnected()?Ss(t,n):(t.getSystem().addToWorld(n),Ss(t,n),to(t.element())&&If(n)),t.syncComponents()})}function ks(n,t){Rf(n,t,Bi)}function Os(n){Ff(n),Di(n.element()),n.getSystem().removeFromWorld(n)}function _s(t){var n=ce(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()});Os(t),n.each(function(n){n.syncComponents()})}function Ts(n){var t=n.components();fn(t,Os),he(n.element()),n.syncComponents()}function Es(n,t){Vf(n,t,Bi)}function Bs(t){var n=fe(t.element());fn(n,function(n){t.getByDom(n).each(Ff)}),Di(t.element())}function Ds(t,n,e,o){e.get().each(function(n){Ts(t)});var r=n.getAttachPoint(t);ks(r,t);var i=t.getSystem().build(o);return ks(t,i),e.set(i),i}function As(n,t,e,o){var r=Ds(n,t,e,o);return t.onOpen(n,r),r}function Ms(t,e,o){o.get().each(function(n){Ts(t),_s(t),e.onClose(t,n),o.clear()})}function Fs(n,t,e){return e.isOpen()}function Is(n){var t,e=Jn("Dismissal",Xf,n);return(t={})[Uf()]={schema:zn([tt("target")]),onReceive:function(t,n){jf.isOpen(t)&&(jf.isPartOf(t,n.target)||e.isExtraPart(t,n.target)||e.fireEventInstead.fold(function(){return jf.close(t)},function(n){return zt(t,n.event)}))}},t}function Rs(n){var t,e=Jn("Reposition",Yf,n);return(t={})[Wf()]={onReceive:function(t){jf.isOpen(t)&&e.fireEventInstead.fold(function(){return e.doReposition(t)},function(n){return zt(t,n.event)})}},t}function Vs(n,t,e){t.store.manager.onLoad(n,t,e)}function Hs(n,t,e){t.store.manager.onUnload(n,t,e)}function Ns(){var n=or(null);return Yi({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function Ps(){var i=or({}),u=or({});return Yi({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return bn(i.get(),n).orThunk(function(){return bn(u.get(),n)})},update:function(n){var t=i.get(),e=u.get(),o={},r={};fn(n,function(t){o[t.value]=t,bn(t,"meta").each(function(n){bn(n,"text").each(function(n){r[n]=t})})}),i.set(P(P({},t),o)),u.set(P(P({},e),r))},clear:function(){i.set({}),u.set({})}})}function zs(n,t,e,o){var r=t.store;e.update([o]),r.setValue(n,o),t.onSetValue(n,o)}function Ls(t,n){return St(t,{},S(n,function(n){return function(t,e){return Xo(t,t,Vo(),Pn(function(n){return Bo("The field: "+t+" is forbidden. "+e)}))}(n.name(),"Cannot configure "+n.name()+" for "+t)}).concat([Ct("dump",l)]))}function js(n){return n.dump}function Us(n,t){return P(P({},n.dump),Sa(t))}function Ws(n){return yn(n,"uiType")}function Gs(n,t,e,o){return Ws(e)&&e.uiType===rl?function(n,t,e,o){return n.exists(function(n){return n!==e.owner})?il.single(!0,nn(e)):bn(o,e.name).fold(function(){throw new Error("Unknown placeholder component: "+e.name+"\nKnown: ["+mn(o)+"]\nNamespace: "+n.getOr("none")+"\nSpec: "+JSON.stringify(e,null,2))},function(n){return n.replace()})}(n,0,e,o):il.single(!1,nn(e))}function Xs(t,e,n,o){var r=L(o,function(n,t){return function(n,t){var e=!1;return{name:nn(n),required:function(){return t.fold(function(n,t){return n},function(n,t){return n})},used:function(){return e},replace:function(){if(e)throw new Error("Trying to use the same placeholder more than once: "+n);return e=!0,t}}}(t,n)}),i=function(t,e,n,o){return E(n,function(n){return ul(t,e,n,o)})}(t,e,n,r);return pn(r,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+t.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),i}function Ys(n){return n.fold(tn.some,tn.none,tn.some,tn.some)}function qs(n){function t(n){return n.name}return n.fold(t,t,t,t)}function Ks(e,o){return function(n){var t=Jn("Converting part type",o,n);return e(t)}}function Js(n,t,e,o){return Sn(t.defaults(n,e,o),e,{uid:n.partUids[t.name]},t.overrides(n,e,o))}function $s(r,n){var t={};return fn(n,function(n){Ys(n).each(function(e){var o=El(r,e.pname);t[e.name]=function(n){var t=Jn("Part: "+e.name+" in "+r,jo(e.schema),n);return P(P({},o),{config:n,validated:t})}})}),t}function Qs(n,t,e){return{uiType:sl(),owner:n,name:t,config:e,validated:{}}}function Zs(n){return E(n,function(n){return n.fold(tn.none,tn.some,tn.none,tn.none).map(function(n){return ut(n.name,n.schema.concat([na(_l())]))}).toArray()})}function nf(n){return S(n,qs)}function tf(n,t,e){return function(n,e,t){var i={},o={};return fn(t,function(n){n.fold(function(o){i[o.pname]=al(!0,function(n,t,e){return o.factory.sketch(Js(n,o,t,e))})},function(n){var t=e.parts[n.name];o[n.name]=nn(n.factory.sketch(Js(e,n,t[_l()]),t))},function(o){i[o.pname]=al(!1,function(n,t,e){return o.factory.sketch(Js(n,o,t,e))})},function(r){i[r.pname]=cl(!0,function(t,n,e){var o=t[r.name];return S(o,function(n){return r.factory.sketch(Sn(r.defaults(t,n,e),n,r.overrides(t,n)))})})})}),{internals:nn(i),externals:nn(o)}}(0,t,e)}function ef(n,t,e){return Xs(tn.some(n),t,t.components,e)}function of(n,t,e){var o=t.partUids[e];return n.getSystem().getByUid(o).toOption()}function rf(n,t,e){return of(n,t,e).getOrDie("Could not find part: "+e)}function uf(n,t,e){var o={},r=t.partUids,i=n.getSystem();return fn(e,function(n){o[n]=nn(i.getByUid(r[n]))}),o}function af(n,t){var e=n.getSystem();return L(t.partUids,function(n,t){return nn(e.getByUid(n))})}function cf(n){return mn(n.partUids)}function sf(n,t,e){var o={},r=t.partUids,i=n.getSystem();return fn(e,function(n){o[n]=nn(i.getByUid(r[n]).getOrDie())}),o}function ff(t,n){var e=nf(n);return An(S(e,function(n){return{key:n,value:t+"-"+n}}))}function lf(t){return Xo("partUids","partUids",No(function(n){return ff(n.uid,t)}),Jo())}function df(n,t,e,o,r){var i=function(n,t){return(0<n.length?[ut("parts",n)]:[]).concat([tt("uid"),pt("dom",{}),pt("components",[]),na("originalSpec"),pt("debug.sketcher",{})]).concat(t)}(o,r);return Jn(n+" [SpecSchema]",zn(i.concat(t)),e)}function mf(n,t,e,o,r){var i=Dl(r),u=Zs(e),a=lf(e),c=df(n,t,i,u,[a]),s=tf(0,c,e);return o(c,ef(n,c,s.internals()),i,s.externals())}var gf=function uI(e,o){var t=function(n){return e(n)?tn.from(n.dom().nodeValue):tn.none()};return{get:function(n){if(!e(n))throw new Error("Can only get "+o+" value of a "+o+" node");return t(n).getOr("")},getOption:t,set:function(n,t){if(!e(n))throw new Error("Can only set raw "+o+" value of a "+o+" node");n.dom().nodeValue=t}}}(Mi,"text"),pf=["img","br"],hf=function(n,i){var u=function(n){for(var t=fe(n),e=t.length-1;0<=e;e--){var o=t[e];if(i(o))return tn.some(o);var r=u(o);if(r.isSome())return r}return tn.none()};return u(n)},vf=te("element","offset"),bf=xn([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),yf=bf.screen,xf=bf.absolute,wf=function(n,t,e,o){var r=n,i=t,u=e,a=o;n<0&&(r=0,u=e+n),t<0&&(i=0,a=o+t);var c=yf(Cu(r,i));return tn.some(Bu(c,u,a))},Sf=function(n,a,c,s,f){return n.map(function(n){var t=[a,n.point()],e=function(n,t,e,o){return n.fold(t,e,o)}(s,function(){return as(t)},function(){return as(t)},function(){return function(n){var t=S(n,rs);return us(t)}(t)}),o=Du(e.left(),e.top(),n.width(),n.height()),r=c.showAbove?pa():ha(),i=(c.showAbove,va()),u=Vc(f,c,r,i,r,i,tn.none());return ja({anchorBox:o,bubble:c.bubble.getOr(La()),overrides:c.overrides,layouts:u,placer:tn.none()})})},Cf=te("element","offset"),kf=[st("getSelection"),tt("root"),st("bubble"),Rc(),pt("overrides",{}),pt("showAbove",!1),Zu("placement",function(n,t,e){var o=ae(t.root).dom(),r=cs(n,0,t),i=fs(o,t).bind(function(n){return es(o,Kc.exactFromRange(n)).orThunk(function(){var t=ir.fromText("\ufeff");return de(n.start(),t),es(o,Kc.exact(t,0,t,1)).map(function(n){return Di(t),n})}).bind(function(n){return wf(n.left(),n.top(),n.width(),n.height())})}),u=fs(o,t).bind(function(n){return Ai(n.start())?tn.some(n.start()):ce(n.start())}).getOr(n.element());return Sf(i,r,t,e,u)})],Of=[tt("node"),tt("root"),st("bubble"),Rc(),pt("overrides",{}),pt("showAbove",!1),Zu("placement",function(r,i,u){var a=cs(r,0,i);return i.node.bind(function(n){var t=n.dom().getBoundingClientRect(),e=wf(t.left,t.top,t.width,t.height),o=i.node.getOr(r.element());return Sf(e,a,i,u,o)})})],_f=[tt("item"),Rc(),pt("overrides",{}),Zu("placement",function(n,t,e){var o=Aa(e,t.item.element()),r=Vc(n.element(),t,ys(),xs(),ys(),xs(),tn.none());return tn.some(ja({anchorBox:o,bubble:La(),overrides:t.overrides,layouts:r,placer:tn.none()}))})],Tf=Qn("anchor",{selection:kf,node:Of,hotspot:Uc,submenu:_f,makeshift:Wc}),Ef=function(n,t,e,o,r,i){var u=i.map(bu);return Bf(n,t,e,o,r,u)},Bf=function(r,i,n,t,u,a){var c=Jn("positioning anchor.info",Tf,t);Ea(function(){ro(u.element(),"position","fixed");var n=co(u.element(),"visibility");ro(u.element(),"visibility","hidden");var t=i.useFixed()?function(){var n=v.document.documentElement;return kc(0,0,n.clientWidth,n.clientHeight)}():function(n){var t=au(n.element()),e=n.element().dom().getBoundingClientRect();return Cc(t.left(),t.top(),e.width,e.height)}(r),e=c.placement,o=a.map(nn).or(i.getBounds);e(r,c,t).each(function(n){n.placer.getOr(ws)(r,t,n,o,u)}),n.fold(function(){fo(u.element(),"visibility")},function(n){ro(u.element(),"visibility",n)}),co(u.element(),"left").isNone()&&co(u.element(),"top").isNone()&&co(u.element(),"right").isNone()&&co(u.element(),"bottom").isNone()&&co(u.element(),"position").is("fixed")&&fo(u.element(),"position")},u.element())},Df=/* */Object.freeze({__proto__:null,position:function(n,t,e,o,r){Ef(n,t,e,o,r,tn.none())},positionWithin:Ef,positionWithinBounds:Bf,getMode:function(n,t,e){return t.useFixed()?"fixed":"absolute"}}),Af=[pt("useFixed",u),st("getBounds")],Mf=Ca({fields:Af,name:"positioning",active:yc,apis:Df}),Ff=function(n){zt(n,pi());var t=n.components();fn(t,Ff)},If=function(n){var t=n.components();fn(t,If),zt(n,gi())},Rf=function(n,t,e){n.getSystem().addToWorld(t),e(n.element(),t.element()),to(n.element())&&If(t),n.syncComponents()},Vf=function(n,t,e){e(n,t.element());var o=fe(t.element());fn(o,function(n){t.getByDom(n).each(If)})},Hf=function(n,t,e){var o=t.getAttachPoint(n);ro(n.element(),"position",Mf.getMode(o)),function(t,n,e,o){co(t.element(),n).fold(function(){_e(t.element(),e)},function(n){Ce(t.element(),e,n)}),ro(t.element(),n,o)}(n,"visibility",t.cloakVisibilityAttr,"hidden")},Nf=function(n,t,e){!function(t){return x(["top","left","right","bottom"],function(n){return co(t,n).isSome()})}(n.element())&&fo(n.element(),"position"),function(n,t,e){if(Oe(n.element(),e)){var o=ke(n.element(),e);ro(n.element(),t,o)}else fo(n.element(),t)}(n,"visibility",t.cloakVisibilityAttr)},Pf=/* */Object.freeze({__proto__:null,cloak:Hf,decloak:Nf,open:As,openWhileCloaked:function(n,t,e,o,r){Hf(n,t),As(n,t,e,o),r(),Nf(n,t)},close:Ms,isOpen:Fs,isPartOf:function(t,e,n,o){return Fs(0,0,n)&&n.get().exists(function(n){return e.isPartOf(t,n,o)})},getState:function(n,t,e){return e.get()},setContent:function(n,t,e,o){return e.get().map(function(){return Ds(n,t,e,o)})}}),zf=/* */Object.freeze({__proto__:null,events:function(e,o){return Gt([qt(ai(),function(n,t){Ms(n,e,o)})])}}),Lf=[Ku("onOpen"),Ku("onClose"),tt("isPartOf"),tt("getAttachPoint"),pt("cloakVisibilityAttr","data-precloak-visibility")],jf=Ca({fields:Lf,name:"sandboxing",active:zf,apis:Pf,state:/* */Object.freeze({__proto__:null,init:function(){var t=or(tn.none()),n=nn("not-implemented");return Yi({readState:n,isOpen:function(){return t.get().isSome()},clear:function(){t.set(tn.none())},set:function(n){t.set(tn.some(n))},get:function(){return t.get()}})}})}),Uf=nn("dismiss.popups"),Wf=nn("reposition.popups"),Gf=nn("mouse.released"),Xf=zn([pt("isExtraPart",nn(!1)),gt("fireEventInstead",[pt("event",hi())])]),Yf=zn([pt("isExtraPart",nn(!1)),gt("fireEventInstead",[pt("event",vi())]),it("doReposition")]),qf=/* */Object.freeze({__proto__:null,onLoad:Vs,onUnload:Hs,setValue:function(n,t,e,o){t.store.manager.setValue(n,t,e,o)},getValue:function(n,t,e){return t.store.manager.getValue(n,t,e)},getState:function(n,t,e){return e}}),Kf=/* */Object.freeze({__proto__:null,events:function(e,o){var n=e.resetOnDom?[Oi(function(n,t){Vs(n,e,o)}),_i(function(n,t){Hs(n,e,o)})]:[ba(e,o,Vs)];return Gt(n)}}),Jf=/* */Object.freeze({__proto__:null,memory:Ns,dataset:Ps,manual:function(){return Yi({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),$f=[st("initialValue"),tt("getFallbackEntry"),tt("getDataKey"),tt("setValue"),Zu("manager",{setValue:zs,getValue:function(n,t,e){var o=t.store,r=o.getDataKey(n);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(n){return n})},onLoad:function(t,e,o){e.store.initialValue.each(function(n){zs(t,e,o,n)})},onUnload:function(n,t,e){e.clear()},state:Ps})],Qf=[tt("getValue"),pt("setValue",Z),st("initialValue"),Zu("manager",{setValue:function(n,t,e,o){t.store.setValue(n,o),t.onSetValue(n,o)},getValue:function(n,t,e){return t.store.getValue(n)},onLoad:function(t,e,n){e.store.initialValue.each(function(n){e.store.setValue(t,n)})},onUnload:Z,state:Xi.init})],Zf=[st("initialValue"),Zu("manager",{setValue:function(n,t,e,o){e.set(o),t.onSetValue(n,o)},getValue:function(n,t,e){return e.get()},onLoad:function(n,t,e){t.store.initialValue.each(function(n){e.isNotSet()&&e.set(n)})},onUnload:function(n,t,e){e.clear()},state:Ns})],nl=[ht("store",{mode:"memory"},Qn("mode",{memory:Zf,manual:Qf,dataset:$f})),Ku("onSetValue"),pt("resetOnDom",!1)],tl=Ca({fields:nl,name:"representing",active:Kf,apis:qf,extra:{setValueFrom:function(n,t){var e=tl.getValue(t);tl.setValue(n,e)}},state:Jf}),el=Ls,ol=Us,rl="placeholder",il=xn([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),ul=function(i,u,a,c){return Gs(i,0,a,c).fold(function(n,t){var e=Ws(a)?t(u,a.config,a.validated):t(u),o=bn(e,"components").getOr([]),r=E(o,function(n){return ul(i,u,n,c)});return[P(P({},e),{components:r})]},function(n,t){if(Ws(a)){var e=t(u,a.config,a.validated);return a.validated.preprocess.getOr(l)(e)}return t(u)})},al=il.single,cl=il.multiple,sl=nn(rl),fl=xn([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),ll=pt("factory",{sketch:l}),dl=pt("schema",[]),ml=tt("name"),gl=Xo("pname","pname",Ho(function(n){return"<alloy."+De(n.name)+">"}),Jo()),pl=Ct("schema",function(){return[st("preprocess")]}),hl=pt("defaults",nn({})),vl=pt("overrides",nn({})),bl=jo([ll,dl,ml,gl,hl,vl]),yl=jo([ll,dl,ml,hl,vl]),xl=jo([ll,dl,ml,gl,hl,vl]),wl=jo([ll,pl,ml,tt("unit"),gl,hl,vl]),Sl=Ks(fl.required,bl),Cl=Ks(fl.external,yl),kl=Ks(fl.optional,xl),Ol=Ks(fl.group,wl),_l=nn("entirety"),Tl=/* */Object.freeze({__proto__:null,required:Sl,external:Cl,optional:kl,group:Ol,asNamedPart:Ys,name:qs,asCommon:function(n){return n.fold(l,l,l,l)},original:_l}),El=function(n,t){return{uiType:sl(),owner:n,name:t}},Bl=/* */Object.freeze({__proto__:null,generate:$s,generateOne:Qs,schemas:Zs,names:nf,substitutes:tf,components:ef,defaultUids:ff,defaultUidsSchema:lf,getAllParts:af,getAllPartNames:cf,getPart:of,getPartOrDie:rf,getParts:uf,getPartsOrDie:sf}),Dl=function(n){return function(n){return yn(n,"uid")}(n)?n:P(P({},n),{uid:Ae("uid")})};function Al(n){var t=Jn("Sketcher for "+n.name,Ql,n),e=L(t.apis,Re),o=L(t.extraApis,function(n,t){return Fe(n,t)});return P(P({name:nn(t.name),configFields:nn(t.configFields),sketch:function(n){return function(n,t,e,o){var r=Dl(o);return e(df(n,t,r,[],[]),r)}(t.name,t.configFields,t.factory,n)}},e),o)}function Ml(n){var t=Jn("Sketcher for "+n.name,Zl,n),e=$s(t.name,t.partFields),o=L(t.apis,Re),r=L(t.extraApis,function(n,t){return Fe(n,t)});return P(P({name:nn(t.name),partFields:nn(t.partFields),configFields:nn(t.configFields),sketch:function(n){return mf(t.name,t.configFields,t.partFields,t.factory,n)},parts:nn(e)},o),r)}function Fl(n){for(var t=[],e=function(n){t.push(n)},o=0;o<n.length;o++)n[o].each(e);return t}function Il(n){return"input"===xe(n)&&"radio"!==ke(n,"type")||"textarea"===xe(n)}function Rl(e,o,n,r){var t=Zc(e.element(),"."+o.highlightClass);fn(t,function(t){x(r,function(n){return n.element()===t})||(Je(t,o.highlightClass),e.getSystem().getByDom(t).each(function(n){o.onDehighlight(e,n),zt(n,Ci())}))})}function Vl(n,t,e,o){Rl(n,t,0,[o]),od(n,t,e,o)||(qe(o.element(),t.highlightClass),t.onHighlight(n,o),zt(o,Si()))}function Hl(e,t,n,o){var r=Zc(e.element(),"."+t.itemClass);return T(r,function(n){return $e(n,t.highlightClass)}).bind(function(n){var t=Ma(n,o,0,r.length-1);return e.getSystem().getByDom(r[t]).toOption()})}function Nl(n,t,e){var o=D(n.slice(0,t)),r=D(n.slice(t+1));return _(o.concat(r),e)}function Pl(n,t,e){var o=D(n.slice(0,t));return _(o,e)}function zl(n,t,e){var o=n.slice(0,t),r=n.slice(t+1);return _(r.concat(o),e)}function Ll(n,t,e){var o=n.slice(t+1);return _(o,e)}function jl(e){return function(n){var t=n.raw();return sn(e,t.which)}}function Ul(n){return function(t){return B(n,function(n){return n(t)})}}function Wl(n){return!0===n.raw().shiftKey}function Gl(n){return!0===n.raw().ctrlKey}function Xl(n,t){return{matches:n,classification:t}}function Yl(n,t,e){t.exists(function(t){return e.exists(function(n){return Rt(n,t)})})||Lt(n,bi(),{prevFocus:t,newFocus:e})}function ql(){function r(n){return Ta(n.element())}return{get:r,set:function(n,t){var e=r(n);n.getSystem().triggerFocus(t,n.element());var o=r(n);Yl(n,e,o)}}}function Kl(){function r(n){return fd.getHighlighted(n).map(function(n){return n.element()})}return{get:r,set:function(t,n){var e=r(t);t.getSystem().getByDom(n).fold(Z,function(n){fd.highlight(t,n)});var o=r(t);Yl(t,e,o)}}}var Jl,$l,Ql=zn([tt("name"),tt("factory"),tt("configFields"),pt("apis",{}),pt("extraApis",{})]),Zl=zn([tt("name"),tt("factory"),tt("configFields"),tt("partFields"),pt("apis",{}),pt("extraApis",{})]),nd=/* */Object.freeze({__proto__:null,getCurrent:function(n,t,e){return t.find(n)}}),td=[tt("find")],ed=Ca({fields:td,name:"composing",apis:nd}),od=function(n,t,e,o){return $e(o.element(),t.highlightClass)},rd=function(n,t,e,o){var r=Zc(n.element(),"."+t.itemClass);return tn.from(r[o]).fold(function(){return K.error("No element found with index "+o)},n.getSystem().getByDom)},id=function(t,n,e){return Vu(t.element(),"."+n.itemClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},ud=function(t,n,e){var o=Zc(t.element(),"."+n.itemClass);return(0<o.length?tn.some(o[o.length-1]):tn.none()).bind(function(n){return t.getSystem().getByDom(n).toOption()})},ad=function(t,n,e){var o=Zc(t.element(),"."+n.itemClass);return Fl(S(o,function(n){return t.getSystem().getByDom(n).toOption()}))},cd=/* */Object.freeze({__proto__:null,dehighlightAll:function(n,t,e){return Rl(n,t,0,[])},dehighlight:function(n,t,e,o){od(n,t,e,o)&&(Je(o.element(),t.highlightClass),t.onDehighlight(n,o),zt(o,Ci()))},highlight:Vl,highlightFirst:function(t,e,o){id(t,e).each(function(n){Vl(t,e,o,n)})},highlightLast:function(t,e,o){ud(t,e).each(function(n){Vl(t,e,o,n)})},highlightAt:function(t,e,o,n){rd(t,e,o,n).fold(function(n){throw new Error(n)},function(n){Vl(t,e,o,n)})},highlightBy:function(t,e,o,n){var r=ad(t,e);_(r,n).each(function(n){Vl(t,e,o,n)})},isHighlighted:od,getHighlighted:function(t,n,e){return Vu(t.element(),"."+n.highlightClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},getFirst:id,getLast:ud,getPrevious:function(n,t,e){return Hl(n,t,0,-1)},getNext:function(n,t,e){return Hl(n,t,0,1)},getCandidates:ad}),sd=[tt("highlightClass"),tt("itemClass"),Ku("onHighlight"),Ku("onDehighlight")],fd=Ca({fields:sd,name:"highlighting",apis:cd}),ld=b(Wl);($l=Jl=Jl||{}).OnFocusMode="onFocus",$l.OnEnterOrSpaceMode="onEnterOrSpace",$l.OnApiMode="onApi";function dd(n,t,e,i,u){function a(t,e,n,o,r){return function(n,t){return _(n,function(n){return n.matches(t)}).map(function(n){return n.classification})}(n(t,e,o,r),e.event()).bind(function(n){return n(t,e,o,r)})}var o={schema:function(){return n.concat([pt("focusManager",ql()),ht("focusInside","onFocus",Xn(function(n){return sn(["onFocus","onEnterOrSpace","onApi"],n)?K.value(n):K.error("Invalid value for focusInside")})),Zu("handler",o),Zu("state",t),Zu("sendFocusIn",u)])},processKey:a,toEvents:function(o,r){var n=o.focusInside!==Jl.OnFocusMode?tn.none():u(o).map(function(e){return qt(Zr(),function(n,t){e(n,o,r),t.stop()})});return Gt(n.toArray().concat([qt(Gr(),function(n,t){a(n,t,e,o,r).fold(function(){!function(t,e){var n=jl([32].concat([13]))(e.event());o.focusInside===Jl.OnEnterOrSpaceMode&&n&&Ht(t,e)&&u(o).each(function(n){n(t,o,r),e.stop()})}(n,t)},function(n){t.stop()})}),qt(Xr(),function(n,t){a(n,t,i,o,r).each(function(n){t.stop()})})]))}};return o}function md(n){function i(n,t){var e=n.visibilitySelector.bind(function(n){return Hu(t,n)}).getOr(t);return 0<ru(e)}function t(t,e,n){(function(n,t){var e=Zc(n.element(),t.selector),o=C(e,function(n){return i(t,n)});return tn.from(o[t.firstTabstop])})(t,e).each(function(n){e.focusManager.set(t,n)})}function u(t,n,e,o,r){return r(n,e,function(n){return function(n,t){return i(n,t)&&n.useTabstopAt(t)}(o,n)}).fold(function(){return o.cyclic?tn.some(!0):tn.none()},function(n){return o.focusManager.set(t,n),tn.some(!0)})}function r(t,n,e,o){var r=Zc(t.element(),e.selector);return function(n,t){return t.focusManager.get(n).bind(function(n){return Hu(n,t.selector)})}(t,e).bind(function(n){return T(r,d(Rt,n)).bind(function(n){return u(t,r,n,e,o)})})}var e=[st("onEscape"),st("onEnter"),pt("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),pt("firstTabstop",0),pt("useTabstopAt",nn(!0)),st("visibilitySelector")].concat([n]),o=nn([Xl(Ul([Wl,jl([9])]),function(n,t,e){var o=e.cyclic?Nl:Pl;return r(n,0,e,o)}),Xl(jl([9]),function(n,t,e){var o=e.cyclic?zl:Ll;return r(n,0,e,o)}),Xl(jl([27]),function(t,e,n){return n.onEscape.bind(function(n){return n(t,e)})}),Xl(Ul([ld,jl([13])]),function(t,e,n){return n.onEnter.bind(function(n){return n(t,e)})})]),a=nn([]);return dd(e,Xi.init,o,a,function(){return tn.some(t)})}function gd(n,t,e){return Il(e)&&jl([32])(t.event())?tn.none():function(n,t,e){return Ut(n,e,oi()),tn.some(!0)}(n,0,e)}function pd(n,t){return tn.some(!0)}function hd(n,t,e){return e.execute(n,t,n.element())}function vd(){var e=or(tn.none());return Yi({readState:function(){return e.get().map(function(n){return{numRows:String(n.numRows()),numColumns:String(n.numColumns())}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,t){e.set(tn.some({numRows:nn(n),numColumns:nn(t)}))},getNumRows:function(){return e.get().map(function(n){return n.numRows()})},getNumColumns:function(){return e.get().map(function(n){return n.numColumns()})}})}function bd(i){return function(n,t,e,o){var r=i(n.element());return Hm(r,n,t,e,o)}}function yd(n,t){var e=Ua(n,t);return bd(e)}function xd(n,t){var e=Ua(t,n);return bd(e)}function wd(r){return function(n,t,e,o){return Hm(r,n,t,e,o)}}function Sd(n){return!function(n){return n.offsetWidth<=0&&n.offsetHeight<=0}(n.dom())}function Cd(n,t,e){var o=Zc(n,e);return function(t,n){return T(t,n).map(function(n){return{index:nn(n),candidates:nn(t)}})}(C(o,Sd),function(n){return Rt(n,t)})}function kd(n,t){return T(n,function(n){return Rt(t,n)})}function Od(e,n,o,t){return t(Math.floor(n/o),n%o).bind(function(n){var t=n.row()*o+n.column();return 0<=t&&t<e.length?tn.some(e[t]):tn.none()})}function _d(r,n,i,u,a){return Od(r,n,u,function(n,t){var e=n===i-1?r.length-n*u:u,o=Ma(t,a,0,e-1);return tn.some({row:nn(n),column:nn(o)})})}function Td(i,n,u,a,c){return Od(i,n,a,function(n,t){var e=Ma(n,c,0,u-1),o=e===u-1?i.length-e*a:a,r=Fa(t,0,o-1);return tn.some({row:nn(e),column:nn(r)})})}function Ed(t,e,n){Vu(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Bd(r){return function(n,t,e,o){return Cd(n,t,e.selector).bind(function(n){return r(n.candidates(),n.index(),o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}}function Dd(n,t,e){return e.captureTab?tn.some(!0):tn.none()}function Ad(n,t,e,r){var i=function(n,t,e){var o=Ma(t,r,0,e.length-1);return o===n?tn.none():function(n){return"button"===xe(n)&&"disabled"===ke(n,"disabled")}(e[o])?i(n,o,e):tn.from(e[o])};return Cd(n,e,t).bind(function(n){var t=n.index(),e=n.candidates();return i(t,t,e)})}function Md(t,e,o){return function(n,t){return t.focusManager.get(n).bind(function(n){return Hu(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})}function Fd(t,e,n){e.getInitial(t).orThunk(function(){return Vu(t.element(),e.selector)}).each(function(n){e.focusManager.set(t,n)})}function Id(n,t,e){return Ad(n,e.selector,t,-1)}function Rd(n,t,e){return Ad(n,e.selector,t,1)}function Vd(r){return function(n,t,e,o){return r(n,t,e,o).bind(function(){return e.executeOnMove?Md(n,t,e):tn.some(!0)})}}function Hd(n,t,e){return e.onEscape(n,t)}function Nd(n,t,e){return tn.from(n[t]).bind(function(n){return tn.from(n[e]).map(function(n){return Qm({rowIndex:t,columnIndex:e,cell:n})})})}function Pd(n,t,e,o){var r=n[t].length,i=Ma(e,o,0,r-1);return Nd(n,t,i)}function zd(n,t,e,o){var r=Ma(e,o,0,n.length-1),i=n[r].length,u=Fa(t,0,i-1);return Nd(n,r,u)}function Ld(n,t,e,o){var r=n[t].length,i=Fa(e+o,0,r-1);return Nd(n,t,i)}function jd(n,t,e,o){var r=Fa(e+o,0,n.length-1),i=n[r].length,u=Fa(t,0,i-1);return Nd(n,r,u)}function Ud(t,e,n){e.previousSelector(t).orThunk(function(){var n=e.selectors;return Vu(t.element(),n.cell)}).each(function(n){e.focusManager.set(t,n)})}function Wd(n,t){return function(r,e,i){var u=i.cycles?n:t;return Hu(e,i.selectors.row).bind(function(n){var t=Zc(n,i.selectors.cell);return kd(t,e).bind(function(e){var o=Zc(r,i.selectors.row);return kd(o,n).bind(function(n){var t=function(n,t){return S(n,function(n){return Zc(n,t.selectors.cell)})}(o,i);return u(t,n,e).map(function(n){return n.cell()})})})})}}function Gd(t,e,o){return o.focusManager.get(t).bind(function(n){return o.execute(t,e,n)})}function Xd(t,e,n){Vu(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Yd(n,t,e){return Ad(n,e.selector,t,-1)}function qd(n,t,e){return Ad(n,e.selector,t,1)}function Kd(n,t,e,o){var r=n.getSystem().build(o);Rf(n,r,e)}function Jd(n,t,e,o){var r=Sg(n);_(r,function(n){return Rt(o.element(),n.element())}).each(_s)}function $d(t,n,e,o,r){var i=Sg(t);return tn.from(i[o]).map(function(n){return Jd(t,0,0,n),r.each(function(n){Kd(t,0,function(n,t){!function(n,t,e){le(n,e).fold(function(){Bi(n,t)},function(n){de(n,t)})}(n,t,o)},n)}),n})}function Qd(n,t){return{key:n,value:{config:{},me:function(n,t){var e=Gt(t);return Ca({fields:[tt("enabled")],name:n,active:{events:nn(e)}})}(n,t),configAsRaw:nn({}),initialConfig:{},state:Xi}}}function Zd(n,t){t.ignore||(Oa(n.element()),t.onFocus(n))}function nm(n,t,e){var o=t.aria;o.update(n,o,e.get())}function tm(t,n,e){n.toggleClass.each(function(n){e.get()?qe(t.element(),n):Je(t.element(),n)})}function em(n,t,e){Eg(n,t,e,!e.get())}function om(n,t,e){e.set(!0),tm(n,t,e),nm(n,t,e)}function rm(n,t,e){e.set(!1),tm(n,t,e),nm(n,t,e)}function im(n,t,e){Eg(n,t,e,t.selected)}function um(){function n(n,t){t.stop(),jt(n)}return[qt(Kr(),n),qt(ii(),n),Zt(Ir()),Zt(Nr())]}function am(n){return Gt(z([n.map(function(e){return Ei(function(n,t){e(n),t.stop()})}).toArray(),um()]))}function cm(n){(Ta(n.element()).isNone()||Tg.isFocused(n))&&(Tg.isFocused(n)||Tg.focus(n),Lt(n,Ig,{item:n}))}function sm(n){Lt(n,Rg,{item:n})}function fm(n,t){var e={};pn(n,function(n,t){fn(n,function(n){e[n]=t})});var o=t,r=function(n){return hn(n,function(n,t){return{k:n,v:t}})}(t),i=L(r,function(n,t){return[t].concat(Yg(e,o,r,t))});return L(e,function(n){return bn(i,n).getOr([n])})}function lm(n){return n.x()}function dm(n,t){return n.x()+n.width()/2-t.width()/2}function mm(n,t){return n.x()+n.width()-t.width()}function gm(n){return n.y()}function pm(n,t){return n.y()+n.height()-t.height()}function hm(n,t,e){return nc(lm(n),pm(n,t),e.innerSoutheast(),ec(),"layout-se")}function vm(n,t,e){return nc(mm(n,t),pm(n,t),e.innerSouthwest(),oc(),"layout-sw")}function bm(n,t,e){return nc(lm(n),gm(n),e.innerNortheast(),rc(),"layout-ne")}function ym(n,t,e){return nc(mm(n,t),gm(n),e.innerNorthwest(),ic(),"layout-nw")}function xm(n){return n.getParam("height",Math.max(n.getElement().offsetHeight,200))}function wm(n){return n.getParam("width",ep.DOM.getStyle(n.getElement(),"width"))}function Sm(n){return tn.from(n.settings.min_width).filter(rn)}function Cm(n){return tn.from(n.settings.min_height).filter(rn)}function km(n){return tn.from(n.getParam("max_width")).filter(rn)}function Om(n){return tn.from(n.getParam("max_height")).filter(rn)}function _m(n){return!1!==n.getParam("menubar",!0,"boolean")}function Tm(n){var t=n.getParam("toolbar",!0),e=!0===t,o=J(t),r=Q(t)&&0<t.length;return!rp(n)&&(r||o||e)}function Em(t){var n=mn(t.settings),e=C(n,function(n){return/^toolbar([1-9])$/.test(n)}),o=S(e,function(n){return t.getParam(n,!1,"string")}),r=C(o,function(n){return"string"==typeof n});return 0<r.length?tn.some(r):tn.none()}var Bm,Dm,Am,Mm=md(Ct("cyclic",nn(!1))),Fm=md(Ct("cyclic",nn(!0))),Im=[pt("execute",gd),pt("useSpace",!1),pt("useEnter",!0),pt("useControlEnter",!1),pt("useDown",!1)],Rm=dd(Im,Xi.init,function(n,t,e,o){var r=e.useSpace&&!Il(n.element())?[32]:[],i=e.useEnter?[13]:[],u=e.useDown?[40]:[],a=r.concat(i).concat(u);return[Xl(jl(a),hd)].concat(e.useControlEnter?[Xl(Ul([Gl,jl([13])]),hd)]:[])},function(n,t,e,o){return e.useSpace&&!Il(n.element())?[Xl(jl([32]),pd)]:[]},function(){return tn.none()}),Vm=/* */Object.freeze({__proto__:null,flatgrid:vd,init:function(n){return n.state(n)}}),Hm=function(t,e,n,o,r){return o.focusManager.get(e).bind(function(n){return t(e.element(),n,o,r)}).map(function(n){return o.focusManager.set(e,n),!0})},Nm=wd,Pm=wd,zm=wd,Lm=[tt("selector"),pt("execute",gd),Ju("onEscape"),pt("captureTab",!1),Za()],jm=Bd(function(n,t,e,o){return _d(n,t,e,o,-1)}),Um=Bd(function(n,t,e,o){return _d(n,t,e,o,1)}),Wm=Bd(function(n,t,e,o){return Td(n,t,e,o,-1)}),Gm=Bd(function(n,t,e,o){return Td(n,t,e,o,1)}),Xm=nn([Xl(jl([37]),yd(jm,Um)),Xl(jl([39]),xd(jm,Um)),Xl(jl([38]),Nm(Wm)),Xl(jl([40]),Pm(Gm)),Xl(Ul([Wl,jl([9])]),Dd),Xl(Ul([ld,jl([9])]),Dd),Xl(jl([27]),function(n,t,e){return e.onEscape(n,t)}),Xl(jl([32].concat([13])),function(t,e,o,n){return function(n,t){return t.focusManager.get(n).bind(function(n){return Hu(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})})]),Ym=nn([Xl(jl([32]),pd)]),qm=dd(Lm,vd,Xm,Ym,function(){return tn.some(Ed)}),Km=[tt("selector"),pt("getInitial",tn.none),pt("execute",gd),Ju("onEscape"),pt("executeOnMove",!1),pt("allowVertical",!0)],Jm=nn([Xl(jl([32]),pd)]),$m=dd(Km,Xi.init,function(n,t,e,o){var r=[37].concat(e.allowVertical?[38]:[]),i=[39].concat(e.allowVertical?[40]:[]);return[Xl(jl(r),Vd(yd(Id,Rd))),Xl(jl(i),Vd(xd(Id,Rd))),Xl(jl([13]),Md),Xl(jl([32]),Md),Xl(jl([27]),Hd)]},Jm,function(){return tn.some(Fd)}),Qm=re(["rowIndex","columnIndex","cell"],[]),Zm=[ut("selectors",[tt("row"),tt("cell")]),pt("cycles",!0),pt("previousSelector",tn.none),pt("execute",gd)],ng=Wd(function(n,t,e){return Pd(n,t,e,-1)},function(n,t,e){return Ld(n,t,e,-1)}),tg=Wd(function(n,t,e){return Pd(n,t,e,1)},function(n,t,e){return Ld(n,t,e,1)}),eg=Wd(function(n,t,e){return zd(n,e,t,-1)},function(n,t,e){return jd(n,e,t,-1)}),og=Wd(function(n,t,e){return zd(n,e,t,1)},function(n,t,e){return jd(n,e,t,1)}),rg=nn([Xl(jl([37]),yd(ng,tg)),Xl(jl([39]),xd(ng,tg)),Xl(jl([38]),Nm(eg)),Xl(jl([40]),Pm(og)),Xl(jl([32].concat([13])),function(t,e,o){return Ta(t.element()).bind(function(n){return o.execute(t,e,n)})})]),ig=nn([Xl(jl([32]),pd)]),ug=dd(Zm,Xi.init,rg,ig,function(){return tn.some(Ud)}),ag=[tt("selector"),pt("execute",gd),pt("moveOnTab",!1)],cg=nn([Xl(jl([38]),zm(Yd)),Xl(jl([40]),zm(qd)),Xl(Ul([Wl,jl([9])]),function(n,t,e,o){return e.moveOnTab?zm(Yd)(n,t,e,o):tn.none()}),Xl(Ul([ld,jl([9])]),function(n,t,e,o){return e.moveOnTab?zm(qd)(n,t,e,o):tn.none()}),Xl(jl([13]),Gd),Xl(jl([32]),Gd)]),sg=nn([Xl(jl([32]),pd)]),fg=dd(ag,Xi.init,cg,sg,function(){return tn.some(Xd)}),lg=[Ju("onSpace"),Ju("onEnter"),Ju("onShiftEnter"),Ju("onLeft"),Ju("onRight"),Ju("onTab"),Ju("onShiftTab"),Ju("onUp"),Ju("onDown"),Ju("onEscape"),pt("stopSpaceKeyup",!1),st("focusIn")],dg=dd(lg,Xi.init,function(n,t,e){return[Xl(jl([32]),e.onSpace),Xl(Ul([ld,jl([13])]),e.onEnter),Xl(Ul([Wl,jl([13])]),e.onShiftEnter),Xl(Ul([Wl,jl([9])]),e.onShiftTab),Xl(Ul([ld,jl([9])]),e.onTab),Xl(jl([38]),e.onUp),Xl(jl([40]),e.onDown),Xl(jl([37]),e.onLeft),Xl(jl([39]),e.onRight),Xl(jl([32]),e.onSpace),Xl(jl([27]),e.onEscape)]},function(n,t,e){return e.stopSpaceKeyup?[Xl(jl([32]),pd)]:[]},function(n){return n.focusIn}),mg=Mm.schema(),gg=Fm.schema(),pg=$m.schema(),hg=qm.schema(),vg=ug.schema(),bg=Rm.schema(),yg=fg.schema(),xg=dg.schema(),wg=ka({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,acyclic:mg,cyclic:gg,flow:pg,flatgrid:hg,matrix:vg,execution:bg,menu:yg,special:xg}),name:"keying",active:{events:function(n,t){return n.handler.toEvents(n,t)}},apis:{focusIn:function(t,e,o){e.sendFocusIn(e).fold(function(){t.getSystem().triggerFocus(t.element(),t.element())},function(n){n(t,e,o)})},setGridSize:function(n,t,e,o,r){!function(n){return N(n,"setGridSize")}(e)?v.console.error("Layout does not support setGridSize"):e.setGridSize(o,r)}},state:Vm}),Sg=function(n,t){return n.components()},Cg=Ca({fields:[],name:"replacing",apis:/* */Object.freeze({__proto__:null,append:function(n,t,e,o){Kd(n,0,Bi,o)},prepend:function(n,t,e,o){Kd(n,0,ge,o)},remove:Jd,replaceAt:$d,replaceBy:function(t,n,e,o,r){var i=Sg(t);return T(i,o).bind(function(n){return $d(t,0,0,n,r)})},set:function(t,n,e,o){Ea(function(){var n=S(o,t.getSystem().build);Cs(t,n)},t.element())},contents:Sg})}),kg=/* */Object.freeze({__proto__:null,focus:Zd,blur:function(n,t){t.ignore||function(n){n.dom().blur()}(n.element())},isFocused:function(n){return function(n){var t=ie(n).dom();return n.dom()===t.activeElement}(n.element())}}),Og=/* */Object.freeze({__proto__:null,exhibit:function(n,t){var e=t.ignore?{}:{attributes:{tabindex:"-1"}};return He(e)},events:function(e){return Gt([qt(Zr(),function(n,t){Zd(n,e),t.stop()})].concat(e.stopMousedown?[qt(Nr(),function(n,t){t.event().prevent()})]:[]))}}),_g=[Ku("onFocus"),pt("stopMousedown",!1),pt("ignore",!1)],Tg=Ca({fields:_g,name:"focusing",active:Og,apis:kg}),Eg=function(n,t,e,o){(o?om:rm)(n,t,e)},Bg=/* */Object.freeze({__proto__:null,onLoad:im,toggle:em,isOn:function(n,t,e){return e.get()},on:om,off:rm,set:Eg}),Dg=/* */Object.freeze({__proto__:null,exhibit:function(){return He({})},events:function(n,t){var e=function(t,e,o){return Ei(function(n){o(n,t,e)})}(n,t,em),o=ba(n,t,im);return Gt(z([n.toggleOnExecute?[e]:[],[o]]))}}),Ag=function(n,t,e){Ce(n.element(),"aria-expanded",e)},Mg=[pt("selected",!1),st("toggleClass"),pt("toggleOnExecute",!0),ht("aria",{mode:"none"},Qn("mode",{pressed:[pt("syncWithExpanded",!1),Zu("update",function(n,t,e){Ce(n.element(),"aria-pressed",e),t.syncWithExpanded&&Ag(n,t,e)})],checked:[Zu("update",function(n,t,e){Ce(n.element(),"aria-checked",e)})],expanded:[Zu("update",Ag)],selected:[Zu("update",function(n,t,e){Ce(n.element(),"aria-selected",e)})],none:[Zu("update",Z)]}))],Fg=Ca({fields:Mg,name:"toggling",active:Dg,apis:Bg,state:(Bm=!1,{init:function(){var t=or(Bm);return{get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(Bm)},readState:function(){return t.get()}}}})}),Ig="alloy.item-hover",Rg="alloy.item-focus",Vg=nn(Ig),Hg=nn(Rg),Ng=[tt("data"),tt("components"),tt("dom"),pt("hasSubmenu",!1),st("toggling"),el("itemBehaviours",[Fg,Tg,wg,tl]),pt("ignoreFocus",!1),pt("domModification",{}),Zu("builder",function(n){return{dom:n.dom,domModification:P(P({},n.domModification),{attributes:P(P(P({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:ol(n.itemBehaviours,[n.toggling.fold(Fg.revoke,function(n){return Fg.config(P({aria:{mode:"checked"}},n))}),Tg.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){sm(n)}}),wg.config({mode:"execution"}),tl.config({store:{mode:"memory",initialValue:n.data}}),Qd("item-type-events",p(um(),[qt(jr(),cm),qt(ri(),Tg.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),pt("eventOrder",{})],Pg=[tt("dom"),tt("components"),Zu("builder",function(n){return{dom:n.dom,components:n.components,events:Gt([function(n){return qt(n,function(n,t){t.stop()})}(ri())])}})],zg=nn([Sl({name:"widget",overrides:function(t){return{behaviours:Sa([tl.config({store:{mode:"manual",getValue:function(n){return t.data},setValue:function(){}}})])}}})]),Lg=[tt("uid"),tt("data"),tt("components"),tt("dom"),pt("autofocus",!1),pt("ignoreFocus",!1),el("widgetBehaviours",[tl,Tg,wg]),pt("domModification",{}),lf(zg()),Zu("builder",function(e){function o(n){return of(n,e,"widget").map(function(n){return wg.focusIn(n),n})}function n(n,t){return Il(t.event().target())||e.autofocus&&t.setSource(n.element()),tn.none()}var t=tf(0,e,zg()),r=ef("item-widget",e,t.internals());return{dom:e.dom,components:r,domModification:e.domModification,events:Gt([Ei(function(n,t){o(n).each(function(n){t.stop()})}),qt(jr(),cm),qt(ri(),function(n,t){e.autofocus?o(n):Tg.focus(n)})]),behaviours:ol(e.widgetBehaviours,[tl.config({store:{mode:"memory",initialValue:e.data}}),Tg.config({ignore:e.ignoreFocus,onFocus:function(n){sm(n)}}),wg.config({mode:"special",focusIn:e.autofocus?function(n){o(n)}:pc(),onLeft:n,onRight:n,onEscape:function(n,t){return Tg.isFocused(n)||e.autofocus?(e.autofocus&&t.setSource(n.element()),tn.none()):(Tg.focus(n),tn.some(!0))}})])}})],jg=Qn("type",{widget:Lg,item:Ng,separator:Pg}),Ug=nn([Ol({factory:{sketch:function(n){var t=Jn("menu.spec item",jg,n);return t.builder(t)}},name:"items",unit:"item",defaults:function(n,t){return t.hasOwnProperty("uid")?t:P(P({},t),{uid:Ae("item")})},overrides:function(n,t){return{type:t.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Wg=nn([tt("value"),tt("items"),tt("dom"),tt("components"),pt("eventOrder",{}),Ls("menuBehaviours",[fd,tl,ed,wg]),ht("movement",{mode:"menu",moveOnTab:!0},Qn("mode",{grid:[Za(),Zu("config",function(n,t){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:n.focusManager}})],matrix:[Zu("config",function(n,t){return{mode:"matrix",selectors:{row:t.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),tt("rowSelector")],menu:[pt("moveOnTab",!0),Zu("config",function(n,t){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:t.moveOnTab,focusManager:n.focusManager}})]})),et("markers",$a()),pt("fakeFocus",!1),pt("focusManager",ql()),Ku("onHighlight")]),Gg=nn("alloy.menu-focus"),Xg=Ml({name:"Menu",configFields:Wg(),partFields:Ug(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:Us(n.menuBehaviours,[fd.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),tl.config({store:{mode:"memory",initialValue:n.value}}),ed.config({find:tn.some}),wg.config(n.movement.config(n,n.movement))]),events:Gt([qt(Hg(),function(t,e){var n=e.event();t.getSystem().getByDom(n.target()).each(function(n){fd.highlight(t,n),e.stop(),Lt(t,Gg(),{menu:t,item:n})})}),qt(Vg(),function(n,t){var e=t.event().item();fd.highlight(n,e)})]),components:t,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Yg=function(e,o,r,n){return bn(r,n).bind(function(n){return bn(e,n).bind(function(n){var t=Yg(e,o,r,n);return tn.some([n].concat(t))})}).getOr([])},qg=function(n){return"prepared"===n.type?tn.some(n.menu):tn.none()},Kg={init:function(){function r(n,e,o){return f(n).bind(function(t){return function(e){return V(i.get(),function(n,t){return n===e})}(n).bind(function(n){return e(n).map(function(n){return{triggeredMenu:t,triggeringItem:n,triggeringPath:o}})})})}var i=or({}),u=or({}),a=or({}),c=or(tn.none()),s=or({}),f=function(n){return t(n).bind(qg)},t=function(n){return bn(u.get(),n)},e=function(n){return bn(i.get(),n)};return{setMenuBuilt:function(n,t){var e;u.set(P(P({},u.get()),((e={})[n]={type:"prepared",menu:t},e)))},setContents:function(n,t,e,o){c.set(tn.some(n)),i.set(e),u.set(t),s.set(o);var r=fm(o,e);a.set(r)},expand:function(e){return bn(i.get(),e).map(function(n){var t=bn(a.get(),e).getOr([]);return[n].concat(t)})},refresh:function(n){return bn(a.get(),n)},collapse:function(n){return bn(a.get(),n).bind(function(n){return 1<n.length?tn.some(n.slice(1)):tn.none()})},lookupMenu:t,lookupItem:e,otherMenus:function(n){var t=s.get();return A(mn(t),n)},getPrimary:function(){return c.get().bind(f)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(tn.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(n,o){var t=C(e(n).toArray(),function(n){return f(n).isSome()});return bn(a.get(),n).bind(function(n){var e=D(t.concat(n));return function(n){for(var t=[],e=0;e<n.length;e++){var o=n[e];if(!o.isSome())return tn.none();t.push(o.getOrDie())}return tn.some(t)}(E(e,function(n,t){return r(n,o,e.slice(0,t+1)).fold(function(){return c.get().is(n)?[]:[tn.none()]},function(n){return[tn.some(n)]})}))})}}},extractPreparedMenu:qg},Jg=nn("collapse-item"),$g=Al({name:"TieredMenu",configFields:[Qu("onExecute"),Qu("onEscape"),$u("onOpenMenu"),$u("onOpenSubmenu"),Ku("onRepositionMenu"),Ku("onCollapseMenu"),pt("highlightImmediately",!0),ut("data",[tt("primary"),tt("menus"),tt("expansions")]),pt("fakeFocus",!1),Ku("onHighlight"),Ku("onHover"),Xu(),tt("dom"),pt("navigateOnHover",!0),pt("stayInDom",!1),Ls("tmenuBehaviours",[wg,fd,ed,Cg]),pt("eventOrder",{})],apis:{collapseMenu:function(n,t){n.collapseMenu(t)},highlightPrimary:function(n,t){n.highlightPrimary(t)},repositionMenus:function(n,t){n.repositionMenus(t)}},factory:function(a,n){function e(n){var t=function(o,r,n){return L(n,function(n,t){function e(){return Xg.sketch(P(P({dom:n.dom},n),{value:t,items:n.items,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:a.fakeFocus?Kl():ql()}))}return t===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})}(n,a.data.primary,a.data.menus),e=o();return g.setContents(a.data.primary,t,a.data.expansions,e),g.getPrimary()}function c(n){return tl.getValue(n).value}function u(t,n){fd.highlight(t,n),fd.getHighlighted(n).orThunk(function(){return fd.getFirst(n)}).each(function(n){Ut(t,n.element(),ri())})}function s(t,n){return Fl(S(n,function(n){return t.lookupMenu(n).bind(function(n){return"prepared"===n.type?tn.some(n.menu):tn.none()})}))}function f(t,n,e){var o=s(n,n.otherMenus(e));fn(o,function(n){Ze(n.element(),[a.markers.backgroundMenu]),a.stayInDom||Cg.remove(t,n)})}function l(n,o){var t=function(o){return r.get().getOrThunk(function(){var e={},n=Zc(o.element(),"."+a.markers.item),t=C(n,function(n){return"true"===ke(n,"aria-haspopup")});return fn(t,function(n){o.getSystem().getByDom(n).each(function(n){var t=c(n);e[t]=n})}),r.set(tn.some(e)),e})}(n);pn(t,function(n,t){var e=sn(o,t);Ce(n.element(),"aria-expanded",e)})}function d(o,r,i){return tn.from(i[0]).bind(function(n){return r.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return tn.none();var t=n.menu,e=s(r,i.slice(1));return fn(e,function(n){qe(n.element(),a.markers.backgroundMenu)}),to(t.element())||Cg.append(o,eu(t)),Ze(t.element(),[a.markers.backgroundMenu]),u(o,t),f(o,r,i),tn.some(t)})})}var m,t,r=or(tn.none()),g=Kg.init(),o=function(n){return L(a.data.menus,function(n,t){return E(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(t=m=m||{})[t.HighlightSubmenu=0]="HighlightSubmenu",t[t.HighlightParent=1]="HighlightParent";function i(r,i,u){void 0===u&&(u=m.HighlightSubmenu);var n=c(i);return g.expand(n).bind(function(o){return l(r,o),tn.from(o[0]).bind(function(e){return g.lookupMenu(e).bind(function(n){var t=function(n,t,e){if("notbuilt"!==e.type)return e.menu;var o=n.getSystem().build(e.nbMenu());return g.setMenuBuilt(t,o),o}(r,e,n);return to(t.element())||Cg.append(r,eu(t)),a.onOpenSubmenu(r,i,t,D(o)),u===m.HighlightSubmenu?(fd.highlightFirst(t),d(r,g,o)):(fd.dehighlightAll(t),tn.some(i))})})})}function p(t,e){var n=c(e);return g.collapse(n).bind(function(n){return l(t,n),d(t,g,n).map(function(n){return a.onCollapseMenu(t,e,n),n})})}function h(e){return function(t,n){return Hu(n.getSource(),"."+a.markers.item).bind(function(n){return t.getSystem().getByDom(n).toOption().bind(function(n){return e(t,n).map(function(){return!0})})})}}function v(n){return fd.getHighlighted(n).bind(fd.getHighlighted)}var b=Gt([qt(Gg(),function(e,o){var n=o.event().item();g.lookupItem(c(n)).each(function(){var n=o.event().menu();fd.highlight(e,n);var t=c(o.event().item());g.refresh(t).each(function(n){return f(e,g,n)})})}),Ei(function(t,n){var e=n.event().target();t.getSystem().getByDom(e).each(function(n){0===c(n).indexOf("collapse-item")&&p(t,n),i(t,n,m.HighlightSubmenu).fold(function(){a.onExecute(t,n)},function(){})})}),Oi(function(t,n){e(t).each(function(n){Cg.append(t,eu(n)),a.onOpenMenu(t,n),a.highlightImmediately&&u(t,n)})})].concat(a.navigateOnHover?[qt(Vg(),function(n,t){var e=t.event().item();!function(t,n){var e=c(n);g.refresh(e).bind(function(n){return l(t,n),d(t,g,n)})}(n,e),i(n,e,m.HighlightParent),a.onHover(n,e)})]:[])),y={collapseMenu:function(t){v(t).each(function(n){p(t,n)})},highlightPrimary:function(t){g.getPrimary().each(function(n){u(t,n)})},repositionMenus:function(o){g.getPrimary().bind(function(t){return v(o).bind(function(n){var t=c(n),e=H(g.getMenus()),o=Fl(S(e,Kg.extractPreparedMenu));return g.getTriggeringPath(t,function(n){return function(n,t,e){return R(t,function(n){if(!n.getSystem().isConnected())return tn.none();var t=fd.getCandidates(n);return _(t,function(n){return c(n)===e})})}(0,o,n)})}).map(function(n){return{primary:t,triggeringPath:n}})}).fold(function(){(function(n){return tn.from(n.components()[0]).filter(function(n){return"menu"===ke(n.element(),"role")})})(o).each(function(n){a.onRepositionMenu(o,n,[])})},function(n){var t=n.primary,e=n.triggeringPath;a.onRepositionMenu(o,t,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Us(a.tmenuBehaviours,[wg.config({mode:"special",onRight:h(function(n,t){return Il(t.element())?tn.none():i(n,t,m.HighlightSubmenu)}),onLeft:h(function(n,t){return Il(t.element())?tn.none():p(n,t)}),onEscape:h(function(n,t){return p(n,t).orThunk(function(){return a.onEscape(n,t).map(function(){return n})})}),focusIn:function(t,n){g.getPrimary().each(function(n){Ut(t,n.element(),ri())})}}),fd.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),ed.config({find:function(n){return fd.getHighlighted(n)}}),Cg.config({})]),eventOrder:a.eventOrder,apis:y,events:b}},extraApis:{tieredData:function(n,t,e){return{primary:n,menus:t,expansions:e}},singleData:function(n,t){return{primary:n,menus:Dn(n,t),expansions:{}}},collapseItem:function(n){return{value:De(Jg()),meta:{text:n}}}}}),Qg=Al({name:"InlineView",configFields:[tt("lazySink"),Ku("onShow"),Ku("onHide"),mt("onEscape"),Ls("inlineBehaviours",[jf,tl,bc]),gt("fireDismissalEventInstead",[pt("event",hi())]),gt("fireRepositionEventInstead",[pt("event",vi())]),pt("getRelated",tn.none),pt("eventOrder",tn.none)],factory:function(i,n){function t(e){jf.isOpen(e)&&tl.getValue(e).each(function(n){switch(n.mode){case"menu":jf.getState(e).each(function(n){$g.repositionMenus(n)});break;case"position":var t=i.lazySink(e).getOrDie();Mf.positionWithinBounds(t,n.anchor,e,n.getBounds())}})}var o=function(n,t,e,o){r(n,t,e,function(){return o.map(function(n){return bu(n)})})},r=function(n,t,e,o){var r=i.lazySink(n).getOrDie();jf.openWhileCloaked(n,e,function(){return Mf.positionWithinBounds(r,t,n,o())}),tl.setValue(n,tn.some({mode:"position",anchor:t,getBounds:o}))},u=function(n,t,e,o){var r=function(n,t,r,e,i){function u(){return n.lazySink(t)}function a(n){return function(n){return 2===n.length}(n)?o:{}}var o="horizontal"===e.type?{layouts:{onLtr:function(){return ha()},onRtl:function(){return va()}}}:{};return $g.sketch({dom:{tag:"div"},data:e.data,markers:e.menu.markers,highlightImmediately:e.menu.highlightImmediately,onEscape:function(){return jf.close(t),n.onEscape.map(function(n){return n(t)}),tn.some(!0)},onExecute:function(){return tn.some(!0)},onOpenMenu:function(n,t){Mf.positionWithinBounds(u().getOrDie(),r,t,i())},onOpenSubmenu:function(n,t,e,o){var r=u().getOrDie();Mf.position(r,P({anchor:"submenu",item:t},a(o)),e)},onRepositionMenu:function(n,t,e){var o=u().getOrDie();Mf.positionWithinBounds(o,r,t,i()),fn(e,function(n){var t=a(n.triggeringPath);Mf.position(o,P({anchor:"submenu",item:n.triggeringItem},t),n.triggeredMenu)})}})}(i,n,t,e,o);jf.open(n,r),tl.setValue(n,tn.some({mode:"menu",menu:r}))},e={setContent:function(n,t){jf.setContent(n,t)},showAt:function(n,t,e){o(n,t,e,tn.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(n,t,e){u(n,t,e,function(){return tn.none()})},showMenuWithinBounds:u,hide:function(n){jf.isOpen(n)&&(tl.setValue(n,tn.none()),jf.close(n))},getContent:function(n){return jf.getState(n)},reposition:t,isOpen:jf.isOpen};return{uid:i.uid,dom:i.dom,behaviours:Us(i.inlineBehaviours,[jf.config({isPartOf:function(n,t,e){return ju(t,e)||function(n,t){return i.getRelated(n).exists(function(n){return ju(n,t)})}(n,e)},getAttachPoint:function(n){return i.lazySink(n).getOrDie()},onOpen:function(n){i.onShow(n)},onClose:function(n){i.onHide(n)}}),tl.config({store:{mode:"memory",initialValue:tn.none()}}),bc.config({channels:P(P({},Is(P({isExtraPart:nn(!1)},i.fireDismissalEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})))),Rs(P(P({isExtraPart:nn(!1)},i.fireRepositionEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})),{doReposition:t})))})]),eventOrder:i.eventOrder,apis:e}},apis:{showAt:function(n,t,e,o){n.showAt(t,e,o)},showWithin:function(n,t,e,o,r){n.showWithin(t,e,o,r)},showWithinBounds:function(n,t,e,o,r){n.showWithinBounds(t,e,o,r)},showMenuAt:function(n,t,e,o){n.showMenuAt(t,e,o)},showMenuWithinBounds:function(n,t,e,o,r){n.showMenuWithinBounds(t,e,o,r)},hide:function(n,t){n.hide(t)},isOpen:function(n,t){return n.isOpen(t)},getContent:function(n,t){return n.getContent(t)},setContent:function(n,t,e){n.setContent(t,e)},reposition:function(n,t){n.reposition(t)}}}),Zg=function(n,t,e){return nc(dm(n,t),gm(n),e.innerNorth(),ac(),"layout-n")},np=function(n,t,e){return nc(dm(n,t),pm(n,t),e.innerSouth(),uc(),"layout-s")},tp=tinymce.util.Tools.resolve("tinymce.util.Delay"),ep=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),op=tinymce.util.Tools.resolve("tinymce.EditorManager"),rp=function(n){return Em(n).fold(function(){return 0<n.getParam("toolbar",[],"string[]").length},function(){return!0})};(Am=Dm=Dm||{})["default"]="wrap",Am.floating="floating",Am.sliding="sliding",Am.scrolling="scrolling";function ip(n){return n.getParam("toolbar_mode","","string")}var up,ap;(ap=up=up||{}).top="top",ap.bottom="bottom";function cp(n){var t=function(n){return n.getParam("fixed_toolbar_container","","string")}(n);return 0<t.length&&n.inline?Vu(Ji(),t):tn.none()}function sp(n){return n.inline&&cp(n).isSome()}function fp(n){return n.inline&&!_m(n)&&!Tm(n)&&!rp(n)}function lp(n){return(n.getParam("toolbar_sticky",!1,"boolean")||n.inline)&&!sp(n)&&!fp(n)}function dp(n){var t=function e(n){return n.uid!==undefined}(n)&&N(n,"uid")?n.uid:Ae("memento");return{get:function(n){return n.getSystem().getByUid(t).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(t).toOption()},asSpec:function(){return P(P({},n),{uid:t})}}}function mp(n){return tn.from(n()["temporary-placeholder"]).getOr("!not found!")}function gp(n,t){return tn.from(t()[n]).getOrThunk(function(){return mp(t)})}var pp=function(n){return n.getParam("toolbar_location",up.top,"string")!==up.bottom},hp=Al({name:"Button",factory:function(n){function e(t){return bn(n.dom,"attributes").bind(function(n){return bn(n,t)})}var t=am(n.action),o=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:t,behaviours:ol(n.buttonBehaviours,[Tg.config({}),wg.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==o)return{role:e("role").getOr("button")};var n=e("type").getOr("button"),t=e("role").map(function(n){return{role:n}}).getOr({});return P({type:n},t)}()},eventOrder:n.eventOrder}},configFields:[pt("uid",undefined),tt("dom"),pt("components",[]),el("buttonBehaviours",[Tg,wg]),st("action"),st("role"),pt("eventOrder",{})]}),vp={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},bp=Al({name:"Notification",factory:function(t){function e(n){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+n+"%"}}}}function o(n){return{dom:{tag:"div",classes:["tox-text"],innerHtml:n+"%"}}}var r=dp({dom:{tag:"p",innerHtml:t.translationProvider(t.text)},behaviours:Sa([Cg.config({})])}),i=dp({dom:{tag:"div",classes:t.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(0)]},o(0)],behaviours:Sa([Cg.config({})])}),n={updateProgress:function(n,t){n.getSystem().isConnected()&&i.getOpt(n).each(function(n){Cg.set(n,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(t)]},o(t)])})},updateText:function(n,t){if(n.getSystem().isConnected()){var e=r.get(n);Cg.set(e,[wo(t)])}}},u=z([t.icon.toArray(),t.level.toArray(),t.level.bind(function(n){return tn.from(vp[n])}).toArray()]);return{uid:t.uid,dom:{tag:"div",attributes:{role:"alert"},classes:t.level.map(function(n){return["tox-notification","tox-notification--in","tox-notification--"+n]}).getOr(["tox-notification","tox-notification--in"])},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:function(n,t){return R(n,function(n){return tn.from(t()[n])}).getOrThunk(function(){return mp(t)})}(u,t.iconProvider)}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[r.asSpec()],behaviours:Sa([Cg.config({})])}].concat(t.progress?[i.asSpec()]:[]).concat(t.closeButton?[hp.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:gp("close",t.iconProvider),attributes:{"aria-label":t.translationProvider("Close")}}}],action:function(n){t.onAction(n)}})]:[]),apis:n}},configFields:[st("level"),tt("progress"),tt("icon"),tt("onAction"),tt("text"),tt("iconProvider"),tt("translationProvider"),xt("closeButton",!0)],apis:{updateProgress:function(n,t,e){n.updateProgress(t,e)},updateText:function(n,t,e){n.updateText(t,e)}}});function yp(n,u,a){var c=u.backstage,s=pp(n);return{open:function(n,t){function e(){t(),Qg.hide(i)}var o=!n.closeButton&&n.timeout&&(0<n.timeout||n.timeout<0),r=tu(bp.sketch({text:n.text,level:sn(["success","error","warning","warn","info"],n.type)?n.type:undefined,progress:!0===n.progressBar,icon:tn.from(n.icon),closeButton:!o,onAction:e,iconProvider:c.shared.providers.icons,translationProvider:c.shared.providers.translate})),i=tu(Qg.sketch(P({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}},s?{}:{fireRepositionEventInstead:{}})));return a.add(i),0<n.timeout&&tp.setTimeout(function(){e()},n.timeout),{close:e,moveTo:function(n,t){Qg.showAt(i,{anchor:"makeshift",x:n,y:t},eu(r))},moveRel:function(n,t){if("banner"!==t){var e=function(n){switch(n){case"bc-bc":return np;case"tc-tc":return Zg;case"tc-bc":return fc;case"bc-tc":default:return lc}}(t),o={anchor:"node",root:Ji(),node:tn.some(ir.fromDom(n)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}};Qg.showAt(i,o,eu(r))}else Qg.showAt(i,u.backstage.shared.anchors.banner(),eu(r))},text:function(n){bp.updateText(r,n)},settings:n,getEl:function(){return r.element().dom()},progressBar:{value:function(n){bp.updateProgress(r,n)}}}},close:function(n){n.close()},reposition:function(n){!function(n){fn(n,function(n){return n.moveTo(0,0)})}(n),function(e){0<e.length&&(ln(e).each(function(n){return n.moveRel(null,"banner")}),fn(e,function(n,t){0<t&&n.moveRel(e[t-1].getEl(),"bc-tc")}))}(n)},getArgs:function(n){return n.settings}}}function xp(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null!==r&&v.clearTimeout(r),r=v.setTimeout(function(){e.apply(null,n),r=null},o)}}}function wp(t,n,e,o,r){var i=eh(t,function(n){return function(n,t){return n.isBlock(t)||sn(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===n.getContentEditable(t)}(t,n)});return tn.from(i.backwards(n,e,o,r))}function Sp(t,e){return oh(ir.fromDom(t.selection.getNode())).getOrThunk(function(){var n=ir.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',t.getDoc());return Bi(n,ir.fromDom(e.extractContents())),e.insertNode(n.dom()),ce(n).each(function(n){return n.dom().normalize()}),function(n){return hf(n,Qc)}(n).map(function(n){t.selection.setCursorLocation(n.dom(),function(n){return"img"===xe(n)?1:$c(n).fold(function(){return fe(n).length},function(n){return n.length})}(n))}),n})}function Cp(n){return n.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")}function kp(n){return""!==n&&-1!==" \xa0\f\n\r\t\x0B".indexOf(n)}function Op(n,t){return n.substring(t.length)}function _p(n,o,r,i){if(void 0===i&&(i=0),!function(n){return n.collapsed&&3===n.startContainer.nodeType}(o))return tn.none();var t=n.getParent(o.startContainer,n.isBlock)||n.getRoot();return wp(n,o.startContainer,o.startOffset,function(n,t,e){return function(n,t,e){var o;for(o=t-1;0<=o;o--){var r=n.charAt(o);if(kp(r))return tn.none();if(r===e)break}return tn.some(o)}(e,t,r).getOr(t)},t).bind(function(n){var t=o.cloneRange();if(t.setStart(n.container,n.offset),t.setEnd(o.endContainer,o.endOffset),t.collapsed)return tn.none();var e=Cp(t);return 0!==e.lastIndexOf(r)||Op(e,r).length<i?tn.none():tn.some({text:Op(e,r),range:t,triggerChar:r})})}function Tp(o,n,r,t){return void 0===t&&(t=0),oh(ir.fromDom(n.startContainer)).fold(function(){return _p(o,n,r,t)},function(n){var t=o.createRng();t.selectNode(n.dom());var e=Cp(t);return tn.some({range:t,text:Op(e,r),triggerChar:r})})}function Ep(n,t){return{container:n,offset:t}}function Bp(e){return function(n){var t=ah(n.startContainer,n.startOffset);return!function(n,t){return wp(n,t.container,t.offset,function(n,t){return 0===t?-1:t},n.getRoot()).filter(function(n){var t=n.container.data.charAt(n.offset-1);return!kp(t)}).isSome()}(e,t)}}function Dp(t,e){var n=e(),o=t.selection.getRng();return function(t,e,n){return R(n.triggerChars,function(n){return Tp(t,e,n)})}(t.dom,o,n).bind(function(n){return ch(t,e,n)})}function Ap(n){var t=n.ui.registry.getAll().popups,e=L(t,function(n){return function(n){return qn("Autocompleter",lh,n)}(n).fold(function(n){throw new Error(Ko(n))},function(n){return n})}),o=function(n){var t={};return fn(n,function(n){t[n]={}}),mn(t)}(vn(e,function(n){return n.ch})),r=H(e);return{dataset:e,triggerChars:o,lookupByChar:function(t){return C(r,function(n){return n.ch===t})}}}function Mp(n,o,t){var r=Zc(n.element(),"."+t);if(0<r.length){var e=T(r,function(n){var t=n.dom().getBoundingClientRect().top,e=r[0].dom().getBoundingClientRect().top;return Math.abs(t-e)>o}).getOr(r.length);return tn.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return tn.none()}function Fp(n,t){return Sa([Qd(n,t)])}function Ip(n,t,e){n.getSystem().broadcastOn([wh],{})}function Rp(n){return bn(Dh,n).getOr(Th)}function Vp(n){return{dom:{tag:"div",classes:[Mh],innerHtml:n}}}function Hp(n){return{dom:{tag:"div",classes:[Fh]},components:[wo(_h.translate(n))]}}function Np(n,t){return{dom:{tag:"div",classes:[Fh]},components:[{dom:{tag:n.tag,styles:n.styles},components:[wo(_h.translate(t))]}]}}function Pp(n){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:Hh(n)}}}function zp(n){return{dom:{tag:"div",classes:[Mh,"tox-collection__item-checkmark"],innerHtml:gp("checkmark",n)}}}function Lp(n,t,e,o,r){var i=e?n.checkMark.orThunk(function(){return t.or(tn.some("")).map(Vp)}):tn.none(),u=n.ariaLabel.map(function(n){return{attributes:{title:_h.translate(n)}}}).getOr({});return{dom:P({tag:"div",classes:[Th,Eh].concat(r?["tox-collection__item-icon-rtl"]:[])},u),optComponents:[i,n.htmlContent.fold(function(){return n.textContent.map(o)},function(n){return tn.some(function(n){return{dom:{tag:"div",classes:[Fh],innerHtml:n}}}(n))}),n.shortcutContent.map(Pp),n.caret]}}function jp(n,t,e,o){void 0===o&&(o=tn.none());var r=_h.isRtl()&&n.iconContent.exists(function(n){return sn(Ph,n)}),i=n.iconContent.map(function(n){return _h.isRtl()&&sn(Nh,n)?n+"-rtl":n}).map(function(n){return function(n,t,e){return tn.from(t()[n]).or(e).getOrThunk(function(){return mp(t)})}(n,t.icons,o)}),u=tn.from(n.meta).fold(function(){return Hp},function(n){return yn(n,"style")?d(Np,n.style):Hp});return"color"===n.presets?function(n,t,e,o){var r,i;return{dom:(r=e.getOr(""),i={tag:"div",attributes:n.map(function(n){return{title:o.translate(n)}}).getOr({}),classes:["tox-swatch"]},P(P({},i),"custom"===t?{tag:"button",classes:p(i.classes,["tox-swatches__picker-btn"]),innerHtml:r}:"remove"===t?{classes:p(i.classes,["tox-swatch--remove"]),innerHtml:r}:{attributes:P(P({},i.attributes),{"data-mce-color":t}),styles:{"background-color":t}})),optComponents:[]}}(n.ariaLabel,n.value,i,t):Lp(n,i,e,u,r)}function Up(n,t,e){t.disabled&&Lh(n,t)}function Wp(n,t){return!0===t.useNative&&sn(zh,xe(n.element()))}function Gp(n){Ce(n.element(),"disabled","disabled")}function Xp(n){_e(n.element(),"disabled")}function Yp(n){Ce(n.element(),"aria-disabled","true")}function qp(n){Ce(n.element(),"aria-disabled","false")}function Kp(t,n,e){n.disableClass.each(function(n){Je(t.element(),n)}),(Wp(t,n)?Xp:qp)(t),n.onEnabled(t)}function Jp(n,t){return Wp(n,t)?function(n){return Oe(n.element(),"disabled")}(n):function(n){return"true"===ke(n.element(),"aria-disabled")}(n)}function $p(n,t){var e=n.getApi(t);return function(n){n(e)}}function Qp(e,o){return Oi(function(n){$p(e,n)(function(n){var t=e.onSetup(n);null!==t&&t!==undefined&&o.set(t)})})}function Zp(t,e){return _i(function(n){return $p(t,n)(e.get())})}var nh,th,eh=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),oh=function(n){return Hu(n,"[data-mce-autocompleter]")},rh=function(e,n){n.on("keypress compositionend",e.onKeypress.throttle),n.on("remove",e.onKeypress.cancel);function o(n,t){Lt(n,Gr(),{raw:t})}n.on("keydown",function(t){function n(){return e.getView().bind(fd.getHighlighted)}8===t.which&&e.onKeypress.throttle(t),e.isActive()&&(27===t.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===t.which?(n().each(jt),t.preventDefault()):40===t.which?(n().fold(function(){e.getView().each(fd.highlightFirst)},function(n){o(n,t)}),t.preventDefault(),t.stopImmediatePropagation()):37!==t.which&&38!==t.which&&39!==t.which||n().each(function(n){o(n,t),t.preventDefault(),t.stopImmediatePropagation()}):13!==t.which&&38!==t.which&&40!==t.which||e.cancelIfNecessary())}),n.on("NodeChange",function(n){e.isActive()&&!e.isProcessingAction()&&oh(ir.fromDom(n.element)).isNone()&&e.cancelIfNecessary()})},ih=tinymce.util.Tools.resolve("tinymce.util.Promise"),uh=function(n){if(function(n){return n.nodeType===v.Node.TEXT_NODE}(n))return Ep(n,n.data.length);var t=n.childNodes;return 0<t.length?uh(t[t.length-1]):Ep(n,t.length)},ah=function(n,t){var e=n.childNodes;return 0<e.length&&t<e.length?ah(e[t],0):0<e.length&&function(n){return n.nodeType===v.Node.ELEMENT_NODE}(n)&&e.length===t?uh(e[e.length-1]):Ep(n,t)},ch=function(t,n,e,o){void 0===o&&(o={});var r=n(),i=t.selection.getRng().startContainer.nodeValue,u=C(r.lookupByChar(e.triggerChar),function(n){return e.text.length>=n.minChars&&n.matches.getOrThunk(function(){return Bp(t.dom)})(e.range,i,e.text)});if(0===u.length)return tn.none();var a=ih.all(S(u,function(t){return t.fetch(e.text,t.maxResults,o).then(function(n){return{matchText:e.text,items:n,columns:t.columns,onAction:t.onAction}})}));return tn.some({lookupData:a,context:e})},sh=jo([ot("type"),dt("text")]),fh=jo([Ct("type",function(){return"autocompleteitem"}),Ct("active",function(){return!1}),Ct("disabled",function(){return!1}),pt("meta",{}),ot("value"),dt("text"),dt("icon")]),lh=jo([ot("type"),ot("ch"),vt("minChars",1),pt("columns",1),vt("maxResults",10),mt("matches"),it("fetch"),it("onAction")]),dh=[xt("disabled",!1),dt("text"),dt("shortcut"),Xo("value","value",Ho(function(){return De("menuitem-value")}),Jo()),pt("meta",{})],mh=jo([ot("type"),wt("onSetup",function(){return Z}),wt("onAction",Z),dt("icon")].concat(dh)),gh=jo([ot("type"),it("getSubmenuItems"),wt("onSetup",function(){return Z}),dt("icon")].concat(dh)),ph=jo([ot("type"),xt("active",!1),wt("onSetup",function(){return Z}),it("onAction")].concat(dh)),hh=jo([ot("type"),xt("active",!1),dt("icon")].concat(dh)),vh=jo([ot("type"),rt("fancytype",["inserttable","colorswatch"]),wt("onAction",Z)]),bh=function(n){return Fp(De("unnamed-events"),n)},yh=[tt("lazySink"),tt("tooltipDom"),pt("exclusive",!0),pt("tooltipComponents",[]),pt("delay",300),yt("mode","normal",["normal","follow-highlight"]),pt("anchor",function(n){return{anchor:"hotspot",hotspot:n,layouts:{onLtr:nn([lc,fc,aa,sa,ca,fa]),onRtl:nn([lc,fc,aa,sa,ca,fa])}}}),Ku("onHide"),Ku("onShow")],xh=/* */Object.freeze({__proto__:null,init:function(){function e(){o.get().each(function(n){v.clearTimeout(n)})}var o=or(tn.none()),t=or(tn.none()),n=nn("not-implemented");return Yi({getTooltip:function(){return t.get()},isShowing:function(){return t.get().isSome()},setTooltip:function(n){t.set(tn.some(n))},clearTooltip:function(){t.set(tn.none())},clearTimer:e,resetTimer:function(n,t){e(),o.set(tn.some(v.setTimeout(function(){n()},t)))},readState:n})}}),wh=De("tooltip.exclusive"),Sh=De("tooltip.show"),Ch=De("tooltip.hide"),kh=/* */Object.freeze({__proto__:null,hideAllExclusive:Ip,setComponents:function(n,t,e,o){e.getTooltip().each(function(n){n.getSystem().isConnected()&&Cg.set(n,o)})}}),Oh=Ca({fields:yh,name:"tooltipping",active:/* */Object.freeze({__proto__:null,events:function(o,r){function e(t){r.getTooltip().each(function(n){_s(n),o.onHide(t,n),r.clearTooltip()}),r.clearTimer()}return Gt(z([[qt(Sh,function(n){r.resetTimer(function(){!function(t){if(!r.isShowing()){Ip(t);var n=o.lazySink(t).getOrDie(),e=t.getSystem().build({dom:o.tooltipDom,components:o.tooltipComponents,events:Gt("normal"===o.mode?[qt(jr(),function(n){zt(t,Sh)}),qt(zr(),function(n){zt(t,Ch)})]:[]),behaviours:Sa([Cg.config({})])});r.setTooltip(e),ks(n,e),o.onShow(t,e),Mf.position(n,o.anchor(t),e)}}(n)},o.delay)}),qt(Ch,function(n){r.resetTimer(function(){e(n)},o.delay)}),qt(ei(),function(n,t){sn(t.channels(),wh)&&e(n)}),_i(function(n){e(n)})],"normal"===o.mode?[qt(Ur(),function(n){zt(n,Sh)}),qt(ni(),function(n){zt(n,Ch)}),qt(jr(),function(n){zt(n,Sh)}),qt(zr(),function(n){zt(n,Ch)})]:[qt(Si(),function(n,t){zt(n,Sh)}),qt(Ci(),function(n){zt(n,Ch)})]]))}}),state:xh,apis:kh}),_h=tinymce.util.Tools.resolve("tinymce.util.I18n"),Th="tox-menu-nav__js",Eh="tox-collection__item",Bh="tox-swatch",Dh={normal:Th,color:Bh},Ah="tox-collection__item--enabled",Mh="tox-collection__item-icon",Fh="tox-collection__item-label",Ih="tox-collection__item-caret",Rh="tox-collection__item--active",Vh=tinymce.util.Tools.resolve("tinymce.Env"),Hh=function(n){var e=Vh.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},t=n.split("+"),o=S(t,function(n){var t=n.toLowerCase().trim();return yn(e,t)?e[t]:n});return Vh.mac?o.join(""):o.join("+")},Nh=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],Ph=["list-bull-circle","list-bull-default","list-bull-square"],zh=["input","button","textarea","select"],Lh=function(t,n,e){n.disableClass.each(function(n){qe(t.element(),n)}),(Wp(t,n)?Gp:Yp)(t),n.onDisabled(t)},jh=/* */Object.freeze({__proto__:null,enable:Kp,disable:Lh,isDisabled:Jp,onLoad:Up,set:function(n,t,e,o){(o?Lh:Kp)(n,t,e)}}),Uh=/* */Object.freeze({__proto__:null,exhibit:function(n,t){return He({classes:t.disabled?t.disableClass.map(M).getOr([]):[]})},events:function(e,n){return Gt([Xt(oi(),function(n,t){return Jp(n,e)}),ba(e,n,Up)])}}),Wh=[pt("disabled",!1),pt("useNative",!0),st("disableClass"),Ku("onDisabled"),Ku("onEnabled")],Gh=Ca({fields:Wh,name:"disabling",active:Uh,apis:jh}),Xh=function(n){return Gh.config({disabled:n,disableClass:"tox-collection__item--state-disabled"})},Yh=function(n){return Gh.config({disabled:n})},qh=function(n){return Gh.config({disabled:n,disableClass:"tox-tbtn--disabled"})},Kh=function(n){return Gh.config({disabled:n,disableClass:"tox-tbtn--disabled",useNative:!1})};(th=nh=nh||{})[th.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",th[th.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";function Jh(n){return E(n,function(n){return n.toArray()})}function $h(n,t,e){var o=or(Z);return{type:"item",dom:t.dom,components:Jh(t.optComponents),data:n.data,eventOrder:ev,hasSubmenu:n.triggersSubmenu,itemBehaviours:Sa([Qd("item-events",[function(e,o){return Ei(function(n,t){$p(e,n)(e.onAction),e.triggersSubmenu||o!==tv.CLOSE_ON_EXECUTE||(zt(n,ai()),t.stop())})}(n,e),Qp(n,o),Zp(n,o)]),Xh(n.disabled),Cg.config({})].concat(n.itemBehaviours))}}function Qh(n){return{value:n.value,meta:P({text:n.text.getOr("")},n.meta)}}function Zh(n,t){var e=function(n){return ep.DOM.encode(n)}(_h.translate(n));if(0<t.length){var o=new RegExp(function(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}(t),"gi");return e.replace(o,function(n){return'<span class="tox-autocompleter-highlight">'+n+"</span>"})}return e}function nv(t,e,n){function o(n){return Lt(n,iv,{row:t,col:e})}function r(n,t){t.stop(),o(n)}var i;return tu({dom:{tag:"div",attributes:(i={role:"button"},i["aria-labelledby"]=n,i)},behaviours:Sa([Qd("insert-table-picker-cell",[qt(jr(),Tg.focus),qt(oi(),o),qt(Kr(),r),qt(ii(),r)]),Fg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Tg.config({onFocus:function(n){return Lt(n,rv,{row:t,col:e})}})])})}var tv=nh,ev={"alloy.execute":["disabling","alloy.base.behaviour","toggling","item-events"]},ov=nn($s("item-widget",zg())),rv=De("cell-over"),iv=De("cell-execute");function uv(n){return{value:nn(n)}}function av(n){return vv.test(n)||bv.test(n)}function cv(n){var t=function(n){var t=n.value().replace(vv,function(n,t,e,o){return t+t+e+e+o+o});return{value:nn(t)}}(n),e=bv.exec(t.value());return null===e?["FFFFFF","FF","FF","FF"]:e}function sv(n){var t=n.toString(16);return 1===t.length?"0"+t:t}function fv(n){var t=sv(n.red())+sv(n.green())+sv(n.blue());return uv(t)}function lv(n,t,e,o){return{red:nn(n),green:nn(t),blue:nn(e),alpha:nn(o)}}function dv(n){var t=parseInt(n,10);return t.toString()===n&&0<=t&&t<=255}function mv(n){var t,e,o,r=(n.hue()||0)%360,i=n.saturation()/100,u=n.value()/100;if(i=xv(0,yv(i,1)),u=xv(0,yv(u,1)),0===i)return t=e=o=wv(255*u),lv(t,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),f=u-c;switch(Math.floor(a)){case 0:t=c,e=s,o=0;break;case 1:t=s,e=c,o=0;break;case 2:t=0,e=c,o=s;break;case 3:t=0,e=s,o=c;break;case 4:t=s,e=0,o=c;break;case 5:t=c,e=0,o=s;break;default:t=e=o=0}return t=wv(255*(t+f)),e=wv(255*(e+f)),o=wv(255*(o+f)),lv(t,e,o,1)}function gv(n){var t=cv(n),e=parseInt(t[1],16),o=parseInt(t[2],16),r=parseInt(t[3],16);return lv(e,o,r,1)}function pv(n,t,e,o){var r=parseInt(n,10),i=parseInt(t,10),u=parseInt(e,10),a=parseFloat(o);return lv(r,i,u,a)}function hv(n){return"rgba("+n.red()+","+n.green()+","+n.blue()+","+n.alpha()+")"}var vv=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,bv=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,yv=Math.min,xv=Math.max,wv=Math.round,Sv=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,Cv=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,kv=nn(lv(255,0,0,1)),Ov=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),_v="tinymce-custom-colors";function Tv(n){var t=[],u=v.document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(n,t){var e=t/255;return("0"+Math.round(n*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=function(n){if(/^[0-9A-Fa-f]{6}$/.test(n))return"#"+n.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=n,a.fillRect(0,0,1,1);var t=a.getImageData(0,0,1,1).data,e=t[0],o=t[1],r=t[2],i=t[3];return"#"+c(e,i)+c(o,i)+c(r,i)},o=0;o<n.length;o+=2)t.push({text:n[o+1],value:e(n[o]),type:"choiceitem"});return t}function Ev(n){return n.getParam("color_map")}function Bv(n,e){var o;return n.dom.getParents(n.selection.getStart(),function(n){var t;(t=n.style["forecolor"===e?"color":"background-color"])&&(o=o||t)}),o}function Dv(n){return Math.max(5,Math.ceil(Math.sqrt(n)))}function Av(n){var t=tb(n),e=Dv(t.length);return Zv(n,e)}function Mv(t,e,n,o){"custom"===n?fb(t)(function(n){n.each(function(n){ob(n),t.execCommand("mceApplyTextcolor",e,n),o(n)})},"#000000"):"remove"===n?(o(""),t.execCommand("mceRemoveTextcolor",e)):(o(n),t.execCommand("mceApplyTextcolor",e,n))}function Fv(n,t){return n.concat(eb().concat(function(n){var t="choiceitem",e={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return n?[e,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[e]}(t)))}function Iv(t,e){return function(n){n(Fv(t,e))}}function Rv(n,t,e){var o,r;o="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,n.setIconFill(o,r),n.setIconStroke(o,r)}function Vv(o,e,r,n,i){o.ui.registry.addSplitButton(e,{tooltip:n,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){return tn.from(Bv(o,r)).bind(function(n){return function(n){if("transparent"===n)return tn.some(lv(0,0,0,0));var t=Sv.exec(n);if(null!==t)return tn.some(pv(t[1],t[2],t[3],"1"));var e=Cv.exec(n);return null!==e?tn.some(pv(e[1],e[2],e[3],e[4])):tn.none()}(n).map(function(n){var t=fv(n).value();return Bt(e.toLowerCase(),t)})}).getOr(!1)},columns:Av(o),fetch:Iv(tb(o),nb(o)),onAction:function(n){null!==i.get()&&Mv(o,r,i.get(),function(){})},onItemAction:function(n,t){Mv(o,r,t,function(n){i.set(n),sb(o,{name:e,color:n})})},onSetup:function(t){null!==i.get()&&Rv(t,e,i.get());function n(n){n.name===e&&Rv(t,n.name,n.color)}return o.on("TextColorChange",n),function(){o.off("TextColorChange",n)}}})}function Hv(t,n,e,o){t.ui.registry.addNestedMenuItem(n,{text:o,icon:"forecolor"===n?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(n){Mv(t,e,n.value,Z)}}]}})}function Nv(n){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:function(n){return"color"===n?"tox-swatches":"tox-menu"}(n),tieredMenu:"tox-tiered-menu"}}function Pv(n){var t=Nv(n);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Rp(n)}}function zv(n,t,e){var o=Nv(e);return{dom:{tag:"div",classes:z([[o.tieredMenu]])},markers:Pv(e)}}function Lv(e,o){return function(n){var t=w(n,o);return S(t,function(n){return{dom:e,components:n}})}}function jv(n,e){var o=[],r=[];return fn(n,function(n,t){e(n,t)?(0<r.length&&o.push(r),r=[],yn(n.dom,"innerHtml")&&r.push(n)):r.push(n)}),0<r.length&&o.push(r),S(o,function(n){return{dom:{tag:"div",classes:["tox-collection__group"]},components:n}})}function Uv(t,e,n){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===t?["tox-collection--list"]:["tox-collection--grid"])},components:[Xg.parts().items({preprocess:function(n){return"auto"!==t&&1<t?Lv({tag:"div",classes:["tox-collection__group"]},t)(n):jv(n,function(n,t){return"separator"===e[t].type})}})]}}function Wv(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function Gv(n){return v.console.error(Ko(n)),v.console.log(n),tn.none()}function Xv(n,t,e,o,r){var i=function(e){return{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Xg.parts().items({preprocess:function(n){return jv(n,function(n,t){return"separator"===e[t].type})}})]}}(e);return{value:n,dom:i.dom,components:i.components,items:e}}function Yv(n,t,e,o,r){var i;return"color"===r?{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Xg.parts().items({preprocess:"auto"!==n?Lv({tag:"div",classes:["tox-swatches__row"]},n):l})]}]}}(o)).dom,components:i.components,items:e}:"normal"===r&&"auto"===o?{value:n,dom:(i=Uv(o,e)).dom,components:i.components,items:e}:"normal"===r&&1===o?{value:n,dom:(i=Uv(1,e)).dom,components:i.components,items:e}:"normal"===r?{value:n,dom:(i=Uv(o,e)).dom,components:i.components,items:e}:"listpreview"!==r||"auto"===o?{value:n,dom:function(n,t,e){var o=Nv(e);return{tag:"div",classes:z([[o.menu,"tox-menu-"+t+"-column"],n?[o.hasIcons]:[]])}}(t,o,r),components:db,items:e}:{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Xg.parts().items({preprocess:Lv({tag:"div",classes:["tox-collection__group"]},n)})]}}(o)).dom,components:i.components,items:e}}function qv(n,t,e,o,r,i,u,a){var c=function(n){return x(n,Wv)}(t),s=mb(t,e,o,"color"!==r?"normal":"color",i,u,a);return Yv(n,c,s,o,r)}function Kv(n,t){var e=Pv(t);return 1===n?{mode:"menu",moveOnTab:!0}:"auto"===n?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}}var Jv="choiceitem",$v=[{type:Jv,text:"Light Green",value:"#BFEDD2"},{type:Jv,text:"Light Yellow",value:"#FBEEB8"},{type:Jv,text:"Light Red",value:"#F8CAC6"},{type:Jv,text:"Light Purple",value:"#ECCAFA"},{type:Jv,text:"Light Blue",value:"#C2E0F4"},{type:Jv,text:"Green",value:"#2DC26B"},{type:Jv,text:"Yellow",value:"#F1C40F"},{type:Jv,text:"Red",value:"#E03E2D"},{type:Jv,text:"Purple",value:"#B96AD9"},{type:Jv,text:"Blue",value:"#3598DB"},{type:Jv,text:"Dark Turquoise",value:"#169179"},{type:Jv,text:"Orange",value:"#E67E23"},{type:Jv,text:"Dark Red",value:"#BA372A"},{type:Jv,text:"Dark Purple",value:"#843FA1"},{type:Jv,text:"Dark Blue",value:"#236FA1"},{type:Jv,text:"Light Gray",value:"#ECF0F1"},{type:Jv,text:"Medium Gray",value:"#CED4D9"},{type:Jv,text:"Gray",value:"#95A5A6"},{type:Jv,text:"Dark Gray",value:"#7E8C8D"},{type:Jv,text:"Navy Blue",value:"#34495E"},{type:Jv,text:"Black",value:"#000000"},{type:Jv,text:"White",value:"#ffffff"}],Qv=function aI(t){void 0===t&&(t=10);var n,e=Ov.getItem(_v),o=J(e)?JSON.parse(e):[],r=t-(n=o).length<0?n.slice(0,t):n,i=function(n){r.splice(n,1)};return{add:function(n){(function(n,t){var e=y(n,t);return-1===e?tn.none():tn.some(e)})(r,n).each(i),r.unshift(n),r.length>t&&r.pop(),Ov.setItem(_v,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),Zv=function(n,t){return n.getParam("color_cols",t,"number")},nb=function(n){return!1!==n.getParam("custom_colors")},tb=function(n){var t=Ev(n);return t!==undefined?Tv(t):$v},eb=function(){return S(Qv.state(),function(n){return{type:Jv,text:n,value:n}})},ob=function(n){Qv.add(n)},rb=function(n){return n.fire("SkinLoaded")},ib=function(n,t){return n.fire("SkinLoadError",t)},ub=function(n){return n.fire("ResizeEditor")},ab=function(n,t){return n.fire("ScrollContent",t)},cb=function(n,t){return n.fire("ResizeContent",t)},sb=function(n,t){return n.fire("TextColorChange",t)},fb=function(i){return function(n,t){var e,o={colorpicker:t},r=(e=n,function(n){var t=n.getData();e(tn.from(t.colorpicker)),n.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(n,t){"hex-valid"===t.name&&(t.value?n.enable("ok"):n.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){n(tn.none())}})}},lb={register:function(n){!function(e){e.addCommand("mceApplyTextcolor",function(n,t){!function(n,t,e){n.undoManager.transact(function(){n.focus(),n.formatter.apply(t,{value:e}),n.nodeChanged()})}(e,n,t)}),e.addCommand("mceRemoveTextcolor",function(n){!function(n,t){n.undoManager.transact(function(){n.focus(),n.formatter.remove(t,{value:null},null,!0),n.nodeChanged()})}(e,n)})}(n);var t=or(null),e=or(null);Vv(n,"forecolor","forecolor","Text color",t),Vv(n,"backcolor","hilitecolor","Background color",e),Hv(n,"forecolor","forecolor","Text color"),Hv(n,"backcolor","hilitecolor","Background color")},getColors:Fv,getFetch:Iv,colorPickerDialog:fb,getCurrentColor:Bv,getColorCols:Av,calcCols:Dv},db=[Xg.parts().items({})],mb=function(n,e,o,r,i,u,a){return Fl(S(n,function(t){return"choiceitem"===t.type?function(n){return qn("choicemenuitem",hh,n)}(t).fold(Gv,function(n){return tn.some(function(t,n,e,o,r,i,u){var a=jp({presets:e,textContent:n?t.text:tn.none(),htmlContent:tn.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:n?t.shortcut:tn.none(),checkMark:n?tn.some(zp(u.icons)):tn.none(),caret:tn.none(),value:t.value},u,!0);return Sn($h({data:Qh(t),disabled:t.disabled,getApi:function(t){return{setActive:function(n){Fg.set(t,n)},isActive:function(){return Fg.isOn(t)},isDisabled:function(){return Gh.isDisabled(t)},setDisabled:function(n){return Gh.set(t,n)}}},onAction:function(n){return o(t.value)},onSetup:function(n){return n.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},a,i),{toggling:{toggleClass:Ah,toggleOnExecute:!1,selected:t.active}})}(n,1===o,r,e,u(t.value),i,a))}):tn.none()}))};var gb,pb,hb={inserttable:function cI(o){var n=De("size-label"),i=function(n,t,e){for(var o=[],r=0;r<t;r++){for(var i=[],u=0;u<e;u++)i.push(nv(r,u,n));o.push(i)}return o}(n,10,10),u=dp({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:n}},components:[wo("0x0")],behaviours:Sa([Cg.config({})])});return{type:"widget",data:{value:De("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ov().widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:function(n){return E(n,function(n){return S(n,eu)})}(i).concat(u.asSpec()),behaviours:Sa([Qd("insert-table-picker",[Qt(rv,function(n,t,e){var o=e.event().row(),r=e.event().col();!function(n,t,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)Fg.set(n[i][u],i<=t&&u<=e)}(i,o,r,10,10),Cg.set(u.get(n),[function(n,t){return wo(t+1+"x"+(n+1))}(o,r)])}),Qt(iv,function(n,t,e){o.onAction({numRows:e.event().row()+1,numColumns:e.event().col()+1}),zt(n,ai())})]),wg.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function sI(t,n){var e=lb.getColors(n.colorinput.getColors(),n.colorinput.hasCustomColors()),o=n.colorinput.getColorCols(),r=qv(De("menu-value"),e,function(n){t.onAction({value:n})},o,"color",tv.CLOSE_ON_EXECUTE,function(){return!1},n.shared.providers),i=P(P({},r),{markers:Pv("color"),movement:Kv(o,"color")});return{type:"widget",data:{value:De("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ov().widget(Xg.sketch(i))]}}},vb=function(t,e,n,o,r,i,u,a){void 0===a&&(a=!0);var c=jp({presets:o,textContent:tn.none(),htmlContent:n?t.text.map(function(n){return Zh(n,e)}):tn.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:tn.none(),checkMark:tn.none(),caret:tn.none(),value:t.value},u.providers,a,t.icon);return $h({data:Qh(t),disabled:t.disabled,getApi:function(){return{}},onAction:function(n){return r(t.value,t.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:function(n,t){return bn(n,"tooltipWorker").map(function(e){return[Oh.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(n){return{anchor:"submenu",item:n,overrides:{maxHeightFunction:Bc}}},mode:"follow-highlight",onShow:function(t,n){e(function(n){Oh.setComponents(t,[Zi({element:ir.fromDom(n)})])})}})]}).getOr([])}(t.meta,u)},c,i)},bb=function(n){var t=n.text.fold(function(){return{}},function(n){return{innerHtml:n}});return{type:"separator",dom:P({tag:"div",classes:[Eh,"tox-collection__group-heading"]},t),components:[]}},yb=function(n,t,e,o){void 0===o&&(o=!0);var r=jp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:tn.none(),ariaLabel:n.text,caret:tn.none(),checkMark:tn.none(),shortcutContent:n.shortcut},e,o);return $h({data:Qh(n),getApi:function(t){return{isDisabled:function(){return Gh.isDisabled(t)},setDisabled:function(n){return Gh.set(t,n)}}},disabled:n.disabled,onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,t)},xb=function(n,t,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i=r?function(n){return{dom:{tag:"div",classes:[Ih],innerHtml:gp("chevron-down",n)}}}(e.icons):function(n){return{dom:{tag:"div",classes:[Ih],innerHtml:gp("chevron-right",n)}}}(e.icons),u=jp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:tn.none(),ariaLabel:n.text,caret:tn.some(i),checkMark:tn.none(),shortcutContent:n.shortcut},e,o);return $h({data:Qh(n),getApi:function(t){return{isDisabled:function(){return Gh.isDisabled(t)},setDisabled:function(n){return Gh.set(t,n)}}},disabled:n.disabled,onAction:Z,onSetup:n.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,t)},wb=function(n,t,e){var o=jp({iconContent:tn.none(),textContent:n.text,htmlContent:tn.none(),ariaLabel:n.text,checkMark:tn.some(zp(e.icons)),caret:tn.none(),shortcutContent:n.shortcut,presets:"normal",meta:n.meta},e,!0);return Sn($h({data:Qh(n),disabled:n.disabled,getApi:function(t){return{setActive:function(n){Fg.set(t,n)},isActive:function(){return Fg.isOn(t)},isDisabled:function(){return Gh.isDisabled(t)},setDisabled:function(n){return Gh.set(t,n)}}},onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},o,t),{toggling:{toggleClass:Ah,toggleOnExecute:!1,selected:n.active}})},Sb=function(t,e){return function(n,t){return Object.prototype.hasOwnProperty.call(n,t)?tn.some(n[t]):tn.none()}(hb,t.fancytype).map(function(n){return n(t,e)})};(pb=gb=gb||{})[pb.ContentFocus=0]="ContentFocus",pb[pb.UiFocus=1]="UiFocus";function Cb(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function kb(n){return x(n,Cb)}function Ob(n,t,e,o,r){function i(n){return r?P(P({},n),{shortcut:tn.none(),icon:n.text.isSome()?tn.none():n.icon}):n}var u=e.shared.providers;switch(n.type){case"menuitem":return function(n){return qn("menuitem",mh,n)}(n).fold(Gv,function(n){return tn.some(yb(i(n),t,u,o))});case"nestedmenuitem":return function(n){return qn("nestedmenuitem",gh,n)}(n).fold(Gv,function(n){return tn.some(xb(i(n),t,u,o,r))});case"togglemenuitem":return function(n){return qn("togglemenuitem",ph,n)}(n).fold(Gv,function(n){return tn.some(wb(i(n),t,u))});case"separator":return function(n){return qn("separatormenuitem",sh,n)}(n).fold(Gv,function(n){return tn.some(bb(n))});case"fancymenuitem":return function(n){return qn("fancymenuitem",vh,n)}(n).fold(Gv,function(n){return Sb(i(n),e)});default:return v.console.error("Unknown item in general menu",n),tn.none()}}function _b(n,t,e,o,r,i){var u=1===o,a=!u||kb(n);return Fl(S(n,function(n){return"separator"===n.type?function(n){return qn("Autocompleter.Separator",sh,n)}(n).fold(Gv,function(n){return tn.some(bb(n))}):function(n){return qn("Autocompleter.Item",fh,n)}(n).fold(Gv,function(n){return tn.some(vb(n,t,u,"normal",e,r,i,a))})}))}function Tb(n,t,e,o,r){var i=kb(t),u=Fl(S(t,function(n){function t(n){return Ob(n,e,o,function(n){return r?!n.hasOwnProperty("text"):i}(n),r)}return"nestedmenuitem"===n.type&&n.getSubmenuItems().length<=0?t(P(P({},n),{disabled:!0})):t(n)}));return(r?Xv:Yv)(n,i,u,1,"normal")}function Eb(n){return $g.singleData(n.value,n)}function Bb(n,t,e){return function(n,t,e,o){return lu(n,t,e,o,!1)}(n,t,Fb,e)}function Db(n,t,e){return function(n,t,e,o){return lu(n,t,e,o,!0)}(n,t,Fb,e)}function Ab(n,t,e){return Hu(n,t,e).isSome()}var Mb=function(u,a){function e(){return s.get().isSome()}function c(){e()&&Qg.hide(l)}function i(n,t,e,o){n.matchLength=t.text.length;var r=R(e,function(n){return tn.from(n.columns)}).getOr(1);Qg.showAt(l,{anchor:"node",root:ir.fromDom(u.getBody()),node:tn.from(n.element)},Xg.sketch(function(n,t,e,o){var r=e===gb.ContentFocus?Kl():ql(),i=Kv(t,o),u=Pv(o);return{dom:n.dom,components:n.components,items:n.items,value:n.value,markers:{selectedItem:u.selectedItem,item:u.item},movement:i,fakeFocus:e===gb.ContentFocus,focusManager:r,menuBehaviours:bh("auto"!==t?[]:[Oi(function(o,n){Mp(o,4,u.item).each(function(n){var t=n.numColumns,e=n.numRows;wg.setGridSize(o,e,t)})})])}}(Yv("autocompleter-value",!0,o,r,"normal"),r,gb.ContentFocus,"normal"))),Qg.getContent(l).each(fd.highlightFirst)}var s=or(tn.none()),f=or(!1),l=tu(Qg.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:Sa([Qd("dismissAutocompleter",[qt(hi(),function(){return d()})])]),lazySink:a.getSink})),d=function(){if(e()){var n=s.get().map(function(n){return n.element});oh(n.getOr(ir.fromDom(u.selection.getNode()))).each(ve),c(),s.set(tn.none()),f.set(!1)}},o=U(function(){return Ap(u)}),m=function(n){(function(t){return s.get().map(function(n){return Tp(u.dom,u.selection.getRng(),n.triggerChar).bind(function(n){return ch(u,o,n,t)})}).getOrThunk(function(){return Dp(u,o)})})(n).fold(d,function(r){!function(n){if(!e()){var t=Sp(u,n.range);s.set(tn.some({triggerChar:n.triggerChar,element:t,matchLength:n.text.length})),f.set(!1)}}(r.context),r.lookupData.then(function(o){s.get().map(function(n){var t=r.context;if(n.triggerChar===t.triggerChar){var e=function(t,n){var e=R(n,function(n){return tn.from(n.columns)}).getOr(1);return E(n,function(i){var n=i.items;return _b(n,i.matchText,function(o,r){var n=u.selection.getRng();Tp(u.dom,n,t).fold(function(){return v.console.error("Lost context. Cursor probably moved")},function(n){var t=n.range,e={hide:function(){d()},reload:function(n){c(),m(n)}};f.set(!0),i.onAction(e,t,o,r),f.set(!1)})},e,tv.BUBBLE_TO_SANDBOX,a)})}(t.triggerChar,o);0<e.length?i(n,t,o,e):10<=t.text.length-n.matchLength?d():c()}})})})},n={onKeypress:xp(function(n){27!==n.which&&m()},50),cancelIfNecessary:d,isMenuOpen:function(){return Qg.isOpen(l)},isActive:e,isProcessingAction:f.get,getView:function(){return Qg.getContent(l)}};rh(n,u)},Fb=nn(!0),Ib=fu;function Rb(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},schedule:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];r=v.setTimeout(function(){e.apply(null,n),r=null},o)}}}function Vb(n){var t=n.raw();return t.touches===undefined||1!==t.touches.length?tn.none():tn.some(t.touches[0])}function Hb(e){var o=or(tn.none()),r=or(!1),i=Rb(function(n){e.triggerEvent(ui(),n),r.set(!0)},400),u=An([{key:Ir(),value:function(e){return Vb(e).each(function(n){i.cancel();var t={x:nn(n.clientX),y:nn(n.clientY),target:e.target};i.schedule(e),r.set(!1),o.set(tn.some(t))}),tn.none()}},{key:Rr(),value:function(n){return i.cancel(),Vb(n).each(function(t){o.get().each(function(n){!function(n,t){var e=Math.abs(n.clientX-t.x()),o=Math.abs(n.clientY-t.y());return 5<e||5<o}(t,n)||o.set(tn.none())})}),tn.none()}},{key:Vr(),value:function(t){i.cancel();return o.get().filter(function(n){return Rt(n.target(),t.target())}).map(function(n){return r.get()?(t.prevent(),!1):e.triggerEvent(ii(),t)})}}]);return{fireIfReady:function(t,n){return bn(u,n).bind(function(n){return n(t)})}}}function Nb(t,n){var e=Jn("Getting GUI events settings",Ub,n),o=Hb(e),r=S(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return Bb(t,n,function(t){o.fireIfReady(t,n).each(function(n){n&&t.kill()}),e.triggerEvent(n,t)&&t.kill()})}),i=or(tn.none()),u=Bb(t,"paste",function(t){o.fireIfReady(t,"paste").each(function(n){n&&t.kill()}),e.triggerEvent("paste",t)&&t.kill(),i.set(tn.some(v.setTimeout(function(){e.triggerEvent(ti(),t)},0)))}),a=Bb(t,"keydown",function(n){e.triggerEvent("keydown",n)?n.kill():!0===e.stopBackspace&&function(n){return 8===n.raw().which&&!sn(["input","textarea"],xe(n.target()))&&!Ab(n.target(),'[contenteditable="true"]')}(n)&&n.prevent()}),c=function(n,t){return jb?Db(n,"focus",t):Bb(n,"focusin",t)}(t,function(n){e.triggerEvent("focusin",n)&&n.kill()}),s=or(tn.none()),f=function(n,t){return jb?Db(n,"blur",t):Bb(n,"focusout",t)}(t,function(n){e.triggerEvent("focusout",n)&&n.kill(),s.set(tn.some(v.setTimeout(function(){e.triggerEvent(ni(),n)},0)))});return{unbind:function(){fn(r,function(n){n.unbind()}),a.unbind(),c.unbind(),f.unbind(),u.unbind(),i.get().each(v.clearTimeout),s.get().each(v.clearTimeout)}}}function Pb(n,t){var e=bn(n,"target").map(function(n){return n()}).getOr(t);return or(e)}function zb(n,o,t,e,r,i){var u=n(o,e),a=function(n,t){var e=or(!1),o=or(!1);return{stop:function(){e.set(!0)},cut:function(){o.set(!0)},isStopped:e.get,isCut:o.get,event:nn(n),setSource:t.set,getSource:t.get}}(t,r);return u.fold(function(){return i.logEventNoHandlers(o,e),Wb.complete()},function(t){var e=t.descHandler();return Ne(e)(a),a.isStopped()?(i.logEventStopped(o,t.element(),e.purpose()),Wb.stopped()):a.isCut()?(i.logEventCut(o,t.element(),e.purpose()),Wb.complete()):ce(t.element()).fold(function(){return i.logNoParent(o,t.element(),e.purpose()),Wb.complete()},function(n){return i.logEventResponse(o,t.element(),e.purpose()),Wb.resume(n)})})}function Lb(n,t,e){var o=function(n){var t=or(!1);return{stop:function(){t.set(!0)},cut:Z,isStopped:t.get,isCut:nn(!1),event:nn(n),setSource:r("Cannot set source of a broadcasted event"),getSource:r("Cannot get source of a broadcasted event")}}(t);return fn(n,function(n){var t=n.descHandler();Ne(t)(o)}),o.isStopped()}var jb=At().browser.isFirefox(),Ub=zn([it("triggerEvent"),pt("stopBackspace",!0)]),Wb=xn([{stopped:[]},{resume:["element"]},{complete:[]}]),Gb=function(t,e,o,n,r,i){return zb(t,e,o,n,r,i).fold(function(){return!0},function(n){return Gb(t,e,o,n,r,i)},function(){return!1})},Xb=function(n,t,e,o,r){var i=Pb(e,o);return Gb(n,t,e,o,i,r)},Yb=te("element","descHandler"),qb=function(n,t){return{id:nn(n),descHandler:nn(t)}};function Kb(){var i={};return{registerId:function(o,r,n){pn(n,function(n,t){var e=i[t]!==undefined?i[t]:{};e[r]=qi(n,o),i[t]=e})},unregisterId:function(e){pn(i,function(n,t){n.hasOwnProperty(e)&&delete n[e]})},filterByType:function(n){return bn(i,n).map(function(n){return vn(n,function(n,t){return qb(t,n)})}).getOr([])},find:function(n,t,e){var o=bn(i,t);return Fr(e,function(n){return function(n,e){return Li(e).fold(function(){return tn.none()},function(t){return n.bind(function(n){return bn(n,t)}).map(function(n){return Yb(e,n)})})}(o,n)},n)}}}function Jb(){function o(n){var t=n.element();return Li(t).fold(function(){return function(n,t){var e=De(Ni+n);return zi(t,e),e}("uid-",n.element())},function(n){return n})}var r=Kb(),i={},u=function(n){Li(n.element()).each(function(n){delete i[n],r.unregisterId(n)})};return{find:function(n,t,e){return r.find(n,t,e)},filter:function(n){return r.filterByType(n)},register:function(n){var t=o(n);N(i,t)&&function(n,t){var e=i[t];if(e!==n)throw new Error('The tagId "'+t+'" is already used by: '+Be(e.element())+"\nCannot use it for: "+Be(n.element())+"\nThe conflicting element is"+(to(e.element())?" ":" not ")+"already in the DOM");u(n)}(n,t);var e=[n];r.registerId(e,t,n.events()),i[t]=n},unregister:u,getById:function(n){return bn(i,n)}}}function $b(e){function o(t){return ce(e.element()).fold(function(){return!0},function(n){return Rt(t,n)})}function r(n,t){return u.find(o,n,t)}function i(e){var n=u.filter(ei());fn(n,function(n){var t=n.descHandler();Ne(t)(e)})}var u=Jb(),n=Nb(e.element(),{triggerEvent:function(t,e){return Gu(t,e.target(),function(n){return function(n,t,e,o){var r=e.target();return Xb(n,t,e,r,o)}(r,t,e,n)})}}),a={debugInfo:nn("real"),triggerEvent:function(t,e,o){Gu(t,e,function(n){return Xb(r,t,o,e,n)})},triggerFocus:function(t,e){Li(t).fold(function(){Oa(t)},function(n){Gu(Zr(),t,function(n){return function(n,t,e,o,r){var i=Pb(e,o);zb(n,t,e,o,i,r)}(r,Zr(),{originator:nn(e),kill:Z,prevent:Z,target:nn(t)},t,n),!1})})},triggerEscape:function(n,t){a.triggerEvent("keydown",n.element(),t.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:tu,addToGui:function(n){s(n)},removeFromGui:function(n){f(n)},addToWorld:function(n){t(n)},removeFromWorld:function(n){c(n)},broadcast:function(n){l(n)},broadcastOn:function(n,t){d(n,t)},broadcastEvent:function(n,t){m(n,t)},isConnected:nn(!0)},t=function(n){n.connect(a),Mi(n.element())||(u.register(n),fn(n.components(),t),a.triggerEvent(si(),n.element(),{target:nn(n.element())}))},c=function(n){Mi(n.element())||(fn(n.components(),c),u.unregister(n)),n.disconnect()},s=function(n){ks(e,n)},f=function(n){_s(n)},l=function(n){i({universal:nn(!0),data:nn(n)})},d=function(n,t){i({universal:nn(!1),channels:nn(n),data:nn(t)})},m=function(n,t){var e=u.filter(n);return Lb(e,t)},g=function(n){return u.getById(n).fold(function(){return K.error(new Error('Could not find component with uid: "'+n+'" in system.'))},K.value)},p=function(n){var t=Li(n).getOr("not found");return g(t)};return t(e),{root:nn(e),element:e.element,destroy:function(){n.unbind(),Di(e.element())},add:s,remove:f,getByUid:g,getByDom:p,addToWorld:t,removeFromWorld:c,broadcast:l,broadcastOn:d,broadcastEvent:m}}function Qb(n){return Sa([Tg.config({onFocus:n.selectOnFocus?function(n){var t=n.element(),e=mo(t);t.dom().setSelectionRange(0,e.length)}:Z})])}function Zb(n){return{tag:n.tag,attributes:P({type:"text"},n.inputAttributes),styles:n.inputStyles,classes:n.inputClasses}}var ny,ty,ey,oy,ry=Al({name:"Container",factory:function(n){var t=n.dom,e=t.attributes,o=c(t,["attributes"]);return{uid:n.uid,dom:P({tag:"div",attributes:P({role:"presentation"},e)},o),components:n.components,behaviours:js(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[pt("components",[]),Ls("containerBehaviours",[]),pt("events",{}),pt("domModification",{}),pt("eventOrder",{})]}),iy=De("form-component-change"),uy=De("form-close"),ay=De("form-cancel"),cy=De("form-action"),sy=De("form-submit"),fy=De("form-block"),ly=De("form-unblock"),dy=De("form-tabchange"),my=De("form-resize"),gy=nn([pt("prefix","form-field"),Ls("fieldBehaviours",[ed,tl])]),py=nn([kl({schema:[tt("dom")],name:"label"}),kl({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[tt("text")],name:"aria-descriptor"}),Sl({factory:{sketch:function(n){var t=Bn(n,["factory"]);return n.factory.sketch(t)}},schema:[tt("factory")],name:"field"})]),hy=Ml({name:"FormField",configFields:gy(),partFields:py(),factory:function(r,n,t,e){var o=Us(r.fieldBehaviours,[ed.config({find:function(n){return of(n,r,"field")}}),tl.config({store:{mode:"manual",getValue:function(n){return ed.getCurrent(n).bind(tl.getValue)},setValue:function(n,t){ed.getCurrent(n).each(function(n){tl.setValue(n,t)})}}})]),i=Gt([Oi(function(n,t){var o=uf(n,r,["label","field","aria-descriptor"]);o.field().each(function(e){var t=De(r.prefix);o.label().each(function(n){Ce(n.element(),"for",t),Ce(e.element(),"id",t)}),o["aria-descriptor"]().each(function(n){var t=De(r.prefix);Ce(n.element(),"id",t),Ce(e.element(),"aria-describedby",t)})})})]),u={getField:function(n){return of(n,r,"field")},getLabel:function(n){return of(n,r,"label")}};return{uid:r.uid,dom:r.dom,components:n,behaviours:o,events:i,apis:u}},apis:{getField:function(n,t){return n.getField(t)},getLabel:function(n,t){return n.getLabel(t)}}}),vy=nn([st("data"),pt("inputAttributes",{}),pt("inputStyles",{}),pt("tag","input"),pt("inputClasses",[]),Ku("onSetValue"),pt("styles",{}),pt("eventOrder",{}),Ls("inputBehaviours",[tl,Tg]),pt("selectOnFocus",!0)]),by=Al({name:"Input",configFields:vy(),factory:function(n,t){return{uid:n.uid,dom:Zb(n),components:[],behaviours:function(n){return P(P({},Qb(n)),Us(n.inputBehaviours,[tl.config({store:P(P({mode:"manual"},n.data.map(function(n){return{initialValue:n}}).getOr({})),{getValue:function(n){return mo(n.element())},setValue:function(n,t){mo(n.element())!==t&&go(n.element(),t)}}),onSetValue:n.onSetValue})]))}(n),eventOrder:n.eventOrder}}}),yy={},xy={exports:yy};ny=undefined,ty=yy,ey=xy,oy=undefined,function(n){"object"==typeof ty&&void 0!==ey?ey.exports=n():"function"==typeof ny&&ny.amd?ny([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function f(i,u,a){function c(t,n){if(!u[t]){if(!i[t]){var e="function"==typeof oy&&oy;if(!n&&e)return e(t,!0);if(s)return s(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[t]={exports:{}};i[t][0].call(r.exports,function(n){return c(i[t][1][n]||n)},r,r.exports,f,i,u,a)}return u[t].exports}for(var s="function"==typeof oy&&oy,n=0;n<a.length;n++)c(a[n]);return c}({1:[function(n,t,e){var o,r,i=t.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(n){if(o===setTimeout)return setTimeout(n,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(n,0);try{return o(n,0)}catch(t){try{return o.call(null,n,0)}catch(t){return o.call(this,n,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(n){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(n){r=a}}();var s,f=[],l=!1,d=-1;function m(){l&&s&&(l=!1,s.length?f=s.concat(f):d=-1,f.length&&g())}function g(){if(!l){var n=c(m);l=!0;for(var t=f.length;t;){for(s=f,f=[];++d<t;)s&&s[d].run();d=-1,t=f.length}s=null,l=!1,function e(n){if(r===clearTimeout)return clearTimeout(n);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(n);try{return r(n)}catch(t){try{return r.call(null,n)}catch(t){return r.call(this,n)}}}(n)}}function p(n,t){this.fun=n,this.array=t}function h(){}i.nextTick=function(n){var t=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)t[e-1]=arguments[e];f.push(new p(n,t)),1!==f.length||l||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,t){(function(t){function o(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],f(n,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,i._immediateFn(function(){var n=1===o._state?r.onFulfilled:r.onRejected;if(null!==n){var t;try{t=n(o._value)}catch(e){return void a(r.promise,e)}u(r.promise,t)}else(1===o._state?u:a)(r.promise,o._value)})):o._deferreds.push(r)}function u(n,t){try{if(t===n)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if(t instanceof i)return n._state=3,n._value=t,void c(n);if("function"==typeof e)return void f(function o(n,t){return function(){n.apply(t,arguments)}}(e,t),n)}n._state=1,n._value=t,c(n)}catch(r){a(n,r)}}function a(n,t){n._state=2,n._value=t,c(n)}function c(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var t=0,e=n._deferreds.length;t<e;t++)r(n,n._deferreds[t]);n._deferreds=null}function s(n,t,e){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.promise=e}function f(n,t){var e=!1;try{n(function(n){e||(e=!0,u(t,n))},function(n){e||(e=!0,a(t,n))})}catch(o){if(e)return;e=!0,a(t,o)}}var n,e;n=this,e=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,t){var e=new this.constructor(o);return r(this,new s(n,t,e)),e},i.all=function(n){var c=Array.prototype.slice.call(n);return new i(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(n){n(t)})},i.reject=function(e){return new i(function(n,t){t(e)})},i.race=function(r){return new i(function(n,t){for(var e=0,o=r.length;e<o;e++)r[e].then(n,t)})},i._immediateFn="function"==typeof t?function(n){t(n)}:function(n){e(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(c,n,s){(function(n,t){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(n,t){this._id=n,this._clearFn=t}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(n){n.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(n,t){clearTimeout(n._idleTimeoutId),n._idleTimeout=t},s.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},s._unrefActive=s.active=function(n){clearTimeout(n._idleTimeoutId);var t=n._idleTimeout;0<=t&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},t))},s.setImmediate="function"==typeof n?n:function(n){var t=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[t]=!0,o(function(){i[t]&&(e?n.apply(null,e):n.call(null),s.clearImmediate(t))}),t},s.clearImmediate="function"==typeof t?t:function(n){delete i[n]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,t,e){var o=n("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();t.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});function wy(n){v.setTimeout(function(){throw n},0)}function Sy(n){var t=xe(n);return sn(Hy,t)}function Cy(n,t){var e=t.getRoot(n).getOr(n.element());Je(e,t.invalidClass),t.notify.each(function(t){Sy(n.element())&&Ce(n.element(),"aria-invalid",!1),t.getContainer(n).each(function(n){ye(n,t.validHtml)}),t.onValid(n)})}function ky(t,n,e,o){var r=n.getRoot(t).getOr(t.element());qe(r,n.invalidClass),n.notify.each(function(n){Sy(t.element())&&Ce(t.element(),"aria-invalid",!0),n.getContainer(t).each(function(n){ye(n,o)}),n.onInvalid(t,o)})}function Oy(t,n,e){return n.validator.fold(function(){return Vy(K.value(!0))},function(n){return n.validate(t)})}function _y(t,e,n){return e.notify.each(function(n){n.onValidate(t)}),Oy(t,e).map(function(n){return t.getSystem().isConnected()?n.fold(function(n){return ky(t,e,0,n),K.error(n)},function(n){return Cy(t,e),K.value(n)}):K.error("No longer in system")})}function Ty(n,t,e,o){var r=Gy(n,t,e,o);return hy.sketch(r)}function Ey(n,t){return hy.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}})}var By,Dy,Ay=xy.exports.boltExport,My=function(n){var e=tn.none(),t=[],o=function(n){r()?u(n):t.push(n)},r=function(){return e.isSome()},i=function(n){fn(n,u)},u=function(t){e.each(function(n){v.setTimeout(function(){t(n)},0)})};return n(function(n){e=tn.some(n),i(t),t=[]}),{get:o,map:function(e){return My(function(t){o(function(n){t(e(n))})})},isReady:r}},Fy={nu:My,pure:function(t){return My(function(n){n(t)})}},Iy=function(e){function n(n){e().then(n,wy)}return{map:function(n){return Iy(function(){return e().then(n)})},bind:function(t){return Iy(function(){return e().then(function(n){return t(n).toPromise()})})},anonBind:function(n){return Iy(function(){return e().then(function(){return n.toPromise()})})},toLazy:function(){return Fy.nu(n)},toCached:function(){var n=null;return Iy(function(){return null===n&&(n=e()),n})},toPromise:e,get:n}},Ry=function(n){return Iy(function(){return new Ay(n)})},Vy=function(n){return Iy(function(){return Ay.resolve(n)})},Hy=["input","textarea"],Ny=/* */Object.freeze({__proto__:null,markValid:Cy,markInvalid:ky,query:Oy,run:_y,isInvalid:function(n,t){var e=t.getRoot(n).getOr(n.element());return $e(e,t.invalidClass)}}),Py=/* */Object.freeze({__proto__:null,events:function(t,n){return t.validator.map(function(n){return Gt([qt(n.onEvent,function(n){_y(n,t).get(l)})].concat(n.validateOnLoad?[Oi(function(n){_y(n,t).get(Z)})]:[]))}).getOr({})}}),zy=[tt("invalidClass"),pt("getRoot",tn.none),gt("notify",[pt("aria","alert"),pt("getContainer",tn.none),pt("validHtml",""),Ku("onValid"),Ku("onInvalid"),Ku("onValidate")]),gt("validator",[tt("validate"),pt("onEvent","input"),pt("validateOnLoad",!0)])],Ly=Ca({fields:zy,name:"invalidating",active:Py,apis:Ny,extra:{validation:function(e){return function(n){var t=tl.getValue(n);return Vy(e(t))}}}}),jy=/* */Object.freeze({__proto__:null,exhibit:function(n,t){return He({attributes:An([{key:t.tabAttr,value:"true"}])})}}),Uy=[pt("tabAttr","data-alloy-tabstop")],Wy=Ca({fields:Uy,name:"tabstopping",active:jy}),Gy=function(n,t,e,o){return{dom:Xy(e),components:n.toArray().concat([t]),fieldBehaviours:Sa(o)}},Xy=function(n){return{tag:"div",classes:["tox-form__group"].concat(n)}},Yy=/* */Object.freeze({__proto__:null,getCoupled:function(n,t,e,o){return e.getOrCreate(n,t,o)}}),qy=[et("others",Yn(K.value,Jo()))],Ky=Ca({fields:qy,name:"coupling",apis:Yy,state:/* */Object.freeze({__proto__:null,init:function(){var i={},n=nn({});return Yi({readState:n,getOrCreate:function(e,o,r){var n=mn(o.others);if(n)return bn(i,r).getOrThunk(function(){var n=bn(o.others,r).getOrDie("No information found for coupled component: "+r)(e),t=e.getSystem().build(n);return i[r]=t});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(n,null,2))}})}})}),Jy=nn("sink"),$y=nn(kl({name:Jy(),overrides:nn({dom:{tag:"div"},behaviours:Sa([Mf.config({useFixed:a})]),events:Gt([Zt(Gr()),Zt(Nr()),Zt(Kr())])})}));(Dy=By=By||{})[Dy.HighlightFirst=0]="HighlightFirst",Dy[Dy.HighlightNone=1]="HighlightNone";function Qy(n,t){var e=n.getHotspot(t).getOr(t),o=n.getAnchorOverrides();return n.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(n){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:n}})}function Zy(n,t,e,o,r,i,u){return function(n,t,r,e,i,o,u){var a=function(n,t,e){return(0,n.fetch)(e).map(t)}(n,t,e),c=kw(e,n);return a.map(function(n){return n.bind(function(n){return tn.from($g.sketch(P(P({},o.menu()),{uid:Ae(""),data:n,highlightImmediately:u===By.HighlightFirst,onOpenMenu:function(n,t){var e=c().getOrDie();Mf.position(e,r,t),jf.decloak(i)},onOpenSubmenu:function(n,t,e){var o=c().getOrDie();Mf.position(o,{anchor:"submenu",item:t},e),jf.decloak(i)},onRepositionMenu:function(n,t,e){var o=c().getOrDie();Mf.position(o,r,t),fn(e,function(n){Mf.position(o,{anchor:"submenu",item:n.triggeringItem},n.triggeredMenu)})},onEscape:function(){return Tg.focus(e),jf.close(i),tn.some(!0)}})))})})}(n,t,Qy(n,e),e,o,r,u).map(function(n){return n.fold(function(){jf.isOpen(o)&&jf.close(o)},function(n){jf.cloak(o),jf.open(o,n),i(o)}),o})}function nx(n,t,e,o,r,i,u){return jf.close(o),Vy(o)}function tx(n,t,e,o,r,i){var u=Ky.getCoupled(e,"sandbox");return(jf.isOpen(u)?nx:Zy)(n,t,e,u,o,r,i)}function ex(n,t,e){var o=ed.getCurrent(t).getOr(t),r=cu(n.element());e?ro(o.element(),"min-width",r+"px"):function(n,t){Ou.set(n,t)}(o.element(),r)}function ox(n){jf.getState(n).each(function(n){$g.repositionMenus(n)})}function rx(o,r,i){var u=Nu(),n=kw(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id(),role:"listbox"}},behaviours:ol(o.sandboxBehaviours,[tl.config({store:{mode:"memory",initialValue:r}}),jf.config({onOpen:function(n,t){var e=Qy(o,r);u.link(r.element()),o.matchWidth&&ex(e.hotspot,t,o.useMinWidth),o.onOpen(e,n,t),i!==undefined&&i.onOpen!==undefined&&i.onOpen(n,t)},onClose:function(n,t){u.unlink(r.element()),i!==undefined&&i.onClose!==undefined&&i.onClose(n,t)},isPartOf:function(n,t,e){return ju(t,e)||ju(r,e)},getAttachPoint:function(){return n().getOrDie()}}),ed.config({find:function(n){return jf.getState(n).bind(function(n){return ed.getCurrent(n)})}}),bc.config({channels:P(P({},Is({isExtraPart:nn(!1)})),Rs({isExtraPart:nn(!1),doReposition:ox}))})])}}function ix(n){var t=Ky.getCoupled(n,"sandbox");ox(t)}function ux(){return[pt("sandboxClasses",[]),el("sandboxBehaviours",[ed,bc,jf,tl])]}function ax(e,t,o){function r(n,t){Lt(n,Dw,{value:t})}var n=hy.parts().field({factory:by,inputClasses:["tox-textfield"],onSetValue:function(n){return Ly.run(n).get(function(){})},inputBehaviours:Sa([Wy.config({}),Ly.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(n){return ce(n.element())},notify:{onValid:function(n){var t=tl.getValue(n);Lt(n,Bw,{color:t})}},validator:{validateOnLoad:!1,validate:function(n){var t=tl.getValue(n);if(0===t.length)return Vy(K.value(!0));var e=ir.fromTag("span");ro(e,"background-color",t);var o=co(e,"background-color").fold(function(){return K.error("blah")},function(n){return K.value(t)});return Vy(o)}}})]),selectOnFocus:!1}),i=e.label.map(function(n){return Ey(n,t.providers)}),u=dp(function(e,o){return Tw.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:Sa([Ew.config({}),Wy.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:o.getSink,fetch:function(t){return Ry(function(n){return e.fetch(n)}).map(function(n){return tn.from(Eb(Sn(qv(De("menu-value"),n,function(n){e.onItemAction(t,n)},e.columns,e.presets,tv.CLOSE_ON_EXECUTE,function(){return!1},o.providers),{movement:Kv(e.columns,e.presets)})))})},parts:{menu:zv(0,0,e.presets)}})}({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[aa]},onLtr:function(){return[ca]}},components:[],fetch:lb.getFetch(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(n,e){u.getOpt(n).each(function(t){"custom"===e?o.colorPicker(function(n){n.fold(function(){return zt(t,Aw)},function(n){r(t,n),ob(n)})},"#ffffff"):r(t,"remove"===e?"":e)})}},t));return hy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:i.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[n,u.asSpec()]}]),fieldBehaviours:Sa([Qd("form-field-events",[qt(Bw,function(n,t){u.getOpt(n).each(function(n){ro(n.element(),"background-color",t.event().color())}),Lt(n,iy,{name:e.name})}),qt(Dw,function(t,e){hy.getField(t).each(function(n){tl.setValue(n,e.event().value()),ed.getCurrent(t).each(Tg.focus)})}),qt(Aw,function(t,n){hy.getField(t).each(function(n){ed.getCurrent(t).each(Tg.focus)})})])])})}function cx(n,t,e){return{hue:nn(n),saturation:nn(t),value:nn(e)}}function sx(t){return kl({name:t+"-edge",overrides:function(n){return n.model.manager.edgeActions[t].fold(function(){return{}},function(o){return{events:Gt([Kt(Ir(),function(n,t,e){return o(n,e)},[n]),Kt(Nr(),function(n,t,e){return o(n,e)},[n]),Kt(Pr(),function(n,t,e){e.mouseIsDown.get()&&o(n,e)},[n])])}})}})}function fx(n){var t=n.event().raw();if(function(n){return-1!==n.type.indexOf("touch")}(t)){var e=t;return e.touches!==undefined&&1===e.touches.length?tn.some(e.touches[0]).map(function(n){return Cu(n.clientX,n.clientY)}):tn.none()}var o=t;return o.clientX!==undefined?tn.some(o).map(function(n){return Cu(n.clientX,n.clientY)}):tn.none()}function lx(n){return n.model.minX}function dx(n){return n.model.minY}function mx(n){return n.model.minX-1}function gx(n){return n.model.minY-1}function px(n){return n.model.maxX}function hx(n){return n.model.maxY}function vx(n){return n.model.maxX+1}function bx(n){return n.model.maxY+1}function yx(n,t,e){return t(n)-e(n)}function xx(n){return yx(n,px,lx)}function wx(n){return yx(n,hx,dx)}function Sx(n){return xx(n)/2}function Cx(n){return wx(n)/2}function kx(n){return n.stepSize}function Ox(n){return n.snapToGrid}function _x(n){return n.snapStart}function Tx(n){return n.rounded}function Ex(n,t){return n[t+"-edge"]!==undefined}function Bx(n){return Ex(n,"left")}function Dx(n){return Ex(n,"right")}function Ax(n){return Ex(n,"top")}function Mx(n){return Ex(n,"bottom")}function Fx(n){return n.model.value.get()}function Ix(n){return{x:nn(n)}}function Rx(n){return{y:nn(n)}}function Vx(n,t){return{x:nn(n),y:nn(t)}}function Hx(n,t){Lt(n,Ww(),{value:t})}function Nx(n,t,e,o){return n<t?n:e<n?e:n===t?t-1:Math.max(t,n-o)}function Px(n,t,e,o){return e<n?n:n<t?t:n===e?e+1:Math.min(e,n+o)}function zx(n,t,e){return Math.max(t,Math.min(e,n))}function Lx(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.step,u=n.snap,a=n.snapStart,c=n.rounded,s=n.hasMinEdge,f=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=s?t-1:t,p=f?e+1:e;if(r<l)return g;if(d<r)return p;var h=function(n,t,e){return Math.min(e,Math.max(n,t))-t}(r,l,d),v=zx(h/m*o+t,g,p);return u&&t<=v&&v<=e?function(u,e,a,c,n){return n.fold(function(){var n=u-e,t=Math.round(n/c)*c;return zx(e+t,e-1,a+1)},function(n){var t=(u-n)%c,e=Math.round(t/c),o=Math.floor((u-n)/c),r=Math.floor((a-n)/c),i=n+Math.min(r,o+e)*c;return Math.max(n,i)})}(v,t,e,i,a):c?Math.round(v):v}function jx(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,a=n.maxBound,c=n.maxOffset,s=n.centerMinEdge,f=n.centerMaxEdge;return r<t?i?0:s:e<r?u?a:f:(r-t)/o*c}function Ux(n){return n.element().dom().getBoundingClientRect()}function Wx(n,t){return n[t]}function Gx(n){var t=Ux(n);return Wx(t,Gw)}function Xx(n){var t=Ux(n);return Wx(t,"right")}function Yx(n){var t=Ux(n);return Wx(t,"top")}function qx(n){var t=Ux(n);return Wx(t,"bottom")}function Kx(n){var t=Ux(n);return Wx(t,"width")}function Jx(n){var t=Ux(n);return Wx(t,"height")}function $x(n,t,e){return(n+t)/2-e}function Qx(n,t){var e=Ux(n),o=Ux(t),r=Wx(e,Gw),i=Wx(e,"right"),u=Wx(o,Gw);return $x(r,i,u)}function Zx(n,t){var e=Ux(n),o=Ux(t),r=Wx(e,"top"),i=Wx(e,"bottom"),u=Wx(o,"top");return $x(r,i,u)}function nw(n,t){Lt(n,Ww(),{value:t})}function tw(n){return{x:nn(n)}}function ew(n,t,e){var o={min:lx(t),max:px(t),range:xx(t),value:e,step:kx(t),snap:Ox(t),snapStart:_x(t),rounded:Tx(t),hasMinEdge:Bx(t),hasMaxEdge:Dx(t),minBound:Gx(n),maxBound:Xx(n),screenRange:Kx(n)};return Lx(o)}function ow(e){return function(n,t){return function(n,t,e){var o=(0<n?Px:Nx)(Fx(e).x(),lx(e),px(e),kx(e));return nw(t,tw(o)),tn.some(o)}(e,n,t).map(function(){return!0})}}function rw(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=Kx(t),u=o.bind(function(n){return tn.some(Qx(n,t))}).getOr(0),a=r.bind(function(n){return tn.some(Qx(n,t))}).getOr(i),c={min:lx(n),max:px(n),range:xx(n),value:e,hasMinEdge:Bx(n),hasMaxEdge:Dx(n),minBound:Gx(t),minOffset:0,maxBound:Xx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return jx(c)}(t,i,e,o,r);return Gx(t)-Gx(n)+u}function iw(n,t){Lt(n,Ww(),{value:t})}function uw(n){return{y:nn(n)}}function aw(n,t,e){var o={min:dx(t),max:hx(t),range:wx(t),value:e,step:kx(t),snap:Ox(t),snapStart:_x(t),rounded:Tx(t),hasMinEdge:Ax(t),hasMaxEdge:Mx(t),minBound:Yx(n),maxBound:qx(n),screenRange:Jx(n)};return Lx(o)}function cw(e){return function(n,t){return function(n,t,e){var o=(0<n?Px:Nx)(Fx(e).y(),dx(e),hx(e),kx(e));return iw(t,uw(o)),tn.some(o)}(e,n,t).map(function(){return!0})}}function sw(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=Jx(t),u=o.bind(function(n){return tn.some(Zx(n,t))}).getOr(0),a=r.bind(function(n){return tn.some(Zx(n,t))}).getOr(i),c={min:dx(n),max:hx(n),range:wx(n),value:e,hasMinEdge:Ax(n),hasMaxEdge:Mx(n),minBound:Yx(t),minOffset:0,maxBound:qx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return jx(c)}(t,i,e,o,r);return Yx(t)-Yx(n)+u}function fw(n,t){Lt(n,Ww(),{value:t})}function lw(n,t){return{x:nn(n),y:nn(t)}}function dw(e,o){return function(n,t){return function(n,t,e,o){var r=0<n?Px:Nx,i=t?Fx(o).x():r(Fx(o).x(),lx(o),px(o),kx(o)),u=t?r(Fx(o).y(),dx(o),hx(o),kx(o)):Fx(o).y();return fw(e,lw(i,u)),tn.some(i)}(e,o,n,t).map(function(){return!0})}}function mw(n){return"<alloy.field."+n+">"}function gw(d,m,g,p){function h(n,t,e,o,r){var i=d(vS+"range"),u=[hy.parts().label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),hy.parts().field({data:r,factory:by,inputAttributes:P({type:"text"},"hex"===t?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:Sa([function(t,o){return Ly.config({invalidClass:m("invalid"),notify:{onValidate:function(n){Lt(n,hS,{type:t})},onValid:function(n){Lt(n,gS,{type:t,value:tl.getValue(n)})},onInvalid:function(n){Lt(n,pS,{type:t,value:tl.getValue(n)})}},validator:{validate:function(n){var t=tl.getValue(n),e=o(t)?K.value(!0):K.error(d("aria.input.invalid"));return Vy(e)},validateOnLoad:!1}})}(t,n),Wy.config({})]),onSetValue:function(n){Ly.isInvalid(n)&&Ly.run(n).get(Z)}})],a="hex"!==t?[hy.parts()["aria-descriptor"]({text:i})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(a)}}function v(n,t){var e=t.red(),o=t.green(),r=t.blue();tl.setValue(n,{red:e,green:o,blue:r})}function b(n,t){y.getOpt(n).each(function(n){ro(n.element(),"background-color","#"+t.value())})}var y=dp({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}});return Al({factory:function(){function r(n){return u[n]().get()}function i(n,t){u[n]().set(t)}function t(n,t){var e=t.event();"hex"!==e.type()?i(e.type(),tn.none()):p(n)}function o(e,n,t){var o=parseInt(t,10);i(n,tn.some(o)),r("red").bind(function(e){return r("green").bind(function(t){return r("blue").map(function(n){return lv(e,t,n,1)})})}).each(function(n){var t=function(t,n){var e=fv(n);return mS.getField(t,"hex").each(function(n){Tg.isFocused(n)||tl.setValue(t,{hex:e.value()})}),e}(e,n);b(e,t)})}function e(n,t){var e=t.event();!function(n){return"hex"===n.type()}(e)?o(n,e.type(),e.value()):function(n,t){g(n);var e=uv(t);i("hex",tn.some(t));var o=gv(e);v(n,o),a(o),Lt(n,Mw(),{hex:e}),b(n,e)}(n,e.value())}function n(n){return{label:d(vS+n+".label"),description:d(vS+n+".description")}}var u={red:nn(or(tn.some(255))),green:nn(or(tn.some(255))),blue:nn(or(tn.some(255))),hex:nn(or(tn.some("ffffff")))},a=function(n){var t=n.red(),e=n.green(),o=n.blue();i("red",tn.some(t)),i("green",tn.some(e)),i("blue",tn.some(o))},c=n("red"),s=n("green"),f=n("blue"),l=n("hex");return Sn(mS.sketch(function(n){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[n.field("red",hy.sketch(h(dv,"red",c.label,c.description,255))),n.field("green",hy.sketch(h(dv,"green",s.label,s.description,255))),n.field("blue",hy.sketch(h(dv,"blue",f.label,f.description,255))),n.field("hex",hy.sketch(h(av,"hex",l.label,l.description,"ffffff"))),y.asSpec()],formBehaviours:Sa([Ly.config({invalidClass:m("form-invalid")}),Qd("rgb-form-events",[qt(gS,e),qt(pS,t),qt(hS,t)])])}}),{apis:{updateHex:function(n,t){tl.setValue(n,{hex:t.value()}),function(n,t){var e=gv(t);v(n,e),a(e)}(n,t),b(n,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(n,t,e){n.updateHex(t,e)}},extraApis:{}})}function pw(n,o){function r(n,t){var e=n.width,o=n.height,r=n.getContext("2d");if(null!==r){r.fillStyle=t,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)}}var i=fS.parts().spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),u=fS.parts().thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}});return Al({factory:function(n){var t=nn({x:nn(0),y:nn(0)}),e=Sa([ed.config({find:tn.some}),Tg.config({})]);return fS.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:t},rounded:!1,components:[i,u],onChange:function(n,t,e){Lt(n,Iw(),{value:e})},onInit:function(n,t,e,o){r(e.element().dom(),hv(kv()))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(n,t,e){!function(n,t){var e=n.components()[0].element().dom();r(e,hv(t))}(t,e)}},extraApis:{}})}function hw(l,d){return Al({name:"ColourPicker",configFields:[tt("dom"),pt("onValidHex",Z),pt("onInvalidHex",Z)],factory:function(n){function t(n,e){u.getOpt(n).each(function(n){var t=gv(e);s.paletteRgba().set(t),i.setRgba(n,t)})}function e(n,t){f.getOpt(n).each(function(n){r.updateHex(n,t)})}function a(t,e,n){fn(n,function(n){n(t,e)})}var o,c,r=gw(l,d,n.onValidHex,n.onInvalidHex),i=pw(0,d),s={paletteRgba:nn(or(kv()))},u=dp(i.sketch({})),f=dp(r.sketch({}));return{uid:n.uid,dom:n.dom,components:[u.asSpec(),function(n,t){var e=fS.parts().spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=fS.parts().thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return fS.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:nn({y:nn(0)})},components:[e,o],sliderBehaviours:Sa([Tg.config({})]),onChange:function(n,t,e){Lt(n,Fw(),{value:e})}})}(0,d),f.asSpec()],behaviours:Sa([Qd("colour-picker-events",[qt(Iw(),(c=[e],function(n,t){var e=t.event().value(),o=function(n){var t,e=0,o=0,r=n.red()/255,i=n.green()/255,u=n.blue()/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?cx(0,0,100*(o=a)):(e=60*((e=r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),t=(c-a)/c,o=c,cx(Math.round(e),Math.round(100*t),Math.round(100*o)))}(s.paletteRgba().get()),r=cx(o.hue(),e.x(),100-e.y()),i=mv(r),u=fv(i);a(n,u,c)})),qt(Fw(),(o=[t,e],function(n,t){var e=function(n){var t=cx((100-n)/100*360,100,100),e=mv(t);return fv(e)}(t.event().value().y());a(n,e,o)}))]),ed.config({find:function(n){return f.getOpt(n)}}),wg.config({mode:"acyclic"})])}}})}function vw(n){return function(n){return wS[n]}(n)}function bw(n,t,e){return tl.config(Sn({store:{mode:"manual",getValue:t,setValue:e}},n.map(function(n){return{store:{initialValue:n}}}).getOr({})))}function yw(n,t,e){return bw(n,function(n){return t(n.element())},function(n,t){return e(n.element(),t)})}function xw(e,t){function o(n,t){t.stop()}function r(n){return function(t,e){fn(n,function(n){n(t,e)})}}function i(n,t){if(!Gh.isDisabled(n)){var e=t.event().raw();a(n,e.dataTransfer.files)}}function u(n,t){var e=t.event().raw().target.files;a(n,e)}var a=function(n,t){tl.setValue(n,function(n){var t=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i");return C(dn(n),function(n){return t.test(n.name)})}(t)),Lt(n,iy,{name:e.name})},c=dp({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:Sa([Qd("input-file-events",[Zt(Kr()),Zt(ii())])])}),n=e.label.map(function(n){return Ey(n,t)}),s=hy.parts().field({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:Sa([TS([]),bS(),Gh.config({}),Fg.config({toggleClass:"dragenter",toggleOnExecute:!1}),Qd("dropzone-events",[qt("dragenter",r([o,Fg.toggle])),qt("dragleave",r([o,Fg.toggle])),qt("dragover",o),qt("drop",r([o,i])),qt(qr(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:t.translate("Drop an image here")}},hp.sketch({dom:{tag:"button",innerHtml:t.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(n){c.get(n).element().dom().click()},buttonBehaviours:Sa([Wy.config({})])})]}]}}}});return Ty(n,s,["tox-form__group--stretched"],[])}function ww(n){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:n},behaviours:Sa([Tg.config({ignore:!0}),Wy.config({})])}}function Sw(n,t){Lt(n,Gr(),{raw:{which:9,shiftKey:t}})}function Cw(n,t){var e=FS&&n.sandboxed,o=P(P({},n.label.map(function(n){return{title:n}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),r=function(o){var r=or("");return{getValue:function(n){return r.get()},setValue:function(n,t){if(o)Ce(n.element(),"srcdoc",t);else{Ce(n.element(),"src","javascript:''");var e=n.element().dom().contentWindow.document;e.open(),e.write(t),e.close()}r.set(t)}}}(e),i=n.label.map(function(n){return Ey(n,t)}),u=hy.parts().field({factory:{sketch:function(n){return MS({uid:n.uid,dom:{tag:"iframe",attributes:o},behaviours:Sa([Wy.config({}),Tg.config({}),OS(tn.none(),r.getValue,r.setValue)])})}}});return Ty(i,u,["tox-form__group--stretched"],[])}var kw=function(t,n){return t.getSystem().getByUid(n.uid+"-"+Jy()).map(function(n){return function(){return K.value(n)}}).getOrThunk(function(){return n.lazySink.fold(function(){return function(){return K.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(n){return function(){return n(t)}})})},Ow=nn([tt("dom"),tt("fetch"),Ku("onOpen"),Ju("onExecute"),pt("getHotspot",tn.some),pt("getAnchorOverrides",nn({})),Rc(),Ls("dropdownBehaviours",[Fg,Ky,wg,Tg]),tt("toggleClass"),pt("eventOrder",{}),st("lazySink"),pt("matchWidth",!1),pt("useMinWidth",!1),st("role")].concat(ux())),_w=nn([Cl({schema:[Xu()],name:"menu",defaults:function(n){return{onExecute:n.onExecute}}}),$y()]),Tw=Ml({name:"Dropdown",configFields:Ow(),partFields:_w(),factory:function(t,n,e,o){function r(n){jf.getState(n).each(function(n){$g.highlightPrimary(n)})}function i(n,t){return jt(n),tn.some(!0)}var u,a,c={expand:function(n){Fg.isOn(n)||tx(t,function(n){return n},n,o,Z,By.HighlightNone).get(Z)},open:function(n){Fg.isOn(n)||tx(t,function(n){return n},n,o,Z,By.HighlightFirst).get(Z)},isOpen:Fg.isOn,close:function(n){Fg.isOn(n)&&tx(t,function(n){return n},n,o,Z,By.HighlightFirst).get(Z)},repositionMenus:function(n){Fg.isOn(n)&&ix(n)}};return{uid:t.uid,dom:t.dom,components:n,behaviours:Us(t.dropdownBehaviours,[Fg.config({toggleClass:t.toggleClass,aria:{mode:"expanded"}}),Ky.config({others:{sandbox:function(n){return rx(t,n,{onOpen:function(){Fg.on(n)},onClose:function(){Fg.off(n)}})}}}),wg.config({mode:"special",onSpace:i,onEnter:i,onDown:function(n,t){if(Tw.isOpen(n)){var e=Ky.getCoupled(n,"sandbox");r(e)}else Tw.open(n);return tn.some(!0)},onEscape:function(n,t){return Tw.isOpen(n)?(Tw.close(n),tn.some(!0)):tn.none()}}),Tg.config({})]),events:am(tn.some(function(n){tx(t,function(n){return n},n,o,r,By.HighlightFirst).get(Z)})),eventOrder:P(P({},t.eventOrder),(u={},u[oi()]=["disabling","toggling","alloy.base.behaviour"],u)),apis:c,domModification:{attributes:P(P({"aria-haspopup":"true"},t.role.fold(function(){return{}},function(n){return{role:n}})),"button"===t.dom.tag?{type:(a="type",bn(t.dom,"attributes").bind(function(n){return bn(n,a)})).getOr("button")}:{})}}},apis:{open:function(n,t){return n.open(t)},expand:function(n,t){return n.expand(t)},close:function(n,t){return n.close(t)},isOpen:function(n,t){return n.isOpen(t)},repositionMenus:function(n,t){return n.repositionMenus(t)}}}),Ew=Ca({fields:[],name:"unselecting",active:/* */Object.freeze({__proto__:null,events:function(){return Gt([Xt($r(),nn(!0))])},exhibit:function(){return He({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Bw=De("color-input-change"),Dw=De("color-swatch-change"),Aw=De("color-picker-cancel"),Mw=nn(De("rgb-hex-update")),Fw=nn(De("slider-update")),Iw=nn(De("palette-update")),Rw=kl({schema:[tt("dom")],name:"label"}),Vw=sx("top-left"),Hw=sx("top"),Nw=sx("top-right"),Pw=sx("right"),zw=sx("bottom-right"),Lw=sx("bottom"),jw=sx("bottom-left"),Uw=[Rw,sx("left"),Pw,Hw,Lw,Vw,Nw,jw,zw,Sl({name:"thumb",defaults:nn({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:Gt([$t(Ir(),n,"spectrum"),$t(Rr(),n,"spectrum"),$t(Vr(),n,"spectrum"),$t(Nr(),n,"spectrum"),$t(Pr(),n,"spectrum"),$t(Lr(),n,"spectrum")])}}}),Sl({schema:[Ct("mouseIsDown",function(){return or(!1)})],name:"spectrum",overrides:function(e){function o(t,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(t,e,n)})}var r=e.model.manager;return{behaviours:Sa([wg.config({mode:"special",onLeft:function(n){return r.onLeft(n,e)},onRight:function(n){return r.onRight(n,e)},onUp:function(n){return r.onUp(n,e)},onDown:function(n){return r.onDown(n,e)}}),Tg.config({})]),events:Gt([qt(Ir(),o),qt(Rr(),o),qt(Nr(),o),qt(Pr(),function(n,t){e.mouseIsDown.get()&&o(n,t)})])}}})],Ww=nn("slider.change.value"),Gw="left",Xw=ow(-1),Yw=ow(1),qw=tn.none,Kw=tn.none,Jw={"top-left":tn.none(),top:tn.none(),"top-right":tn.none(),right:tn.some(function(n,t){Hx(n,Ix(vx(t)))}),"bottom-right":tn.none(),bottom:tn.none(),"bottom-left":tn.none(),left:tn.some(function(n,t){Hx(n,Ix(mx(t)))})},$w=/* */Object.freeze({__proto__:null,setValueFrom:function(n,t,e){var o=ew(n,t,e),r=tw(o);return nw(n,r),o},setToMin:function(n,t){var e=lx(t);nw(n,tw(e))},setToMax:function(n,t){var e=px(t);nw(n,tw(e))},findValueOfOffset:ew,getValueFromEvent:function(n){return fx(n).map(function(n){return n.left()})},findPositionOfValue:rw,setPositionFromValue:function(n,t,e,o){var r=Fx(e),i=rw(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=cu(t.element())/2;ro(t.element(),"left",i-u+"px")},onLeft:Xw,onRight:Yw,onUp:qw,onDown:Kw,edgeActions:Jw}),Qw=tn.none,Zw=tn.none,nS=cw(-1),tS=cw(1),eS={"top-left":tn.none(),top:tn.some(function(n,t){Hx(n,Rx(gx(t)))}),"top-right":tn.none(),right:tn.none(),"bottom-right":tn.none(),bottom:tn.some(function(n,t){Hx(n,Rx(bx(t)))}),"bottom-left":tn.none(),left:tn.none()},oS=/* */Object.freeze({__proto__:null,setValueFrom:function(n,t,e){var o=aw(n,t,e),r=uw(o);return iw(n,r),o},setToMin:function(n,t){var e=dx(t);iw(n,uw(e))},setToMax:function(n,t){var e=hx(t);iw(n,uw(e))},findValueOfOffset:aw,getValueFromEvent:function(n){return fx(n).map(function(n){return n.top()})},findPositionOfValue:sw,setPositionFromValue:function(n,t,e,o){var r=Fx(e),i=sw(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),u=ru(t.element())/2;ro(t.element(),"top",i-u+"px")},onLeft:Qw,onRight:Zw,onUp:nS,onDown:tS,edgeActions:eS}),rS=dw(-1,!1),iS=dw(1,!1),uS=dw(-1,!0),aS=dw(1,!0),cS={"top-left":tn.some(function(n,t){Hx(n,Vx(mx(t),gx(t)))}),top:tn.some(function(n,t){Hx(n,Vx(Sx(t),gx(t)))}),"top-right":tn.some(function(n,t){Hx(n,Vx(vx(t),gx(t)))}),right:tn.some(function(n,t){Hx(n,Vx(vx(t),Cx(t)))}),"bottom-right":tn.some(function(n,t){Hx(n,Vx(vx(t),bx(t)))}),bottom:tn.some(function(n,t){Hx(n,Vx(Sx(t),bx(t)))}),"bottom-left":tn.some(function(n,t){Hx(n,Vx(mx(t),bx(t)))}),left:tn.some(function(n,t){Hx(n,Vx(mx(t),Cx(t)))})},sS=/* */Object.freeze({__proto__:null,setValueFrom:function(n,t,e){var o=ew(n,t,e.left()),r=aw(n,t,e.top()),i=lw(o,r);return fw(n,i),i},setToMin:function(n,t){var e=lx(t),o=dx(t);fw(n,lw(e,o))},setToMax:function(n,t){var e=px(t),o=hx(t);fw(n,lw(e,o))},getValueFromEvent:function(n){return fx(n)},setPositionFromValue:function(n,t,e,o){var r=Fx(e),i=rw(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=sw(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),a=cu(t.element())/2,c=ru(t.element())/2;ro(t.element(),"left",i-a+"px"),ro(t.element(),"top",u-c+"px")},onLeft:rS,onRight:iS,onUp:uS,onDown:aS,edgeActions:cS}),fS=Ml({name:"Slider",configFields:[pt("stepSize",1),pt("onChange",Z),pt("onChoose",Z),pt("onInit",Z),pt("onDragStart",Z),pt("onDragEnd",Z),pt("snapToGrid",!1),pt("rounded",!0),st("snapStart"),et("model",Qn("mode",{x:[pt("minX",0),pt("maxX",100),Ct("value",function(n){return or(n.mode.minX)}),tt("getInitialValue"),Zu("manager",$w)],y:[pt("minY",0),pt("maxY",100),Ct("value",function(n){return or(n.mode.minY)}),tt("getInitialValue"),Zu("manager",oS)],xy:[pt("minX",0),pt("maxX",100),pt("minY",0),pt("maxY",100),Ct("value",function(n){return or({x:nn(n.mode.minX),y:nn(n.mode.minY)})}),tt("getInitialValue"),Zu("manager",sS)]})),Ls("sliderBehaviours",[wg,tl]),Ct("mouseIsDown",function(){return or(!1)})],partFields:Uw,factory:function(i,n,t,e){function u(n){return rf(n,i,"thumb")}function a(n){return rf(n,i,"spectrum")}function o(n){return of(n,i,"left-edge")}function r(n){return of(n,i,"right-edge")}function c(n){return of(n,i,"top-edge")}function s(n){return of(n,i,"bottom-edge")}function f(n,t){v.setPositionFromValue(n,t,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})}function l(n,t){h.value.set(t);var e=u(n);return f(n,e),i.onChange(n,e,t),tn.some(!0)}function d(e){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&of(e,i,"thumb").each(function(n){var t=h.value.get();i.onChoose(e,n,t)})}function m(n,t){t.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))}function g(n,t){t.stop(),i.onDragEnd(n,u(n)),d(n)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:n,behaviours:Us(i.sliderBehaviours,[wg.config({mode:"special",focusIn:function(n){return of(n,i,"spectrum").map(wg.focusIn).map(nn(!0))}}),tl.config({store:{mode:"manual",getValue:function(n){return h.value.get()}}}),bc.config({channels:(p={},p[Gf()]={onReceive:d},p)})]),events:Gt([qt(Ww(),function(n,t){l(n,t.event().value())}),Oi(function(n,t){var e=h.getInitialValue();h.value.set(e);var o=u(n);f(n,o);var r=a(n);i.onInit(n,o,r,h.value.get())}),qt(Ir(),m),qt(Vr(),g),qt(Nr(),m),qt(Lr(),g)]),apis:{resetToMin:function(n){v.setToMin(n,i)},resetToMax:function(n){v.setToMax(n,i)},changeValue:l,refresh:f},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,t){n.resetToMin(t)},resetToMax:function(n,t){n.resetToMax(t)},refresh:function(n,t){n.refresh(t)}}}),lS=[Ls("formBehaviours",[tl])],dS=function(o,n){return{uid:o.uid,dom:o.dom,components:n,behaviours:Us(o.formBehaviours,[tl.config({store:{mode:"manual",getValue:function(n){var t=af(n,o);return L(t,function(n,t){return n().bind(function(n){return function(n,t){return n.fold(function(){return K.error(t)},K.value)}(ed.getCurrent(n),new Error("Cannot find a current component to extract the value from for form part '"+t+"': "+Be(n.element())))}).map(tl.getValue)})},setValue:function(e,n){pn(n,function(t,n){of(e,o,n).each(function(n){ed.getCurrent(n).each(function(n){tl.setValue(n,t)})})})}}})]),apis:{getField:function(n,t){return of(n,o,t).bind(ed.getCurrent)}}}},mS={getField:Re(function(n,t,e){return n.getField(t,e)}),sketch:function(n){var e,t=(e=[],{field:function(n,t){return e.push(n),Qs("form",mw(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=S(r,function(n){return Sl({name:n,pname:mw(n)})});return mf("form",lS,i,dS,o)}},gS=De("valid-input"),pS=De("invalid-input"),hS=De("validating-input"),vS="colorcustom.rgb.",bS=function(){return ed.config({find:tn.some})},yS=function(n){return ed.config({find:n.getOpt})},xS=function(n){return ed.config({find:function(t){return le(t.element(),n).bind(function(n){return t.getSystem().getByDom(n).toOption()})}})},wS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},SS=tinymce.util.Tools.resolve("tinymce.Resource"),CS=jo([pt("preprocess",l),pt("postprocess",l)]),kS=function(r,n){var i=Jn("RepresentingConfigs.memento processors",CS,n);return tl.config({store:{mode:"manual",getValue:function(n){var t=r.get(n),e=tl.getValue(t);return i.postprocess(e)},setValue:function(n,t){var e=i.preprocess(t),o=r.get(n);tl.setValue(o,e)}}})},OS=bw,_S=function(n){return yw(n,be,ye)},TS=function(n){return tl.config({store:{mode:"memory",initialValue:n}})},ES=De("alloy-fake-before-tabstop"),BS=De("alloy-fake-after-tabstop"),DS=function(n){return Ab(n,["."+ES,"."+BS].join(","),nn(!1))},AS=function(n,t){var e=t.element();$e(e,ES)?Sw(n,!0):$e(e,BS)&&Sw(n,!1)},MS=function(n){return{dom:{tag:"div",classes:["tox-navobj"]},components:[ww([ES]),n,ww([BS])],behaviours:Sa([xS(1)])}},FS=!(At().browser.isIE()||At().browser.isEdge());function IS(n,t){return HS(v.document.createElement("canvas"),n,t)}function RS(n){var t=IS(n.width,n.height);return VS(t).drawImage(n,0,0),t}function VS(n){return n.getContext("2d")}function HS(n,t,e){return n.width=t,n.height=e,n}function NS(n){return n.naturalWidth||n.width}function PS(n){return n.naturalHeight||n.height}var zS,LS,jS=window.Promise?window.Promise:(zS=US.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){v.setTimeout(n,1)},LS=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},US.prototype["catch"]=function(n){return this.then(null,n)},US.prototype.then=function(e,o){var r=this;return new US(function(n,t){GS.call(r,new KS(e,o,n,t))})},US.all=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var c=Array.prototype.slice.call(1===n.length&&LS(n[0])?n[0]:n);return new US(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},US.resolve=function(t){return t&&"object"==typeof t&&t.constructor===US?t:new US(function(n){n(t)})},US.reject=function(e){return new US(function(n,t){t(e)})},US.race=function(r){return new US(function(n,t){for(var e=0,o=r;e<o.length;e++)o[e].then(n,t)})},US);function US(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],JS(n,WS(XS,this),WS(YS,this))}function WS(n,t){return function(){return n.apply(t,arguments)}}function GS(o){var r=this;null!==this._state?zS(function(){var n=r._state?o.onFulfilled:o.onRejected;if(null!==n){var t;try{t=n(r._value)}catch(e){return void o.reject(e)}o.resolve(t)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function XS(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void JS(WS(t,n),WS(XS,this),WS(YS,this))}this._state=!0,this._value=n,qS.call(this)}catch(e){YS.call(this,e)}}function YS(n){this._state=!1,this._value=n,qS.call(this)}function qS(){for(var n=0,t=this._deferreds;n<t.length;n++){var e=t[n];GS.call(this,e)}this._deferreds=[]}function KS(n,t,e,o){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.resolve=e,this.reject=o}function JS(n,t,e){var o=!1;try{n(function(n){o||(o=!0,t(n))},function(n){o||(o=!0,e(n))})}catch(r){if(o)return;o=!0,e(r)}}function $S(e){return new jS(function(n,t){(function p(n){var t=n.split(","),e=/data:([^;]+)/.exec(t[0]);if(!e)return tn.none();for(var o=e[1],r=t[1],i=v.atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var f=1024*s,l=Math.min(1024+f,u),d=new Array(l-f),m=f,g=0;m<l;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return tn.some(new v.Blob(c,{type:o}))})(e).fold(function(){t("uri is not base64: "+e)},n)})}function QS(n,o,r){return o=o||"image/png",v.HTMLCanvasElement.prototype.toBlob?new jS(function(t,e){n.toBlob(function(n){n?t(n):e()},o,r)}):$S(n.toDataURL(o,r))}function ZS(n){return function t(a){return new jS(function(n,t){var e=v.URL.createObjectURL(a),o=new v.Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),n(o)}function u(){r(),t("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()})}(n).then(function(n){!function e(n){v.URL.revokeObjectURL(n.src)}(n);var t=IS(NS(n),PS(n));return VS(t).drawImage(n,0,0),t})}function nC(n,t,e){var o=t.type;function r(t,e){return n.then(function(n){return function o(n,t,e){return t=t||"image/png",n.toDataURL(t,e)}(n,t,e)})}return{getType:nn(o),toBlob:function i(){return jS.resolve(t)},toDataURL:function u(){return e},toBase64:function a(){return e.split(",")[1]},toAdjustedBlob:function c(t,e){return n.then(function(n){return QS(n,t,e)})},toAdjustedDataURL:r,toAdjustedBase64:function s(n,t){return r(n,t).then(function(n){return n.split(",")[1]})},toCanvas:function f(){return n.then(RS)}}}function tC(t){return function n(e){return new jS(function(n){var t=new v.FileReader;t.onloadend=function(){n(t.result)},t.readAsDataURL(e)})}(t).then(function(n){return nC(ZS(t),t,n)})}function eC(t,n){return QS(t,n).then(function(n){return nC(jS.resolve(t),n,t.toDataURL())})}function oC(n,t,e){var o="string"==typeof n?parseFloat(n):n;return e<o?o=e:o<t&&(o=t),o}var rC=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function iC(n,t){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=t[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=n[u+5*a]*o[a];r[u+5*i]=e}}return r}function uC(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=VS(n);var r=function E(n,t){for(var e,o,r,i,u=n.data,a=t[0],c=t[1],s=t[2],f=t[3],l=t[4],d=t[5],m=t[6],g=t[7],p=t[8],h=t[9],v=t[10],b=t[11],y=t[12],x=t[13],w=t[14],S=t[15],C=t[16],k=t[17],O=t[18],_=t[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*f+l,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*C+r*k+i*O+_;return n}(o.getImageData(0,0,n.width,n.height),e);return o.putImageData(r,0,0),eC(n,t)}(n,t.getType(),e)})}function aC(t,e){return t.toCanvas().then(function(n){return function u(n,t,e){var o=VS(n);var r=o.getImageData(0,0,n.width,n.height),i=o.getImageData(0,0,n.width,n.height);return i=function w(n,t,e){function o(n,t,e){return e<n?n=e:n<t&&(n=t),n}for(var r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=n.data,a=t.data,c=n.width,s=n.height,f=0;f<s;f++)for(var l=0;l<c;l++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(l+h-i,0,c-1),b=4*(o(f+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(f*c+l);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return t}(r,i,e),o.putImageData(i,0,0),eC(n,t)}(n,t.getType(),e)})}function cC(e){return function(n,t){return uC(n,e([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t))}}function sC(n,t,e,o){return uC(n,function r(n,t,e,o){return iC(n,[t=oC(t,0,2),0,0,0,0,0,e=oC(e,0,2),0,0,0,0,0,o=oC(o,0,2),0,0,0,0,0,1,0,0,0,0,0,1])}([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t,e,o))}var fC=function fI(t){return function(n){return uC(n,t)}}([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1]),lC=cC(function lI(n,t){return iC(n,[1,0,0,0,t=oC(255*t,-255,255),0,1,0,0,t,0,0,1,0,t,0,0,0,1,0,0,0,0,0,1])}),dC=cC(function dI(n,t){var e;return t=oC(t,-1,1),iC(n,[(e=(t*=100)<0?127+t/100*127:127*(e=0===(e=t%1)?rC[t]:rC[Math.floor(t)]*(1-e)+rC[Math.floor(t)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),mC=function mI(t){return function(n){return aC(n,t)}}([0,-1,0,-1,5,-1,0,-1,0]),gC=function gI(c){return function(t,e){return t.toCanvas().then(function(n){return function(n,t,e){var o=VS(n),r=new Array(256);for(var i=0;i<r.length;i++)r[i]=c(i,e);var u=function a(n,t){for(var e=n.data,o=0;o<e.length;o+=4)e[o]=t[e[o]],e[o+1]=t[e[o+1]],e[o+2]=t[e[o+2]];return n}(o.getImageData(0,0,n.width,n.height),r);return o.putImageData(u,0,0),eC(n,t)}(n,t.getType(),e)})}}(function(n,t){return 255*Math.pow(n/255,1-t)});function pC(n,t,e){var o=NS(n),r=PS(n),i=t/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=function s(u,a,c){return new jS(function(n){var t=NS(u),e=PS(u),o=Math.floor(t*a),r=Math.floor(e*c),i=IS(o,r);VS(i).drawImage(u,0,0,t,e,0,0,o,r),n(i)})}(n,i,u);return a?c.then(function(n){return pC(n,t,e)}):c}function hC(t,e){return t.toCanvas().then(function(n){return function a(n,t,e){var o=IS(n.width,n.height),r=VS(o),i=0,u=0;90!==(e=e<0?360+e:e)&&270!==e||HS(o,o.height,o.width);90!==e&&180!==e||(i=o.width);270!==e&&180!==e||(u=o.height);return r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(n,0,0),eC(o,t)}(n,t.getType(),e)})}function vC(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=IS(n.width,n.height),r=VS(o);"v"===e?(r.scale(1,-1),r.drawImage(n,0,-o.height)):(r.scale(-1,1),r.drawImage(n,-o.width,0));return eC(o,t)}(n,t.getType(),e)})}function bC(t,e,o,r,i){return t.toCanvas().then(function(n){return function a(n,t,e,o,r,i){var u=IS(r,i);return VS(u).drawImage(n,-e,-o),eC(u,t)}(n,t.getType(),e,o,r,i)})}function yC(n){return fC(n)}function xC(n){return mC(n)}function wC(n,t){return gC(n,t)}function SC(n,t){return lC(n,t)}function CC(n,t){return dC(n,t)}function kC(n,t){return vC(n,t)}function OC(n,t,e){return function r(t,e,o){return t.toCanvas().then(function(n){return pC(n,e,o).then(function(n){return eC(n,t.getType())})})}(n,t,e)}function _C(n,t){return hC(n,t)}function TC(n,t){return P({dom:{tag:"span",innerHtml:n,classes:["tox-icon","tox-tbtn__icon-wrap"]}},t)}function EC(n,t){return TC(gp(n,t),{})}function BC(n,t){return TC(gp(n,t),{behaviours:Sa([Cg.config({})])})}function DC(n,t,e){return{dom:{tag:"span",innerHtml:e.translate(n),classes:[t+"__select-label"]},behaviours:Sa([Cg.config({})])}}function AC(n,t,o){function e(n,t){var e=tl.getValue(n);return Tg.focus(e),Lt(e,"keydown",{raw:t.event().raw()}),Tw.close(e),tn.some(!0)}var r=or(Z),i=n.text.map(function(n){return dp(DC(n,t,o.providers))}),u=n.icon.map(function(n){return dp(BC(n,o.providers.icons))}),a=n.role.fold(function(){return{}},function(n){return{role:n}}),c=n.tooltip.fold(function(){return{}},function(n){var t=o.providers.translate(n);return{title:t,"aria-label":t}});return dp(Tw.sketch(P(P({},a),{dom:{tag:"button",classes:[t,t+"--select"].concat(S(n.classes,function(n){return t+"--"+n})),attributes:P({},c)},components:Jh([u.map(function(n){return n.asSpec()}),i.map(function(n){return n.asSpec()}),tn.some({dom:{tag:"div",classes:[t+"__select-chevron"],innerHtml:gp("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:Sa(p(n.dropdownBehaviours,[Yh(n.disabled),Ew.config({}),Cg.config({}),Qd("dropdown-events",[Qp(n,r),Zp(n,r)]),Qd("menubutton-update-display-text",[qt(ek,function(t,e){i.bind(function(n){return n.getOpt(t)}).each(function(n){Cg.set(n,[wo(o.providers.translate(e.event().text()))])})}),qt(ok,function(t,e){u.bind(function(n){return n.getOpt(t)}).each(function(n){Cg.set(n,[BC(e.event().icon(),o.providers.icons)])})})])])),eventOrder:Sn(tk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:Sa([wg.config({mode:"special",onLeft:e,onRight:e})]),lazySink:o.getSink,toggleClass:t+"--active",parts:{menu:zv(0,n.columns,n.presets)},fetch:function(){return Ry(n.fetch)}}))).asSpec()}function MC(n){return"separator"===n.type}function FC(n,e){var t=O(n,function(n,t){return function(n){return J(n)}(t)?""===t?n:"|"===t?0<n.length&&!MC(n[n.length-1])?n.concat([rk]):n:yn(e,t.toLowerCase())?n.concat([e[t.toLowerCase()]]):n:n.concat([t])},[]);return 0<t.length&&MC(t[t.length-1])&&t.pop(),t}function IC(n,t){return function(n){return yn(n,"getSubmenuItems")}(n)?function(n,t){var e=n.getSubmenuItems(),o=ik(e,t);return{item:n,menus:Sn(o.menus,Dn(n.value,o.items)),expansions:Sn(o.expansions,Dn(n.value,n.value))}}(n,t):{item:n,menus:{},expansions:{}}}function RC(n,e,o,t){var r=De("primary-menu"),i=ik(n,o.shared.providers.menuItems());if(0===i.items.length)return tn.none();var u=Tb(r,i.items,e,o,t),a=L(i.menus,function(n,t){return Tb(t,n,e,o,!1)}),c=Sn(a,Dn(r,u));return tn.from($g.tieredData(r,c,i.expansions))}function VC(e){return{isDisabled:function(){return Gh.isDisabled(e)},setDisabled:function(n){return Gh.set(e,n)},setActive:function(n){var t=e.element();n?(qe(t,"tox-tbtn--enabled"),Ce(t,"aria-pressed",!0)):(Je(t,"tox-tbtn--enabled"),_e(t,"aria-pressed"))},isActive:function(){return $e(e.element(),"tox-tbtn--enabled")}}}function HC(n,t,e,o){return AC({text:n.text,icon:n.icon,tooltip:n.tooltip,role:o,fetch:function(t){n.fetch(function(n){t(RC(n,tv.CLOSE_ON_EXECUTE,e,!1))})},onSetup:n.onSetup,getApi:VC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Wy.config({})]},t,e.shared)}function NC(t,o,r){return function(n){n(S(t,function(n){var t=n.text.fold(function(){return{}},function(n){return{text:n}});return P(P({type:n.type,active:!1},t),{onAction:function(e){return function(n){var t=!n.isActive();n.setActive(t),e.storage.set(t),r.shared.getSink().each(function(n){o().getOpt(n).each(function(n){Oa(n.element()),Lt(n,cy,{name:e.name,value:e.storage.get()})})})}}(n),onSetup:function(t){return function(n){n.setActive(t.storage.get())}}(n)})}))}}function PC(n,t,e,o,r){void 0===e&&(e=[]);var i=t.fold(function(){return{}},function(n){return{action:n}}),u=P({buttonBehaviours:Sa([Yh(n.disabled),Wy.config({}),Qd("button press",[Yt("click"),Yt("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},i),a=Sn(u,{dom:o});return Sn(a,{components:r})}function zC(n,t,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:n.tooltip.map(function(n){return{"aria-label":e.translate(n),title:e.translate(n)}}).getOr({})},i=n.icon.map(function(n){return EC(n,e.icons)}),u=Jh([i]);return PC(n,t,o,r,u)}function LC(n,t,e,o){void 0===o&&(o=[]);var r=zC(n,tn.some(t),e,o);return hp.sketch(r)}function jC(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(n.text),u=n.icon?n.icon.map(function(n){return EC(n,e.icons)}):tn.none(),a=u.isSome()?Jh([u]):[],c=u.isSome()?{}:{innerHtml:i},s=p(n.primary||n.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],n.borderless?["tox-button--naked"]:[],r),f=P(P({tag:"button",classes:s},c),{attributes:{title:i}});return PC(n,t,o,f,a)}function UC(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=jC(n,tn.some(t),e,o,r);return hp.sketch(i)}function WC(t,e){return function(n){"custom"===e?Lt(n,cy,{name:t,value:{}}):"submit"===e?zt(n,sy):"cancel"===e?zt(n,ay):v.console.error("Unknown button type: ",e)}}function GC(t,n,e){if(function(n,t){return"menu"===t}(0,n)){var o=t,r=P(P({},t),{onSetup:function(n){return n.setDisabled(t.disabled),Z},fetch:NC(o.items,function(){return i},e)}),i=dp(HC(r,"tox-tbtn",e,tn.none()));return i.asSpec()}if(function(n,t){return"custom"===t||"cancel"===t||"submit"===t}(0,n)){var u=WC(t.name,n),a=P(P({},t),{borderless:!1});return UC(a,u,e.shared.providers,[])}v.console.error("Unknown footer button type: ",n)}function XC(n,t){var e=WC(n.name,"custom");return function(n,t){return Ty(n,t,[],[])}(tn.none(),hy.parts().field(P({factory:hp},jC(n,tn.some(e),t,[TS(""),bS()]))))}function YC(n,t){return Sl({factory:hy,name:n,overrides:function(o){return{fieldBehaviours:Sa([Qd("coupled-input-behaviour",[qt(Yr(),function(e){(function(n,t,e){return of(n,t,e).bind(ed.getCurrent)})(e,o,t).each(function(t){of(e,o,"lock").each(function(n){Fg.isOn(n)&&o.onLockedChange(e,t,n)})})})])])}}})}function qC(n){var t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(n);if(null===t)return K.error(n);var e=parseFloat(t[1]),o=t[2];return K.value({value:e,unit:o})}function KC(n,t){function e(n){return Object.prototype.hasOwnProperty.call(o,n)}var o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1};return n.unit===t?tn.some(n.value):e(n.unit)&&e(t)?o[n.unit]===o[t]?tn.some(n.value):tn.some(n.value/o[n.unit]*o[t]):tn.none()}function JC(n){return tn.none()}function $C(n,t){return function(n,t,e){return n.isSome()&&t.isSome()?tn.some(e(n.getOrDie(),t.getOrDie())):tn.none()}(qC(n).toOption(),qC(t).toOption(),function(n,t){return KC(n,t.unit).map(function(n){return t.value/n}).map(function(n){return function(t,e){return function(n){return KC(n,e).map(function(n){return{value:n*t,unit:e}})}}(n,t.unit)}).getOr(JC)}).getOr(JC)}function QC(o,t){function n(n){return{dom:{tag:"div",classes:["tox-form__group"]},components:n}}function e(e){return hy.parts().field({factory:by,inputClasses:["tox-textfield"],inputBehaviours:Sa([Gh.config({disabled:o.disabled}),Wy.config({}),Qd("size-input-events",[qt(Ur(),function(n,t){Lt(n,i,{isField1:e})}),qt(qr(),function(n,t){Lt(n,iy,{name:o.name})})])]),selectOnFocus:!1})}function r(n){return{dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}}}var a=JC,i=De("ratio-event"),u=ck.parts().lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:gp("lock",t.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:gp("unlock",t.icons)}}],buttonBehaviours:Sa([Yh(o.disabled),Wy.config({})])}),c=ck.parts().field1(n([hy.parts().label(r("Width")),e(!0)])),s=ck.parts().field2(n([hy.parts().label(r("Height")),e(!1)]));return ck.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,n([r("&nbsp;"),u])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(n,t,e){qC(tl.getValue(n)).each(function(n){a(n).each(function(n){tl.setValue(t,function(n){var t,e={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},o=n.value.toFixed((t=n.unit)in e?e[t]:1);return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+n.unit}(n))})})},coupledFieldBehaviours:Sa([Gh.config({disabled:o.disabled,onDisabled:function(n){ck.getField1(n).bind(hy.getField).each(Gh.disable),ck.getField2(n).bind(hy.getField).each(Gh.disable),ck.getLock(n).each(Gh.disable)},onEnabled:function(n){ck.getField1(n).bind(hy.getField).each(Gh.enable),ck.getField2(n).bind(hy.getField).each(Gh.enable),ck.getLock(n).each(Gh.enable)}}),Qd("size-input-events2",[qt(i,function(n,t){var e=t.event().isField1(),o=e?ck.getField1(n):ck.getField2(n),r=e?ck.getField2(n):ck.getField1(n),i=o.map(tl.getValue).getOr(""),u=r.map(tl.getValue).getOr("");a=$C(i,u)})])])})}function ZC(r,c){function n(n,t,e,o){return dp(UC({name:n,text:n,disabled:e,primary:o,icon:tn.none(),borderless:!1},t,c))}function t(n,t,e,o){return dp(LC({name:n,icon:tn.some(n),tooltip:tn.some(t),disabled:o,primary:!1,borderless:!1},e,c))}function u(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(Gh)&&Gh.disable(t)})}function a(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(Gh)&&Gh.enable(t)})}function i(n,t,e){Lt(n,t,e)}function e(n){return zt(n,mk.disable())}function o(n){return zt(n,mk.enable())}function s(n,t){e(n),i(n,sk.transform(),{transform:t}),o(n)}function f(n){return function(){Q.getOpt(n).each(function(n){Cg.set(n,[J])})}}function l(n,t){e(n),i(n,sk.transformApply(),{transform:t,swap:f(n)}),o(n)}function d(){return n("Back",function(n){return i(n,sk.back(),{swap:f(n)})},!1,!1)}function m(){return dp({dom:{tag:"div",classes:["tox-spacer"]},behaviours:Sa([Gh.config({})])})}function g(){return n("Apply",function(n){return i(n,sk.apply(),{swap:f(n)})},!0,!0)}function p(){return function(n){var t=r.getRect();return function(n,t,e,o,r){return bC(n,t,e,o,r)}(n,t.x,t.y,t.w,t.h)}}function h(t,e){return function(n){return t(n,e)}}function v(n,t){!function(n,t){e(n),i(n,sk.tempTransform(),{transform:t}),o(n)}(n,t)}function b(n,t,e,o,r){var i=fS.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(n)}}),u=fS.parts().spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=fS.parts().thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return dp(fS.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:nn({x:nn(o)})},components:[i,u,a],sliderBehaviours:Sa([Tg.config({})]),onChoose:t}))}function y(n,t,e,o,r){return[d(),function(n,r,t,e,o){return b(n,function(n,t,e){var o=h(r,e.x()/100);s(n,o)},t,e,o)}(n,t,e,o,r),g()]}function x(n,t,e,o,r){var i=y(n,t,e,o,r);return ry.sketch({dom:k,components:i.map(function(n){return n.asSpec()}),containerBehaviours:Sa([Qd("image-tools-filter-panel-buttons-events",[qt(mk.disable(),function(n,t){u(i,n)}),qt(mk.enable(),function(n,t){a(i,n)})])])})}function w(t,e,o){return function(n){return function(n,t,e,o){return sC(n,t,e,o)}(n,t,e,o)}}function S(n){return b(n,function(a,n,t){var e=j.getOpt(a),o=W.getOpt(a),r=U.getOpt(a);e.each(function(u){o.each(function(i){r.each(function(n){var t=tl.getValue(u).x()/100,e=tl.getValue(n).x()/100,o=tl.getValue(i).x()/100,r=w(t,e,o);s(a,r)})})})},0,100,200)}function C(t,e,o){return function(n){i(n,sk.swap(),{transform:e,swap:function(){Q.getOpt(n).each(function(n){Cg.set(n,[t]),o(n)})}})}}var k={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},O=Z,_=[d(),m(),n("Apply",function(n){var t=p();l(n,t),r.hideCrop()},!1,!0)],T=ry.sketch({dom:k,components:_.map(function(n){return n.asSpec()}),containerBehaviours:Sa([Qd("image-tools-crop-buttons-events",[qt(mk.disable(),function(n,t){u(_,n)}),qt(mk.enable(),function(n,t){a(_,n)})])])}),E=dp(QC({name:"size",label:tn.none(),constrain:!0,disabled:!1},c)),B=[d(),m(),E,m(),n("Apply",function(o){E.getOpt(o).each(function(n){var t=tl.getValue(n),e=function(t,e){return function(n){return OC(n,t,e)}}(parseInt(t.width,10),parseInt(t.height,10));l(o,e)})},!1,!0)],D=ry.sketch({dom:k,components:B.map(function(n){return n.asSpec()}),containerBehaviours:Sa([Qd("image-tools-resize-buttons-events",[qt(mk.disable(),function(n,t){u(B,n)}),qt(mk.enable(),function(n,t){a(B,n)})])])}),A=h(kC,"h"),M=h(kC,"v"),F=h(_C,-90),I=h(_C,90),R=[d(),m(),t("flip-horizontally","Flip horizontally",function(n){v(n,A)},!1),t("flip-vertically","Flip vertically",function(n){v(n,M)},!1),t("rotate-left","Rotate counterclockwise",function(n){v(n,F)},!1),t("rotate-right","Rotate clockwise",function(n){v(n,I)},!1),m(),g()],V=ry.sketch({dom:k,components:R.map(function(n){return n.asSpec()}),containerBehaviours:Sa([Qd("image-tools-fliprotate-buttons-events",[qt(mk.disable(),function(n,t){u(R,n)}),qt(mk.enable(),function(n,t){a(R,n)})])])}),H=[d(),m(),g()],N=ry.sketch({dom:k,components:H.map(function(n){return n.asSpec()})}),P=x("Brightness",SC,-100,0,100),z=x("Contrast",CC,-100,0,100),L=x("Gamma",wC,-100,0,100),j=S("R"),U=S("G"),W=S("B"),G=[d(),j,U,W,g()],X=ry.sketch({dom:k,components:G.map(function(n){return n.asSpec()})}),Y=tn.some(xC),q=tn.some(yC),K=[t("crop","Crop",C(T,tn.none(),function(n){r.showCrop()}),!1),t("resize","Resize",C(D,tn.none(),function(n){E.getOpt(n).each(function(n){var t=r.getMeasurements(),e=t.width,o=t.height;tl.setValue(n,{width:e,height:o})})}),!1),t("orientation","Orientation",C(V,tn.none(),O),!1),t("brightness","Brightness",C(P,tn.none(),O),!1),t("sharpen","Sharpen",C(N,Y,O),!1),t("contrast","Contrast",C(z,tn.none(),O),!1),t("color-levels","Color levels",C(X,tn.none(),O),!1),t("gamma","Gamma",C(L,tn.none(),O),!1),t("invert","Invert",C(N,q,O),!1)],J=ry.sketch({dom:k,components:K.map(function(n){return n.asSpec()})}),$=ry.sketch({dom:{tag:"div"},components:[J],containerBehaviours:Sa([Cg.config({})])}),Q=dp($);return{memContainer:Q,getApplyButton:function(n){return Q.getOpt(n).map(function(n){var t=n.components()[0];return t.components()[t.components().length-1]})}}}var nk=De("toolbar.button.execute"),tk={"alloy.execute":["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},ek=De("update-menu-text"),ok=De("update-menu-icon"),rk={type:"separator"},ik=function(n,r){var t=FC(J(n)?n.split(" "):n,r);return k(t,function(n,t){var e=function(n){if(MC(n))return n;var t=bn(n,"value").getOrThunk(function(){return De("generated-menu-item")});return Sn({value:t},n)}(t),o=IC(e,r);return{menus:Sn(n.menus,o.menus),items:[o.item].concat(n.items),expansions:Sn(n.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},uk=nn([pt("field1Name","field1"),pt("field2Name","field2"),$u("onLockedChange"),Yu(["lockClass"]),pt("locked",!1),el("coupledFieldBehaviours",[ed,tl])]),ak=nn([YC("field1","field2"),YC("field2","field1"),Sl({factory:hp,schema:[tt("dom")],name:"lock",overrides:function(n){return{buttonBehaviours:Sa([Fg.config({selected:n.locked,toggleClass:n.markers.lockClass,aria:{mode:"pressed"}})])}}})]),ck=Ml({name:"FormCoupledInputs",configFields:uk(),partFields:ak(),factory:function(o,n,t,e){return{uid:o.uid,dom:o.dom,components:n,behaviours:ol(o.coupledFieldBehaviours,[ed.config({find:tn.some}),tl.config({store:{mode:"manual",getValue:function(n){var t,e=sf(n,o,["field1","field2"]);return(t={})[o.field1Name]=tl.getValue(e.field1()),t[o.field2Name]=tl.getValue(e.field2()),t},setValue:function(n,t){var e=sf(n,o,["field1","field2"]);N(t,o.field1Name)&&tl.setValue(e.field1(),t[o.field1Name]),N(t,o.field2Name)&&tl.setValue(e.field2(),t[o.field2Name])}}})]),apis:{getField1:function(n){return of(n,o,"field1")},getField2:function(n){return of(n,o,"field2")},getLock:function(n){return of(n,o,"lock")}}}},apis:{getField1:function(n,t){return n.getField1(t)},getField2:function(n,t){return n.getField2(t)},getLock:function(n,t){return n.getLock(t)}}}),sk={undo:nn(De("undo")),redo:nn(De("redo")),zoom:nn(De("zoom")),back:nn(De("back")),apply:nn(De("apply")),swap:nn(De("swap")),transform:nn(De("transform")),tempTransform:nn(De("temp-transform")),transformApply:nn(De("transform-apply"))},fk=nn("save-state"),lk=nn("disable"),dk=nn("enable"),mk={formActionEvent:cy,saveState:fk,disable:lk,enable:dk},gk=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),pk=tinymce.util.Tools.resolve("tinymce.geom.Rect"),hk=tinymce.util.Tools.resolve("tinymce.util.Observable"),vk=tinymce.util.Tools.resolve("tinymce.util.Tools"),bk=tinymce.util.Tools.resolve("tinymce.util.VK");function yk(n){var t,e;if(n.changedTouches)for(t="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<t.length;e++)n[t[e]]=n.changedTouches[0][t[e]]}function xk(n,r){var i,u,t,a,c,f,l,d=r.document||v.document;r=r||{};var m=d.getElementById(r.handle||n);t=function(n){var t,e,o=function s(n){var t,e,o,r,i,u,a,c=Math.max;return t=n.documentElement,e=n.body,o=c(t.scrollWidth,e.scrollWidth),r=c(t.clientWidth,e.clientWidth),i=c(t.offsetWidth,e.offsetWidth),u=c(t.scrollHeight,e.scrollHeight),a=c(t.clientHeight,e.clientHeight),{width:o<i?r:o,height:u<c(t.offsetHeight,e.offsetHeight)?a:u}}(d);yk(n),n.preventDefault(),u=n.button,t=m,f=n.screenX,l=n.screenY,e=v.window.getComputedStyle?v.window.getComputedStyle(t,null).getPropertyValue("cursor"):t.runtimeStyle.cursor,i=gk("<div></div>").css({position:"absolute",top:0,left:0,width:o.width,height:o.height,zIndex:2147483647,opacity:1e-4,cursor:e}).appendTo(d.body),gk(d).on("mousemove touchmove",c).on("mouseup touchend",a),r.start(n)},c=function(n){if(yk(n),n.button!==u)return a(n);n.deltaX=n.screenX-f,n.deltaY=n.screenY-l,n.preventDefault(),r.drag(n)},a=function(n){yk(n),gk(d).off("mousemove touchmove",c).off("mouseup touchend",a),i.remove(),r.stop&&r.stop(n)},this.destroy=function(){gk(m).off()},gk(m).on("mousedown touchstart",t)}function wk(t){function u(n,s){c.getOpt(n).each(function(n){var e=l.get(),o=cu(n.element()),r=ru(n.element()),i=s.dom().naturalWidth*e,u=s.dom().naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),t={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};io(s,t),f.getOpt(n).each(function(n){io(n.element(),t)}),d.get().each(function(n){var t=m.get();n.setRect({x:t.x*e+a,y:t.y*e+c,w:t.w*e,h:t.h*e}),n.setClampRect({x:a,y:c,w:i,h:u}),n.setViewPortRect({x:0,y:0,w:o,h:r})})})}function e(n,t){var i=ir.fromTag("img");return Ce(i,"src",t),function(e){return new ih(function(n){var t=function(){e.removeEventListener("load",t),n(e)};e.complete?n(e):e.addEventListener("load",t)})}(i.dom()).then(function(){return c.getOpt(n).map(function(n){var t=Zi({element:i});Cg.replaceAt(n,1,tn.some(t));var e=a.get(),o={x:0,y:0,w:i.dom().naturalWidth,h:i.dom().naturalHeight};a.set(o);var r=pk.inflate(o,-20,-20);return m.set(r),e.w===o.w&&e.h===o.h||function(n,u){c.getOpt(n).each(function(n){var t=cu(n.element()),e=ru(n.element()),o=u.dom().naturalWidth,r=u.dom().naturalHeight,i=Math.min(t/o,e/r);1<=i?l.set(1):l.set(i)})}(n,i),u(n,i),i})})}var f=dp({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),l=or(1),d=or(tn.none()),m=or({x:0,y:0,w:1,h:1}),a=or({x:0,y:0,w:1,h:1}),n=ry.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[f.asSpec(),{dom:{tag:"img",attributes:{src:t}}},{dom:{tag:"div"},behaviours:Sa([Qd("image-panel-crop-events",[Oi(function(n){c.getOpt(n).each(function(n){var t=n.element().dom(),e=Ok({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},t,function(){});e.toggleVisibility(!1),e.on("updateRect",function(n){var t=n.rect,e=l.get(),o={x:Math.round(t.x/e),y:Math.round(t.y/e),w:Math.round(t.w/e),h:Math.round(t.h/e)};m.set(o)}),d.set(tn.some(e))})})])])}],containerBehaviours:Sa([Cg.config({}),Qd("image-panel-events",[Oi(function(n){e(n,t)})])])}),c=dp(n);return{memContainer:c,updateSrc:e,zoom:function(n,t){var e=l.get(),o=0<t?Math.min(2,e+.1):Math.max(.1,e-.1);l.set(o),c.getOpt(n).each(function(n){var t=n.components()[1].element();u(n,t)})},showCrop:function(){d.get().each(function(n){n.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(n){n.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var n=a.get();return{width:n.w,height:n.h}}}}function Sk(n,t,e,o,r){return LC({name:n,icon:tn.some(t),disabled:e,tooltip:tn.some(n),primary:!1,borderless:!1},o,r)}function Ck(n,t){t?Gh.enable(n):Gh.disable(n)}var kk=0,Ok=function(s,e,f,o,r){var l,t,i,u="tox-",a="tox-crid-"+kk++,c=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}];i=["top","right","bottom","left"];var d=function(n,t){return{x:t.x+n.x,y:t.y+n.y,w:t.w,h:t.h}},m=function(n,t){return{x:t.x-n.x,y:t.y-n.y,w:t.w,h:t.h}};function g(n,t,e,o){var r,i,u,a,c;r=t.x,i=t.y,u=t.w,a=t.h,r+=e*n.deltaX,i+=o*n.deltaY,(u+=e*n.deltaW)<20&&(u=20),(a+=o*n.deltaH)<20&&(a=20),c=s=pk.clamp({x:r,y:i,w:u,h:a},f,"move"===n.name),c=m(f,c),l.fire("updateRect",{rect:c}),v(c)}function p(t){function n(n,t){t.h<0&&(t.h=0),t.w<0&&(t.w=0),gk("#"+a+"-"+n,o).css({left:t.x,top:t.y,width:t.w,height:t.h})}vk.each(c,function(n){gk("#"+a+"-"+n.name,o).css({left:t.w*n.xMul+t.x,top:t.h*n.yMul+t.y})}),n("top",{x:e.x,y:e.y,w:e.w,h:t.y-e.y}),n("right",{x:t.x+t.w,y:t.y,w:e.w-t.x-t.w+e.x,h:t.h}),n("bottom",{x:e.x,y:t.y+t.h,w:e.w,h:e.h-t.y-t.h+e.y}),n("left",{x:e.x,y:t.y,w:t.x-e.x,h:t.h}),n("move",t)}function h(n){p(s=n)}function v(n){h(d(f,n))}return function b(){gk('<div id="'+a+'" class="'+u+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),vk.each(i,function(n){gk("#"+a,o).append('<div id="'+a+"-"+n+'"class="'+u+'croprect-block" style="display: none" data-mce-bogus="all">')}),vk.each(c,function(n){gk("#"+a,o).append('<div id="'+a+"-"+n.name+'" class="'+u+"croprect-handle "+u+"croprect-handle-"+n.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+n.label+'" aria-grabbed="false" title="'+n.label+'">')}),t=vk.map(c,function n(t){var e;return new xk(a,{document:o.ownerDocument,handle:a+"-"+t.name,start:function(){e=s},drag:function(n){g(t,e,n.deltaX,n.deltaY)}})}),p(s),gk(o).on("focusin focusout",function(n){gk(n.target).attr("aria-grabbed","focus"===n.type?"true":"false")}),gk(o).on("keydown",function(t){var i;function n(n,t,e,o,r){n.stopPropagation(),n.preventDefault(),g(i,e,o,r)}switch(vk.each(c,function(n){if(t.target.id===a+"-"+n.name)return i=n,!1}),t.keyCode){case bk.LEFT:n(t,0,s,-10,0);break;case bk.RIGHT:n(t,0,s,10,0);break;case bk.UP:n(t,0,s,0,-10);break;case bk.DOWN:n(t,0,s,0,10);break;case bk.ENTER:case bk.SPACEBAR:t.preventDefault(),r()}})}(),l=vk.extend({toggleVisibility:function y(n){var t;t=vk.map(c,function(n){return"#"+a+"-"+n.name}).concat(vk.map(i,function(n){return"#"+a+"-"+n})).join(","),n?gk(t,o).show():gk(t,o).hide()},setClampRect:function x(n){f=n,p(s)},setRect:h,getInnerRect:function(){return m(f,s)},setInnerRect:v,setViewPortRect:function w(n){e=n,p(s)},destroy:function n(){vk.each(t,function(n){n.destroy()}),t=[]}},hk)};function _k(n){var t=or(n),e=or(tn.none()),o=function s(){var e=[],o=-1;function n(){return 0<o}function t(){return-1!==o&&o<e.length-1}return{data:e,add:function r(n){var t;return t=e.splice(++o),e.push(n),{state:n,removed:t}},undo:function i(){if(n())return e[--o]},redo:function u(){if(t())return e[++o]},canUndo:n,canRedo:t}}();function r(n){t.set(n)}function i(n){v.URL.revokeObjectURL(n.url)}function u(n){var t=a(n);return r(t),function(n){vk.each(n,i)}(o.add(t).removed),t.url}o.add(n);var a=function(n){return{blob:n,url:v.URL.createObjectURL(n)}},c=function(){e.get().each(i),e.set(tn.none())};return{getBlobState:function(){return t.get()},setBlobState:r,addBlobState:u,getTempState:function(){return e.get().fold(function(){return t.get()},function(n){return n})},updateTempState:function(n){var t=a(n);return c(),e.set(tn.some(t)),t.url},addTempState:function(n){var t=a(n);return e.set(tn.some(t)),t.url},applyTempState:function(t){return e.get().fold(function(){},function(n){u(n.blob),t()})},destroyTempState:c,undo:function(){var n=o.undo();return r(n),n.url},redo:function(){var n=o.redo();return r(n),n.url},getHistoryStates:function(){return{undoEnabled:o.canUndo(),redoEnabled:o.canRedo()}}}}function Tk(n,t){function i(n){var t=s.getHistoryStates();m.updateButtonUndoStates(n,t.undoEnabled,t.redoEnabled),Lt(n,mk.formActionEvent,{name:mk.saveState(),value:t.undoEnabled})}function u(n){return n.toBlob()}function a(n){Lt(n,mk.formActionEvent,{name:mk.disable(),value:{}})}function r(t,n,e,o,r){return a(t),function(n){return tC(n)}(n).then(e).then(u).then(o).then(function(n){return l(t,n).then(function(n){return i(t),r(),f(t),n})})["catch"](function(n){return v.console.log(n),f(t),n})}function c(n,t,e){var o=s.getBlobState().blob;r(n,o,t,function(n){return s.updateTempState(n)},e)}var s=_k(n.currentState),f=function(n){e.getApplyButton(n).each(function(n){Gh.enable(n)}),Lt(n,mk.formActionEvent,{name:mk.enable(),value:{}})},l=function(n,t){return a(n),o.updateSrc(n,t)},d=function(n){var t=s.getBlobState().url;return s.destroyTempState(),i(n),t},o=wk(n.currentState.url),m=function(n){var o=dp(Sk("Undo","undo",!0,function(n){Lt(n,sk.undo(),{direction:1})},n)),r=dp(Sk("Redo","redo",!0,function(n){Lt(n,sk.redo(),{direction:1})},n));return{container:ry.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),Sk("Zoom in","zoom-in",!1,function(n){Lt(n,sk.zoom(),{direction:1})},n),Sk("Zoom out","zoom-out",!1,function(n){Lt(n,sk.zoom(),{direction:-1})},n)]}),updateButtonUndoStates:function(n,t,e){o.getOpt(n).each(function(n){Ck(n,t)}),r.getOpt(n).each(function(n){Ck(n,e)})}}}(t),e=ZC(o,t);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[e.memContainer.asSpec(),o.memContainer.asSpec(),m.container],behaviours:Sa([tl.config({store:{mode:"manual",getValue:function(){return s.getBlobState()}}}),Qd("image-tools-events",[qt(sk.undo(),function(t,n){var e=s.undo();l(t,e).then(function(n){f(t),i(t)})}),qt(sk.redo(),function(t,n){var e=s.redo();l(t,e).then(function(n){f(t),i(t)})}),qt(sk.zoom(),function(n,t){var e=t.event().direction();o.zoom(n,e)}),qt(sk.back(),function(n,t){!function(t){var n=d(t);l(t,n).then(function(n){f(t)})}(n),t.event().swap()(),o.hideCrop()}),qt(sk.apply(),function(n,t){s.applyTempState(function(){d(n),t.event().swap()()})}),qt(sk.transform(),function(n,t){return c(n,t.event().transform(),Z)}),qt(sk.tempTransform(),function(n,t){return function(n,t){var e=s.getTempState().blob;r(n,e,t,function(n){return s.addTempState(n)},Z)}(n,t.event().transform())}),qt(sk.transformApply(),function(n,t){return function(e,n,t){var o=s.getBlobState().blob;r(e,o,n,function(n){var t=s.addBlobState(n);return d(e),t},t)}(n,t.event().transform(),t.event().swap())}),qt(sk.swap(),function(t,n){!function(n){m.updateButtonUndoStates(n,!1,!1)}(t);var e=n.event().transform(),o=n.event().swap();e.fold(function(){o()},function(n){c(t,n,o)})})]),bS()])}}function Ek(e,t){var n=e.label.map(function(n){return Ey(n,t)}),o=[Gh.config({disabled:e.disabled}),wg.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(n){return zt(n,sy),tn.some(!0)}}),Qd("textfield-change",[qt(Yr(),function(n,t){Lt(n,iy,{name:e.name})}),qt(ti(),function(n,t){Lt(n,iy,{name:e.name})})]),Wy.config({})],r=e.validation.map(function(o){return Ly.config({getRoot:function(n){return ce(n.element())},invalidClass:"tox-invalid",validator:{validate:function(n){var t=tl.getValue(n),e=o.validator(t);return Vy(!0===e?K.value(t):K.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(nn({}),function(n){return{placeholder:t.translate(n)}}),u=e.inputMode.fold(nn({}),function(n){return{inputmode:n}}),a=P(P({},i),u),c=hy.parts().field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:Sa(z([o,r])),selectOnFocus:!1,factory:by}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),f=[Gh.config({disabled:e.disabled,onDisabled:function(n){hy.getField(n).each(Gh.disable)},onEnabled:function(n){hy.getField(n).each(Gh.enable)}})];return Ty(n,c,s,f)}function Bk(n){var t=or(null);return Yi({readState:function(){return{timer:null!==t.get()?"set":"unset"}},setTimer:function(n){t.set(n)},cancel:function(){var n=t.get();null!==n&&n.cancel()}})}function Dk(n,t,e){var o=tl.getValue(e);tl.setValue(t,o),x_(t)}function Ak(n,t){var e=n.element(),o=mo(e),r=e.dom();"number"!==ke(e,"type")&&t(r,o)}function Mk(n,t,e){if(n.selectsOver){var o=tl.getValue(t),r=n.getDisplayText(o),i=tl.getValue(e);return 0===n.getDisplayText(i).indexOf(r)?tn.some(function(){Dk(0,t,e),function(n,e){Ak(n,function(n,t){return n.setSelectionRange(e,t.length)})}(t,r.length)}):tn.none()}return tn.none()}function Fk(n){return O_(Ry(n))}function Ik(n){return{type:"menuitem",value:n.url,text:n.title,meta:{attach:n.attach},onAction:function(){}}}function Rk(n,t){return{type:"menuitem",value:t,text:n,meta:{attach:undefined},onAction:function(){}}}function Vk(n,t){return function(n){return S(n,Ik)}(function(t,n){return C(n,function(n){return n.type===t})}(n,t))}function Hk(n,t){var e=n.toLowerCase();return C(t,function(n){var t=n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.text;return Bt(t.toLowerCase(),e)||Bt(n.value.toLowerCase(),e)})}function Nk(e,n,o){var t=tl.getValue(n),r=t.meta.text!==undefined?t.meta.text:t.value;return o.getLinkInformation().fold(function(){return[]},function(n){var t=Hk(r,function(n){return S(n,function(n){return Rk(n,n)})}(o.getHistory(e)));return"file"===e?function(n){return O(n,function(n,t){return 0===n.length||0===t.length?n.concat(t):n.concat(T_,t)},[])}([t,Hk(r,function(n){return Vk("header",n.targets)}(n)),Hk(r,z([function(n){return tn.from(n.anchorTop).map(function(n){return Rk("<top>",n)}).toArray()}(n),function(n){return Vk("anchor",n.targets)}(n),function(n){return tn.from(n.anchorBottom).map(function(n){return Rk("<bottom>",n)}).toArray()}(n)]))]):t})}function Pk(r,o,i){function u(n){var t=tl.getValue(n);i.addToHistory(t.value,r.filetype)}var n,t,e,a,c,s=o.shared.providers,f=hy.parts().field({factory:k_,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":E_,type:"url"},minChars:0,responseTime:0,fetch:function(n){var t=Nk(r.filetype,n,i),e=RC(t,tv.BUBBLE_TO_SANDBOX,o,!1);return Vy(e)},getHotspot:function(n){return h.getOpt(n)},onSetValue:function(n,t){n.hasConfigured(Ly)&&Ly.run(n).get(Z)},typeaheadBehaviours:Sa(z([i.getValidationHandler().map(function(e){return Ly.config({getRoot:function(n){return ce(n.element())},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(n,t){d.getOpt(n).each(function(n){Ce(n.element(),"title",s.translate(t))})}},validator:{validate:function(n){var t=tl.getValue(n);return __(function(o){e({type:r.filetype,url:t.value},function(n){if("invalid"===n.status){var t=K.error(n.message);o(t)}else{var e=K.value(n.message);o(e)}})})},validateOnLoad:!1}})}).toArray(),[Gh.config({disabled:r.disabled}),Wy.config({}),Qd("urlinput-events",z(["file"===r.filetype?[qt(Yr(),function(n){Lt(n,iy,{name:r.name})})]:[],[qt(qr(),function(n){Lt(n,iy,{name:r.name}),u(n)}),qt(ti(),function(n){Lt(n,iy,{name:r.name}),u(n)})]]))]])),eventOrder:(n={},n[Yr()]=["streaming","urlinput-events","invalidating"],n),model:{getDisplayText:function(n){return n.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:zv(0,0,"normal")},onExecute:function(n,t,e){Lt(t,sy,{})},onItemExecute:function(n,t,e,o){u(n),Lt(n,iy,{name:r.name})}}),l=r.label.map(function(n){return Ey(n,s)}),d=dp((t="invalid",e=tn.some(E_),void 0===(a="warning")&&(a=t),void 0===c&&(c=t),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+t],innerHtml:gp(a,s.icons),attributes:P({title:s.translate(c),"aria-live":"polite"},e.fold(function(){return{}},function(n){return{id:n}}))}})),m=dp({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=De("browser.url.event"),h=dp({dom:{tag:"div",classes:["tox-control-wrap"]},components:[f,m.asSpec()],behaviours:Sa([Gh.config({disabled:r.disabled})])}),v=dp(UC({name:r.name,icon:tn.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(n){return zt(n,p)},s,[],["tox-browse-url"]));return hy.sketch({dom:Xy([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:z([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:Sa([Gh.config({disabled:r.disabled,onDisabled:function(n){hy.getField(n).each(Gh.disable),v.getOpt(n).each(Gh.disable)},onEnabled:function(n){hy.getField(n).each(Gh.enable),v.getOpt(n).each(Gh.enable)}}),Qd("url-input-events",[qt(p,function(o){ed.getCurrent(o).each(function(t){var n=tl.getValue(t),e=P({fieldname:r.name},n);g.each(function(n){n(e).get(function(n){tl.setValue(t,n),Lt(o,iy,{name:r.name})})})})})])])})}function zk(u,t){function n(o){return function(t,e){Hu(e.event().target(),"[data-collection-item-value]").each(function(n){o(t,e,n,ke(n,"data-collection-item-value"))})}}var e=u.label.map(function(n){return Ey(n,t)}),o=n(function(n,t,e,o){t.stop(),Lt(n,cy,{name:u.name,value:o})}),r=[qt(jr(),n(function(n,t,e){Oa(e)})),qt(Kr(),o),qt(ii(),o),qt(Ur(),n(function(n,t,e){Vu(n.element(),"."+Rh).each(function(n){Je(n,Rh)}),qe(e,Rh)})),qt(Wr(),n(function(n){Vu(n.element(),"."+Rh).each(function(n){Je(n,Rh)})})),Ei(n(function(n,t,e,o){Lt(n,cy,{name:u.name,value:o})}))],i=hy.parts().field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:l},behaviours:Sa([Cg.config({}),tl.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,n){!function(n,t){var e=S(t,function(n){var t=_h.translate(n.text),e=1===u.columns?'<div class="tox-collection__item-label">'+t+"</div>":"",o='<div class="tox-collection__item-icon">'+n.icon+"</div>",r={_:" "," - ":" ","-":" "},i=t.replace(/\_| \- |\-/g,function(n){return r[n]});return'<div class="tox-collection__item" tabindex="-1" data-collection-item-value="'+function(n){return'"'===n?"&quot;":n}(n.value)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),o=1<u.columns&&"auto"!==u.columns?w(e,u.columns):[e],r=S(o,function(n){return'<div class="tox-collection__group">'+n.join("")+"</div>"});ye(n.element(),r.join(""))}(o,n),"auto"===u.columns&&Mp(o,5,"tox-collection__item").each(function(n){var t=n.numRows,e=n.numColumns;wg.setGridSize(o,t,e)}),zt(o,my)}}),Wy.config({}),wg.config(function(n,t){return 1===n?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===n?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===t?".tox-swatches__row":".tox-collection__group",cell:"color"===t?"."+Bh:"."+Eh}}}(u.columns,"normal")),Qd("collection-events",r)])});return Ty(e,i,["tox-form__group--collection"],[])}function Lk(r){return function(t,e,o){return bn(e,"name").fold(function(){return r(e,o)},function(n){return t.field(n,r(e,o))})}}function jk(t,n,e){var o=Sn(e,{shared:{interpreter:function(n){return A_(t,n,o)}}});return A_(t,n,o)}function Uk(n){return{colorPicker:function(e){return function(n,t){lb.colorPickerDialog(e)(n,t)}}(n),hasCustomColors:function(n){return function(){return nb(n)}}(n),getColors:function(n){return function(){return tb(n)}}(n),getColorCols:function(n){return function(){return lb.getColorCols(n)}}(n)}}function Wk(e){return function(n){return tn.from(n.getParam("style_formats")).filter(Q)}(e).map(function(n){var t=function(t,n){function e(n){fn(n,function(n){t.formatter.has(n.name)||t.formatter.register(n.name,n.format)})}var o=R_(n);return t.formatter?e(o.customFormats):t.on("init",function(){e(o.customFormats)}),o.formats}(e,n);return function(n){return n.getParam("style_formats_merge",!1,"boolean")}(e)?I_.concat(t):t}).getOr(I_)}function Gk(n,t,e){var o={type:"formatter",isSelected:t(n.format),getStylePreview:e(n.format)};return Sn(n,o)}function Xk(r,n,i,u){var o=function(n){return S(n,function(n){var t=mn(n);if(N(n,"items")){var e=o(n.items);return Sn(function(n){return Sn(n,{type:"submenu"})}(n),{getStyleItems:function(){return e}})}return N(n,"format")?function(n){return Gk(n,i,u)}(n):1===t.length&&sn(t,"title")?Sn(n,{type:"separator"}):function(n){var t=De(n.title),e={type:"formatter",format:t,isSelected:i(t),getStylePreview:u(t)},o=Sn(n,e);return r.formatter.register(t,o),o}(n)})};return o(n)}function Yk(t){return function(n){if(n&&1===n.nodeType){if(n.contentEditable===t)return!0;if(n.getAttribute("data-mce-contenteditable")===t)return!0}return!1}}function qk(n,t,e,o,r){return{type:n,title:t,url:e,level:o,attach:r}}function Kk(n){return n.innerText||n.textContent}function Jk(n){return function(n){return n&&"A"===n.nodeName&&(n.id||n.name)!==undefined}(n)&&P_(n)}function $k(n){return n&&/^(H[1-6])$/.test(n.nodeName)}function Qk(n){return $k(n)&&P_(n)}function Zk(n){var t=function(n){return n.id?n.id:De("h")}(n);return qk("header",Kk(n),"#"+t,function(n){return $k(n)?parseInt(n.nodeName.substr(1),10):0}(n),function(){n.id=t})}function nO(n){var t=n.id||n.name,e=Kk(n);return qk("anchor",e||"#"+t,"#"+t,0,Z)}function tO(n){return function(n,t){return S(Zc(ir.fromDom(t),n),function(n){return n.dom()})}("h1,h2,h3,h4,h5,h6,a:not([href])",n)}function eO(n){return 0<V_(n.title).length}function oO(n){return J(n)&&/^https?/.test(n)}function rO(n){return $(n)&&V(n,function(n){return!function(n){return Q(n)&&n.length<=5&&B(n,oO)}(n)}).isNone()}function iO(){var n,t=v.localStorage.getItem(L_);if(null===t)return{};try{n=JSON.parse(t)}catch(e){if(e instanceof SyntaxError)return v.console.log("Local storage "+L_+" was not valid JSON",e),{};throw e}return rO(n)?n:(v.console.log("Local storage "+L_+" was not valid format",n),{})}function uO(n){var t=iO();return Object.prototype.hasOwnProperty.call(t,n)?t[n]:[]}function aO(t,n){if(oO(t)){var e=iO(),o=Object.prototype.hasOwnProperty.call(e,n)?e[n]:[],r=C(o,function(n){return n!==t});e[n]=[t].concat(r).slice(0,5),function(n){if(!rO(n))throw new Error("Bad format for history:\n"+JSON.stringify(n));v.localStorage.setItem(L_,JSON.stringify(n))}(e)}}function cO(n){return!!n}function sO(n){return L(vk.makeMap(n,/[, ]/),cO)}function fO(n,t,e){var o=function(n,t){return j_.call(n,t)?tn.some(n[t]):tn.none()}(n,t).getOr(e);return J(o)?tn.some(o):tn.none()}function lO(n){return tn.some(n.file_picker_callback).filter(on)}function dO(n,t){var e=function(n){var t=tn.some(n.file_picker_types).filter(cO),e=tn.some(n.file_browser_callback_types).filter(cO),o=t.or(e).map(sO);return lO(n).fold(function(){return!1},function(n){return o.fold(function(){return!0},function(n){return 0<mn(n).length&&n})})}(n);return en(e)?e?lO(n):tn.none():e[t]?lO(n):tn.none()}function mO(t){return{getHistory:uO,addToHistory:aO,getLinkInformation:function(){return function(n){return!1===n.settings.typeahead_urls?tn.none():tn.some({targets:z_(n.getBody()),anchorTop:fO(n.settings,"anchor_top","#top").getOrUndefined(),anchorBottom:fO(n.settings,"anchor_bottom","#bottom").getOrUndefined()})}(t)},getValidationHandler:function(){return function(n){return tn.from(n.settings.file_picker_validator_handler).filter(on).orThunk(function(){return tn.from(n.settings.filepicker_validator_handler).filter(on)})}(t)},getUrlPicker:function(n){return function(r,i){return dO(r.settings,i).map(function(o){return function(t){return Ry(function(e){var n=P({filetype:i,fieldname:t.fieldname},tn.from(t.meta).getOr({}));o.call(r,function(n,t){if(!J(n))throw new Error("Expected value to be string");if(t!==undefined&&!$(t))throw new Error("Expected meta to be a object");e({value:n,meta:t})},t.value,n)})}})}(t,n)}}}function gO(n,t,e){var o=or(!1),r={shared:{providers:{icons:function(){return t.ui.registry.getAll().icons},menuItems:function(){return t.ui.registry.getAll().menuItems},translate:_h.translate},interpreter:function(n){return function(n,t){return A_(D_,n,t)}(n,r)},anchors:F_(t,e),getSink:function(){return K.value(n)}},urlinput:mO(t),styleselect:function(o){function r(n){return function(){return o.formatter.match(n)}}function i(t){return function(){var n=o.formatter.get(t);return n!==undefined?tn.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(t))}):tn.none()}}var u=function(n){var t=n.items;return t!==undefined&&0<t.length?E(t,u):[n.format]},a=or([]),c=or([]),e=or([]),s=or([]),f=or(!1);o.on("PreInit",function(n){var t=Wk(o),e=Xk(o,t,r,i);a.set(e),c.set(E(e,u))}),o.on("addStyleModifications",function(n){var t=Xk(o,n.items,r,i);e.set(t),f.set(n.replace),s.set(E(t,u))});return{getData:function(){var n=f.get()?[]:a.get(),t=e.get();return n.concat(t)},getFlattenedKeys:function(){var n=f.get()?[]:c.get(),t=s.get();return n.concat(t)}}}(t),colorinput:Uk(t),dialog:function(n){return{isDraggableModal:function(n){return function(){return function(n){return n.getParam("draggable_modal",!1,"boolean")}(n)}}(n)}}(t),isContextMenuOpen:function(){return o.get()},setContextMenuState:function(n){return o.set(n)}};return r}function pO(n,t,o){var e=function(n,e){return O(n,function(t,n){return e(n,t.len).fold(nn(t),function(n){return{len:n.finish(),list:t.list.concat([n])}})},{len:0,list:[]}).list}(n,function(n,t){var e=o(n);return tn.some({element:nn(n),start:nn(t),finish:nn(t+e),width:nn(e)})}),r=C(e,function(n){return n.finish()<=t}),i=k(r,function(n,t){return n+t.width()},0),u=e.slice(r.length);return{within:nn(r),extra:nn(u),withinWidth:nn(i)}}function hO(n){return S(n,function(n){return n.element()})}function vO(n,t,e,o){var r=function(n,t,e){var o=pO(t,n,e);return 0===o.extra().length?tn.some(o):tn.none()}(n,t,e).getOrThunk(function(){return pO(t,n-e(o),e)}),i=r.within(),u=r.extra(),a=r.withinWidth();return 1===u.length&&u[0].width()<=e(o)?function(n,t,e){var o=hO(n.concat(t));return $_(o,[],e)}(i,u,a):1<=u.length?function(n,t,e,o){var r=hO(n).concat([e]);return $_(r,hO(t),o)}(i,u,o,a):function(n,t,e){return $_(hO(n),[],e)}(i,0,a)}function bO(n,t){var e=S(t,function(n){return eu(n)});J_.setGroups(n,e)}function yO(n,t,e){var o=rf(n,t,"primary"),r=Ky.getCoupled(n,"overflowGroup");ro(o.element(),"visibility","hidden");var i=t.builtGroups.get().concat([r]),u=function(n){return R(n,function(t){return Ta(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()})})}(i);e([]),bO(o,i);var a=cu(o.element()),c=vO(a,t.builtGroups.get(),function(n){return cu(n.element())},r);0===c.extra().length?(Cg.remove(o,r),e([])):(bO(o,c.within()),e(c.extra())),fo(o.element(),"visibility"),lo(o.element()),u.each(Tg.focus)}function xO(n,t){var e=Ky.getCoupled(n,"toolbarSandbox");jf.isOpen(e)?jf.close(e):jf.open(e,t.toolbar())}function wO(n,t,e,o){var r=e.getBounds.map(function(n){return n()}),i=e.lazySink(n).getOrDie();Mf.positionWithinBounds(i,{anchor:"hotspot",hotspot:n,layouts:o,overrides:{maxWidthFunction:U_()}},t,r)}function SO(n,t,e,o,r){J_.setGroups(t,r),wO(n,t,e,o),Fg.on(n)}function CO(n){return S(n,function(n){return eu(n)})}function kO(n,e,o){yO(n,o,function(t){o.overflowGroups.set(t),e.getOpt(n).each(function(n){oT.setGroups(n,CO(t))})})}function OO(t,n){return n.getAnimationRoot.fold(function(){return t.element()},function(n){return n(t)})}function _O(n){return n.dimension.property}function TO(n,t){return n.dimension.getDimension(t)}function EO(n,t){var e=OO(n,t);Ze(e,[t.shrinkingClass,t.growingClass])}function BO(n,t){Je(n.element(),t.openClass),qe(n.element(),t.closedClass),ro(n.element(),_O(t),"0px"),lo(n.element())}function DO(n,t){Je(n.element(),t.closedClass),qe(n.element(),t.openClass),fo(n.element(),_O(t))}function AO(n,t,e,o){e.setCollapsed(),ro(n.element(),_O(t),TO(t,n.element())),lo(n.element()),EO(n,t),BO(n,t),t.onStartShrink(n),t.onShrunk(n)}function MO(n,t,e,o){var r=o.getOrThunk(function(){return TO(t,n.element())});e.setCollapsed(),ro(n.element(),_O(t),r),lo(n.element());var i=OO(n,t);Je(i,t.growingClass),qe(i,t.shrinkingClass),BO(n,t),t.onStartShrink(n)}function FO(n,t,e){var o=TO(t,n.element());("0px"===o?AO:MO)(n,t,e,tn.some(o))}function IO(n,t,e){var o=OO(n,t),r=$e(o,t.shrinkingClass),i=TO(t,n.element());DO(n,t);var u=TO(t,n.element());(r?function(){ro(n.element(),_O(t),i),lo(n.element())}:function(){BO(n,t)})(),Je(o,t.shrinkingClass),qe(o,t.growingClass),DO(n,t),ro(n.element(),_O(t),u),e.setExpanded(),t.onStartGrow(n)}function RO(n,t,e){var o=OO(n,t);return!0===$e(o,t.growingClass)}function VO(n,t,e){var o=OO(n,t);return!0===$e(o,t.shrinkingClass)}function HO(n,t){var e=n.outerContainer;!function(n,t){var e=n.outerContainer.element();t&&(n.mothership.broadcastOn([Uf()],{target:e}),n.uiMothership.broadcastOn([Uf()],{target:e})),n.mothership.broadcastOn([gT],{readonly:t}),n.uiMothership.broadcastOn([gT],{readonly:t})}(n,t),It("*",e.element()).forEach(function(n){e.getSystem().getByDom(n).each(function(n){n.hasConfigured(Gh)&&Gh.set(n,t)})})}function NO(n,t){n.on("init",function(){n.readonly&&HO(t,!0)}),n.on("SwitchMode",function(){return HO(t,n.readonly)}),function(n){return n.getParam("readonly",!1,"boolean")}(n)&&n.setMode("readonly")}function PO(e){var n;return bc.config({channels:(n={},n[gT]={schema:pT,onReceive:function(n,t){e(n).each(function(n){!function(t,e){It("*",t.element()).forEach(function(n){t.getSystem().getByDom(n).each(function(n){n.hasConfigured(Gh)&&Gh.set(n,e)})})}(n,t.readonly)})}},n)})}function zO(n){var t=n.title.fold(function(){return{}},function(n){return{attributes:{title:n}}});return{dom:P({tag:"div",classes:["tox-toolbar__group"]},t),components:[Y_.parts().items({})],items:n.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:Sa([Wy.config({}),Tg.config({})])}}function LO(n){return Y_.sketch(zO(n))}function jO(e,n,t){var o=Oi(function(n){var t=S(e.initGroups,LO);J_.setGroups(n,t)});return Sa([wg.config({mode:n,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Qd("toolbar-events",[o]),PO(t)])}function UO(n,t){var e=n.cyclicKeying?"cyclic":"acyclic";return{uid:n.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":zO({title:tn.none(),items:[]}),"overflow-button":zC({name:"more",icon:tn.some("more-drawer"),disabled:!1,tooltip:tn.some("More..."),primary:!1,borderless:!1},tn.none(),n.backstage.shared.providers)},splitToolbarBehaviours:jO(n,e,t)}}function WO(r){var n=UO(r,rT.getOverflow),t=rT.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return rT.sketch(P(P({},n),{lazySink:r.getSink,getOverflowBounds:function(){var n=r.moreDrawerData.lazyHeader().element(),t=yu(n),e=ue(n),o=yu(e);return vu(t.x()+4,o.y(),t.width()-8,o.height())},parts:P(P({},n.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:r.attributes}}}),components:[t],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))}function GO(n){var t=dT.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=dT.parts().overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=UO(n,tn.none);return dT.sketch(P(P({},o),{components:[t,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(n){n.getSystem().broadcastOn([mT()],{type:"opened"})},onClosed:function(n){n.getSystem().broadcastOn([mT()],{type:"closed"})}}))}function XO(n){var t=n.cyclicKeying?"cyclic":"acyclic";return J_.sketch({uid:n.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(n.type===Dm.scrolling?["tox-toolbar--scrolling"]:[])},components:[J_.parts().groups({})],toolbarBehaviours:jO(n,t,nn(tn.none()))})}function YO(n){return qn("toolbarbutton",vT,n)}function qO(n){return qn("menubutton",yT,n)}function KO(n){return qn("ToggleButton",ST,n)}function JO(t){return{isDisabled:function(){return Gh.isDisabled(t)},setDisabled:function(n){return Gh.set(t,n)}}}function $O(t){return{setActive:function(n){Fg.set(t,n)},isActive:function(){return Fg.isOn(t)},isDisabled:function(){return Gh.isDisabled(t)},setDisabled:function(n){return Gh.set(t,n)}}}function QO(n,t){return n.map(function(n){return{"aria-label":t.translate(n),title:t.translate(n)}}).getOr({})}function ZO(t,e,n,o,r,i){function u(n){return _h.isRtl()&&sn(UT,n)?n+"-rtl":n}var a,c=_h.isRtl()&&t.exists(function(n){return sn(WT,n)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:QO(n,i)},components:Jh([t.map(function(n){return EC(u(n),i.icons)}),e.map(function(n){return DC(n,"tox-tbtn",i)})]),eventOrder:(a={},a[Nr()]=["focusing","alloy.base.behaviour","common-button-display-events"],a),buttonBehaviours:Sa([Qd("common-button-display-events",[qt(Nr(),function(n,t){t.event().prevent(),zt(n,jT)})])].concat(o.map(function(n){return VT.config({channel:n,initialData:{icon:t,text:e},renderComponents:function(n,t){return Jh([n.icon.map(function(n){return EC(u(n),i.icons)}),n.text.map(function(n){return DC(n,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}}function n_(n,t,e){var o=or(Z),r=ZO(n.icon,n.text,n.tooltip,tn.none(),tn.none(),e);return hp.sketch({dom:r.dom,components:r.components,eventOrder:tk,buttonBehaviours:Sa([Qd("toolbar-button-events",[function(e){return Ei(function(t,n){$p(e,t)(function(n){Lt(t,nk,{buttonApi:n}),e.onAction(n)})})}({onAction:n.onAction,getApi:t.getApi}),Qp(t,o),Zp(t,o)]),Kh(n.disabled)].concat(t.toolbarButtonBehaviours))})}function t_(t,n){function e(e){return{isDisabled:function(){return Gh.isDisabled(e)},setDisabled:function(n){return Gh.set(e,n)},setIconFill:function(n,t){Vu(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){Ce(n,"fill",t)})},setIconStroke:function(n,t){Vu(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){Ce(n,"stroke",t)})},setActive:function(t){Ce(e.element(),"aria-pressed",t),Vu(e.element(),"span").each(function(n){e.getSystem().getByDom(n).each(function(n){return Fg.set(n,t)})})},isActive:function(){return Vu(e.element(),"span").exists(function(n){return e.getSystem().getByDom(n).exists(Fg.isOn)})}}}var o,r=De("channel-update-split-dropdown-display"),i=or(Z),u={getApi:e,onSetup:t.onSetup};return LT.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:P({"aria-pressed":!1},QO(t.tooltip,n.providers))},onExecute:function(n){t.onAction(e(n))},onItemExecute:function(n,t,e){},splitDropdownBehaviours:Sa([qh(!1),Qd("split-dropdown-events",[qt(jT,Tg.focus),Qp(u,i),Zp(u,i)]),Ew.config({})]),eventOrder:(o={},o[gi()]=["alloy.base.behaviour","split-dropdown-events"],o),toggleClass:"tox-tbtn--enabled",lazySink:n.getSink,fetch:function(e,r,o){return function(t){return Ry(function(n){return r.fetch(n)}).map(function(n){return tn.from(Eb(Sn(qv(De("menu-value"),n,function(n){r.onItemAction(e(t),n)},r.columns,r.presets,tv.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),o),{movement:Kv(r.columns,r.presets),menuBehaviours:bh("auto"!==r.columns?[]:[Oi(function(o,n){Mp(o,4,Rp(r.presets)).each(function(n){var t=n.numRows,e=n.numColumns;wg.setGridSize(o,t,e)})})])})))})}}(e,t,n.providers),parts:{menu:zv(0,t.columns,t.presets)},components:[LT.parts().button(ZO(t.icon,t.text,tn.none(),tn.some(r),tn.some([Fg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),n.providers)),LT.parts().arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:gp("chevron-down",n.providers.icons)}}),LT.parts()["aria-descriptor"]({text:n.providers.translate("To open the popup, press Shift+Enter")})]})}function e_(o,r){return qt(nk,function(n,t){var e=function(n){return{hide:function(){return zt(n,ai())},getValue:function(){return tl.getValue(n)}}}(o.get(n));r.onAction(e,t.event().buttonApi())})}function o_(n,t,e){var o={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===t.type?function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=Kn(KO(P(P({},r),{type:"togglebutton",onAction:function(){}})));return XT(i,e.backstage.shared.providers,[e_(n,t)])}(n,t,o):function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=Kn(YO(P(P({},r),{type:"button",onAction:function(){}})));return GT(i,e.backstage.shared.providers,[e_(n,t)])}(n,t,o)}function r_(n){var t=pu(v.window),e=bu(ir.fromDom(n.getContentAreaContainer())),o=_m(n)||Tm(n)||rp(n),r=function(n,t){var e=Math.max(t.x(),n.x()),o=n.right()-e,r=t.width()-(e-t.x());return{x:e,width:Math.min(o,r)}}(e,t),i=r.x,u=r.width;if(n.inline&&!o)return vu(i,t.y(),u,t.height());var a=function(n,t,e){var o=ir.fromDom(n.getContainer()),r=Vu(o,".tox-editor-header").getOr(o),i=bu(r),u=i.y()>=t.bottom(),a=pp(n)&&!u;if(n.inline&&a)return{y:Math.max(i.bottom(),e.y()),bottom:e.bottom()};if(n.inline&&!a)return{y:e.y(),bottom:Math.min(i.y(),e.bottom())};var c=bu(o);return a?{y:Math.max(i.bottom(),e.y()),bottom:Math.min(c.bottom(),e.bottom())}:{y:Math.max(c.y(),e.y()),bottom:Math.min(i.y(),e.bottom())}}(n,e,t),c=a.y,s=a.bottom;return vu(i,c,u,s-c)}function i_(t,n){return R(n,function(n){return n.predicate(t.dom())?tn.some({toolbarApi:n,elem:t}):tn.none()})}function u_(o,r){return function(t){function n(){t.setActive(o.formatter.match(r));var n=o.formatter.formatChanged(r,t.setActive).unbind;e.set(tn.some(n))}var e=or(tn.none());return o.initialized?n():o.on("init",n),function(){return e.get().each(function(n){return n()})}}}function a_(t){return function(n){return function(){t.undoManager.transact(function(){t.focus(),t.execCommand("mceToggleFormat",!1,n.format)})}}}function c_(n,t,e){var o=e.dataset,r="basic"===o.type?function(){return S(o.data,function(n){return Gk(n,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:function(n,u,a){function r(n,t,e,o){var r=u.shared.providers.translate(n.title);if("separator"===n.type)return tn.some({type:"separator",text:r});if("submenu"!==n.type)return tn.some(P({type:"togglemenuitem",text:r,active:n.isSelected(o),disabled:e,onAction:a.onAction(n)},n.getStylePreview().fold(function(){return{}},function(n){return{meta:{style:n}}})));var i=E(n.getStyleItems(),function(n){return c(n,t,o)});return 0===t&&i.length<=0?tn.none():tn.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return E(n.getStyleItems(),function(n){return c(n,t,o)})}})}function i(n){var t=a.getCurrentValue(),e=a.shouldHide?0:1;return E(n,function(n){return c(n,e,t)})}var c=function(n,t,e){var o="formatter"===n.type&&a.isInvalid(n);return 0===t?o?[]:r(n,t,!1,e).toArray():r(n,t,o,e).toArray()};return{validateItems:i,getFetch:function(o,r){return function(n){var t=r(),e=i(t);n(RC(e,tv.CLOSE_ON_EXECUTE,o,!1))}}}}(0,t,e),getStyleItems:r}}function s_(o,n,t){var e=c_(0,n,t),r=e.items,i=e.getStyleItems;return AC({text:t.icon.isSome()?tn.none():tn.some(""),icon:t.icon,tooltip:tn.from(t.tooltip),role:tn.none(),fetch:r.getFetch(n,i),onSetup:function(e){return t.setInitialValue.each(function(n){return n(e.getComponent())}),t.nodeChangeHandler.map(function(n){var t=n(e.getComponent());return o.on("NodeChange",t),function(){o.off("NodeChange",t)}}).getOr(Z)},getApi:function(n){return{getComponent:function(){return n}}},columns:1,presets:"normal",classes:t.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",n.shared)}var f_,l_,d_,m_,g_,p_=Al({name:"HtmlSelect",configFields:[tt("options"),Ls("selectBehaviours",[Tg,tl]),pt("selectClasses",[]),pt("selectAttributes",{}),st("data")],factory:function(e,n){var t=S(e.options,function(n){return{dom:{tag:"option",value:n.value,innerHtml:n.text}}}),o=e.data.map(function(n){return Dn("initialValue",n)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:t,behaviours:Us(e.selectBehaviours,[Tg.config({}),tl.config({store:P({mode:"manual",getValue:function(n){return mo(n.element())},setValue:function(n,t){_(e.options,function(n){return n.value===t}).isSome()&&go(n.element(),t)}},o)})])}}}),h_=/* */Object.freeze({__proto__:null,events:function(n,t){var e=n.stream.streams.setup(n,t);return Gt([qt(n.event,e),_i(function(){return t.cancel()})].concat(n.cancelEvent.map(function(n){return[qt(n,function(){return t.cancel()})]}).getOr([])))}}),v_=/* */Object.freeze({__proto__:null,throttle:Bk,init:function(n){return n.stream.streams.state(n)}}),b_=[et("stream",Qn("mode",{throttle:[tt("delay"),pt("stopEvent",!0),Zu("streams",{setup:function(n,t){var e=n.stream,o=xp(n.onStream,e.delay);return t.setTimer(o),function(n,t){o.throttle(n,t),e.stopEvent&&t.stop()}},state:Bk})]})),pt("event","input"),st("cancelEvent"),$u("onStream")],y_=Ca({fields:b_,name:"streaming",active:h_,state:v_}),x_=function(n){Ak(n,function(n,t){return n.setSelectionRange(t.length,t.length)})},w_=nn("alloy.typeahead.itemexecute"),S_=nn([st("lazySink"),tt("fetch"),pt("minChars",5),pt("responseTime",1e3),Ku("onOpen"),pt("getHotspot",tn.some),pt("getAnchorOverrides",nn({})),pt("layouts",tn.none()),pt("eventOrder",{}),St("model",{},[pt("getDisplayText",function(n){return n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.value}),pt("selectsOver",!0),pt("populateFromBrowse",!0)]),Ku("onSetValue"),Ju("onExecute"),Ku("onItemExecute"),pt("inputClasses",[]),pt("inputAttributes",{}),pt("inputStyles",{}),pt("matchWidth",!0),pt("useMinWidth",!1),pt("dismissOnBlur",!0),Yu(["openClass"]),st("initialData"),Ls("typeaheadBehaviours",[Tg,tl,y_,wg,Fg,Ky]),Ct("previewing",function(){return or(!0)})].concat(vy()).concat(ux())),C_=nn([Cl({schema:[Xu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(t,e){o.previewing.get()?t.getSystem().getByUid(o.uid).each(function(n){Mk(o.model,n,e).fold(function(){return fd.dehighlight(t,e)},function(n){return n()})}):t.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&Dk(o.model,n,e)}),o.previewing.set(!1)},onExecute:function(n,t){return n.getSystem().getByUid(o.uid).toOption().map(function(n){return Lt(n,w_(),{item:t}),!0})},onHover:function(n,t){o.previewing.set(!1),n.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&Dk(o.model,n,t)})}}}})]),k_=Ml({name:"Typeahead",configFields:S_(),partFields:C_(),factory:function(r,n,t,i){function e(n,t,e){r.previewing.set(!1);var o=Ky.getCoupled(n,"sandbox");if(jf.isOpen(o))ed.getCurrent(o).each(function(n){fd.getHighlighted(n).fold(function(){e(n)},function(){Wt(o,n.element(),"keydown",t)})});else{Zy(r,u(n),n,o,i,function(n){ed.getCurrent(n).each(e)},By.HighlightFirst).get(Z)}}var o=Qb(r),u=function(o){return function(n){return n.map(function(n){var t=H(n.menus),e=E(t,function(n){return C(n.items,function(n){return"item"===n.type})});return tl.getState(o).update(S(e,function(n){return n.data})),n})}},a=[Tg.config({}),tl.config({onSetValue:r.onSetValue,store:P({mode:"dataset",getDataKey:function(n){return mo(n.element())},getFallbackEntry:function(n){return{value:n,meta:{}}},setValue:function(n,t){go(n.element(),r.model.getDisplayText(t))}},r.initialData.map(function(n){return Dn("initialValue",n)}).getOr({}))}),y_.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(n,t){var e=Ky.getCoupled(n,"sandbox");if(Tg.isFocused(n)&&mo(n.element()).length>=r.minChars){var o=ed.getCurrent(e).bind(function(n){return fd.getHighlighted(n).map(tl.getValue)});r.previewing.set(!0);Zy(r,u(n),n,e,i,function(n){ed.getCurrent(e).each(function(n){o.fold(function(){r.model.selectsOver&&fd.highlightFirst(n)},function(t){fd.highlightBy(n,function(n){return tl.getValue(n).value===t.value}),fd.getHighlighted(n).orThunk(function(){return fd.highlightFirst(n),tn.none()})})})},By.HighlightFirst).get(Z)}},cancelEvent:ci()}),wg.config({mode:"special",onDown:function(n,t){return e(n,t,fd.highlightFirst),tn.some(!0)},onEscape:function(n){var t=Ky.getCoupled(n,"sandbox");return jf.isOpen(t)?(jf.close(t),tn.some(!0)):tn.none()},onUp:function(n,t){return e(n,t,fd.highlightLast),tn.some(!0)},onEnter:function(t){var n=Ky.getCoupled(t,"sandbox"),e=jf.isOpen(n);if(e&&!r.previewing.get())return ed.getCurrent(n).bind(function(n){return fd.getHighlighted(n)}).map(function(n){return Lt(t,w_(),{item:n}),!0});var o=tl.getValue(t);return zt(t,ci()),r.onExecute(n,t,o),e&&jf.close(n),tn.some(!0)}}),Fg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),Ky.config({others:{sandbox:function(n){return rx(r,n,{onOpen:function(){return Fg.on(n)},onClose:function(){return Fg.off(n)}})}}}),Qd("typeaheadevents",[Ei(function(n){var t=Z;tx(r,u(n),n,i,t,By.HighlightFirst).get(Z)}),qt(w_(),function(n,t){var e=Ky.getCoupled(n,"sandbox");Dk(r.model,n,t.event().item()),zt(n,ci()),r.onItemExecute(n,e,t.event().item(),tl.getValue(n)),jf.close(e),x_(n)})].concat(r.dismissOnBlur?[qt(ni(),function(n){var t=Ky.getCoupled(n,"sandbox");Ta(t.element()).isNone()&&jf.close(t)})]:[]))];return{uid:r.uid,dom:Zb(Sn(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:P(P({},o),Us(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),O_=function(i){return P(P({},i),{toCached:function(){return O_(i.toCached())},bindFuture:function(t){return O_(i.bind(function(n){return n.fold(function(n){return Vy(K.error(n))},function(n){return t(n)})}))},bindResult:function(t){return O_(i.map(function(n){return n.bind(t)}))},mapResult:function(t){return O_(i.map(function(n){return n.map(t)}))},mapError:function(t){return O_(i.map(function(n){return n.mapError(t)}))},foldResult:function(t,e){return i.map(function(n){return n.fold(t,e)})},withTimeout:function(n,r){return O_(Ry(function(t){var e=!1,o=v.setTimeout(function(){e=!0,t(K.error(r()))},n);i.get(function(n){e||(v.clearTimeout(o),t(n))})}))}})},__=Fk,T_={type:"separator"},E_=De("aria-invalid"),B_={bar:Lk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:S(n.items,t.interpreter)}}(n,t.shared)}),collection:Lk(function(n,t){return zk(n,t.shared.providers)}),alertbanner:Lk(function(n,t){return function(t,n){return ry.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+t.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[hp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:gp(t.icon,n.icons),attributes:{title:n.translate(t.iconTooltip)}},action:function(n){Lt(n,cy,{name:"alert-banner",value:t.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:n.translate(t.text)}}]})}(n,t.shared.providers)}),input:Lk(function(n,t){return function(n,t){return Ek({name:n.name,multiline:!1,label:n.label,inputMode:n.inputMode,placeholder:n.placeholder,flex:!1,disabled:n.disabled,classname:"tox-textfield",validation:tn.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),textarea:Lk(function(n,t){return function(n,t){return Ek({name:n.name,multiline:!0,label:n.label,inputMode:tn.none(),placeholder:n.placeholder,flex:!0,disabled:n.disabled,classname:"tox-textarea",validation:tn.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),label:Lk(function(n,t){return function(n,t){var e={dom:{tag:"label",innerHtml:t.providers.translate(n.label),classes:["tox-label"]}},o=S(n.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[e].concat(o),behaviours:Sa([bS(),Cg.config({}),_S(tn.none()),wg.config({mode:"acyclic"})])}}(n,t.shared)}),iframe:(f_=function(n,t){return Cw(n,t.shared.providers)},function(n,t,e){var o=Sn(t,{source:"dynamic"});return Lk(f_)(n,o,e)}),button:Lk(function(n,t){return XC(n,t.shared.providers)}),checkbox:Lk(function(n,t){return function(e,t){function n(n){return n.element().dom().click(),tn.some(!0)}function o(n){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+n],innerHtml:gp("checked"===n?"selected":"unselected",t.icons)}}}var r=tl.config({store:{mode:"manual",getValue:function(n){return n.element().dom().checked},setValue:function(n,t){n.element().dom().checked=t}}}),i=hy.parts().field({factory:{sketch:l},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:Sa([bS(),Gh.config({disabled:e.disabled}),Wy.config({}),Tg.config({}),r,wg.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Qd("checkbox-events",[qt(qr(),function(n,t){Lt(n,iy,{name:e.name})})])])}),u=hy.parts().label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:t.translate(e.label)},behaviours:Sa([Ew.config({})])}),a=dp({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[o("checked"),o("unchecked")]});return hy.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[i,a.asSpec(),u],fieldBehaviours:Sa([Gh.config({disabled:e.disabled,disableClass:"tox-checkbox--disabled",onDisabled:function(n){hy.getField(n).each(Gh.disable)},onEnabled:function(n){hy.getField(n).each(Gh.enable)}})])})}(n,t.shared.providers)}),colorinput:Lk(function(n,t){return ax(n,t.shared,t.colorinput)}),colorpicker:Lk(function(n){function t(n){return"tox-"+n}var e=hw(vw,t),r=dp(e.sketch({dom:{tag:"div",classes:[t("color-picker-container")],attributes:{role:"presentation"}},onValidHex:function(n){Lt(n,cy,{name:"hex-valid",value:!0})},onInvalidHex:function(n){Lt(n,cy,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:Sa([tl.config({store:{mode:"manual",getValue:function(n){var t=r.get(n);return ed.getCurrent(t).bind(function(n){return tl.getValue(n).hex}).map(function(n){return"#"+n}).getOr("")},setValue:function(n,t){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t),o=r.get(n);ed.getCurrent(o).fold(function(){v.console.log("Can not find form")},function(n){tl.setValue(n,{hex:tn.from(e[1]).getOr("")}),mS.getField(n,"hex").each(function(n){zt(n,Yr())})})}}}),bS()])}}),dropzone:Lk(function(n,t){return xw(n,t.shared.providers)}),grid:Lk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+n.columns+"col"]},components:S(n.items,t.interpreter)}}(n,t.shared)}),selectbox:Lk(function(n,t){return function(e,t){var n=S(e.items,function(n){return{text:t.translate(n.text),value:n.value}}),o=e.label.map(function(n){return Ey(n,t)}),r=hy.parts().field({dom:{},selectAttributes:{size:e.size},options:n,factory:p_,selectBehaviours:Sa([Gh.config({disabled:e.disabled}),Wy.config({}),Qd("selectbox-change",[qt(qr(),function(n,t){Lt(n,iy,{name:e.name})})])])}),i=1<e.size?tn.none():tn.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:gp("chevron-down",t.icons)}}),u={dom:{tag:"div",classes:["tox-selectfield"]},components:z([[r],i.toArray()])};return hy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:z([o.toArray(),[u]]),fieldBehaviours:Sa([Gh.config({disabled:e.disabled,onDisabled:function(n){hy.getField(n).each(Gh.disable)},onEnabled:function(n){hy.getField(n).each(Gh.enable)}})])})}(n,t.shared.providers)}),sizeinput:Lk(function(n,t){return QC(n,t.shared.providers)}),urlinput:Lk(function(n,t){return Pk(n,t,t.urlinput)}),customeditor:Lk(function(e){var o=or(tn.none()),t=dp({dom:{tag:e.tag}}),r=or(tn.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:Sa([Qd("editor-foo-events",[Oi(function(n){t.getOpt(n).each(function(t){(!function(n){return Object.prototype.hasOwnProperty.call(n,"init")}(e)?SS.load(e.scriptId,e.scriptUrl).then(function(n){return n(t.element().dom(),e.settings)}):e.init(t.element().dom())).then(function(t){r.get().each(function(n){t.setValue(n)}),r.set(tn.none()),o.set(tn.some(t))})})})]),tl.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(n){return n.getValue()})},setValue:function(n,t){o.get().fold(function(){r.set(tn.some(t))},function(n){return n.setValue(t)})}}}),bS()]),components:[t.asSpec()]}}),htmlpanel:Lk(function(n){return"presentation"===n.presets?ry.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html}}):ry.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html,attributes:{role:"document"}},containerBehaviours:Sa([Wy.config({}),Tg.config({})])})}),imagetools:Lk(function(n,t){return Tk(n,t.shared.providers)}),table:Lk(function(n,t){return function(n,t){function e(n){return{dom:{tag:"th",innerHtml:t.translate(n)}}}function o(n){return{dom:{tag:"td",innerHtml:t.translate(n)}}}function r(n){return{dom:{tag:"tr"},components:S(n,o)}}var i,u;return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(u=n.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:S(u,e)}]}),(i=n.cells,{dom:{tag:"tbody"},components:S(i,r)})],behaviours:Sa([Wy.config({}),Tg.config({})])}}(n,t.shared.providers)}),panel:Lk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:n.classes},components:S(n.items,t.shared.interpreter)}}(n,t)})},D_={field:function(n,t){return t}},A_=function(t,e,o){return bn(B_,e.type).fold(function(){return v.console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(n){return n(t,e,o)})},M_={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},F_=function(n,t){function e(){return ir.fromDom(n.getBody())}var o=sp(n)||!pp(n);return{inlineDialog:function(n,t,e){return e?function(){return{anchor:"node",root:n(),node:tn.from(n()),bubble:za(-12,-12,M_),layouts:{onRtl:function(){return[bm]},onLtr:function(){return[ym]}},overrides:{maxHeightFunction:Bc()}}}:function(){return{anchor:"hotspot",hotspot:t(),bubble:za(-12,12,M_),layouts:{onRtl:function(){return[aa]},onLtr:function(){return[ca]}},overrides:{maxHeightFunction:Bc()}}}}(e,t,o),banner:function(n,t,e){return e?function(){return{anchor:"node",root:n(),node:tn.from(n()),layouts:{onRtl:function(){return[Zg]},onLtr:function(){return[Zg]}}}}:function(){return{anchor:"hotspot",hotspot:t(),layouts:{onRtl:function(){return[lc]},onLtr:function(){return[lc]}}}}}(e,t,o),cursor:function(t,n){return function(){return{anchor:"selection",root:n(),getSelection:function(){var n=t.selection.getRng();return tn.some(Kc.range(ir.fromDom(n.startContainer),n.startOffset,ir.fromDom(n.endContainer),n.endOffset))}}}}(n,e),node:function(t){return function(n){return{anchor:"node",root:t(),node:n}}}(e)}},I_=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strike-through",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}]}],R_=function(n){return O(n,function(n,t){if(function(n){return yn(n,"items")}(t)){var e=R_(t.items);return{customFormats:n.customFormats.concat(e.customFormats),formats:n.formats.concat([{title:t.title,items:e.formats}])}}if(function(n){return yn(n,"inline")}(t)||function(n){return yn(n,"block")}(t)||function(n){return yn(n,"selector")}(t)){var o="custom-"+t.title.toLowerCase();return{customFormats:n.customFormats.concat([{name:o,format:t}]),formats:n.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return P(P({},n),{formats:n.formats.concat(t)})},{customFormats:[],formats:[]})},V_=vk.trim,H_=Yk("true"),N_=Yk("false"),P_=function(n){return function(n){for(;n=n.parentNode;){var t=n.contentEditable;if(t&&"inherit"!==t)return H_(n)}return!1}(n)&&!N_(n)},z_=function(n){var t=tO(n);return C(function(n){return S(C(n,Qk),Zk)}(t).concat(function(n){return S(C(n,Jk),nO)}(t)),eO)},L_="tinymce-url-history",j_=Object.prototype.hasOwnProperty,U_=nn(function(n,t){!function(n,t){var e=Ou.max(n,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);ro(n,"max-width",e+"px")}(n,Math.floor(t))}),W_="contexttoolbar-hide",G_=nn([tt("items"),Yu(["itemSelector"]),Ls("tgroupBehaviours",[wg])]),X_=nn([Ol({name:"items",unit:"item"})]),Y_=Ml({name:"ToolbarGroup",configFields:G_(),partFields:X_(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,behaviours:Us(n.tgroupBehaviours,[wg.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),q_=nn([tt("dom"),pt("shell",!0),Ls("toolbarBehaviours",[Cg])]),K_=nn([kl({name:"groups",overrides:function(){return{behaviours:Sa([Cg.config({})])}}})]),J_=Ml({name:"Toolbar",configFields:q_(),partFields:K_(),factory:function(t,n,e,o){var r=function(n){return t.shell?tn.some(n):of(n,t,"groups")},i=t.shell?{behaviours:[Cg.config({})],components:[]}:{behaviours:[],components:n};return{uid:t.uid,dom:t.dom,components:i.components,behaviours:Us(t.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,t){r(n).fold(function(){throw v.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){Cg.set(n,t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)}}}),$_=te("within","extra","withinWidth"),Q_=nn([Ls("splitToolbarBehaviours",[Ky]),Ct("builtGroups",function(){return or([])})]),Z_=nn([Yu(["overflowToggledClass"]),mt("getOverflowBounds"),tt("lazySink"),Ct("overflowGroups",function(){return or([])})].concat(Q_())),nT=nn([Sl({factory:J_,schema:q_(),name:"primary"}),Cl({schema:q_(),name:"overflow"}),Cl({name:"overflow-button"}),Cl({name:"overflow-group"})]),tT=nn([Yu(["toggledClass"]),tt("lazySink"),it("fetch"),mt("getBounds"),gt("fireDismissalEventInstead",[pt("event",hi())]),Rc()]),eT=nn([Cl({name:"button",overrides:function(n){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:Sa([Fg.config({toggleClass:n.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),Cl({factory:J_,schema:q_(),name:"toolbar",overrides:function(t){return{toolbarBehaviours:Sa([wg.config({mode:"cyclic",onEscape:function(n){return of(n,t,"button").each(Tg.focus),tn.none()}})])}}})]),oT=Ml({name:"FloatingToolbarButton",factory:function(o,n,r,t){return P(P({},hp.sketch(P(P({},t.button()),{action:function(n){xO(n,t)},buttonBehaviours:ol({dump:t.button().buttonBehaviours},[Ky.config({others:{toolbarSandbox:function(n){return function(o,e,r){var i=Nu();return{dom:{tag:"div",attributes:{id:i.id()}},behaviours:Sa([wg.config({mode:"special",onEscape:function(n){return jf.close(n),tn.some(!0)}}),jf.config({onOpen:function(n,t){r.fetch().get(function(n){SO(o,t,r,e.layouts,n),i.link(o.element()),wg.focusIn(t)})},onClose:function(){Fg.off(o),Tg.focus(o),i.unlink(o.element())},isPartOf:function(n,t,e){return ju(t,e)||ju(o,e)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),bc.config({channels:P(P({},Is(P({isExtraPart:nn(!1)},r.fireDismissalEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})))),Rs({isExtraPart:nn(!1),doReposition:function(){jf.getState(Ky.getCoupled(o,"toolbarSandbox")).each(function(n){wO(o,n,r,e.layouts)})}}))})])}}(n,r,o)}}})])}))),{apis:{setGroups:function(t,e){jf.getState(Ky.getCoupled(t,"toolbarSandbox")).each(function(n){SO(t,n,o,r.layouts,e)})},reposition:function(t){jf.getState(Ky.getCoupled(t,"toolbarSandbox")).each(function(n){wO(t,n,o,r.layouts)})},toggle:function(n){xO(n,t)},getToolbar:function(n){return jf.getState(Ky.getCoupled(n,"toolbarSandbox"))}}})},configFields:tT(),partFields:eT(),apis:{setGroups:function(n,t,e){n.setGroups(t,e)},reposition:function(n,t){n.reposition(t)},toggle:function(n,t){n.toggle(t)},getToolbar:function(n,t){return n.getToolbar(t)}}}),rT=Ml({name:"SplitFloatingToolbar",configFields:Z_(),partFields:nT(),factory:function(e,n,t,o){var r=dp(oT.sketch({fetch:function(){return Ry(function(n){n(CO(e.overflowGroups.get()))})},layouts:{onLtr:function(){return[ca]},onRtl:function(){return[aa]},onBottomLtr:function(){return[fa]},onBottomRtl:function(){return[sa]}},getBounds:t.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:e.uid,dom:e.dom,components:n,behaviours:Us(e.splitToolbarBehaviours,[Ky.config({others:{overflowGroup:function(){return Y_.sketch(P(P({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(n,t){e.builtGroups.set(S(t,n.getSystem().build)),kO(n,r,e)},refresh:function(n){return kO(n,r,e)},toggle:function(n){r.getOpt(n).each(function(n){oT.toggle(n)})},reposition:function(n){r.getOpt(n).each(function(n){oT.reposition(n)})},getOverflow:function(n){return r.getOpt(n).bind(function(n){return oT.getToolbar(n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},reposition:function(n,t){n.reposition(t)},toggle:function(n,t){n.toggle(t)},getOverflow:function(n,t){return n.getOverflow(t)}}}),iT=/* */Object.freeze({__proto__:null,refresh:function(n,t,e){if(e.isExpanded()){fo(n.element(),_O(t));var o=TO(t,n.element());ro(n.element(),_O(t),o)}},grow:function(n,t,e){e.isExpanded()||IO(n,t,e)},shrink:function(n,t,e){e.isExpanded()&&FO(n,t,e)},immediateShrink:function(n,t,e){e.isExpanded()&&AO(n,t,e,tn.none())},hasGrown:function(n,t,e){return e.isExpanded()},hasShrunk:function(n,t,e){return e.isCollapsed()},isGrowing:RO,isShrinking:VO,isTransitioning:function(n,t,e){return!0===RO(n,t)||!0===VO(n,t)},toggleGrow:function(n,t,e){(e.isExpanded()?FO:IO)(n,t,e)},disableTransitions:EO}),uT=/* */Object.freeze({__proto__:null,exhibit:function(n,t){var e=t.expanded;return He(e?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Dn(t.dimension.property,"0px")})},events:function(e,o){return Gt([ne(Jr(),function(n,t){t.event().raw().propertyName===e.dimension.property&&(EO(n,e),o.isExpanded()&&fo(n.element(),e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(n))})])}}),aT=[tt("closedClass"),tt("openClass"),tt("shrinkingClass"),tt("growingClass"),st("getAnimationRoot"),Ku("onShrunk"),Ku("onStartShrink"),Ku("onGrown"),Ku("onStartGrow"),pt("expanded",!1),et("dimension",Qn("property",{width:[Zu("property","width"),Zu("getDimension",function(n){return cu(n)+"px"})],height:[Zu("property","height"),Zu("getDimension",function(n){return ru(n)+"px"})]}))],cT=Ca({fields:aT,name:"sliding",active:uT,apis:iT,state:/* */Object.freeze({__proto__:null,init:function(n){var t=or(n.expanded);return Yi({isExpanded:function(){return!0===t.get()},isCollapsed:function(){return!1===t.get()},setCollapsed:d(t.set,!1),setExpanded:d(t.set,!0),readState:function(){return"expanded: "+t.get()}})}})}),sT=nn([Yu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ku("onOpened"),Ku("onClosed")].concat(Q_())),fT=nn([Sl({factory:J_,schema:q_(),name:"primary"}),Sl({factory:J_,schema:q_(),name:"overflow",overrides:function(t){return{toolbarBehaviours:Sa([cT.config({dimension:{property:"height"},closedClass:t.markers.closedClass,openClass:t.markers.openClass,shrinkingClass:t.markers.shrinkingClass,growingClass:t.markers.growingClass,onShrunk:function(n){of(n,t,"overflow-button").each(function(n){Fg.off(n),Tg.focus(n)}),t.onClosed(n)},onGrown:function(n){wg.focusIn(n),t.onOpened(n)},onStartGrow:function(n){of(n,t,"overflow-button").each(Fg.on)}}),wg.config({mode:"acyclic",onEscape:function(n){return of(n,t,"overflow-button").each(Tg.focus),tn.some(!0)}})])}}}),Cl({name:"overflow-button",overrides:function(n){return{buttonBehaviours:Sa([Fg.config({toggleClass:n.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),Cl({name:"overflow-group"})]),lT=function(n,t){of(n,t,"overflow").each(function(e){yO(n,t,function(n){var t=S(n,function(n){return eu(n)});J_.setGroups(e,t)}),of(n,t,"overflow-button").each(function(n){cT.hasGrown(e)&&Fg.on(n)}),cT.refresh(e)})},dT=Ml({name:"SplitSlidingToolbar",configFields:sT(),partFields:fT(),factory:function(o,n,t,e){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:n,behaviours:Us(o.splitToolbarBehaviours,[Ky.config({others:{overflowGroup:function(t){return Y_.sketch(P(P({},e["overflow-group"]()),{items:[hp.sketch(P(P({},e["overflow-button"]()),{action:function(n){zt(t,r)}}))]}))}}}),Qd("toolbar-toggle-events",[qt(r,function(t){of(t,o,"overflow").each(function(n){lT(t,o),cT.toggleGrow(n)})})])]),apis:{setGroups:function(n,t){!function(n,t){var e=S(t,n.getSystem().build);o.builtGroups.set(e)}(n,t),lT(n,o)},refresh:function(n){return lT(n,o)},toggle:function(n){return function(t,e){of(t,e,"overflow").each(function(n){lT(t,e),cT.toggleGrow(n)})}(n,o)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},toggle:function(n,t){n.toggle(t)}}}),mT=nn(De("toolbar-height-change")),gT="silver.readonly",pT=jo([(l_="readonly",et(l_,Zo))]),hT=[xt("disabled",!1),dt("tooltip"),dt("icon"),dt("text"),wt("onSetup",function(){return Z})],vT=jo([ot("type"),it("onAction")].concat(hT)),bT=[dt("text"),dt("tooltip"),dt("icon"),it("fetch"),wt("onSetup",function(){return Z})],yT=jo(p([ot("type")],bT)),xT=jo([ot("type"),dt("tooltip"),dt("icon"),dt("text"),mt("select"),it("fetch"),wt("onSetup",function(){return Z}),yt("presets","normal",["normal","color","listpreview"]),pt("columns",1),it("onAction"),it("onItemAction")]),wT=[xt("active",!1)].concat(hT),ST=jo(wT.concat([ot("type"),it("onAction")])),CT=jo([ot("type"),et("items",(d_=[Gn([ot("name"),ct("items",Qo)]),Qo],{extract:function(n,t,e){for(var o=[],r=0,i=d_;r<i.length;r++){var u=i[r].extract(n,t,e);if(u.stype===W.Value)return u;o.push(u)}return zo(o)},toString:function(){return"oneOf("+S(d_,function(n){return n.toString()}).join(", ")+")"}}))].concat(hT)),kT=[wt("predicate",function(){return!1}),yt("scope","node",["node","editor"]),yt("position","selection",["node","selection","line"])],OT=hT.concat([pt("type","contextformbutton"),pt("primary",!1),it("onAction"),Ct("original",l)]),_T=wT.concat([pt("type","contextformbutton"),pt("primary",!1),it("onAction"),Ct("original",l)]),TT=hT.concat([pt("type","contextformbutton")]),ET=wT.concat([pt("type","contextformtogglebutton")]),BT=Qn("type",{contextformbutton:OT,contextformtogglebutton:_T}),DT=jo([pt("type","contextform"),wt("initValue",function(){return""}),dt("label"),ct("commands",BT),ft("launch",Qn("type",{contextformbutton:TT,contextformtogglebutton:ET}))].concat(kT)),AT=jo([pt("type","contexttoolbar"),ot("items")].concat(kT)),MT=/* */Object.freeze({__proto__:null,getState:function(n,t,e){return e}}),FT=/* */Object.freeze({__proto__:null,events:function(i,u){function r(o,r){i.updateState.each(function(n){var t=n(o,r);u.set(t)}),i.renderComponents.each(function(n){var t=n(r,u.get()),e=S(t,o.getSystem().build);Cs(o,e)})}return Gt([qt(ei(),function(n,t){var e=t,o=i.channel;sn(e.channels(),o)&&r(n,e.data())}),Oi(function(t,n){i.initialData.each(function(n){r(t,n)})})])}}),IT=/* */Object.freeze({__proto__:null,init:function(){var t=or(tn.none());return{readState:function(){return t.get().fold(function(){return"none"},function(n){return n})},get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(tn.none())}}}}),RT=[tt("channel"),st("renderComponents"),st("updateState"),st("initialData")],VT=Ca({fields:RT,name:"reflecting",active:FT,apis:MT,state:IT}),HT=nn([tt("toggleClass"),tt("fetch"),$u("onExecute"),pt("getHotspot",tn.some),pt("getAnchorOverrides",nn({})),Rc(),$u("onItemExecute"),st("lazySink"),tt("dom"),Ku("onOpen"),Ls("splitDropdownBehaviours",[Ky,wg,Tg]),pt("matchWidth",!1),pt("useMinWidth",!1),pt("eventOrder",{}),st("role")].concat(ux())),NT=Sl({factory:hp,schema:[tt("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:Sa([Tg.revoke()])}},overrides:function(t){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(t.uid).each(jt)},buttonBehaviours:Sa([Fg.config({toggleOnExecute:!1,toggleClass:t.toggleClass})])}}}),PT=Sl({factory:hp,schema:[tt("dom")],name:"button",defaults:function(){return{buttonBehaviours:Sa([Tg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(e.uid).each(function(n){e.onExecute(n,t)})}}}}),zT=nn([NT,PT,kl({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[tt("text")],name:"aria-descriptor"}),Cl({schema:[Xu()],name:"menu",defaults:function(o){return{onExecute:function(t,e){t.getSystem().getByUid(o.uid).each(function(n){o.onItemExecute(n,t,e)})}}}}),$y()]),LT=Ml({name:"SplitDropdown",configFields:HT(),partFields:zT(),factory:function(o,n,t,e){function r(n){ed.getCurrent(n).each(function(n){fd.highlightFirst(n),wg.focusIn(n)})}function i(n){tx(o,function(n){return n},n,e,r,By.HighlightFirst).get(Z)}function u(n){var t=rf(n,o,"button");return jt(t),tn.some(!0)}var a=P(P({},Gt([Oi(function(e,n){of(e,o,"aria-descriptor").each(function(n){var t=De("aria");Ce(n.element(),"id",t),Ce(e.element(),"aria-describedby",t)})})])),am(tn.some(i))),c={repositionMenus:function(n){Fg.isOn(n)&&ix(n)}};return{uid:o.uid,dom:o.dom,components:n,apis:c,eventOrder:P(P({},o.eventOrder),{"alloy.execute":["disabling","toggling","alloy.base.behaviour"]}),events:a,behaviours:Us(o.splitDropdownBehaviours,[Ky.config({others:{sandbox:function(n){var t=rf(n,o,"arrow");return rx(o,n,{onOpen:function(){Fg.on(t),Fg.on(n)},onClose:function(){Fg.off(t),Fg.off(n)}})}}}),wg.config({mode:"special",onSpace:u,onEnter:u,onDown:function(n){return i(n),tn.some(!0)}}),Tg.config({}),Fg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(n,t){return n.repositionMenus(t)}}}),jT=De("focus-button"),UT=["checklist","ordered-list"],WT=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],GT=function(n,t,e){return n_(n,{toolbarButtonBehaviours:[].concat(0<e.length?[Qd("toolbarButtonWith",e)]:[]),getApi:JO,onSetup:n.onSetup},t)},XT=function(n,t,e){return Sn(n_(n,{toolbarButtonBehaviours:[Cg.config({}),Fg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[Qd("toolbarToggleButtonWith",e)]:[]),getApi:$O,onSetup:n.onSetup},t))},YT=function(n,t,e){var o=t.label.fold(function(){return{}},function(n){return{"aria-label":n}}),r=dp(by.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:Sa([wg.config({mode:"special",onEnter:function(n){return i.findPrimary(n).map(function(n){return jt(n),!0})},onLeft:function(n,t){return t.cut(),tn.none()},onRight:function(n,t){return t.cut(),tn.none()}})])})),i=function(t,n,e){var o=S(n,function(n){return dp(o_(t,n,e))});return{asSpecs:function(){return S(o,function(n){return n.asSpec()})},findPrimary:function(e){return R(n,function(n,t){return n.primary?tn.from(o[t]).bind(function(n){return n.getOpt(e)}).filter(b(Gh.isDisabled)):tn.none()})}}}(r,t.commands,e.shared.providers);return XO({type:n,uid:De("context-toolbar"),initGroups:[{title:tn.none(),items:[r.asSpec()]},{title:tn.none(),items:i.asSpecs()}],onEscape:tn.none,cyclicKeying:!0})},qT=function(t,n){function e(n){return Rt(n,o)}var o=ir.fromDom(n.getBody()),r=ir.fromDom(n.selection.getNode());return Mr(r,o)?tn.none():i_(r,t.inNodeScope).orThunk(function(){return i_(r,t.inEditorScope).orThunk(function(){return Vt(r,function(n){return e(n)?tn.none():i_(n,t.inNodeScope)},e)})})},KT=function(e,r){function o(t,e){var o=Kn(function(n){return qn("ContextForm",DT,n)}(e));(n[t]=o).launch.map(function(n){c["form:"+t]=P(P({},e.launch),{type:"contextformtogglebutton"===n.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?a.push(o):u.push(o),s[t]=o}function i(t,e){(function(n){return qn("ContextToolbar",AT,n)})(e).each(function(n){"editor"===e.scope?a.push(n):u.push(n),s[t]=n})}var n={},u=[],a=[],c={},s={},t=mn(e);return fn(t,function(n){var t=e[n];"contextform"===t.type?o(n,t):"contexttoolbar"===t.type&&i(n,t)}),{forms:n,inNodeScope:u,inEditorScope:a,lookupTable:s,formNavigators:c}},JT=De("forward-slide"),$T=De("backward-slide"),QT=De("change-slide-event"),ZT="tox-pop--resizing";(g_=m_=m_||{})[g_.SemiColon=0]="SemiColon",g_[g_.Space=1]="Space";function nE(n,t,e,o){return{type:"basic",data:function(n){return S(n,function(n){var t=n,e=n,o=n.split("=");return 1<o.length&&(t=o[0],e=o[1]),{title:t,format:e}})}(function(n,t){return t===m_.SemiColon?n.replace(/;$/,"").split(";"):n.split(" ")}(bn(n.settings,t).getOr(e),o))}}function tE(e){function t(n){var t=_(HB,function(n){return e.formatter.match(n.format)}).fold(function(){return"left"},function(n){return n.title.toLowerCase()});Lt(n,ok,{icon:"align-"+t})}var n=tn.some(function(n){return function(){return t(n)}}),o=tn.some(function(n){return t(n)}),r=function(n){return{type:"basic",data:n}}(HB);return{tooltip:"Align",icon:tn.some("align-left"),isSelectedFor:function(n){return function(){return e.formatter.match(n)}},getCurrentValue:nn(tn.none()),getPreviewFor:function(n){return function(){return tn.none()}},onAction:a_(e),setInitialValue:o,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!e.formatter.canApply(n.format)}}}function eE(n){var t=n.split(/\s*,\s*/);return S(t,function(n){return n.replace(/^['"]+|['"]+$/g,"")})}function oE(r){function i(){function e(n){return n?eE(n)[0]:""}var n=r.queryCommandValue("FontName"),t=u.data,o=n?n.toLowerCase():"";return{matchOpt:_(t,function(n){var t=n.format;return t.toLowerCase()===o||e(t).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return function(n){var t;return 0===n.indexOf("-apple-system")&&(t=eE(n.toLowerCase()),B(NB,function(n){return-1<t.indexOf(n.toLowerCase())}))}(o)?tn.from({title:"System Font",format:o}):tn.none()}),font:n}}function t(n){var t=i(),e=t.matchOpt,o=t.font,r=e.fold(function(){return o},function(n){return n.title});Lt(n,ek,{text:r})}var n=tn.some(function(n){return function(){return t(n)}}),e=tn.some(function(n){return t(n)}),u=nE(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",m_.SemiColon);return{tooltip:"Fonts",icon:tn.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(n){return function(){return tn.some({tag:"div",styles:-1===n.indexOf("dings")?{"font-family":n}:{}})}},onAction:function(n){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,n.format)})}},setInitialValue:e,nodeChangeHandler:n,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function rE(n,t){return/[0-9.]+px$/.test(n)?function(n,t){var e=Math.pow(10,t);return Math.round(n*e)/e}(72*parseInt(n,10)/96,t||0)+"pt":n}function iE(e){function i(){var o=tn.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var n=function(n){var t=rE(i,n),e=function(n){return bn(PB,n).getOr("")}(t);o=_(r,function(n){return n.format===i||n.format===t||n.format===e})},t=3;o.isNone()&&0<=t;t--)n(t);return{matchOpt:o,size:i}}function t(n){var t=i(),e=t.matchOpt,o=t.size,r=e.fold(function(){return o},function(n){return n.title});Lt(n,ek,{text:r})}var n=nn(nn(tn.none())),o=tn.some(function(n){return function(){return t(n)}}),r=tn.some(function(n){return t(n)}),u=nE(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",m_.Space);return{tooltip:"Font sizes",icon:tn.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getPreviewFor:n,getCurrentValue:function(){return i().matchOpt},onAction:function(n){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,n.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function uE(e,n,t){var o=n();return R(t,function(t){return _(o,function(n){return e.formatter.matchNode(t,n.format)})}).orThunk(function(){return e.formatter.match("p")?tn.some({title:"Paragraph",format:"p"}):tn.none()})}function aE(n){var t=n.selection.getStart(!0)||n.getBody();return n.dom.getParents(t,function(){return!0},n.getBody())}function cE(o){function e(n,t){var e=function(n){return uE(o,function(){return r.data},n)}(n).fold(function(){return"Paragraph"},function(n){return n.title});Lt(t,ek,{text:e})}var n=tn.some(function(t){return function(n){return e(n.parents,t)}}),t=tn.some(function(n){var t=aE(o);e(t,n)}),r=nE(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",m_.SemiColon);return{tooltip:"Blocks",icon:tn.none(),isSelectedFor:function(n){return function(){return o.formatter.match(n)}},getCurrentValue:nn(tn.none()),getPreviewFor:function(t){return function(){var n=o.formatter.get(t);return tn.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(t))})}},onAction:a_(o),setInitialValue:t,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!o.formatter.canApply(n.format)}}}function sE(i,n){function e(n,t){var e=function(n){var t=n.items;return t!==undefined&&0<t.length?E(t,e):[{title:n.title,format:n.format}]},o=E(Wk(i),e),r=uE(i,function(){return o},n).fold(function(){return"Paragraph"},function(n){return n.title});Lt(t,ek,{text:r})}var t=tn.some(function(t){return function(n){return e(n.parents,t)}}),o=tn.some(function(n){var t=aE(i);e(t,n)});return{tooltip:"Formats",icon:tn.none(),isSelectedFor:function(n){return function(){return i.formatter.match(n)}},getCurrentValue:nn(tn.none()),getPreviewFor:function(t){return function(){var n=i.formatter.get(t);return n!==undefined?tn.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styles:i.dom.parseStyle(i.formatter.getCssText(t))}):tn.none()}},onAction:a_(i),setInitialValue:o,nodeChangeHandler:t,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(n){return!i.formatter.canApply(n.format)},dataset:n}}function fE(r,i){return function(n,t,e){var o=r(n).mapError(function(n){return Ko(n)}).getOrDie();return i(o,t,e)}}function lE(n){var t=n.toolbar,e=n.buttons;return!1===t?[]:t===undefined||!0===t?function(e){var n=S(zB,function(n){var t=C(n.items,function(n){return yn(e,n)||yn(jB,n)});return{name:n.name,items:t}});return C(n,function(n){return 0<n.items.length})}(e):J(t)?function(n){var t=n.split("|");return S(t,function(n){return{items:n.trim().split(" ")}})}(t):function(n){return h(n,function(n){return yn(n,"name")&&yn(n,"items")})}(t)?t:(v.console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])}function dE(t,e,o,r,i,n){return bn(e,o.toLowerCase()).orThunk(function(){return n.bind(function(n){return R(n,function(n){return bn(e,n+o.toLowerCase())})})}).fold(function(){return bn(jB,o.toLowerCase()).map(function(n){return n(t,i)}).orThunk(function(){return tn.none()})},function(n){return"grouptoolbarbutton"!==n.type||r?function(t,e,o){return bn(LB,t.type).fold(function(){return v.console.error("skipping button defined by",t),tn.none()},function(n){return tn.some(n(t,e,o))})}(n,i,t):(v.console.warn("Ignoring the '"+o+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),tn.none())})}function mE(n){return(co(n,"position").is("fixed")?tn.none():se(n)).orThunk(function(){var e=ir.fromTag("span");return ce(n).bind(function(n){Bi(n,e);var t=se(e);return Di(e),t})})}function gE(n){return mE(n).map(au).getOrThunk(function(){return Cu(0,0)})}function pE(n,t){var e=n.element();qe(e,t.transitionClass),Je(e,t.fadeOutClass),qe(e,t.fadeInClass),t.onShow(n)}function hE(n,t){var e=n.element();qe(e,t.transitionClass),Je(e,t.fadeInClass),qe(e,t.fadeOutClass),t.onHide(n)}function vE(n,t,e){return B(n,function(n){switch(n){case"bottom":return function(n,t){return n.bottom()<=t.bottom()}(t,e);case"top":return function(n,t){return n.y()>=t.y()}(t,e)}})}function bE(t,n){return n.getInitialPosition().map(function(n){return vu(n.bounds.x(),n.bounds.y(),cu(t),ru(t))})}function yE(n,t,e){e.setInitialPosition(tn.some({style:function(n){var t={},e=n.dom();if(no(e))for(var o=0;o<e.style.length;o++){var r=e.style.item(o);t[r]=e.style[r]}return t}(n),position:ao(n,"position")||"static",bounds:t}))}function xE(e,o,r){return r.getInitialPosition().bind(function(n){switch(r.setInitialPosition(tn.none()),n.position){case"static":return tn.some(eD["static"]());case"absolute":var t=mE(e).map(bu).getOrThunk(function(){return bu(Ji())});return tn.some(eD.absolute(xc("absolute",bn(n.style,"left").map(function(n){return o.x()-t.x()}),bn(n.style,"top").map(function(n){return o.y()-t.y()}),bn(n.style,"right").map(function(n){return t.right()-o.right()}),bn(n.style,"bottom").map(function(n){return t.bottom()-o.bottom()}))));default:return tn.none()}})}function wE(n,t,e,o){var r=n.element();return co(r,"position").is("fixed")?function(t,e,o,r){return bE(t,r).filter(function(n){return vE(e.modes,n,o)}).bind(function(n){return xE(t,n,r)})}(r,t,e,o):function(n,t,e,o){var r=bu(n);if(vE(t.modes,r,e))return tn.none();yE(n,r,o);var i=xu(),u=r.x()-i.x(),a=e.y()-i.y(),c=i.bottom()-e.bottom(),s=r.y()<=e.y();return tn.some(eD.fixed(xc("fixed",tn.some(u),s?tn.some(a):tn.none(),tn.none(),s?tn.none():tn.some(c))))}(r,t,e,o)}function SE(t,n){fn(["left","right","top","bottom","position"],function(n){return fo(t.element(),n)}),n.onUndocked(t)}function CE(n,t,e){Ba(n.element(),e),("fixed"===e.position()?t.onDocked:t.onUndocked)(n)}function kE(o,n,r,i,u){void 0===u&&(u=!1),n.contextual.each(function(e){e.lazyContext(o).each(function(n){var t=function(n,t){return n.y()<t.bottom()&&n.bottom()>t.y()}(n,i);t!==r.isVisible()&&(r.setVisible(t),u&&!t?(Qe(o.element(),[e.fadeOutClass]),e.onHide(o)):(t?pE:hE)(o,e))})})}function OE(t,e,n){var o=t.element();n.setDocked(!1),function(n,t){var e=n.element();return bE(e,t).bind(function(n){return xE(e,n,t)})}(t,n).each(function(n){n.fold(function(){return SE(t,e)},function(n){return CE(t,e,n)},Z)}),n.setVisible(!0),e.contextual.each(function(n){Ze(o,[n.fadeInClass,n.fadeOutClass,n.transitionClass]),n.onShow(t)}),oD(t,e,n)}function _E(n,t,e){e.isDocked()&&OE(n,t,e)}function TE(o,r){var i=o.element();ce(i).each(function(n){var t="padding-"+(r?"top":"bottom");if(aD.isDocked(o)){var e=cu(n);ro(i,"width",e+"px"),ro(n,t,function(n){return iu(n)+(parseInt(ao(n,"margin-top"),10)||0)+(parseInt(ao(n,"margin-bottom"),10)||0)}(i)+"px")}else fo(i,"width"),fo(n,t)})}function EE(n,t){t?(Je(n,cD.fadeOutClass),Qe(n,[cD.transitionClass,cD.fadeInClass])):(Je(n,cD.fadeInClass),Qe(n,[cD.fadeOutClass,cD.transitionClass]))}function BE(n,t){var e=ir.fromDom(n.getContainer());t?(qe(e,sD),Je(e,fD)):(qe(e,fD),Je(e,sD))}function DE(u,e){function o(t){e().each(function(n){return t(n.element())})}function n(n){u.inline||TE(n,a),BE(u,aD.isDocked(n)),n.getSystem().broadcastOn([Wf()],{}),e().each(function(n){return n.getSystem().broadcastOn([Wf()],{})})}var r=or(tn.none()),a=pp(u),t=u.inline?[]:function(t){var n;return[bc.config({channels:(n={},n[mT()]={onReceive:function(n){TE(n,t)}},n)})]}(a);return p([Tg.config({}),aD.config({contextual:P({lazyContext:function(n){var t=iu(n.element()),e=u.inline?u.getContentAreaContainer():u.getContainer(),o=bu(ir.fromDom(e)),r=o.height()-t,i=o.y()+(a?0:t);return tn.some(vu(o.x(),i,o.width(),r))},onShow:function(){o(function(n){return EE(n,!0)})},onShown:function(t){o(function(n){return Ze(n,[cD.transitionClass,cD.fadeInClass])}),r.get().each(function(n){!function(t,e){var o=ie(e);_a(o).filter(function(n){return!Rt(e,n)}).filter(function(n){return Rt(n,ir.fromDom(o.dom().body))||Mr(t,n)}).each(function(){return Oa(e)})}(t.element(),n),r.set(tn.none())})},onHide:function(n){r.set(function(n,t){return Ta(n).orThunk(function(){return t().toOption().bind(function(n){return Ta(n.element())})})}(n.element(),e)),o(function(n){return EE(n,!1)})},onHidden:function(){o(function(n){return Ze(n,[cD.transitionClass])})}},cD),modes:[a?"top":"bottom"],onDocked:n,onUndocked:n})],t)}function AE(n){return"<alloy.field."+n+">"}function ME(n){return{element:function(){return n.element().dom()}}}function FE(e,o){var r=S(mn(o),function(n){var t=o[n],e=Kn(function(n){return qn("sidebar",SD,n)}(t));return{name:n,getApi:ME,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return S(r,function(n){var t=or(Z);return e.slot(n.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:bh([Qp(n,t),Zp(n,t),qt(yi(),function(t,n){var e=n.event();_(r,function(n){return n.name===e.name()}).each(function(n){(e.visible()?n.onShow:n.onHide)(n.getApi(t))})})])})})}function IE(n,t){ed.getCurrent(n).each(function(n){return Cg.set(n,[function(t){return wD.sketch(function(n){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:FE(n,t),slotBehaviours:bh([Oi(function(n){return wD.hideAllSlots(n)})])}})}(t)])})}function RE(n){return ed.getCurrent(n).bind(function(n){return cT.isGrowing(n)||cT.hasGrown(n)?ed.getCurrent(n).bind(function(t){return _(wD.getSlotNames(t),function(n){return wD.isShowing(t,n)})}):tn.none()})}function VE(n){var t=ir.fromHtml(n),e=fe(t),o=function(n){var t=n.dom().attributes!==undefined?n.dom().attributes:[];return O(t,function(n,t){var e;return"class"===t.name?n:P(P({},n),((e={})[t.name]=t.value,e))},{})}(t),r=function(n){return Array.prototype.slice.call(n.dom().classList,0)}(t),i=0===e.length?{}:{innerHtml:be(t)};return P({tag:xe(t),classes:r,attributes:o},i)}function HE(n,t,e){var o=n.element();!0===t?(Cg.set(n,[function(n){return{dom:{tag:"div",attributes:{"aria-label":n.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:VE('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:Sa([wg.config({mode:"special",onTab:function(){return tn.some(!0)},onShiftTab:function(){return tn.some(!0)}}),Tg.config({})])}}(e)]),fo(o,"display"),_e(o,"aria-hidden")):(Cg.set(n,[]),ro(o,"display","none"),Ce(o,"aria-hidden","true"))}function NE(n){return"string"==typeof n?n.split(" "):n}function PE(e,o){var r=P(P({},FD),o.menus),t=0<mn(o.menus).length,n=o.menubar===undefined||!0===o.menubar?NE("file edit view insert format tools table help"):NE(!1===o.menubar?"":o.menubar),i=C(n,function(n){return t&&o.menus.hasOwnProperty(n)&&o.menus[n].hasOwnProperty("items")||FD.hasOwnProperty(n)}),u=S(i,function(n){var t=r[n];return function(n,e,t){var o=function(n){return n.getParam("removed_menuitems","")}(t).split(/[ ,]/);return{text:n.title,getItems:function(){return E(n.items,function(n){var t=n.toLowerCase();return 0===t.trim().length?[]:x(o,function(n){return n===t})?[]:"separator"===t||"|"===t?[{type:"separator"}]:e.menuItems[t]?[e.menuItems[t]]:[]})}}}({title:t.title,items:NE(t.items)},o,e)});return C(u,function(n){return 0<n.getItems().length&&x(n.getItems(),function(n){return"separator"!==n.type})})}function zE(n,t){var e,o=function(n){var t=n.settings,e=t.skin,o=t.skin_url;if(!1!==e){var r=e||"oxide";o=o?n.documentBaseURI.toAbsolute(o):op.baseURL+"/skins/ui/"+r}return o}(t);o&&(e=o+"/skin.min.css",t.contentCSS.push(o+(n?"/content.inline":"/content")+".min.css")),!1===function(n){return!1===n.getParam("skin")}(t)&&e?ep.DOM.styleSheetLoader.load(e,ID(t),RD(t,"Skin could not be loaded")):ID(t)()}function LE(e,n,o,r){var t=n.outerContainer,i=o.toolbar,u=o.buttons;if(h(i,J)){var a=i.map(function(n){var t={toolbar:n,buttons:u,allowToolbarGroups:o.allowToolbarGroups};return UB(e,t,{backstage:r},tn.none())});MD.setToolbars(t,a)}else MD.setToolbar(t,UB(e,o,{backstage:r},tn.none()))}function jE(n){return function(n){var t=xm(n),e=Cm(n),o=Om(n);return UD(t).map(function(n){return jD(n,e,o)})}(n).getOr(xm(n))}function UE(n){var t=wm(n),e=Sm(n),o=km(n);return UD(t).map(function(n){return jD(n,e,o)})}function WE(n,t){var e=bu(n);return t?e.y():e.bottom()}function GE(n,t){return function(){n.execCommand("mceToggleFormat",!1,t)}}function XE(n){!function(e){vk.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(n,t){e.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onSetup:u_(e,n.name),onAction:GE(e,n.name)})});for(var n=1;n<=6;n++){var t="h"+n;e.ui.registry.addToggleButton(t,{text:t.toUpperCase(),tooltip:"Heading "+n,onSetup:u_(e,t),onAction:GE(e,t)})}}(n),function(t){vk.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(n){t.ui.registry.addButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)}})})}(n),function(t){vk.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)},onSetup:u_(t,n.name)})})}(n)}function YE(n,t,e){function o(){return!!t.undoManager&&t.undoManager[e]()}function r(){n.setDisabled(t.readonly||!o())}return n.setDisabled(!o()),t.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return t.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}}function qE(n,t){return{anchor:"makeshift",x:n,y:t}}function KE(n){return"longpress"===n.type||0===n.type.indexOf("touch")}function JE(n,t){var e=ep.DOM.getPos(n);return function(n,t,e){return qE(n.x+t,n.y+e)}(t,e.x,e.y)}function $E(n,t){return"contextmenu"===t.type||"longpress"===t.type?n.inline?function(n){if(KE(n)){var t=n.touches[0];return qE(t.pageX,t.pageY)}return qE(n.pageX,n.pageY)}(t):JE(n.getContentAreaContainer(),function(n){if(KE(n)){var t=n.touches[0];return qE(t.clientX,t.clientY)}return qE(n.clientX,n.clientY)}(t)):ZD(n)}function QE(n){return{anchor:"node",node:tn.some(ir.fromDom(n.selection.getNode())),root:ir.fromDom(n.getBody())}}function ZE(n,t,e,o,r,i){var u=e(),a=function(n,t,e){return e?QE(n):$E(n,t)}(n,t,i);RC(u,tv.CLOSE_ON_EXECUTE,o,!1).map(function(n){t.preventDefault(),Qg.showMenuAt(r,a,{menu:{markers:Pv("normal")},data:n})})}function nB(t,e,n,o,r,i,u){var a=function(n,t,e){var o=t?QE(n):$E(n,e);return P({bubble:za(0,12,tA),layouts:nA,overrides:{maxWidthFunction:U_(),maxHeightFunction:Bc()}},o)}(t,i,e);RC(n,tv.CLOSE_ON_EXECUTE,o,!0).map(function(n){e.preventDefault(),Qg.showMenuWithinBounds(r,a,{menu:{markers:Pv("normal"),highlightImmediately:u},data:n,type:"horizontal"},function(){return tn.some(r_(t))}),t.fire(W_)})}function tB(t,e,o,r,i,u){function n(){var n=o();nB(t,e,n,r,i,u,!(f||c||s&&l))}var a=At(),c=a.os.isiOS(),s=a.os.isOSX(),f=a.os.isAndroid(),l=a.deviceType.isTouch();if(!s&&!c||u)f&&!u&&t.selection.setCursorLocation(e.target,0),n();else{var d=function(){!function(n){function t(){tp.setEditorTimeout(n,function(){n.selection.setRng(e)},10),i()}var e=n.selection.getRng();n.once("touchend",t);function o(n){n.preventDefault(),n.stopImmediatePropagation()}n.on("mousedown",o,!0);function r(){return i()}n.once("longpresscancel",r);var i=function(){n.off("touchend",t),n.off("longpresscancel",r),n.off("mousedown",o)}}(t),n()};!function(n,t){var e=n.selection;if(e.isCollapsed()||t.touches.length<1)return!1;var o=t.touches[0],r=e.getRng();return es(n.getWin(),Kc.domRange(r)).exists(function(n){return n.left()<=o.clientX&&n.right()>=o.clientX&&n.top()<=o.clientY&&n.bottom()>=o.clientY})}(t,e)?(t.once("selectionchange",d),t.once("touchend",function(){return t.off("selectionchange",d)})):d()}}function eB(n){return"string"==typeof n?n.split(/[ ,]/):n}function oB(n){return J(n)?"|"===n:"separator"===n.type}function rB(n,t){if(0===t.length)return n;var e=I(n).filter(function(n){return!oB(n)}).fold(function(){return[]},function(n){return[iA]});return n.concat(e).concat(t).concat([iA])}function iB(i,n,t){function e(n){return Qg.hide(a)}function o(o){if(eA(i)&&o.preventDefault(),!function(n,t){return t.ctrlKey&&!eA(n)}(i,o)&&!rA(i)){var r=function(n,t){return"longpress"!==t.type&&(2!==t.button||t.target===n.getBody()&&""===t.pointerType)}(i,o);(u()?tB:ZE)(i,o,function(){var n=r?i.selection.getStart(!0):o.target,t=i.ui.registry.getAll(),e=oA(i);return function(r,n,i){var t=O(n,function(n,t){if(yn(r,t)){var e=r[t].update(i);if(J(e))return rB(n,e.split(" "));if(0<e.length){var o=S(e,uA);return rB(n,o)}return n}return n.concat([t])},[]);return 0<t.length&&oB(t[t.length-1])&&t.pop(),t}(t.contextMenus,e,n)},t,a,r)}}var u=At().deviceType.isTouch,a=tu(Qg.sketch({dom:{tag:"div"},lazySink:n,onEscape:function(){return i.focus()},onShow:function(){return t.setContextMenuState(!0)},onHide:function(){return t.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:Sa([Qd("dismissContextMenu",[qt(hi(),function(n,t){jf.close(n),i.focus()})])])}));i.on("init",function(){var n="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(u()?"":" ResizeWindow");i.on(n,e),i.on("longpress contextmenu",o)})}function uB(t){return function(n){return n.translate(-t.left(),-t.top())}}function aB(t){return function(n){return n.translate(t.left(),t.top())}}function cB(e){return function(n,t){return O(e,function(n,t){return t(n)},Cu(n,t))}}function sB(n,t,e){return n.fold(cB([aB(e),uB(t)]),cB([uB(t)]),cB([]))}function fB(n,t,e){return n.fold(cB([aB(e)]),cB([]),cB([aB(t)]))}function lB(n,t,e){return n.fold(cB([]),cB([uB(e)]),cB([aB(t),uB(e)]))}function dB(n,t,e){var o=n.fold(function(n,t){return{position:tn.some("absolute"),left:tn.some(n+"px"),top:tn.some(t+"px")}},function(n,t){return{position:tn.some("absolute"),left:tn.some(n-e.left()+"px"),top:tn.some(t-e.top()+"px")}},function(n,t){return{position:tn.some("fixed"),left:tn.some(n+"px"),top:tn.some(t+"px")}});return P({right:tn.none(),bottom:tn.none()},o)}function mB(n,i,u,a){function t(o,r){return function(n,t){var e=o(i,u,a);return r(n.getOr(e.left()),t.getOr(e.top()))}}return n.fold(t(lB,cA),t(fB,sA),t(sB,fA))}function gB(n,t,e,o){return function(n,t){var e=n.element(),o=parseInt(ke(e,t.leftAttr),10),r=parseInt(ke(e,t.topAttr),10);return isNaN(o)||isNaN(r)?tn.none():tn.some(Cu(o,r))}(n,t).fold(function(){return e},function(n){return fA(n.left()+o.left(),n.top()+o.top())})}function pB(n,t,e,o,r,i){var u=gB(n,t,e,o),a=t.mustSnap?lA(n,t,u,r,i):dA(n,t,u,r,i),c=sB(u,r,i);return function(n,t,e){var o=n.element();Ce(o,t.leftAttr,e.left()+"px"),Ce(o,t.topAttr,e.top()+"px")}(n,t,c),a.fold(function(){return{coord:fA(c.left(),c.top()),extra:tn.none()}},function(n){return{coord:n.output(),extra:n.extra()}})}function hB(n,t){!function(n,t){var e=n.element();_e(e,t.leftAttr),_e(e,t.topAttr)}(n,t)}function vB(n,e,o,r){return R(n,function(n){var t=n.sensor();return function(n,t,e,o,r,i){var u=fB(n,r,i),a=fB(t,r,i);return Math.abs(u.left()-a.left())<=e&&Math.abs(u.top()-a.top())<=o}(e,t,n.range().left(),n.range().top(),o,r)?tn.some({output:nn(mB(n.output(),e,o,r)),extra:n.extra}):tn.none()})}function bB(n,t){n.getSystem().addToGui(t),function(n){ce(n.element()).filter(Ai).each(function(t){co(t,"z-index").each(function(n){Ce(t,gA,n)}),ro(t,"z-index",ao(n.element(),"z-index"))})}(t)}function yB(n){!function(n){ce(n.element()).filter(Ai).each(function(n){var t=ke(n,gA);Oe(n,gA)?ro(n,"z-index",t):fo(n,"z-index"),_e(n,gA)})}(n),n.getSystem().removeFromGui(n)}function xB(n,t,e){return n.getSystem().build(ry.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:e}))}function wB(t){return function(n,t,e,o){return n.isSome()&&t.isSome()&&e.isSome()?tn.some(o(n.getOrDie(),t.getOrDie(),e.getOrDie())):tn.none()}(co(t,"left"),co(t,"top"),co(t,"position"),function(n,t,e){return("fixed"===e?fA:cA)(parseInt(n,10),parseInt(t,10))}).getOrThunk(function(){var n=au(t);return sA(n.left(),n.top())})}function SB(e,n,o,r,i,u,t){return function(n,t,e,o,r){var i=r.bounds,u=fB(t,e,o),a=Fa(u.left(),i.x(),i.x()+i.width()-r.width),c=Fa(u.top(),i.y(),i.y()+i.height()-r.height),s=sA(a,c);return t.fold(function(){var n=lB(s,e,o);return cA(n.left(),n.top())},function(){return s},function(){var n=sB(s,e,o);return fA(n.left(),n.top())})}(0,n.fold(function(){var n=function(n,e,o){return n.fold(function(n,t){return cA(n+e,t+o)},function(n,t){return sA(n+e,t+o)},function(n,t){return fA(n+e,t+o)})}(o,u.left(),u.top()),t=sB(n,r,i);return fA(t.left(),t.top())},function(t){var n=pB(e,t,o,u,r,i);return n.extra.each(function(n){t.onSensor(e,n)}),n.coord}),r,i,t)}function CB(n,t){return{bounds:n.getBounds(),height:iu(t.element()),width:su(t.element())}}function kB(t,e,n,o,r){var i=n.update(o,r),u=n.getStartData().getOrThunk(function(){return CB(e,t)});i.each(function(n){!function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=ie(n.element()),u=du(i),a=gE(r),c=wB(r),s=SB(n,t.snaps,c,u,a,o,e),f=dB(s,0,a);uo(r,f)}t.onDrag(n,r,o)}(t,e,u,n)})}function OB(t,n,e,o){n.each(yB),e.snaps.each(function(n){hB(t,n)});var r=e.getTarget(t.element());o.reset(),e.onDrop(t,r)}function _B(n){return function(t,e){function o(n){e.setStartData(CB(t,n))}return Gt(p([qt(di(),function(n){e.getStartData().each(function(){return o(n)})})],n(t,e,o)))}}function TB(u,a,c){return[qt(Nr(),function(t,n){if(0===n.event().raw().button){n.stop();var e=function(){return OB(t,tn.some(i),u,a)},o=Rb(e,200),r={drop:e,delayDrop:o.schedule,forceDrop:e,move:function(n){o.cancel(),kB(t,u,a,vA,n)}},i=xB(t,u.blockerClass,function(e){return Gt([qt(Nr(),e.forceDrop),qt(Lr(),e.drop),qt(Pr(),function(n,t){e.move(t.event())}),qt(zr(),e.delayDrop)])}(r));c(t),bB(t,i)}})]}function EB(i,u,a){var c=or(tn.none());return[qt(Ir(),function(t,n){n.stop();function e(){OB(t,c.get(),i,u),c.set(tn.none())}var o={drop:e,delayDrop:function(){},forceDrop:e,move:function(n){kB(t,i,u,yA,n)}},r=xB(t,i.blockerClass,function(e){return Gt([qt(Ir(),e.forceDrop),qt(Vr(),e.drop),qt(Hr(),e.drop),qt(Rr(),function(n,t){e.move(t.event())})])}(o));c.set(tn.some(r));a(t),bB(t,r)}),qt(Rr(),function(n,t){t.stop(),kB(n,i,u,yA,t.event())}),qt(Vr(),function(n,t){t.stop(),OB(n,c.get(),i,u),c.set(tn.none())}),qt(Hr(),function(n){OB(n,c.get(),i,u),c.set(tn.none())})]}function BB(n,r,i,u,t,e){return n.fold(function(){return OA.snap({sensor:sA(i-20,u-20),range:Cu(t,e),output:sA(tn.some(i),tn.some(u)),extra:{td:r}})},function(n){var t=i-20,e=u-20,o=n.element().dom().getBoundingClientRect();return OA.snap({sensor:sA(t,e),range:Cu(40,40),output:sA(tn.some(i-o.width/2),tn.some(u-o.height/2)),extra:{td:r}})})}function DB(n,o,r){return{getSnapPoints:n,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(n,t){var e=t.td;!function(n,t){return n.exists(function(n){return Rt(n,t)})}(o.get(),e)&&(o.set(tn.some(e)),r(e))},mustSnap:!0}}function AB(n){return dp(hp.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:Sa([OA.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:n}),Ew.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))}var MB,FB,IB,RB,VB,HB=[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}],NB=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],PB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},zB=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],LB={button:fE(YO,function(n,t){return function(n,t){return GT(n,t,[])}(n,t.backstage.shared.providers)}),togglebutton:fE(KO,function(n,t){return function(n,t){return XT(n,t,[])}(n,t.backstage.shared.providers)}),menubutton:fE(qO,function(n,t){return HC(n,"tox-tbtn",t.backstage,tn.none())}),splitbutton:fE(function(n){return qn("SplitButton",xT,n)},function(n,t){return t_(n,t.backstage.shared)}),grouptoolbarbutton:fE(function(n){return qn("GroupToolbarButton",CT,n)},function(n,t,e){var o,r=e.ui.registry.getAll().buttons,i=((o={})[jc]=pp(e)?Wa.TopToBottom:Wa.BottomToTop,o);switch(ip(e)){case Dm.floating:return function(t,n,e,o){var r=n.shared;return oT.sketch({lazySink:r.getSink,fetch:function(){return Ry(function(n){n(S(e(t.items),LO))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:ZO(t.icon,t.text,t.tooltip,tn.none(),tn.none(),r.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:o}}}})}(n,t.backstage,function(n){return UB(e,{buttons:r,toolbar:n,allowToolbarGroups:!1},t,tn.none())},i);default:throw new Error("Toolbar groups are only supported when using floating toolbar mode")}}),styleSelectButton:function(n,t){return function(n,t){var e=P({type:"advanced"},t.styleselect);return s_(n,t,sE(n,e))}(n,t.backstage)},fontsizeSelectButton:function(n,t){return function(n,t){return s_(n,t,iE(n))}(n,t.backstage)},fontSelectButton:function(n,t){return function(n,t){return s_(n,t,oE(n))}(n,t.backstage)},formatButton:function(n,t){return function(n,t){return s_(n,t,cE(n))}(n,t.backstage)},alignMenuButton:function(n,t){return function(n,t){return s_(n,t,tE(n))}(n,t.backstage)}},jB={styleselect:LB.styleSelectButton,fontsizeselect:LB.fontsizeSelectButton,fontselect:LB.fontSelectButton,formatselect:LB.formatButton,align:LB.alignMenuButton},UB=function(e,o,r,i){var n=lE(o),t=S(n,function(n){var t=E(n.items,function(n){return 0===n.trim().length?[]:dE(e,o.buttons,n,o.allowToolbarGroups,r,i).toArray()});return{title:tn.from(e.translate(n.name)),items:t}});return C(t,function(n){return 0<n.items.length})},WB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},GB={maxHeightFunction:Bc(),maxWidthFunction:U_()},XB={onLtr:function(){return[fc,lc,sa,aa,fa,ca,Zg,np,bm,hm,ym,vm]},onRtl:function(){return[fc,lc,fa,ca,sa,aa,Zg,np,ym,vm,bm,hm]}},YB={onLtr:function(){return[lc,aa,ca,sa,fa,fc,Zg,np,bm,hm,ym,vm]},onRtl:function(){return[lc,ca,aa,fa,sa,fc,Zg,np,ym,vm,bm,hm]}},qB=function(u,n,e,a){function c(){return r_(u)}function s(){if(l()&&a.backstage.isContextMenuOpen())return!0;var n=function(){var n=g.get().map(function(n){return n.getBoundingClientRect()}).getOrThunk(function(){return u.selection.getRng().getBoundingClientRect()}),t=u.inline?du().top():yu(ir.fromDom(u.getBody())).y();return{y:n.top+t,bottom:n.bottom+t}}(),t=c();return!function(n,t,e,o){return Math.max(n,e)<=Math.min(t,o)}(n.y,n.bottom,t.y(),t.bottom())}function t(){Qg.hide(d)}function o(){m.get().each(function(n){var t=d.element();fo(t,"display"),s()?ro(t,"display","none"):Mf.positionWithinBounds(e,n,d,tn.some(c()))})}function f(n){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[n],behaviours:Sa([wg.config({mode:"acyclic"}),Qd("pop-dialog-wrap-events",[Oi(function(n){u.shortcuts.add("ctrl+F9","focus statusbar",function(){return wg.focusIn(n)})}),_i(function(n){u.shortcuts.remove("ctrl+F9")})])])}}var l=At().deviceType.isTouch,d=tu(function(n){var e=or([]);return Qg.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(n){e.set([]),Qg.getContent(n).each(function(n){fo(n.element(),"visibility")}),Je(n.element(),ZT),fo(n.element(),"width")},inlineBehaviours:Sa([Qd("context-toolbar-events",[ne(Jr(),function(n,t){Qg.getContent(n).each(function(n){}),Je(n.element(),ZT),fo(n.element(),"width")}),qt(QT,function(t,e){fo(t.element(),"width");var n=cu(t.element());Qg.setContent(t,e.event().contents()),qe(t.element(),ZT);var o=cu(t.element());ro(t.element(),"width",n+"px"),Qg.getContent(t).each(function(n){e.event().focus().bind(function(n){return Oa(n),Ta(t.element())}).orThunk(function(){return wg.focusIn(n),_a()})}),tp.setTimeout(function(){ro(t.element(),"width",o+"px")},0)}),qt(JT,function(n,t){Qg.getContent(n).each(function(n){e.set(e.get().concat([{bar:n,focus:_a()}]))}),Lt(n,QT,{contents:t.event().forwardContents(),focus:tn.none()})}),qt($T,function(t,n){I(e.get()).each(function(n){e.set(e.get().slice(0,e.get().length-1)),Lt(t,QT,{contents:eu(n.bar),focus:n.focus})})})]),wg.config({mode:"special",onEscape:function(t){return I(e.get()).fold(function(){return n.onEscape()},function(n){return zt(t,$T),tn.some(!0)})}})]),lazySink:function(){return K.value(n.sink)}})}({sink:e,onEscape:function(){return u.focus(),tn.some(!0)}})),m=or(tn.none()),g=or(tn.none()),r=or(null),p=U(function(){return KT(n,function(n){var t=h(n);Lt(d,JT,{forwardContents:f(t)})})}),h=function(n){var t,e,o=u.ui.registry.getAll().buttons,r=ip(u)===Dm.scrolling?Dm.scrolling:Dm["default"],i=p();return"contexttoolbar"===n.type?(t=P(P({},o),i.formNavigators),e=UB(u,{buttons:t,toolbar:n.items,allowToolbarGroups:!1},a,tn.some(["form:"])),XO({type:r,uid:De("context-toolbar"),initGroups:e,onEscape:tn.none,cyclicKeying:!0})):YT(r,n,a.backstage)};u.on("contexttoolbar-show",function(t){var n=p();bn(n.lookupTable,t.toolbarKey).each(function(n){y(n,t.target===u?tn.none():tn.some(t)),Qg.getContent(d).each(wg.focusIn)})});function v(n,t){var e="node"===n?a.backstage.shared.anchors.node(t):a.backstage.shared.anchors.cursor();return Sn(e,function(n,t){return"line"===n?{bubble:za(12,0,WB),layouts:{onLtr:function(){return[la]},onRtl:function(){return[da]}},overrides:GB}:{bubble:za(0,12,WB),layouts:t?YB:XB,overrides:GB}}(n,l()))}function i(){var n=p();qT(n,u).fold(function(){m.set(tn.none()),Qg.hide(d)},function(n){y(n.toolbarApi,tn.some(n.elem.dom()))})}function b(n){x(),r.set(n)}var y=function(n,t){if(x(),!l()||!a.backstage.isContextMenuOpen()){var e=h(n),o=t.map(ir.fromDom),r=v(n.position,o);m.set(tn.some(r)),g.set(t);var i=d.element();fo(i,"display"),Qg.showWithinBounds(d,r,f(e),function(){return tn.some(c())}),s()&&ro(i,"display","none")}},x=function(){var n=r.get();null!==n&&(tp.clearTimeout(n),r.set(null))};u.on("init",function(){u.on(W_,t),u.on("ScrollContent ScrollWindow longpress",o),u.on("click keyup SetContent ObjectResized ResizeEditor",function(n){b(tp.setEditorTimeout(u,i,0))}),u.on("focusout",function(n){tp.setEditorTimeout(u,function(){Ta(e.element()).isNone()&&Ta(d.element()).isNone()&&(m.set(tn.none()),Qg.hide(d))},0)}),u.on("SwitchMode",function(){u.readonly&&(m.set(tn.none()),Qg.hide(d))}),u.on("NodeChange",function(n){Ta(d.element()).fold(function(){b(tp.setEditorTimeout(u,i,0))},function(n){})})})},KB=function(n,o,r){function t(t,e){fn([o,r],function(n){n.broadcastEvent(t,e)})}function e(t,e){fn([o,r],function(n){n.broadcastOn([t],e)})}function i(n){return e(Uf(),{target:n.target()})}function u(n){return e(Uf(),{target:ir.fromDom(n.target)})}function a(n){0===n.button&&e(Gf(),{target:ir.fromDom(n.target)})}function c(n){return t(di(),Ib(n))}function s(n){e(Wf(),{}),t(mi(),Ib(n))}function f(){return e(Wf(),{})}var l=Bb(ir.fromDom(v.document),"touchstart",i),d=Bb(ir.fromDom(v.document),"touchmove",function(n){return t(fi(),n)}),m=Bb(ir.fromDom(v.document),"touchend",function(n){return t(li(),n)}),g=Bb(ir.fromDom(v.document),"mousedown",i),p=Bb(ir.fromDom(v.document),"mouseup",function(n){0===n.raw().button&&e(Gf(),{target:n.target()})});n.on("PostRender",function(){n.on("click",u),n.on("tap",u),n.on("mouseup",a),n.on("ScrollWindow",c),n.on("ResizeWindow",s),n.on("ResizeEditor",f)}),n.on("remove",function(){n.off("click",u),n.off("tap",u),n.off("mouseup",a),n.off("ScrollWindow",c),n.off("ResizeWindow",s),n.off("ResizeEditor",f),g.unbind(),l.unbind(),d.unbind(),m.unbind(),p.unbind()}),n.on("detach",function(){Bs(o),Bs(r),o.destroy(),r.destroy()})},JB=Bl,$B=Tl,QB=nn([pt("shell",!1),tt("makeItem"),pt("setupItem",Z),el("listBehaviours",[Cg])]),ZB=kl({name:"items",overrides:function(){return{behaviours:Sa([Cg.config({})])}}}),nD=nn([ZB]),tD=Ml({name:nn("CustomList")(),configFields:QB(),partFields:nD(),factory:function(s,n,t,e){var o=s.shell?{behaviours:[Cg.config({})],components:[]}:{behaviours:[],components:n},r=function(n){return s.shell?tn.some(n):of(n,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:Us(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw v.console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(t){var n=Cg.contents(t),e=c.length,o=e-n.length,r=0<o?function(n,t){for(var e=[],o=0;o<n;o++)e.push(t(o));return e}(o,function(){return s.makeItem()}):[],i=n.slice(e);fn(i,function(n){return Cg.remove(t,n)}),fn(r,function(n){return Cg.append(t,n)});var u=Cg.contents(t);fn(u,function(n,t){s.setupItem(a,n,c[t],t)})})}}}},apis:{setItems:function(n,t,e){n.setItems(t,e)}}}),eD=xn([{"static":[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),oD=function(n,t,e){n.getSystem().isConnected()&&function(t,e,o){var r=e.lazyViewport(t),i=o.isDocked();i&&kE(t,e,o,r),wE(t,e,r,o).each(function(n){o.setDocked(!i),n.fold(function(){return SE(t,e)},function(n){return CE(t,e,n)},function(n){kE(t,e,o,r,!0),CE(t,e,n)})})}(n,t,e)},rD=/* */Object.freeze({__proto__:null,refresh:oD,reset:_E,isDocked:function(n,t,e){return e.isDocked()}}),iD=/* */Object.freeze({__proto__:null,events:function(o,r){return Gt([ne(Jr(),function(t,e){o.contextual.each(function(n){$e(t.element(),n.transitionClass)&&(Ze(t.element(),[n.transitionClass,n.fadeInClass]),(r.isVisible()?n.onShown:n.onHidden)(t));e.stop()})}),qt(di(),function(n,t){oD(n,o,r)}),qt(mi(),function(n,t){_E(n,o,r)})])}}),uD=[gt("contextual",[ot("fadeInClass"),ot("fadeOutClass"),ot("transitionClass"),it("lazyContext"),Ku("onShow"),Ku("onShown"),Ku("onHide"),Ku("onHidden")]),wt("lazyViewport",xu),(MB="modes",FB=["top","bottom"],IB=Qo,ht(MB,FB,Ln(IB))),Ku("onDocked"),Ku("onUndocked")],aD=Ca({fields:uD,name:"docking",active:iD,apis:rD,state:/* */Object.freeze({__proto__:null,init:function(){var t=or(!1),e=or(!0),o=or(tn.none());return Yi({isDocked:function(){return t.get()},setDocked:function(n){return t.set(n)},getInitialPosition:function(){return o.get()},setInitialPosition:function(n){return o.set(n)},isVisible:function(){return e.get()},setVisible:function(n){return e.set(n)},readState:function(){return"docked:  "+t.get()+", visible: "+e.get()}})}})}),cD={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},sD="tox-tinymce--toolbar-sticky-on",fD="tox-tinymce--toolbar-sticky-off",lD=/* */Object.freeze({__proto__:null,setup:function(t,n){t.inline||(pp(t)||t.on("ResizeEditor",function(){n().each(aD.reset)}),t.on("ResizeWindow ResizeEditor",function(){n().each(function(n){return TE(n,pp(t))})}),t.on("SkinLoaded",function(){n().each(function(n){aD.isDocked(n)?aD.reset(n):aD.refresh(n)})}),t.on("FullscreenStateChanged",function(){n().each(aD.reset)})),t.on("AfterScrollIntoView",function(e){n().each(function(n){aD.refresh(n);var t=n.element();Sd(t)&&function(n,t){var e=ie(t),o=e.dom().defaultView.innerHeight,r=du(e),i=ir.fromDom(n.elm),u=yu(i),a=ru(i),c=u.y(),s=c+a,f=au(t),l=ru(t),d=f.top(),m=d+l,g=Math.abs(d-r.top())<2,p=Math.abs(m-(r.top()+o))<2;if(g&&c<m)mu(r.left(),c-l,e);else if(p&&d<s){var h=c-o+a+l;mu(r.left(),h,e)}}(e,t)})}),t.on("PostRender",function(){BE(t,!1)})},isDocked:function(n){return n().map(aD.isDocked).getOr(!1)},getBehaviours:DE}),dD=Z,mD=u,gD=nn([]),pD=/* */Object.freeze({__proto__:null,setup:dD,isDocked:mD,getBehaviours:gD}),hD=Al({factory:function(t,o){var n={focus:wg.focusIn,setMenus:function(n,t){var e=S(t,function(t){var n={type:"menubutton",text:t.text,fetch:function(n){n(t.getItems())}},e=qO(n).mapError(function(n){return Ko(n)}).getOrDie();return HC(e,"tox-mbtn",o.backstage,tn.some("menuitem"))});Cg.set(n,e)}};return{uid:t.uid,dom:t.dom,components:[],behaviours:Sa([Cg.config({}),Qd("menubar-events",[Oi(function(n){t.onSetup(n)}),qt(jr(),function(e,n){Vu(e.element(),".tox-mbtn--active").each(function(t){Hu(n.event().target(),".tox-mbtn").each(function(n){Rt(t,n)||e.getSystem().getByDom(t).each(function(t){e.getSystem().getByDom(n).each(function(n){Tw.expand(n),Tw.close(t),Tg.focus(n)})})})})}),qt(bi(),function(e,n){n.event().prevFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(t){n.event().newFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(n){Tw.isOpen(t)&&(Tw.expand(n),Tw.close(t))})})})]),wg.config({mode:"flow",selector:".tox-mbtn",onEscape:function(n){return t.onEscape(n),tn.some(!0)}}),Wy.config({})]),apis:n,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[tt("dom"),tt("uid"),tt("onEscape"),tt("backstage"),pt("onSetup",Z)],apis:{focus:function(n,t){n.focus(t)},setMenus:function(n,t,e){n.setMenus(t,e)}}}),vD="container",bD=[Ls("slotBehaviours",[])],yD=function(r,n){function t(n){return cf(r)}function e(e,o){return function(n,t){return of(n,r,t).map(function(n){return e(n,t)}).getOr(o)}}function o(n,t){return"true"!==ke(n.element(),"aria-hidden")}var i,u=e(o,!1),a=e(function(n,t){if(o(n)){var e=n.element();ro(e,"display","none"),Ce(e,"aria-hidden","true"),Lt(n,yi(),{name:t,visible:!1})}}),c=(i=a,function(t,n){fn(n,function(n){return i(t,n)})}),s=e(function(n,t){if(!o(n)){var e=n.element();fo(e,"display"),_e(e,"aria-hidden"),Lt(n,yi(),{name:t,visible:!0})}}),f={getSlotNames:t,getSlot:function(n,t){return of(n,r,t)},isShowing:u,hideSlot:a,hideAllSlots:function(n){return c(n,t())},showSlot:s};return{uid:r.uid,dom:r.dom,components:n,behaviours:js(r.slotBehaviours),apis:f}},xD=L({getSlotNames:function(n,t){return n.getSlotNames(t)},getSlot:function(n,t,e){return n.getSlot(t,e)},isShowing:function(n,t,e){return n.isShowing(t,e)},hideSlot:function(n,t,e){return n.hideSlot(t,e)},hideAllSlots:function(n,t){return n.hideAllSlots(t)},showSlot:function(n,t,e){return n.showSlot(t,e)}},function(n){return Re(n)}),wD=P(P({},xD),{sketch:function(n){var e,t=(e=[],{slot:function(n,t){return e.push(n),Qs(vD,AE(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=S(r,function(n){return Sl({name:n,pname:AE(n)})});return mf(vD,bD,i,yD,o)}}),SD=jo([dt("icon"),dt("tooltip"),wt("onShow",Z),wt("onHide",Z),wt("onSetup",function(){return Z})]),CD=De("FixSizeEvent"),kD=De("AutoSizeEvent"),OD=$B.optional({factory:hD,name:"menubar",schema:[tt("backstage")]}),_D=$B.optional({factory:{sketch:function(n){return tD.sketch({uid:n.uid,dom:n.dom,listBehaviours:Sa([wg.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return XO({type:n.type,uid:De("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],onEscape:function(){return tn.none()}})},setupItem:function(n,t,e,o){J_.setGroups(t,e)},shell:!0})}},name:"multiple-toolbar",schema:[tt("dom"),tt("onEscape")]}),TD=$B.optional({factory:{sketch:function(n){return function(n){return n.type===Dm.sliding?GO:n.type===Dm.floating?WO:XO}(n)({type:n.type,uid:n.uid,onEscape:function(){return n.onEscape(),tn.some(!0)},cyclicKeying:!1,initGroups:[],getSink:n.getSink,backstage:n.backstage,moreDrawerData:{lazyToolbar:n.lazyToolbar,lazyMoreButton:n.lazyMoreButton,lazyHeader:n.lazyHeader},attributes:n.attributes})}},name:"toolbar",schema:[tt("dom"),tt("onEscape"),tt("getSink")]}),ED=$B.optional({factory:{sketch:function(n){var t=n.editor,e=n.sticky?DE:gD;return{uid:n.uid,dom:n.dom,components:n.components,behaviours:Sa(e(t,n.getSink))}}},name:"header",schema:[tt("dom")]}),BD=$B.optional({name:"socket",schema:[tt("dom")]}),DD=$B.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:Sa([Wy.config({}),Tg.config({}),cT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(n){ed.getCurrent(n).each(wD.hideAllSlots),zt(n,kD)},onGrown:function(n){zt(n,kD)},onStartGrow:function(n){Lt(n,CD,{width:co(n.element(),"width").getOr("")})},onStartShrink:function(n){Lt(n,CD,{width:cu(n.element())+"px"})}}),Cg.config({}),ed.config({find:function(n){var t=Cg.contents(n);return ln(t)}})])}],behaviours:Sa([xS(0),Qd("sidebar-sliding-events",[qt(CD,function(n,t){ro(n.element(),"width",t.event().width())}),qt(kD,function(n,t){fo(n.element(),"width")})])])}}},name:"sidebar",schema:[tt("dom")]}),AD=$B.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:Sa([Cg.config({})]),components:[]}}},name:"throbber",schema:[tt("dom")]}),MD=Ml({name:"OuterContainer",factory:function(e,n,t){var o={getSocket:function(n){return JB.getPart(n,e,"socket")},setSidebar:function(n,t){JB.getPart(n,e,"sidebar").each(function(n){return IE(n,t)})},toggleSidebar:function(n,t){JB.getPart(n,e,"sidebar").each(function(n){return function(n,e){ed.getCurrent(n).each(function(t){ed.getCurrent(t).each(function(n){cT.hasGrown(t)?wD.isShowing(n,e)?cT.shrink(t):(wD.hideAllSlots(n),wD.showSlot(n,e)):(wD.hideAllSlots(n),wD.showSlot(n,e),cT.grow(t))})})}(n,t)})},whichSidebar:function(n){return JB.getPart(n,e,"sidebar").bind(RE).getOrNull()},getHeader:function(n){return JB.getPart(n,e,"header")},getToolbar:function(n){return JB.getPart(n,e,"toolbar")},setToolbar:function(n,t){JB.getPart(n,e,"toolbar").each(function(n){n.getApis().setGroups(n,t)})},setToolbars:function(n,t){JB.getPart(n,e,"multiple-toolbar").each(function(n){tD.setItems(n,t)})},refreshToolbar:function(n){JB.getPart(n,e,"toolbar").each(function(n){return n.getApis().refresh(n)})},getThrobber:function(n){return JB.getPart(n,e,"throbber")},focusToolbar:function(n){JB.getPart(n,e,"toolbar").orThunk(function(){return JB.getPart(n,e,"multiple-toolbar")}).each(function(n){wg.focusIn(n)})},setMenubar:function(n,t){JB.getPart(n,e,"menubar").each(function(n){hD.setMenus(n,t)})},focusMenubar:function(n){JB.getPart(n,e,"menubar").each(function(n){hD.focus(n)})}};return{uid:e.uid,dom:e.dom,components:n,apis:o,behaviours:e.behaviours}},configFields:[tt("dom"),tt("behaviours")],partFields:[ED,OD,TD,_D,BD,DD,AD],apis:{getSocket:function(n,t){return n.getSocket(t)},setSidebar:function(n,t,e){n.setSidebar(t,e)},toggleSidebar:function(n,t,e){n.toggleSidebar(t,e)},whichSidebar:function(n,t){return n.whichSidebar(t)},getHeader:function(n,t){return n.getHeader(t)},getToolbar:function(n,t){return n.getToolbar(t)},setToolbar:function(n,t,e){var o=S(e,function(n){return LO(n)});n.setToolbar(t,o)},setToolbars:function(n,t,e){var o=S(e,function(n){return S(n,LO)});n.setToolbars(t,o)},refreshToolbar:function(n,t){return n.refreshToolbar(t)},getThrobber:function(n,t){return n.getThrobber(t)},setMenubar:function(n,t,e){n.setMenubar(t,e)},focusMenubar:function(n,t){n.focusMenubar(t)},focusToolbar:function(n,t){n.focusToolbar(t)}}}),FD={file:{title:"File",items:"newdocument restoredraft | preview | print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},ID=function(n){function t(){n._skinLoaded=!0,rb(n)}return function(){n.initialized?t():n.on("init",t)}},RD=function(n,t){return function(){return ib(n,{message:t})}},VD=d(zE,!1),HD=d(zE,!0),ND=ep.DOM,PD=At(),zD=PD.os.isiOS()&&PD.os.version.major<=12,LD={render:function(e,o,n,t,r){var i=or(0);VD(e),function(n,t){Vf(n,t,me)}(ir.fromDom(r.targetNode),o.mothership),Es(Ji(),o.uiMothership),e.on("PostRender",function(){LE(e,o,n,t),i.set(e.getWin().innerWidth),MD.setMenubar(o.outerContainer,PE(e,n)),MD.setSidebar(o.outerContainer,n.sidebar),function(r){function n(n){var t=r.getDoc().documentElement,e=u.get(),o=a.get();e.left()!==i.innerWidth||e.top()!==i.innerHeight?(u.set(Cu(i.innerWidth,i.innerHeight)),cb(r,n)):o.left()===t.offsetWidth&&o.top()===t.offsetHeight||(a.set(Cu(t.offsetWidth,t.offsetHeight)),cb(r,n))}function t(n){return ab(r,n)}var i=r.getWin(),e=r.getDoc().documentElement,u=or(Cu(i.innerWidth,i.innerHeight)),a=or(Cu(e.offsetWidth,e.offsetHeight));ND.bind(i,"resize",n),ND.bind(i,"scroll",t);var o=Db(ir.fromDom(r.getBody()),"load",n);r.on("remove",function(){o.unbind(),ND.unbind(i,"resize",n),ND.unbind(i,"scroll",t)})}(e)});var u=MD.getSocket(o.outerContainer).getOrDie("Could not find expected socket element");if(!0===zD){io(u.element(),{overflow:"scroll","-webkit-overflow-scrolling":"touch"});var a=function(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null===r&&(r=v.setTimeout(function(){e.apply(null,n),r=null},o))}}}(function(){e.fire("ScrollContent")},20);Bb(u.element(),"scroll",a.throttle)}NO(e,o),e.addCommand("ToggleSidebar",function(n,t){MD.toggleSidebar(o.outerContainer,t),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return MD.whichSidebar(o.outerContainer)});var c=ip(e);return c!==Dm.sliding&&c!==Dm.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var n=e.getWin().innerWidth;n!==i.get()&&(MD.refreshToolbar(o.outerContainer),i.set(n))}),{iframeContainer:u.element().dom(),editorContainer:o.outerContainer.element().dom()}}},jD=function(t,n,e){var o=n.filter(function(n){return t<n}),r=e.filter(function(n){return n<t});return o.or(r).getOr(t)},UD=function(n){return/^[0-9\.]+(|px)$/i.test(""+n)?tn.some(parseInt(""+n,10)):tn.none()},WD=function(n){return rn(n)?n+"px":n},GD={render:function(t,i,e,o,n){var u,r=ep.DOM,a=sp(t),c=lp(t),s=ir.fromDom(n.targetNode),f=km(t).or(UE(t)),l=ip(t),d=l===Dm.floating,m=l===Dm.sliding||d,g=pp(t),p=or(WE(s,g)),h=or(!1);HD(t);function v(n){void 0===n&&(n=!1),m&&MD.refreshToolbar(i.outerContainer),a||function(n){var t=m?n.fold(function(){return 0},function(n){return 1<n.components().length?ru(n.components()[1].element()):0}):0,e=bu(s),o=g?e.y()-ru(u.element())+t:e.bottom();io(i.outerContainer.element(),{position:"absolute",top:Math.round(o)+"px",left:Math.round(e.x())+"px"});var r=f.getOrThunk(function(){var n=UD(ao(Ji(),"margin-left")).getOr(0);return cu(Ji())-e.x()+n});ro(u.element(),"max-width",r+"px")}(MD.getToolbar(i.outerContainer)),c&&(n?aD.reset(u):aD.refresh(u)),d&&MD.getToolbar(i.outerContainer).each(function(n){rT.reposition(n)})}function b(){h.set(!0),ro(i.outerContainer.element(),"display","flex"),r.addClass(t.getBody(),"mce-edit-focus"),fo(i.uiMothership.element(),"display"),v()}function y(){h.set(!1),i.outerContainer&&(ro(i.outerContainer.element(),"display","none"),r.removeClass(t.getBody(),"mce-edit-focus")),ro(i.uiMothership.element(),"display","none")}function x(){if(u)b();else{u=MD.getHeader(i.outerContainer).getOrDie();var n=function(n){return cp(n).getOr(Ji())}(t);Es(n,i.mothership),Es(n,i.uiMothership),LE(t,i,e,o),MD.setMenubar(i.outerContainer,PE(t,e)),b(),t.on("activate",b),t.on("deactivate",y),t.on("SkinLoaded ResizeWindow",function(){h.get()&&v(!0)}),t.on("NodeChange keydown",function(){tp.requestAnimationFrame(function(){var n=WE(s,g);h.get()&&n!==p.get()&&(v(!0),p.set(n))})}),t.nodeChanged()}}return t.on("focus",x),t.on("blur hide",y),t.on("init",function(){t.hasFocus()&&x()}),NO(t,i),{editorContainer:i.outerContainer.element().dom()}}},XD=function(t){vk.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,onAction:function(){return t.execCommand(n.cmd)},icon:n.icon,onSetup:u_(t,n.name)})});var n="alignnone",e="No alignment",o="JustifyNone",r="align-none";t.ui.registry.addButton(n,{tooltip:e,onAction:function(){return t.execCommand(o)},icon:r})},YD=function(n){XE(n),function(t){vk.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(n){t.ui.registry.addMenuItem(n.name,{text:n.text,icon:n.icon,shortcut:n.shortcut,onAction:function(){return t.execCommand(n.action)}})}),t.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:GE(t,"code")})}(n)},qD=function(n){!function(t){t.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(n){return YE(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(n){return YE(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n),function(t){t.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(n){return YE(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(n){return YE(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n)},KD=function(n){!function(n){n.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return n.execCommand("mceToggleVisualAid")}})}(n),function(t){t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(n){return function(t,n){t.setActive(n.hasVisual);function e(n){t.setActive(n.hasVisual)}return n.on("VisualAid",e),function(){return n.off("VisualAid",e)}}(n,t)},onAction:function(){t.execCommand("mceToggleVisualAid")}})}(n)},JD=function(n){!function(t){t.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(n){return function(n,t){n.setDisabled(!t.queryCommandState("outdent"));function e(){n.setDisabled(!t.queryCommandState("outdent"))}return t.on("NodeChange",e),function(){return t.off("NodeChange",e)}}(n,t)},onAction:function(){return t.execCommand("outdent")}}),t.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return t.execCommand("indent")}})}(n)},$D=function(n,t){!function(n,t){var e=c_(0,t,tE(n));n.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=c_(0,t,oE(n));n.ui.registry.addNestedMenuItem("fontformats",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=P({type:"advanced"},t.styleselect),o=c_(0,t,sE(n,e));n.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return o.items.validateItems(o.getStyleItems())}})}(n,t),function(n,t){var e=c_(0,t,cE(n));n.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=c_(0,t,iE(n));n.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t)},QD=function(n,t){XD(n),YD(n),$D(n,t),qD(n),lb.register(n),KD(n),JD(n)},ZD=function(n){return{anchor:"selection",root:ir.fromDom(n.selection.getNode())}},nA={onLtr:function(){return[lc,aa,ca,sa,fa,fc,Zg,np,bm,hm,ym,vm]},onRtl:function(){return[lc,ca,aa,fa,sa,fc,Zg,np,ym,vm,bm,hm]}},tA={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},eA=function(n){return n.settings.contextmenu_never_use_native||!1},oA=function(n){return function(n,t,e){var o=n.ui.registry.getAll().contextMenus;return bn(n.settings,t).map(eB).getOrThunk(function(){return C(eB(e),function(n){return yn(o,n)})})}(n,"contextmenu","link linkchecker image imagetools table spellchecker configurepermanentpen")},rA=function(n){return!1===n.getParam("contextmenu")},iA={type:"separator"},uA=function(t){if(J(t))return t;switch(t.type){case"separator":return iA;case"submenu":return{type:"nestedmenuitem",text:t.text,icon:t.icon,getSubmenuItems:function(){var n=t.getSubmenuItems();return J(n)?n:S(n,uA)}};default:return{type:"menuitem",text:t.text,icon:t.icon,onAction:function(n){return function(){return n()}}(t.onAction)}}},aA=xn([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),cA=aA.offset,sA=aA.absolute,fA=aA.fixed,lA=function(n,t,r,i,u){var e=t.getSnapPoints(n);return vB(e,r,i,u).orThunk(function(){return O(e,function(t,e){var n=e.sensor(),o=function(n,t,e,o,r,i){var u=fB(n,r,i),a=fB(t,r,i),c=Math.abs(u.left()-a.left()),s=Math.abs(u.top()-a.top());return Cu(c,s)}(r,n,e.range().left(),e.range().top(),i,u);return t.deltas.fold(function(){return{deltas:tn.some(o),snap:tn.some(e)}},function(n){return(o.left()+o.top())/2<=(n.left()+n.top())/2?{deltas:tn.some(o),snap:tn.some(e)}:t})},{deltas:tn.none(),snap:tn.none()}).snap.map(function(n){return{output:nn(mB(n.output(),r,i,u)),extra:n.extra}})})},dA=function(n,t,e,o,r){var i=t.getSnapPoints(n);return vB(i,e,o,r)},mA=/* */Object.freeze({__proto__:null,snapTo:function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=ie(n.element()),u=du(i),a=gE(r),c=function(n,t,e){return{coord:mB(n.output(),n.output(),t,e),extra:n.extra()}}(o,u,a),s=dB(c.coord,0,a);uo(r,s)}}}),gA="data-initial-z-index",pA=gt("snaps",[tt("getSnapPoints"),Ku("onSensor"),tt("leftAttr"),tt("topAttr"),pt("lazyViewport",xu),pt("mustSnap",!1)]),hA=[pt("useFixed",u),tt("blockerClass"),pt("getTarget",l),pt("onDrag",Z),pt("repositionTarget",!0),pt("onDrop",Z),wt("getBounds",xu),pA],vA=/* */Object.freeze({__proto__:null,getData:function(n){return tn.from(Cu(n.x(),n.y()))},getDelta:function(n,t){return Cu(t.left()-n.left(),t.top()-n.top())}}),bA=p(hA,[Zu("dragger",{handlers:_B(TB)})]),yA=/* */Object.freeze({__proto__:null,getData:function(n){var t=n.raw().touches;return 1===t.length?function(n){var t=n[0];return tn.some(Cu(t.clientX,t.clientY))}(t):tn.none()},getDelta:function(n,t){return Cu(t.left()-n.left(),t.top()-n.top())}}),xA=bA,wA=p(hA,[Zu("dragger",{handlers:_B(EB)})]),SA=p(hA,[Zu("dragger",{handlers:_B(function(n,t,e){return p(TB(n,t,e),EB(n,t,e))})})]),CA=/* */Object.freeze({__proto__:null,mouse:xA,touch:wA,mouseOrTouch:SA}),kA=/* */Object.freeze({__proto__:null,init:function(){var o=tn.none(),t=tn.none(),n=nn({});return Yi({readState:n,reset:function(){o=tn.none(),t=tn.none()},update:function(t,n){return t.getData(n).bind(function(n){return function(t,e){var n=o.map(function(n){return t.getDelta(n,e)});return o=tn.some(e),n}(t,n)})},getStartData:function(){return t},setStartData:function(n){t=tn.some(n)}})}}),OA=ka({branchKey:"mode",branches:CA,name:"dragging",active:{events:function(n,t){return n.dragger.handlers(n,t)}},extra:{snap:re(["sensor","range","output"],["extra"])},state:kA,apis:mA}),_A=At(),TA=function(c,e){function t(n){var t=yu(n);return BB(g.getOpt(e),n,t.x(),t.y(),t.width(),t.height())}function o(n){var t=yu(n);return BB(p.getOpt(e),n,t.right(),t.bottom(),t.width(),t.height())}function r(n,t,e,o){var r=e(t);OA.snapTo(n,r),function(n,t,e,o){var r=t.dom().getBoundingClientRect();fo(n.element(),"display");var i=ae(ir.fromDom(c.getBody())).dom().innerHeight,u=e(r),a=o(r,i);(u||a)&&ro(n.element(),"display","none")}(n,t,function(n){return n[o]<0},function(n,t){return n[o]>t})}function i(n){return r(h,n,t,"top")}function u(n){return r(v,n,o,"bottom")}var a=or([]),s=or([]),n=or(!1),f=or(tn.none()),l=or(tn.none()),d=DB(function(){return S(a.get(),function(n){return t(n)})},f,function(t){l.get().each(function(n){c.fire("TableSelectorChange",{start:t,finish:n})})}),m=DB(function(){return S(s.get(),function(n){return o(n)})},l,function(t){f.get().each(function(n){c.fire("TableSelectorChange",{start:n,finish:t})})}),g=AB(d),p=AB(m),h=tu(g.asSpec()),v=tu(p.asSpec());_A.deviceType.isTouch()&&(c.on("TableSelectionChange",function(t){n.get()||(ks(e,h),ks(e,v),n.set(!0)),f.set(tn.some(t.start)),l.set(tn.some(t.finish)),t.otherCells.each(function(n){a.set(n.upOrLeftCells),s.set(n.downOrRightCells),i(t.start),u(t.finish)})}),c.on("ResizeEditor ResizeWindow ScrollContent",function(){f.get().each(i),l.get().each(u)}),c.on("TableSelectionClear",function(){n.get()&&(_s(h),_s(v),n.set(!1)),f.set(tn.none()),l.set(tn.none())}))};(VB=RB=RB||{})[VB.None=0]="None",VB[VB.Both=1]="Both",VB[VB.Vertical=2]="Vertical";function EA(n,t,e){var o=ir.fromDom(n.getContainer()),r=function(n,t,e,o,r){var i={};return i.height=jD(o+t.top(),Cm(n),Om(n)),e===RB.Both&&(i.width=jD(r+t.left(),Sm(n),km(n))),i}(n,t,e,ru(o),cu(o));pn(r,function(n,t){return ro(o,t,WD(n))}),ub(n)}function BA(n){if(1===n.nodeType){if("BR"===n.nodeName||n.getAttribute("data-mce-bogus"))return!0;if("bookmark"===n.getAttribute("data-mce-type"))return!0}return!1}function DA(o,t){var r,n,e;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(n=function(){var n=[];return o.getParam("elementpath",!0,"boolean")&&n.push(vM(o,{})),Bt(o.settings.plugins,"wordcount")&&n.push(function(n,o){function r(n,t,e){return Cg.set(n,[wo(o.translate(["{0} "+e,t[e]]))])}return hp.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:Sa([Wy.config({}),Cg.config({}),tl.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Qd("wordcount-events",[Ei(function(n){var t=tl.getValue(n),e="words"===t.mode?"characters":"words";tl.setValue(n,{mode:e,count:t.count}),r(n,t.count,e)}),Oi(function(e){n.on("wordCountUpdate",function(n){var t=tl.getValue(e).mode;tl.setValue(e,{mode:t,count:n.wordCount}),r(e,n.wordCount,t)})})])])})}(o,t)),o.getParam("branding",!0,"boolean")&&n.push(function(){var n=_h.translate(["Powered by {0}","Tiny"]);return{dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+n+'">'+n+"</a>"}}}()),0<n.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:n}]:[]}(),e=function(n){var t=!Bt(n.settings.plugins,"autoresize"),e=n.getParam("resize",t);return!1===e?RB.None:"both"===e?RB.Both:RB.Vertical}(o),e!==RB.None&&n.push((r=e,{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize"),"aria-hidden":"true"},innerHtml:gp("resize-handle",t.icons)},behaviours:Sa([OA.config({mode:"mouse",repositionTarget:!1,onDrag:function(n,t,e){EA(o,e,r)},blockerClass:"tox-blocker"})])})),n)}}function AA(n){return[ot("type"),function(n){return et(n,$o)}("columns"),n]}function MA(t){return Xo("items","items",Ro(),Ln(Xn(function(n){return qn("Checking item of "+t,hF,n).fold(function(n){return K.error(Ko(n))},function(n){return K.value(n)})})))}function FA(n){return J(n.type)&&J(n.name)}function IA(n){var t=function(n){return C(AF(n),FA)}(n),e=E(t,function(t){return function(n){return tn.from(MF[n.type])}(t).fold(function(){return[]},function(n){return[et(t.name,n)]})});return jo(e)}function RA(n){return{internalDialog:Kn(function(n){return qn("dialog",DF,n)}(n)),dataValidator:IA(n),initialData:n.initialData}}function VA(n){var e=[],o={};return pn(n,function(n,t){n.fold(function(){e.push(t)},function(n){o[t]=n})}),0<e.length?K.error(e):K.value(o)}function HA(n,t){ro(n,"height",t+"px"),At().browser.isIE()?fo(n,"flex-basis"):ro(n,"flex-basis",t+"px")}function NA(n,o,r){Ru(n,'[role="dialog"]').each(function(e){Vu(e,'[role="tablist"]').each(function(t){r.get().map(function(n){return ro(o,"height","0"),ro(o,"flex-basis","0"),Math.min(n,function(n,t,e){var o,r=ue(n).dom(),i=Ru(n,".tox-dialog-wrap").getOr(n);o="fixed"===ao(i,"position")?Math.max(r.clientHeight,v.window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight);var u=ru(t),a=t.dom().offsetLeft>=e.dom().offsetLeft+cu(e)?Math.max(ru(e),u):u,c=parseInt(ao(n,"margin-top"),10)||0,s=parseInt(ao(n,"margin-bottom"),10)||0;return o-(ru(n)+c+s-a)}(e,o,t))}).each(function(n){HA(o,n)})})})}function PA(n){return Vu(n,'[role="tabpanel"]')}function zA(o){var i;return{smartTabHeight:(i=or(tn.none()),{extraEvents:[Oi(function(n){var t=n.element();PA(t).each(function(e){ro(e,"visibility","hidden"),n.getSystem().getByDom(e).toOption().each(function(n){var t=function(n){return ln(F(n,function(n,t){return t<n?-1:n<t?1:0}))}(function(o,r,i){return S(o,function(n,t){Cg.set(i,o[t].view());var e=r.dom().getBoundingClientRect();return Cg.set(i,[]),e.height})}(o,e,n));i.set(t)}),NA(t,e,i),fo(e,"visibility"),function(n,t){ln(n).each(function(n){return XF.showTab(t,n.value)})}(o,n),tp.requestAnimationFrame(function(){NA(t,e,i)})})}),qt(mi(),function(n){var t=n.element();PA(t).each(function(n){NA(t,n,i)})}),qt(my,function(n,t){var r=n.element();PA(r).each(function(t){var n=_a();ro(t,"visibility","hidden");var e=co(t,"height").map(function(n){return parseInt(n,10)});fo(t,"height"),fo(t,"flex-basis");var o=t.dom().getBoundingClientRect().height;e.forall(function(n){return n<o})?(i.set(tn.from(o)),NA(r,t,i)):e.each(function(n){HA(t,n)}),fo(t,"visibility"),n.each(Oa)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}}function LA(n,t,e,o){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:P(P({},t.map(function(n){return{id:n}}).getOr({})),o?{"aria-live":"polite"}:{})},components:[],behaviours:Sa([xS(0),VT.config({channel:$F,updateState:function(n,t){return tn.some({isTabPanel:function(){return"tabpanel"===t.body.type}})},renderComponents:function(n){switch(n.body.type){case"tabpanel":return[function(n,e){function o(n){var t=tl.getValue(n),e=VA(t).getOr({}),o=i.get(),r=Sn(o,e);i.set(r)}function r(n){var t=i.get();tl.setValue(n,t)}var i=or({}),u=or(null),t=S(n.tabs,function(n){return{value:n.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:e.shared.providers.translate(n.title)},view:function(){return[mS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"]},components:S(n.items,function(n){return jk(t,n,e)}),formBehaviours:Sa([wg.config({mode:"acyclic",useTabstopAt:b(DS)}),Qd("TabView.form.events",[Oi(r),_i(o)]),bc.config({channels:An([{key:YF,value:{onReceive:o}},{key:qF,value:{onReceive:r}}])})])}})]}}}),a=zA(t).smartTabHeight;return XF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(n,t,e){var o=tl.getValue(t);Lt(n,dy,{name:o,oldName:u.get()}),u.set(o)},tabs:t,components:[XF.parts().tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[zF.parts().tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:Sa([Wy.config({})])}),XF.parts().tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:a.selectFirst,tabSectionBehaviours:Sa([Qd("tabpanel",a.extraEvents),wg.config({mode:"acyclic"}),ed.config({find:function(n){return ln(XF.getViewItems(n))}}),tl.config({store:{mode:"manual",getValue:function(n){return n.getSystem().broadcastOn([YF],{}),i.get()},setValue:function(n,t){i.set(t),n.getSystem().broadcastOn([qF],{})}}})])})}(n.body,e)];default:return[function(n,e){var t=dp(mS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"].concat(n.classes)},components:S(n.items,function(n){return jk(t,n,e)})}}));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[t.asSpec()]}],behaviours:Sa([wg.config({mode:"acyclic",useTabstopAt:b(DS)}),yS(t),kS(t,{postprocess:function(n){return VA(n).fold(function(n){return v.console.error(n),{}},function(n){return n})}})])}}(n.body,e)]}},initialData:n})])}}function jA(n,e){return[Qt(Ur(),AS),n(uy,function(n,t){e.onClose(),t.onClose()}),n(ay,function(n,t,e,o){t.onCancel(n),zt(o,uy)}),qt(ly,function(n,t){return e.onUnblock()}),qt(fy,function(n,t){return e.onBlock(t.event())})]}function UA(n,t){function e(n,t){return ry.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+n]},components:S(t,function(n){return n.memento.asSpec()})})}var o=function(n,t){for(var e=[],o=[],r=0,i=n.length;r<i;r++){var u=n[r];(t(u,r)?e:o).push(u)}return{pass:e,fail:o}}(t.map(function(n){return n.footerButtons}).getOr([]),function(n){return"start"===n.align});return[e("start",o.pass),e("end",o.fail)]}function WA(n,o){return{dom:VE('<div class="tox-dialog__footer"></div>'),components:[],behaviours:Sa([VT.config({channel:QF,initialData:n,updateState:function(n,t){var e=S(t.buttons,function(n){var t=dp(function(n,t){return GC(n,n.type,t)}(n,o));return{name:n.name,align:n.align,memento:t}});return tn.some({lookupByName:function(n,t){return function(t,n,e){return _(n,function(n){return n.name===e}).bind(function(n){return n.memento.getOpt(t)})}(n,e,t)},footerButtons:e})},renderComponents:UA})])}}function GA(n,t){return CM.parts().footer(WA(n,t))}function XA(t,e){if(t.getRoot().getSystem().isConnected()){var o=ed.getCurrent(t.getFormWrapper()).getOr(t.getFormWrapper());return mS.getField(o,e).fold(function(){var n=t.getFooter();return VT.getState(n).get().bind(function(n){return n.lookupByName(o,e)})},function(n){return tn.some(n)})}return tn.none()}function YA(u,o,a){function n(n){var t=u.getRoot();t.getSystem().isConnected()&&n(t)}var c={getData:function(){var n=u.getRoot(),t=n.getSystem().isConnected()?u.getFormWrapper():n,e=tl.getValue(t),o=L(a,function(n){return n.get()});return P(P({},e),o)},setData:function(i){n(function(n){var t=c.getData(),e=P(P({},t),i),o=function(n,t){var e=n.getRoot();return VT.getState(e).get().map(function(n){return Kn(qn("data",n.dataValidator,t))}).getOr(t)}(u,e),r=u.getFormWrapper();tl.setValue(r,o),pn(a,function(n,t){yn(e,t)&&n.set(e[t])})})},disable:function(n){XA(u,n).each(Gh.disable)},enable:function(n){XA(u,n).each(Gh.enable)},focus:function(n){XA(u,n).each(Tg.focus)},block:function(t){if(!J(t))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){Lt(n,fy,{message:t})})},unblock:function(){n(function(n){zt(n,ly)})},showTab:function(e){n(function(n){var t=u.getBody();VT.getState(t).get().exists(function(n){return n.isTabPanel()})&&ed.getCurrent(t).each(function(n){XF.showTab(n,e)})})},redial:function(e){n(function(n){var t=o(e);n.getSystem().broadcastOn([KF],t),n.getSystem().broadcastOn([JF],t.internalDialog),n.getSystem().broadcastOn([$F],t.internalDialog),n.getSystem().broadcastOn([QF],t.internalDialog),c.setData(t.initialData)})},close:function(){n(function(n){zt(n,uy)})}};return c}function qA(n,t){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[n,t]}}function KA(n,t){return CM.parts().close(hp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:n,buttonBehaviours:Sa([Wy.config({})])}))}function JA(){return CM.parts().title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})}function $A(n,t){return CM.parts().body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:VE("<p>"+t.translate(n)+"</p>")}]}]})}function QA(n){return CM.parts().footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:n})}function ZA(n,t){return[ry.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:n}),ry.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})]}function nM(t){var n,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return CM.sketch({lazySink:t.lazySink,onEscape:function(n){return t.onEscape(n),tn.some(!0)},useTabstopAt:function(n){return!DS(n)},dom:{tag:"div",classes:[e].concat(t.extraClasses),styles:P({position:"relative"},t.extraStyles)},components:p([t.header,t.body],t.footer.toArray()),parts:{blocker:{dom:VE('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:eI?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:Sa(p([Tg.config({}),Qd("dialog-events",t.dialogEvents.concat([ne(Ur(),function(n,t){wg.focusIn(n)})])),Qd("scroll-lock",[Oi(function(){qe(Ji(),i)}),_i(function(){Je(Ji(),i)})])],t.extraBehaviours)),eventOrder:P((n={},n[oi()]=["dialog-events"],n[gi()]=["scroll-lock","dialog-events","alloy.base.behaviour"],n[pi()]=["alloy.base.behaviour","dialog-events","scroll-lock"],n),t.eventOrder)})}function tM(n){return hp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close"),title:n.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:'<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M17.953 7.453L13.422 12l4.531 4.547-1.406 1.406L12 13.422l-4.547 4.531-1.406-1.406L10.578 12 6.047 7.453l1.406-1.406L12 10.578l4.547-4.531z" fill-rule="evenodd"></path></svg>'}}],action:function(n){zt(n,ay)}})}function eM(n,t,e){function o(n){return[wo(e.translate(n.title))]}return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:P({},t.map(function(n){return{id:n}}).getOr({}))},components:o(n),behaviours:Sa([VT.config({channel:JF,renderComponents:o})])}}function oM(){return{dom:VE('<div class="tox-dialog__draghandle"></div>')}}function rM(n,t){return function(n,t){var e=CM.parts().title(eM(n,tn.none(),t)),o=CM.parts().draghandle(oM()),r=CM.parts().close(tM(t)),i=[e].concat(n.draggable?[o]:[]).concat([r]);return ry.sketch({dom:VE('<div class="tox-dialog__header"></div>'),components:i})}({title:t.shared.providers.translate(n),draggable:t.dialog.isDraggableModal()},t.shared.providers)}function iM(n,t){return{onClose:function(){return t.closeWindow()},onBlock:function(e){CM.setBusy(n(),function(n,t){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message()},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:VE('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){CM.setIdle(n())}}}function uM(n,t,e,o){var r;return tu(nM(P(P({},n),{lazySink:o.shared.getSink,extraBehaviours:p([VT.config({channel:KF,updateState:function(n,t){return tn.some(t)},initialData:t}),TS({})],n.extraBehaviours),onEscape:function(n){zt(n,ay)},dialogEvents:e,eventOrder:(r={},r[ei()]=["reflecting","receiving"],r[gi()]=["scroll-lock","reflecting","messages","dialog-events","alloy.base.behaviour"],r[pi()]=["alloy.base.behaviour","dialog-events","messages","reflecting","scroll-lock"],r)})))}function aM(n){return S(n,function(n){return"menu"===n.type?function(n){var t=S(n.items,function(n){var t=or(!1);return P(P({},n),{storage:t})});return P(P({},n),{items:t})}(n):n})}function cM(n){return O(n,function(n,t){return"menu"!==t.type?n:O(t.items,function(n,t){return n[t.name]=t.storage,n},n)},{})}function sM(n,t,e){var o=rM(n.internalDialog.title,e),r=function(n,t){var e=LA(n,tn.none(),t,!1);return CM.parts().body(e)}({body:n.internalDialog.body},e),i=aM(n.internalDialog.buttons),u=cM(i),a=GA({buttons:i},e),c=tI(function(){return d},iM(function(){return l},t)),s="normal"!==n.internalDialog.size?"large"===n.internalDialog.size?["tox-dialog--width-lg"]:["tox-dialog--width-md"]:[],f={header:o,body:r,footer:tn.some(a),extraClasses:s,extraBehaviours:[],extraStyles:{}},l=uM(f,n,c,e),d=YA({getRoot:function(){return l},getBody:function(){return CM.getBody(l)},getFooter:function(){return CM.getFooter(l)},getFormWrapper:function(){var n=CM.getBody(l);return ed.getCurrent(n).getOr(n)}},t.redial,u);return{dialog:l,instanceApi:d}}function fM(n,t,e,o){var r,i,u=De("dialog-label"),a=De("dialog-content"),c=dp(function(n,t,e){return ry.sketch({dom:VE('<div class="tox-dialog__header"></div>'),components:[eM(n,tn.some(t),e),oM(),tM(e)],containerBehaviours:Sa([OA.config({mode:"mouse",blockerClass:"blocker",getTarget:function(n){return Hu(n,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])})}({title:n.internalDialog.title,draggable:!0},u,e.shared.providers)),s=dp(function(n,t,e,o){return LA(n,tn.some(t),e,o)}({body:n.internalDialog.body},a,e,o)),f=aM(n.internalDialog.buttons),l=cM(f),d=dp(function(n,t){return WA(n,t)}({buttons:f},e)),m=tI(function(){return p},{onBlock:function(){},onUnblock:function(){},onClose:function(){return t.closeWindow()}}),g=tu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:(r={role:"dialog"},r["aria-labelledby"]=u,r["aria-describedby"]=""+a,r)},eventOrder:(i={},i[ei()]=[VT.name(),bc.name()],i[oi()]=["execute-on-form"],i[gi()]=["reflecting","execute-on-form"],i),behaviours:Sa([wg.config({mode:"cyclic",onEscape:function(n){return zt(n,uy),tn.some(!0)},useTabstopAt:function(n){return!DS(n)&&("button"!==xe(n)||"disabled"!==ke(n,"disabled"))}}),VT.config({channel:KF,updateState:function(n,t){return tn.some(t)},initialData:n}),Tg.config({}),Qd("execute-on-form",m.concat([ne(Ur(),function(n,t){wg.focusIn(n)})])),TS({})]),components:[c.asSpec(),s.asSpec(),d.asSpec()]}),p=YA({getRoot:function(){return g},getFooter:function(){return d.get(g)},getBody:function(){return s.get(g)},getFormWrapper:function(){var n=s.get(g);return ed.getCurrent(n).getOr(n)}},t.redial,l);return{dialog:g,instanceApi:p}}function lM(n){return $(n)&&-1!==rI.indexOf(n.mceAction)}function dM(e,n,o,t){var r,i=rM(e.title,t),u=function(n){var t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[MS({dom:{tag:"iframe",attributes:{src:n.url}},behaviours:Sa([Wy.config({}),Tg.config({})])})]}],behaviours:Sa([wg.config({mode:"acyclic",useTabstopAt:b(DS)})])};return CM.parts().body(t)}(e),a=e.buttons.bind(function(n){return 0===n.length?tn.none():tn.some(GA({buttons:n},t))}),c=nI(function(){return h},iM(function(){return p},n)),s=P(P({},e.height.fold(function(){return{}},function(n){return{height:n+"px","max-height":n+"px"}})),e.width.fold(function(){return{}},function(n){return{width:n+"px","max-width":n+"px"}})),f=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],l=new oI(e.url,{base_uri:new oI(v.window.location.href)}),d=l.protocol+"://"+l.host+(l.port?":"+l.port:""),m=or(tn.none()),g=[Qd("messages",[Oi(function(){var n=Bb(ir.fromDom(v.window),"message",function(n){if(l.isSameOrigin(new oI(n.raw().origin))){var t=n.raw().data;lM(t)?function(n,t,e){switch(e.mceAction){case"insertContent":n.insertContent(e.content);break;case"setContent":n.setContent(e.content);break;case"execCommand":var o=!!en(e.ui)&&e.ui;n.execCommand(e.cmd,o,e.value);break;case"close":t.close();break;case"block":t.block(e.message);break;case"unblock":t.unblock()}}(o,h,t):function(n){return!lM(n)&&$(n)&&yn(n,"mceAction")}(t)&&e.onMessage(h,t)}});m.set(tn.some(n))}),_i(function(){m.get().each(function(n){return n.unbind()})})]),bc.config({channels:(r={},r[ZF]={onReceive:function(n,t){Vu(n.element(),"iframe").each(function(n){n.dom().contentWindow.postMessage(t,d)})}},r)})],p=uM({header:i,body:u,footer:a,extraClasses:f,extraBehaviours:g,extraStyles:s},e,c,t),h=function(t){function n(n){t.getSystem().isConnected()&&n(t)}return{block:function(t){if(!J(t))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){Lt(n,fy,{message:t})})},unblock:function(){n(function(n){zt(n,ly)})},close:function(){n(function(n){zt(n,uy)})},sendMessage:function(t){n(function(n){n.getSystem().broadcastOn([ZF],t)})}}}(p);return{dialog:p,instanceApi:h}}var mM,gM,pM,hM,vM=function(i,r){r.delimiter||(r.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:Sa([wg.config({mode:"flow",selector:"div[role=button]"}),Wy.config({}),Cg.config({}),Qd("elementPathEvents",[Oi(function(e,n){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return wg.focusIn(e)}),i.on("NodeChange",function(n){var t=function(n){for(var t=[],e=n.length;0<e--;){var o=n[e];if(1===o.nodeType&&!BA(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||t.push({name:r.name,element:o}),r.isPropagationStopped())break}}return t}(n.parents);0<t.length?Cg.set(e,function(n){var t=S(n||[],function(t,n){return hp.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":n,"tab-index":-1,"aria-level":n+1},innerHtml:t.name},action:function(n){i.focus(),i.selection.select(t.element),i.nodeChanged()}})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+r.delimiter+" "}};return O(t.slice(1),function(n,t){var e=n;return e.push(o),e.push(t),e},[t[0]])}(t)):Cg.set(e,[])})})])]),components:[]}},bM=function(l){function d(){return e.bind(MD.getHeader)}function m(){return K.value(v)}function g(){return e.bind(function(n){return MD.getThrobber(n)}).getOrDie("Could not find throbber element")}var n,t=l.inline,p=t?GD:LD,h=lp(l)?lD:pD,e=tn.none(),o=At(),r=o.browser.isIE()?["tox-platform-ie"]:[],i=o.deviceType.isTouch()?["tox-platform-touch"]:[],u=pp(l),a=_h.isRtl()?{attributes:{dir:"rtl"}}:{},c={attributes:(n={},n[jc]=u?Wa.TopToBottom:Wa.BottomToTop,n)},v=tu({dom:P({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(r).concat(i)},a),behaviours:Sa([Mf.config({useFixed:function(){return h.isDocked(d)}})])}),s=dp({dom:{tag:"div",classes:["tox-anchorbar"]}}),b=gO(v,l,function(){return e.bind(function(n){return s.getOpt(n)}).getOrDie("Could not find a anchor bar element")}),f=MD.parts().menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:b,onEscape:function(){l.focus()}}),y=ip(l),x=MD.parts().toolbar(P({dom:{tag:"div",classes:["tox-toolbar"]},getSink:m,backstage:b,onEscape:function(){l.focus()},type:y,lazyToolbar:function(){return e.bind(function(n){return MD.getToolbar(n)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return d().getOrDie("Could not find header element")}},c)),w=MD.parts()["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},onEscape:function(){},type:y}),S=MD.parts().socket({dom:{tag:"div",classes:["tox-edit-area"]}}),C=MD.parts().sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),k=MD.parts().throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:b}),O=l.getParam("statusbar",!0,"boolean")&&!t?tn.some(DA(l,b.shared.providers)):tn.none(),_={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[S,C]},T=rp(l),E=Tm(l),B=_m(l),D=MD.parts().header({dom:P({tag:"div",classes:["tox-editor-header"]},c),components:z([B?[f]:[],T?[w]:E?[x]:[],sp(l)?[]:[s.asSpec()]]),sticky:lp(l),editor:l,getSink:m}),A=z([u?[D]:[],t?[]:[_],u?[]:[D]]),M=z([[{dom:{tag:"div",classes:["tox-editor-container"]},components:A}],t?[]:O.toArray(),[k]]),F=fp(l),I=P(P({role:"application"},_h.isRtl()?{dir:"rtl"}:{}),F?{"aria-hidden":"true"}:{}),R=tu(MD.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(u?[]:["tox-tinymce--toolbar-bottom"]).concat(i).concat(r),styles:P({visibility:"hidden"},F?{opacity:"0",border:"0"}:{}),attributes:I},components:M,behaviours:Sa([wg.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})])}));e=tn.some(R),l.shortcuts.add("alt+F9","focus menubar",function(){MD.focusMenubar(R)}),l.shortcuts.add("alt+F10","focus toolbar",function(){MD.focusToolbar(R)});var V=$b(R),H=$b(v);KB(l,V,H);function N(){var n=WD(jE(l)),t=WD(function(n){return UE(n).getOr(wm(n))}(l));return l.inline||(so("div","width",t)&&ro(R.element(),"width",t),so("div","height",n)?ro(R.element(),"height",n):ro(R.element(),"height","200px")),n}return{mothership:V,uiMothership:H,backstage:b,renderUI:function(){h.setup(l,d),QD(l,b),iB(l,m,b),function(o){var r=o.ui.registry.getAll().sidebars;fn(mn(r),function(t){function e(){return tn.from(o.queryCommandValue("ToggleSidebar")).is(t)}var n=r[t];o.ui.registry.addToggleButton(t,{icon:n.icon,tooltip:n.tooltip,onAction:function(n){o.execCommand("ToggleSidebar",!1,t),n.setActive(e())},onSetup:function(n){function t(){return n.setActive(e())}return o.on("ToggleSidebar",t),function(){o.off("ToggleSidebar",t)}}})})}(l),function(e,t,o){function r(n){n!==i.get()&&(HE(t(),n,o.providers),i.set(n))}var i=or(!1),u=or(tn.none());e.on("ProgressState",function(n){if(u.get().each(tp.clearTimeout),rn(n.time)){var t=tp.setEditorTimeout(e,function(){return r(n.state)},n.time);u.set(tn.some(t))}else r(n.state),u.set(tn.none())})}(l,g,b.shared),L(function(n){return n.getParam("toolbar_groups",{},"object")}(l),function(n,t){l.ui.registry.addGroupToolbarButton(t,n)});var n=l.ui.registry.getAll(),t=n.buttons,e=n.menuItems,o=n.contextToolbars,r=n.sidebars,i=Em(l),u={menuItems:e,menus:l.settings.menu?L(l.settings.menu,function(n){return P(P({},n),{items:n.items})}):{},menubar:l.settings.menubar,toolbar:i.getOrThunk(function(){return l.getParam("toolbar",!0)}),allowToolbarGroups:y===Dm.floating,buttons:t,sidebar:r};qB(l,o,v,{backstage:b}),TA(l,v);var a=l.getElement(),c=N(),s={mothership:V,uiMothership:H,outerContainer:R},f={targetNode:a,height:c};return p.render(l,s,u,b,f)},getUi:function(){return{channels:{broadcastAll:H.broadcast,broadcastOn:H.broadcastOn,register:function(){}}}}}},yM=function(n,t){var e=tn.from(ke(n,"id")).fold(function(){var n=De("dialog-label");return Ce(t,"id",n),n},l);Ce(n,"aria-labelledby",e)},xM=nn([tt("lazySink"),st("dragBlockClass"),wt("getBounds",xu),pt("useTabstopAt",nn(!0)),pt("eventOrder",{}),Ls("modalBehaviours",[wg]),Ju("onExecute"),Qu("onEscape")]),wM={sketch:l},SM=nn([kl({name:"draghandle",overrides:function(n,t){return{behaviours:Sa([OA.config({mode:"mouse",getTarget:function(n){return Ru(n,'[role="dialog"]').getOr(n)},blockerClass:n.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:n.getDragBounds})])}}}),Sl({schema:[tt("dom")],name:"title"}),Sl({factory:wM,schema:[tt("dom")],name:"close"}),Sl({factory:wM,schema:[tt("dom")],name:"body"}),kl({factory:wM,schema:[tt("dom")],name:"footer"}),Cl({factory:{sketch:function(n,t){return P(P({},n),{dom:t.dom,components:t.components})}},schema:[pt("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),pt("components",[])],name:"blocker"})]),CM=Ml({name:"ModalDialog",configFields:xM(),partFields:SM(),factory:function(o,n,t,r){var a=De("alloy.dialog.busy"),c=De("alloy.dialog.idle"),s=Sa([wg.config({mode:"special",onTab:function(){return tn.some(!0)},onShiftTab:function(){return tn.some(!0)}}),Tg.config({})]),e=De("modal-events"),i=P(P({},o.eventOrder),{"alloy.system.attached":[e].concat(o.eventOrder["alloy.system.attached"]||[])});return{uid:o.uid,dom:o.dom,components:n,apis:{show:function(i){var n=o.lazySink(i).getOrDie(),u=or(tn.none()),t=r.blocker(),e=n.getSystem().build(P(P({},t),{components:t.components.concat([eu(i)]),behaviours:Sa([Tg.config({}),Qd("dialog-blocker-events",[ne(Ur(),function(){wg.focusIn(i)}),qt(c,function(n,t){Oe(i.element(),"aria-busy")&&(_e(i.element(),"aria-busy"),u.get().each(function(n){return Cg.remove(i,n)}))}),qt(a,function(n,t){Ce(i.element(),"aria-busy","true");var e=t.event().getBusySpec();u.get().each(function(n){Cg.remove(i,n)});var o=e(i,s),r=n.getSystem().build(o);u.set(tn.some(r)),Cg.append(i,eu(r)),r.hasConfigured(wg)&&wg.focusIn(r)})])])}));ks(n,e),wg.focusIn(i)},hide:function(t){ce(t.element()).each(function(n){t.getSystem().getByDom(n).each(function(n){_s(n)})})},getBody:function(n){return rf(n,o,"body")},getFooter:function(n){return rf(n,o,"footer")},setIdle:function(n){zt(n,c)},setBusy:function(n,t){Lt(n,a,{getBusySpec:t})}},eventOrder:i,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Us(o.modalBehaviours,[Cg.config({}),wg.config({mode:"cyclic",onEnter:o.onExecute,onEscape:o.onEscape,useTabstopAt:o.useTabstopAt}),Qd(e,[Oi(function(n){yM(n.element(),rf(n,o,"title").element()),function(n,t){var e=tn.from(ke(n,"id")).fold(function(){var n=De("dialog-describe");return Ce(t,"id",n),n},l);Ce(n,"aria-describedby",e)}(n.element(),rf(n,o,"body").element())})])])}},apis:{show:function(n,t){n.show(t)},hide:function(n,t){n.hide(t)},getBody:function(n,t){return n.getBody(t)},getFooter:function(n,t){return n.getFooter(t)},setBusy:function(n,t,e){n.setBusy(t,e)},setIdle:function(n,t){n.setIdle(t)}}}),kM=[ot("type"),ot("text"),rt("level",["info","warn","error","success"]),ot("icon"),pt("url","")],OM=jo(kM),_M=[ot("type"),ot("text"),xt("disabled",!1),xt("primary",!1),Xo("name","name",Ho(function(){return De("button-name")}),Qo),dt("icon"),xt("borderless",!1)],TM=jo(_M),EM=[ot("type"),ot("name"),ot("label"),xt("disabled",!1)],BM=jo(EM),DM=Zo,AM=[ot("type"),ot("name")],MM=AM.concat([dt("label")]),FM=jo(MM),IM=Qo,RM=jo(MM),VM=Qo,HM=jo(MM),NM=Ln(Yo),PM=MM.concat([xt("sandboxed",!0)]),zM=jo(PM),LM=Qo,jM=MM.concat([dt("inputMode"),dt("placeholder"),xt("maximized",!1),xt("disabled",!1)]),UM=jo(jM),WM=Qo,GM=MM.concat([at("items",[ot("text"),ot("value")]),vt("size",1),xt("disabled",!1)]),XM=jo(GM),YM=Qo,qM=MM.concat([xt("constrain",!0),xt("disabled",!1)]),KM=jo(qM),JM=jo([ot("width"),ot("height")]),$M=MM.concat([dt("placeholder"),xt("maximized",!1),xt("disabled",!1)]),QM=jo($M),ZM=Qo,nF=MM.concat([yt("filetype","file",["image","media","file"]),pt("disabled",!1)]),tF=jo(nF),eF=jo([ot("value"),pt("meta",{})]),oF=AM.concat([bt("tag","textarea"),ot("scriptId"),ot("scriptUrl"),(mM="settings",gM=undefined,ht(mM,gM,er))]),rF=AM.concat([bt("tag","textarea"),it("init")]),iF=Xn(function(n){return qn("customeditor.old",zn(rF),n).orThunk(function(){return qn("customeditor.new",zn(oF),n)})}),uF=Qo,aF=[ot("type"),ot("html"),yt("presets","presentation",["presentation","document"])],cF=jo(aF),sF=MM.concat([et("currentState",jo([tt("blob"),ot("url")]))]),fF=jo(sF),lF=MM.concat([pt("columns","auto")]),dF=jo(lF),mF=Gn([ot("value"),ot("text"),ot("icon")]),gF=[ot("type"),ct("header",Qo),ct("cells",Ln(Qo))],pF=jo(gF),hF=qo(function(){return $n("type",{alertbanner:OM,bar:jo(function(n){return[ot("type"),n]}(MA("bar"))),button:TM,checkbox:BM,colorinput:FM,colorpicker:RM,dropzone:HM,grid:jo(AA(MA("grid"))),iframe:zM,input:UM,selectbox:XM,sizeinput:KM,textarea:QM,urlinput:tF,customeditor:iF,htmlpanel:cF,imagetools:fF,collection:dF,label:jo(function(n){return[ot("type"),ot("label"),n]}(MA("label"))),table:pF,panel:bF})}),vF=[ot("type"),pt("classes",[]),ct("items",hF)],bF=jo(vF),yF=[Xo("name","name",Ho(function(){return De("tab-name")}),Qo),ot("title"),ct("items",hF)],xF=[ot("type"),at("tabs",yF)],wF=jo(xF),SF=jo([ot("type"),ot("name")].concat(dh)),CF=Zo,kF=[Xo("name","name",Ho(function(){return De("button-name")}),Qo),dt("icon"),yt("align","end",["start","end"]),xt("primary",!1),xt("disabled",!1)],OF=p(kF,[ot("text")]),_F=p([rt("type",["submit","cancel","custom"])],OF),TF=p([rt("type",["menu"]),dt("text"),dt("tooltip"),dt("icon"),ct("items",SF)],kF),EF=OF,BF=Qn("type",{submit:_F,cancel:_F,custom:_F,menu:TF}),DF=jo([ot("title"),et("body",$n("type",{panel:bF,tabpanel:wF})),bt("size","normal"),ct("buttons",BF),pt("initialData",{}),wt("onAction",Z),wt("onChange",Z),wt("onSubmit",Z),wt("onClose",Z),wt("onCancel",Z),pt("onTabChange",Z)]),AF=function(n){return $(n)?[n].concat(E(H(n),AF)):Q(n)?E(n,AF):[]},MF={checkbox:DM,colorinput:IM,colorpicker:VM,dropzone:NM,input:WM,iframe:LM,sizeinput:JM,selectbox:YM,size:JM,textarea:ZM,urlinput:eF,customeditor:uF,collection:mF,togglemenuitem:CF},FF=jo(p([rt("type",["cancel","custom"])],EF)),IF=jo([ot("title"),ot("url"),lt("height"),lt("width"),(pM="buttons",hM=FF,ft(pM,Ln(hM))),wt("onAction",Z),wt("onCancel",Z),wt("onClose",Z),wt("onMessage",Z)]),RF={open:function(n,t){var e=RA(t);return n(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(n,t){return n(Kn(function(n){return qn("dialog",IF,n)}(t)))},redial:function(n){return RA(n)}},VF=Al({name:"TabButton",configFields:[pt("uid",undefined),tt("value"),Xo("dom","dom",No(function(){return{attributes:{role:"tab",id:De("aria"),"aria-selected":"false"}}}),Jo()),st("action"),pt("domModification",{}),Ls("tabButtonBehaviours",[Tg,wg,tl]),tt("view")],factory:function(n,t){return{uid:n.uid,dom:n.dom,components:n.components,events:am(n.action),behaviours:Us(n.tabButtonBehaviours,[Tg.config({}),wg.config({mode:"execution",useSpace:!0,useEnter:!0}),tl.config({store:{mode:"memory",initialValue:n.value}})]),domModification:n.domModification}}}),HF=nn([tt("tabs"),tt("dom"),pt("clickToDismiss",!1),Ls("tabbarBehaviours",[fd,wg]),Yu(["tabClass","selectedClass"])]),NF=Ol({factory:VF,name:"tabs",unit:"tab",overrides:function(o){function r(n,t){fd.dehighlight(n,t),Lt(n,wi(),{tabbar:n,button:t})}function i(n,t){fd.highlight(n,t),Lt(n,xi(),{tabbar:n,button:t})}return{action:function(n){var t=n.getSystem().getByUid(o.uid).getOrDie(),e=fd.isHighlighted(t,n);(e&&o.clickToDismiss?r:e?Z:i)(t,n)},domModification:{classes:[o.markers.tabClass]}}}}),PF=nn([NF]),zF=Ml({name:"Tabbar",configFields:HF(),partFields:PF(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Us(n.tabbarBehaviours,[fd.config({highlightClass:n.markers.selectedClass,itemClass:n.markers.tabClass,onHighlight:function(n,t){Ce(t.element(),"aria-selected","true")},onDehighlight:function(n,t){Ce(t.element(),"aria-selected","false")}}),wg.config({mode:"flow",getInitial:function(n){return fd.getHighlighted(n).map(function(n){return n.element()})},selector:"."+n.markers.tabClass,executeOnMove:!0})])}}}),LF=Al({name:"Tabview",configFields:[Ls("tabviewBehaviours",[Cg])],factory:function(n,t){return{uid:n.uid,dom:n.dom,behaviours:Us(n.tabviewBehaviours,[Cg.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),jF=nn([pt("selectFirst",!0),Ku("onChangeTab"),Ku("onDismissTab"),pt("tabs",[]),Ls("tabSectionBehaviours",[])]),UF=Sl({factory:zF,schema:[tt("dom"),ut("markers",[tt("tabClass"),tt("selectedClass")])],name:"tabbar",defaults:function(n){return{tabs:n.tabs}}}),WF=Sl({factory:LF,name:"tabview"}),GF=nn([UF,WF]),XF=Ml({name:"TabSection",configFields:jF(),partFields:GF(),factory:function(r,n,t,e){function o(o){var t=tl.getValue(o);of(o,r,"tabview").each(function(e){_(r.tabs,function(n){return n.value===t}).each(function(n){var t=n.view();(function(n,t){return tn.from(ke(n,t))})(o.element(),"id").each(function(n){Ce(e.element(),"aria-labelledby",n)}),Cg.set(e,t),r.onChangeTab(e,o,t)})})}function i(n,t){of(n,r,"tabbar").each(function(n){t(n).each(jt)})}return{uid:r.uid,dom:r.dom,components:n,behaviours:js(r.tabSectionBehaviours),events:Gt(z([r.selectFirst?[Oi(function(n,t){i(n,fd.getFirst)})]:[],[qt(xi(),function(n,t){var e=t.event().button();o(e)}),qt(wi(),function(n,t){var e=t.event().button();r.onDismissTab(n,e)})]])),apis:{getViewItems:function(n){return of(n,r,"tabview").map(function(n){return Cg.contents(n)}).getOr([])},showTab:function(n,e){i(n,function(t){var n=fd.getCandidates(t);return _(n,function(n){return tl.getValue(n)===e}).filter(function(n){return!fd.isHighlighted(t,n)})})}}}},apis:{getViewItems:function(n,t){return n.getViewItems(t)},showTab:function(n,t,e){n.showTab(t,e)}}}),YF="send-data-to-section",qF="send-data-to-view",KF=De("update-dialog"),JF=De("update-title"),$F=De("update-body"),QF=De("update-footer"),ZF=De("body-send-message"),nI=function(i,n){function t(n,r){return qt(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){VT.getState(t).get().each(function(n){e(n,t)})};return p(jA(t,n),[t(cy,function(n,t,e){t.onAction(n,{name:e.name()})})])},tI=function(i,n){function t(n,r){return qt(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){VT.getState(t).get().each(function(n){e(n.internalDialog,t)})};return p(jA(t,n),[t(sy,function(n,t){return t.onSubmit(n)}),t(iy,function(n,t,e){t.onChange(n,{name:e.name()})}),t(cy,function(n,t,e,o){function r(){return wg.focusIn(o)}var i=_a();t.onAction(n,{name:e.name(),value:e.value()}),_a().fold(function(){r()},function(n){!Mr(o.element(),n)||Oe(n,"disabled")?r():Mr(n,i.getOrNull())&&Oe(i.getOrDie(),"disabled")&&r()})}),t(dy,function(n,t,e){t.onTabChange(n,{newTabName:e.name(),oldTabName:e.oldName()})}),_i(function(n){var t=i();tl.setValue(n,t.getData())})])},eI=Vh.deviceType.isTouch(),oI=tinymce.util.Tools.resolve("tinymce.util.URI"),rI=["insertContent","setContent","execCommand","close","block","unblock"],iI=function(n){var l=n.backstage,d=n.editor,m=lp(d),g=pp(d),e=function(c){var s=c.backstage.shared;return{open:function(n,t){function e(){CM.hide(u),t()}var o=dp(GC({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:tn.none()},"cancel",c.backstage)),r=JA(),i=KA(e,s.providers),u=tu(nM({lazySink:function(){return s.getSink()},header:qA(r,i),body:$A(n,s.providers),footer:tn.some(QA(ZA([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[qt(ay,e)],eventOrder:{}}));CM.show(u);var a=o.get(u);Tg.focus(a)}}}(n),o=function(s){var f=s.backstage.shared;return{open:function(n,t){function e(n){CM.hide(a),t(n)}var o=dp(GC({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:tn.none()},"submit",s.backstage)),r=GC({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:tn.none()},"cancel",s.backstage),i=JA(),u=KA(function(){return e(!1)},f.providers),a=tu(nM({lazySink:function(){return f.getSink()},header:qA(i,u),body:$A(n,f.providers),footer:tn.some(QA(ZA([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[qt(ay,function(){return e(!1)}),qt(sy,function(){return e(!0)})],eventOrder:{}}));CM.show(a);var c=o.get(a);Tg.focus(c)}}}(n),r=function(n,e){return RF.openUrl(function(n){var t=dM(n,{closeWindow:function(){CM.hide(t.dialog),e(t.instanceApi)}},d,l);return CM.show(t.dialog),t.instanceApi},n)},i=function(n,i){return RF.open(function(n,t,e){var o=t,r=sM({dataValidator:e,initialData:o,internalDialog:n},{redial:RF.redial,closeWindow:function(){CM.hide(r.dialog),i(r.instanceApi)}},l);return CM.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},n)},u=function(n,c,s,f){return RF.open(function(n,t,e){function o(){return i.on(function(n){Qg.reposition(n),aD.refresh(n)})}var r=function(n,t){return Kn(qn("data",t,n))}(t,e),i=function(){var t=or(tn.none());return{clear:function(){t.set(tn.none())},set:function(n){t.set(tn.some(n))},isSet:function(){return t.get().isSome()},on:function(n){t.get().each(n)}}}(),u=fM({dataValidator:e,initialData:r,internalDialog:n},{redial:RF.redial,closeWindow:function(){i.on(Qg.hide),d.off("ResizeEditor",o),i.clear(),s(u.instanceApi)}},l,f),a=tu(Qg.sketch(P(P({lazySink:l.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},g?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:Sa(p([Qd("window-manager-inline-events",[qt(hi(),function(n,t){zt(u.dialog,ay)})])],function(n,t,e){return t&&e?[]:[aD.config({contextual:{lazyContext:function(){return tn.some(bu(ir.fromDom(n.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]}(d,m,g)))})));return i.set(a),Qg.showWithin(a,c,eu(u.dialog),tn.some(Ji())),m&&g||(aD.refresh(a),d.on("ResizeEditor",o)),u.instanceApi.setData(r),wg.focusIn(u.dialog),u.instanceApi},n)};return{open:function(n,t,e){return t!==undefined&&"toolbar"===t.inline?u(n,l.shared.anchors.inlineDialog(),e,t.ariaAttrs):t!==undefined&&"cursor"===t.inline?u(n,l.shared.anchors.cursor(),e,t.ariaAttrs):i(n,e)},openUrl:function(n,t){return r(n,t)},alert:function(n,t){e.open(n,function(){t()})},close:function(n){n.close()},confirm:function(n,t){o.open(n,function(n){t(n)})}}};!function pI(){n.add("silver",function(n){var t=bM(n),e=t.uiMothership,o=t.backstage,r=t.renderUI,i=t.getUi;Mb(n,o.shared);var u=iI({editor:n,backstage:o});return{renderUI:r,getWindowManagerImpl:nn(u),getNotificationManagerImpl:function(){return yp(n,{backstage:o},e)},ui:i()}})}()}(window);