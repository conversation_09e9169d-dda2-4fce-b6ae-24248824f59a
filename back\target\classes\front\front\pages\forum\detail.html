<!-- 论坛详情 -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>论坛详情</title>
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <!-- 样式 -->
    <link rel="stylesheet" href="../../css/style.css" />
    <!-- 主题（主要颜色设置） -->
    <link rel="stylesheet" href="../../css/theme.css" />
    <!-- 通用的css -->
    <link rel="stylesheet" href="../../css/common.css" />
</head>
<style>
    #swiper {
        overflow: hidden;
    }
    #swiper .layui-carousel-ind li {
        width: 20px;
        height: 10px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0,0,0,.3);
        border-radius: 6px;
        background-color: #f7f7f7;
        box-shadow: 0 0 6px rgba(255,0,0,.8);
    }
    #swiper .layui-carousel-ind li.layui-this {
        width: 30px;
        height: 10px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0,0,0,.3);
        border-radius: 6px;
    }

    .index-title {
        text-align: center;
        box-sizing: border-box;
        width: 980px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    .forum-container .btn-container {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        width: 100%;
        flex-wrap: wrap;
        justify-content: space-between !important;
    }
    .forum-container .btn-container button i {
        margin-right: 4px;
    }
</style>
<body style="padding-bottom: 20px">

<div id="app">
    <!-- 轮播图 -->
    <div class="layui-carousel" id="swiper" :style='{"boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"0 auto","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
        <div carousel-item id="swiper-item">
            <div v-for="(item,index) in swiperList" :key="index">
                <img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" />
            </div>
        </div>
    </div>
    <!-- 轮播图 -->

    <!-- 标题 -->
    <div class="index-title sub_backgroundColor sub_borderColor" :style='{"padding":"10px","margin":"10px auto","borderColor":"rgba(0,0,0,.3)","color":"rgba(255, 255, 255, 1)","borderRadius":"4px","borderWidth":"0","fontSize":"20px","borderStyle":"solid","height":"auto"}'>
        <span>FORUM / INFORMATION</span><span>论坛</span>
    </div>
    <!-- 标题 -->

    <div class="forum-container">
        <h1 class="title">{{detail.forumName}}</h1>
        <div v-if="detail.yonghuId" class="auth-container">
            发布人{{detail.yonghuName}}&nbsp;&nbsp;&nbsp;发布人权限：用户&nbsp;&nbsp;&nbsp;时间：{{detail.insertTime}}
        </div>
        <div v-if="detail.ziyuanzheId" class="auth-container">
            发布人{{detail.ziyuanzheName}}&nbsp;&nbsp;&nbsp;发布人权限：自愿者&nbsp;&nbsp;&nbsp;时间：{{detail.insertTime}}
        </div>
        <div v-if="detail.usersId" class="auth-container">
            发布人：管理员&nbsp;&nbsp;&nbsp;发布人权限：管理员&nbsp;&nbsp;&nbsp;时间：{{detail.insertTime}}
        </div>
        <div class="content" v-html="detail.forumContent">
        </div>

        <div class="btn-container main_backgroundColor" :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"10px 0 10px 0","borderColor":"rgba(0,0,0,.3)","borderRadius":"4px","alignItems":"center","borderWidth":"0","borderStyle":"solid","justifyContent":"flex-end","height":"64px"}'>
            <div class="title" style="color:#666;font-size: 18px;">
                评论列表
            </div>
            <button  class="layui-btn layui-btn-lg btn-theme main_backgroundColor"
                     :style='{"padding":"0 15px","boxShadow":"0 0 8px rgba(0,0,0,0)","margin":"0 0 0 10px","borderColor":"rgba(135, 206, 250, 1)","color":"#fff","borderRadius":"4px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"40px"}'
                     @click="dialogFormVisible = true">
                <i class="layui-icon">&#xe654;</i> 点击评论
            </button>
        </div>

        <div class="message-list" v-if="dataList&&dataList.length" :style='{"padding":"0 20px 20px","boxShadow":"0 0 0px rgba(255,0,0,.3)","margin":"0","borderColor":"rgba(135, 206, 250, 1)","backgroundColor":"#fff","borderRadius":"4px","borderWidth":"0","borderStyle":"solid"}'>
            <div :style='{"padding":"20px 0","boxShadow":"0 0 0px rgba(255,0,0,0)","margin":"0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"#fff","borderRadius":"0","borderWidth":"0 0 1px 0","borderStyle":"solid"}'  class="message-item" v-for="(item,index) in dataList" v-bind:key="index" >
                <div v-if="item.yonghuId" class="username-container" style="display: flex;align-items: center;">
                    <img :style='{"boxShadow":"0 0 0px rgba(255,0,0,.3)","margin":"0 10px 0 0","borderColor":"rgba(0,0,0,.3)","borderRadius":"50%","borderWidth":"0","width":"40px","borderStyle":"solid","height":"40px"}' class="avator" :src="item.yonghuPhoto">
                    <span style="display: inline-block;" :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(255,0,0,.3)","margin":"0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"#fff","color":"rgba(6, 82, 121, 1)","borderRadius":"4px","borderWidth":"0","width":"auto","lineHeight":"28px","fontSize":"14px","borderStyle":"solid"}' class="username">用户：{{item.yonghuName}}</span>
                </div>
                <div v-if="item.ziyuanzheId" class="username-container" style="display: flex;align-items: center;">
                    <img :style='{"boxShadow":"0 0 0px rgba(255,0,0,.3)","margin":"0 10px 0 0","borderColor":"rgba(0,0,0,.3)","borderRadius":"50%","borderWidth":"0","width":"40px","borderStyle":"solid","height":"40px"}' class="avator" :src="item.ziyuanzhePhoto">
                    <span style="display: inline-block;" :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(255,0,0,.3)","margin":"0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"#fff","color":"rgba(6, 82, 121, 1)","borderRadius":"4px","borderWidth":"0","width":"auto","lineHeight":"28px","fontSize":"14px","borderStyle":"solid"}' class="username">自愿者：{{item.ziyuanzheName}}</span>
                </div>
                <div v-if="item.usersId" class="username-container" style="display: flex;align-items: center;">
                    <img :style='{"boxShadow":"0 0 0px rgba(255,0,0,.3)","margin":"0 10px 0 0","borderColor":"rgba(0,0,0,.3)","borderRadius":"50%","borderWidth":"0","width":"40px","borderStyle":"solid","height":"40px"}' class="avator" src="../../img/avator.png">
                    <span style="display: inline-block;" :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(255,0,0,.3)","margin":"0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"#fff","color":"rgba(6, 82, 121, 1)","borderRadius":"4px","borderWidth":"0","width":"auto","lineHeight":"28px","fontSize":"14px","borderStyle":"solid"}' class="username">管理员</span>
                </div>
                <div class="content" class="content" style="margin: 0;flex: 1;">
                    <span style="display: inline-block;" class="message main_color" :style='{"padding":"8px","boxShadow":"0 0 0px rgba(255,0,0,.3)","margin":"8px 0 0 50px","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(255, 0, 0, 0)","borderRadius":"4px","borderWidth":"0","fontSize":"14px","borderStyle":"solid"}'>
                        {{item.forumContent}}
                    </span>
                </div>
            </div>
        </div>

        <div class="pager" id="pager"></div>
    </div>



    <el-dialog title="评论" :visible.sync="dialogFormVisible">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
            <el-form-item label="评论内容" :label-width="formLabelWidth" prop="forumContent">
                <el-input type="textarea" v-model="ruleForm.forumContent"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="addComment()">确 定</el-button>
        </div>
    </el-dialog>




</div>

<!-- layui -->
<script src="../../layui/layui.js"></script>
<!-- vue -->
<script src="../../js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="../../xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="../../xznstatic/css/element.min.css">
<!-- 组件配置信息 -->
<script src="../../js/config.js"></script>
<!-- 扩展插件配置信息 -->
<script src="../../modules/config.js"></script>
<!-- 工具方法 -->
<script src="../../js/utils.js"></script>

<script>
    var vue = new Vue({
        el: '#app',
        data: {
            // 轮播图
            swiperList: [{
                img: '../../img/banner.jpg'
            }],
            id:null,
            detail: {},
            dataList: [],
            dialogFormVisible: false,
            formLabelWidth: '120px',
            ruleForm:{
                forumStateTypes: 2,
                forumContent: '',
            },
            rules: {
                forumContent: [
                    { required: true, message: '请输入回复内容', trigger: 'blur' }
                ],
            },
        },
        /*created () {
            this.pageList()
        },*/
        methods: {
            jump(url) {
                jump(url)
            },
            addComment(){
                let _this = this;
                this.ruleForm.superIds = this.detail.id
                this.$refs["ruleForm"].validate((valid) => {
                    if (valid) {
                        layui.http.requestJson('forum/save', 'post',_this.ruleForm, function (res) {
                            layer.msg('回复成功', {
                                time: 1000,
                                icon: 6
                            }, function () {
                                _this.pageList()
                                _this.dialogFormVisible = false
                            });
                        });
                    }
                });
            },
            pageList() {
                let _this = this
                layui.http.request('forum/info/' + _this.id, 'get', {}, function(res) {
                    _this.detail = res.data
                    var data = {
                        superIds : _this.detail.id,
                        forumStateTypes:2
                    }
                    //评论信息
                    layui.http.request('forum/list', 'get', data, function(res) {
                        _this.dataList = res.data.list
                    });
                });
            }
        }
    })

    layui.use(['layer', 'element', 'carousel', 'laypage', 'http', 'jquery'], function() {
        var layer = layui.layer;
        var element = layui.element;
        var carousel = layui.carousel;
        var laypage = layui.laypage;
        var http = layui.http;
        var jquery = layui.jquery;

        vue.id = http.getParam('id');

        // 获取轮播图 数据
        http.request('config/list', 'get', {
            page: 1,
            limit: 5
        }, function(res) {
            if (res.data.list.length > 0) {
                var swiperItemHtml = '';
                for (let item of res.data.list) {
                    if (item.value != "" && item.value != null) {
                        swiperItemHtml +=
                                '<div>' +
                                '<img class="swiper-item" src="' + item.value + '">' +
                                '</div>';
                    }
                }
                jquery('#swiper-item').html(swiperItemHtml);
                // 轮播图
                vue.$nextTick(() => {
                    carousel.render({
                    elem: '#swiper',
                    width: '100%',
                    height: '450px',
                    arrow: 'hover',
                    anim: 'default',
                    autoplay: 'true',
                    interval: '3000',
                    indicator: 'inside'
                });

            })
            }
        });


        vue.pageList()

    });
</script>
</body>
</html>

