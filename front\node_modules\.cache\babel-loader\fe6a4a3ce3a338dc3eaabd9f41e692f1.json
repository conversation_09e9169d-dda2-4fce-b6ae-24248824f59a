{"remainingRequest": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js!C:\\code\\t\\157\\front\\src\\utils\\storage.js", "dependencies": [{"path": "C:\\code\\t\\157\\front\\src\\utils\\storage.js", "mtime": 1645616829794}, {"path": "C:\\code\\t\\157\\front\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\code\\t\\157\\front\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Y29uc3Qgc3RvcmFnZSA9IHsKICBzZXQoa2V5LCB2YWx1ZSkgewogICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oa2V5LCBKU09OLnN0cmluZ2lmeSh2YWx1ZSkpOwogIH0sCiAgZ2V0KGtleSkgewogICAgcmV0dXJuIGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSkgPyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShrZXkpLnJlcGxhY2UoJyInLCAnJykucmVwbGFjZSgnIicsICcnKSA6ICIiOwogIH0sCiAgZ2V0T2JqKGtleSkgewogICAgcmV0dXJuIGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSkgPyBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSkpIDogbnVsbDsKICB9LAogIHJlbW92ZShrZXkpIHsKICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7CiAgfSwKICBjbGVhcigpIHsKICAgIGxvY2FsU3RvcmFnZS5jbGVhcigpOwogIH0KfTsKZXhwb3J0IGRlZmF1bHQgc3RvcmFnZTs="}, {"version": 3, "names": ["storage", "set", "key", "value", "localStorage", "setItem", "JSON", "stringify", "get", "getItem", "replace", "get<PERSON><PERSON>j", "parse", "remove", "removeItem", "clear"], "sources": ["C:/code/t/157/front/src/utils/storage.js"], "sourcesContent": ["const storage = {\r\n    set(key, value) {\r\n        localStorage.setItem(key, JSON.stringify(value));\r\n    },\r\n    get(key) {\r\n        return localStorage.getItem(key)?localStorage.getItem(key).replace('\"','').replace('\"',''):\"\";\r\n    },\r\n    getObj(key) {\r\n        return localStorage.getItem(key)?JSON.parse(localStorage.getItem(key)):null;\r\n    },\r\n    remove(key) {\r\n        localStorage.removeItem(key);\r\n    },\r\n    clear() {\r\n\tlocalStorage.clear();\r\n    }\r\n}\r\nexport default storage;\r\n"], "mappings": "AAAA,MAAMA,OAAO,GAAG;EACZC,GAAGA,CAACC,GAAG,EAAEC,KAAK,EAAE;IACZC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAEI,IAAI,CAACC,SAAS,CAACJ,KAAK,CAAC,CAAC;EACpD,CAAC;EACDK,GAAGA,CAACN,GAAG,EAAE;IACL,OAAOE,YAAY,CAACK,OAAO,CAACP,GAAG,CAAC,GAACE,YAAY,CAACK,OAAO,CAACP,GAAG,CAAC,CAACQ,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC,GAAC,EAAE;EACjG,CAAC;EACDC,MAAMA,CAACT,GAAG,EAAE;IACR,OAAOE,YAAY,CAACK,OAAO,CAACP,GAAG,CAAC,GAACI,IAAI,CAACM,KAAK,CAACR,YAAY,CAACK,OAAO,CAACP,GAAG,CAAC,CAAC,GAAC,IAAI;EAC/E,CAAC;EACDW,MAAMA,CAACX,GAAG,EAAE;IACRE,YAAY,CAACU,UAAU,CAACZ,GAAG,CAAC;EAChC,CAAC;EACDa,KAAKA,CAAA,EAAG;IACXX,YAAY,CAACW,KAAK,CAAC,CAAC;EACjB;AACJ,CAAC;AACD,eAAef,OAAO", "ignoreList": []}]}